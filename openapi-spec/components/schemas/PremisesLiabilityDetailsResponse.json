{"properties": {"owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Owner Address", "description": "Address of the property owner"}, "premises_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Premises Type", "description": "Type of premises"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location of incident on premises"}, "is_inside": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Inside", "description": "Whether the incident occurred inside or outside"}, "weather_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Weather Type", "description": "Weather conditions at time of incident"}, "third_party_property_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Third Party Property Involved", "description": "Whether third party property was involved"}, "hazard_signs_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Hazard Signs Present", "description": "Whether signs/warnings about hazards were present"}, "claimant_activity_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claimant Activity Description", "description": "Description of claimant's activity at time of incident"}, "security_cameras_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Security Cameras Present", "description": "Whether security cameras were present"}, "insured_relationship": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Insured Relationship", "description": "Insured relationship to premises (owner, tenant, other, unknown)"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "PremisesLiabilityDetailsResponse", "description": "Schema for premises liability details in responses."}