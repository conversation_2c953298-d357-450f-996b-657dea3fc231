{"properties": {"payment_type": {"$ref": "./PaymentType.json", "description": "Type of payment (INDEMNITY, EXPENSE, or DEFENSE)"}, "amount": {"type": "string", "title": "Amount", "description": "Payment amount (must be positive)"}, "payee": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Payee", "description": "Recipient of the payment"}, "payment_date": {"type": "string", "format": "date-time", "title": "Payment Date", "description": "Date the payment was issued"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "Notes about the payment"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "financials_id": {"type": "string", "format": "uuid", "title": "Financials Id", "description": "Associated financials ID"}, "created_by_id": {"type": "string", "format": "uuid", "title": "Created By Id", "description": "User who created the payment"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["payment_type", "amount", "payee", "payment_date", "id", "financials_id", "created_by_id", "created_at", "updated_at"], "title": "PaymentResponse", "description": "Schema for payment response."}