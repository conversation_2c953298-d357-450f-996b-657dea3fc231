{"properties": {"total_entries": {"type": "integer", "title": "Total Entries", "description": "Total number of audit entries"}, "by_entity": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By Entity", "description": "Count of entries by entity type"}, "by_change_type": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By Change Type", "description": "Count of entries by change type"}, "by_user": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By User", "description": "Count of entries by user"}, "recent_activity": {"items": {"$ref": "./AuditTrailResponse.json"}, "type": "array", "title": "Recent Activity", "description": "Most recent audit entries", "default": []}}, "type": "object", "required": ["total_entries", "by_entity", "by_change_type", "by_user"], "title": "AuditSummaryResponse", "description": "Schema for audit summary response."}