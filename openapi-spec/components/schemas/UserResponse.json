{"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "first_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Last Name"}, "role": {"$ref": "./UserRole.json"}, "authority_role": {"$ref": "./AuthorityRole.json", "default": "NO_AUTHORITY"}, "status": {"$ref": "./UserStatus.json", "default": "PENDING"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"type": "string", "maxLength": 50, "title": "Timezone", "default": "UTC"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}, "email_verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Email Verified At"}, "last_password_change_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Password Change At"}, "force_password_change": {"type": "boolean", "title": "Force Password Change"}, "locked_until": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked Until"}, "failed_login_attempts": {"type": "integer", "title": "Failed Login Attempts"}, "permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions"}}, "type": "object", "required": ["email", "first_name", "last_name", "role", "id", "created_at", "updated_at", "force_password_change", "failed_login_attempts"], "title": "UserResponse", "description": "Schema for user data in API responses.", "example": {"authority_role": "BASIC", "created_at": "2024-01-01T00:00:00Z", "department": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "first_name": "<PERSON>", "id": "123e4567-e89b-12d3-a456-426614174000", "job_title": "Senior Adjuster", "last_name": "<PERSON><PERSON>", "permissions": ["VIEW_OWN_CLAIMS", "CREATE_CLAIMS", "EDIT_ASSIGNED_CLAIMS"], "phone_number": "+1234567890", "role": "ADJUSTER", "status": "ACTIVE", "timezone": "America/New_York", "updated_at": "2024-01-01T00:00:00Z"}}