{"properties": {"customer_id": {"type": "string", "format": "uuid", "title": "Customer Id", "description": "ID of the customer the FNOL belongs to"}, "reported_by": {"type": "string", "maxLength": 200, "title": "Reported By", "description": "Name of the person reporting the loss"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the incident"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the FNOL"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_state": {"$ref": "./USState.json", "description": "US state where the incident occurred"}, "incident_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Incident Time", "description": "Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"}, "reporter_relationship": {"anyOf": [{"$ref": "./ReporterRelationship.json"}, {"type": "null"}], "description": "Relationship of the reporter to the claim/incident"}, "communication_preference": {"anyOf": [{"$ref": "./CommunicationPreference.json"}, {"type": "null"}], "description": "Preferred method of communication for responses"}, "fnol_number": {"anyOf": [{"type": "string", "pattern": "^[A-Z0-9]{4}-FNOL-\\d{7}$"}, {"type": "null"}], "title": "Fnol Number", "description": "Unique FNOL identifier in format PREFIX-FNOL-NNNNNNN (auto-generated if not provided)"}}, "type": "object", "required": ["customer_id", "reported_by", "incident_state"], "title": "FNOLCreate", "description": "Create FNOL schema."}