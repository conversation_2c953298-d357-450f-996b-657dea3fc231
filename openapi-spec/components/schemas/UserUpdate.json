{"properties": {"first_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Last Name"}, "role": {"anyOf": [{"$ref": "./UserRole.json"}, {"type": "null"}]}, "authority_role": {"anyOf": [{"$ref": "./AuthorityRole.json"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "./UserStatus.json"}, {"type": "null"}]}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Timezone"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "password": {"anyOf": [{"type": "string", "maxLength": 64, "minLength": 8}, {"type": "null"}], "title": "Password", "description": "New password in plain text", "examples": ["NewStrongP@ssw0rd!"]}}, "type": "object", "title": "UserUpdate", "description": "Schema for updating user data."}