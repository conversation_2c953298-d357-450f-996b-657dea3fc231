{"properties": {"damage_type": {"$ref": "./DamageType.json", "description": "Type of damage (e.g., FIRE, WATER, WIND)"}, "damage_description": {"type": "string", "title": "Damage Description", "description": "Description of the damage"}, "damage_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Damage Severity", "description": "Severity of the damage (Minor, Moderate, Severe)"}, "affected_area": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Affected Area", "description": "Specific area affected by the damage"}, "damage_cause": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Damage Cause", "description": "Cause of the damage"}, "date_of_damage": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date Of Damage", "description": "Date when the damage occurred"}, "repair_status": {"anyOf": [{"$ref": "./RepairStatus.json"}, {"type": "null"}], "description": "Status of repairs"}, "repair_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Description", "description": "Description of the repair work"}, "repair_vendor": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Vendor performing the repairs"}, "estimated_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost", "description": "Estimated cost of repairs"}, "actual_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost", "description": "Actual cost of repairs"}, "repair_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Start Date", "description": "Date when repairs started"}, "repair_completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Completion Date", "description": "Date when repairs were completed"}, "estimated_replacement_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Replacement Cost", "description": "Estimated cost to replace the item"}, "deductible_applied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Deductible Applied", "description": "Whether a deductible was applied"}, "depreciation_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Depreciation Amount", "description": "Amount of depreciation applied"}}, "type": "object", "required": ["damage_type", "damage_description"], "title": "DamageInstanceCreate", "description": "Schema for creating a damage instance."}