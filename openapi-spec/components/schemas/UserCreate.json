{"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "first_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Last Name"}, "role": {"$ref": "./UserRole.json"}, "authority_role": {"$ref": "./AuthorityRole.json", "default": "NO_AUTHORITY"}, "status": {"$ref": "./UserStatus.json", "default": "PENDING"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"type": "string", "maxLength": 50, "title": "Timezone", "default": "UTC"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "password": {"type": "string", "maxLength": 64, "minLength": 8, "title": "Password", "description": "User password in plain text", "examples": ["StrongP@ssw0rd!"]}}, "type": "object", "required": ["email", "first_name", "last_name", "role", "password"], "title": "UserCreate", "description": "Schema for creating a new user."}