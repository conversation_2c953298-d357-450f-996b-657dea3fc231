{"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "JWT access token for authentication"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "<PERSON><PERSON> used to refresh the access token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Type of token (usually 'bearer')"}}, "type": "object", "required": ["access_token", "refresh_token", "token_type"], "title": "TokenResponse", "description": "Token response schema."}