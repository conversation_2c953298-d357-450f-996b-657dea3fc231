{"properties": {"total_claims": {"type": "integer", "title": "Total Claims", "description": "Total number of claims"}, "total_claims_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in total claims compared to previous period"}, "open_claims": {"type": "integer", "title": "Open Claims", "description": "Number of open claims"}, "open_claims_percentage": {"type": "number", "title": "Open Claims Percentage", "description": "Percentage of open claims out of all claims"}, "closed_claims": {"type": "integer", "title": "Closed Claims", "description": "Number of closed claims"}, "closed_claims_percentage": {"type": "number", "title": "Closed Claims Percentage", "description": "Percentage of closed claims out of all claims"}, "avg_resolution_time_days": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Avg Resolution Time Days", "description": "Average time to close claims in days"}, "avg_resolution_time_days_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in average resolution time compared to previous period"}}, "type": "object", "required": ["total_claims", "open_claims", "open_claims_percentage", "closed_claims", "closed_claims_percentage"], "title": "ClaimsKpiData", "description": "Data for Claims KPI report."}