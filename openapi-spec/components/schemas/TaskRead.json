{"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"$ref": "./TaskPriority.json", "default": "MEDIUM"}, "status": {"$ref": "./TaskStatus.json", "default": "PENDING"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "hr_id": {"type": "string", "title": "Hr Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this task"}, "assignee": {"anyOf": [{"$ref": "./UserReadBasic.json"}, {"type": "null"}]}, "creator": {"anyOf": [{"$ref": "./UserReadBasic.json"}, {"type": "null"}]}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["title", "id", "hr_id", "claim_id", "created_at", "updated_at"], "title": "TaskRead", "description": "Schema for representing a task in API responses."}