{"properties": {"injury_nature": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Injury Nature", "description": "Nature of alleged injury"}, "offense_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Offense Date", "description": "Date of alleged offense"}, "offensive_material_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Offensive Material Description", "description": "Description of alleged offensive material/action"}, "publication_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Publication Location", "description": "Location of publication/distribution"}, "material_creator": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Material Creator", "description": "Identification of material creator"}, "material_removed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Material Removed", "description": "Whether the infringing material was removed"}, "removal_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Removal Date", "description": "Date of material removal"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "PersonalAdvertisingInjuryDetailsResponse", "description": "Schema for personal and advertising injury details in responses."}