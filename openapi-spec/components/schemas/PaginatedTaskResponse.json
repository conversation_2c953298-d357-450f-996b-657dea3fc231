{"properties": {"items": {"items": {"$ref": "./TaskRead.json"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedTaskResponse", "description": "Paginated response for task listings."}