{"properties": {"reserve_type": {"$ref": "./ReserveType.json", "description": "Type of reserve"}, "amount": {"type": "string", "title": "Amount", "description": "Reserve amount"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["reserve_type", "amount", "id", "created_at", "updated_at"], "title": "ReserveResponse", "description": "Schema for reserve in responses."}