{"properties": {"recovery_status": {"anyOf": [{"$ref": "./RecoveryStatus.json"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "carrier_claim_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Carrier Claim Number", "description": "Third-party carrier's claim number"}, "carrier_adjuster": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Adjuster", "description": "Third-party carrier's adjuster name"}, "expected_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expected Amount", "description": "Expected recovery amount"}, "received_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Received Amount", "description": "Amount actually recovered"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim this recovery data belongs to"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Claim number for reference"}}, "type": "object", "required": ["claim_id", "claim_number"], "title": "RecoveryDetailsResponse", "description": "Schema for recovery details response."}