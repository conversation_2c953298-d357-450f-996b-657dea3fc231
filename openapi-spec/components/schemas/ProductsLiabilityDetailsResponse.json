{"properties": {"manufacturer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Manufacturer Name", "description": "Name of the manufacturer"}, "manufacturer_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Manufacturer Address", "description": "Address of the manufacturer"}, "product_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Product Type", "description": "Type of product"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date of purchase"}, "installation_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Installation Date", "description": "Date of installation if applicable"}, "usage_complies_with_intent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Usage Complies With Intent", "description": "Whether product was used as intended"}, "product_modified": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Product Modified", "description": "Whether product was modified"}, "serial_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Serial Number", "description": "Product serial number"}, "warnings_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Warnings Present", "description": "Whether warnings were present on product"}, "third_party_materials_used": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Third Party Materials Used", "description": "Whether third party materials were used"}, "similar_incidents_history": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Similar Incidents History", "description": "History of similar incidents or complaints"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "ProductsLiabilityDetailsResponse", "description": "Schema for products liability details in responses."}