{"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury occurrence"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the injury occurred"}, "injured_person_type": {"anyOf": [{"$ref": "./InjuredPersonType.json"}, {"type": "null"}], "description": "Type of injured person"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "incident_report_status": {"anyOf": [{"$ref": "./IncidentReportStatus.json"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "./MedicalTreatmentRequirements.json"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "./InsuranceBillingStatus.json"}, {"type": "null"}], "description": "Status of insurance billing"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "created_at", "updated_at"], "title": "BodilyInjuryDetailsResponse", "description": "Schema for bodily injury details in responses."}