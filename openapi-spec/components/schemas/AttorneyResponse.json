{"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the attorney"}, "attorney_type": {"$ref": "./AttorneyType.json", "description": "Type of attorney (PLAINTIFF, DEFENSE, etc.)"}, "firm_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Firm Name", "description": "Name of the law firm"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the attorney"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the attorney"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the attorney or law firm"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes about the attorney"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this attorney"}}, "type": "object", "required": ["name", "attorney_type", "id", "claim_id", "created_at", "updated_at"], "title": "AttorneyResponse", "description": "Schema for attorney response data."}