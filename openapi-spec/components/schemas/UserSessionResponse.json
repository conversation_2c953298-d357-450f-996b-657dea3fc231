{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the session"}, "ip_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ip Address", "description": "IP address the session was created from"}, "user_agent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Agent", "description": "Browser/client user agent information"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the session was created"}, "last_active_at": {"type": "string", "format": "date-time", "title": "Last Active At", "description": "When the session was last active"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the session will expire"}}, "type": "object", "required": ["id", "created_at", "last_active_at", "expires_at"], "title": "UserSessionResponse", "description": "User session response schema."}