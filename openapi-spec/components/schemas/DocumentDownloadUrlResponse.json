{"properties": {"download_url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Download Url", "description": "Pre-signed URL for downloading the file"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the download URL will expire"}}, "type": "object", "required": ["download_url", "expires_at"], "title": "DocumentDownloadUrlResponse", "description": "Schema for document download URL response."}