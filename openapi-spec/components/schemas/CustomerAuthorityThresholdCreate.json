{"properties": {"authority_role": {"$ref": "./AuthorityRole.json"}, "reserve_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Reserve Limit"}, "payment_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}}, "type": "object", "required": ["authority_role", "reserve_limit", "payment_limit", "customer_id"], "title": "CustomerAuthorityThresholdCreate", "description": "Schema for creating customer-specific authority thresholds."}