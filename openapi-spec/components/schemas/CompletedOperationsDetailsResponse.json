{"properties": {"work_type": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Work Type", "description": "Type of work completed"}, "completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completion Date", "description": "Date work was completed"}, "customer_acceptance_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Customer Acceptance Date", "description": "Date customer accepted work"}, "contract_status": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contract Status", "description": "Status of contract"}, "contract_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contract Details", "description": "Details of the contract"}, "defect_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defect Description", "description": "Description of the alleged defect in work"}, "problem_detection_timeline": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Problem Detection Timeline", "description": "Timeline of problem detection"}, "repair_attempts": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Attempts", "description": "Details of repair attempts made"}, "repair_descriptions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Descriptions", "description": "Descriptions of repairs performed"}, "applicable_warranties": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Applicable Warranties", "description": "Applicable warranties for the work"}, "subcontractor_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Subcontractor Involved", "description": "Whether subcontractors were involved"}, "subcontractor_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subcontractor Details", "description": "Details about subcontractors"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "CompletedOperationsDetailsResponse", "description": "Schema for completed operations details in responses."}