{"properties": {"value": {"anyOf": [{"type": "number"}, {"type": "integer"}, {"type": "string"}], "title": "Value", "description": "Absolute value of the change"}, "direction": {"type": "string", "enum": ["increase", "decrease", "neutral"], "title": "Direction", "description": "Direction of the change"}, "percentage": {"type": "number", "title": "Percentage", "description": "Percentage of change"}}, "type": "object", "required": ["value", "direction", "percentage"], "title": "MetricChange", "description": "Schema for representing metric changes with direction and percentage."}