{"properties": {"entity_type": {"$ref": "./EntityType.json", "description": "Type of entity being audited"}, "entity_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Entity Id", "description": "ID of the specific entity if applicable"}, "change_type": {"$ref": "./ChangeType.json", "description": "Type of change (CREATE, UPDATE, DELETE)"}, "field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name", "description": "Name of the field that changed"}, "previous_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Previous Value", "description": "Previous value of the field"}, "new_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "New Value", "description": "New value of the field"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Human-readable description of the change"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim this audit entry belongs to"}, "changed_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Changed By Id", "description": "ID of the user who made the change"}}, "type": "object", "required": ["entity_type", "change_type", "claim_id"], "title": "AuditTrailCreate", "description": "Schema for creating a new audit trail entry."}