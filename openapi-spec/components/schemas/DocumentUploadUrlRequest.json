{"properties": {"file_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "File Name", "description": "Original name of the file being uploaded"}, "content_type": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Content Type", "description": "MIME type of the file being uploaded"}, "document_type": {"$ref": "./DocumentType.json", "description": "Type of document being uploaded"}}, "additionalProperties": false, "type": "object", "required": ["file_name", "content_type", "document_type"], "title": "DocumentUploadUrlRequest", "description": "Schema for requesting a document upload URL."}