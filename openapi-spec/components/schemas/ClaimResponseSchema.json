{"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "./RecoveryStatus.json"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "claim_number": {"type": "string", "title": "Claim Number"}, "type": {"$ref": "./ClaimType.json"}, "status": {"$ref": "./ClaimStatus.json"}, "created_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "customer": {"anyOf": [{"$ref": "./CustomerResponse.json"}, {"type": "null"}]}, "documents": {"anyOf": [{"items": {"$ref": "./DocumentSchema.json"}, "type": "array"}, {"type": "null"}], "title": "Documents"}, "notes": {"anyOf": [{"items": {"$ref": "./NoteSchema.json"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "tasks": {"anyOf": [{"items": {"$ref": "./TaskSchema.json"}, "type": "array"}, {"type": "null"}], "title": "Tasks"}, "status_history": {"anyOf": [{"items": {"$ref": "./StatusHistorySchema.json"}, "type": "array"}, {"type": "null"}], "title": "Status History"}, "financials": {"anyOf": [{"$ref": "./ClaimFinancialsInDB.json"}, {"type": "null"}]}, "witnesses": {"anyOf": [{"items": {"$ref": "./WitnessResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Witnesses"}, "attorneys": {"anyOf": [{"items": {"$ref": "./AttorneyResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Attorneys"}, "assigned_to": {"anyOf": [{"$ref": "./UserResponse.json"}, {"type": "null"}]}, "supervisor": {"anyOf": [{"$ref": "./UserResponse.json"}, {"type": "null"}]}, "created_by": {"anyOf": [{"$ref": "./UserResponse.json"}, {"type": "null"}]}}, "type": "object", "required": ["id", "customer_id", "claim_number", "type", "status", "created_at", "updated_at"], "title": "ClaimResponseSchema", "description": "Schema for claim responses."}