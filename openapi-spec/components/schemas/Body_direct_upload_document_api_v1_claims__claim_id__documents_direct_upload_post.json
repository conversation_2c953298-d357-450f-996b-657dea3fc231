{"properties": {"file": {"type": "string", "format": "binary", "title": "File", "description": "File to upload"}, "document_type": {"$ref": "./DocumentType.json", "description": "Document type"}, "document_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Name", "description": "Document display name"}, "document_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Description", "description": "Document description"}}, "type": "object", "required": ["file", "document_type"], "title": "Body_direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post"}