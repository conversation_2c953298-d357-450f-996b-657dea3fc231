{"properties": {"type": {"$ref": "./DocumentType.json", "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}, "file_path": {"type": "string", "maxLength": 1024, "minLength": 1, "title": "File Path", "description": "Server path where the file is stored"}, "file_size": {"type": "integer", "maximum": 104857600, "exclusiveMinimum": 0, "title": "File Size", "description": "Size of the file in bytes, maximum 100MB"}, "mime_type": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Mime Type", "description": "MIME type of the file (e.g., application/pdf)"}}, "type": "object", "required": ["type", "name", "file_path", "file_size", "mime_type"], "title": "DocumentCreate", "description": "<PERSON><PERSON>a for creating a document after upload."}