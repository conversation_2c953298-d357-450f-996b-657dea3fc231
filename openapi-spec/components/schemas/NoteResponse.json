{"properties": {"content": {"type": "string", "title": "Content", "description": "The content of the note."}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this note"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "author_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Author Id", "description": "ID of the user who created the note."}, "author_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Author <PERSON><PERSON>", "description": "Email of the user who created the note."}, "author": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Author", "description": "Name of the user who created the note."}}, "type": "object", "required": ["content", "id", "claim_id", "created_at", "updated_at"], "title": "NoteResponse", "description": "Schema for returning a note via the API."}