{"properties": {"total_reserves": {"type": "string", "title": "Total Reserves", "description": "Total reserves amount"}, "total_reserves_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in total reserves compared to previous period"}, "total_payments": {"type": "string", "title": "Total Payments", "description": "Total payments amount"}, "total_payments_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in total payments compared to previous period"}, "avg_claim_value": {"type": "string", "title": "Avg Claim Value", "description": "Average claim value"}, "avg_claim_value_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in average claim value compared to previous period"}, "recovery_amount": {"type": "string", "title": "Recovery Amount", "description": "Total recovery amount"}, "recovery_amount_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in recovery amount compared to previous period"}}, "type": "object", "required": ["total_reserves", "total_payments", "avg_claim_value", "recovery_amount"], "title": "FinancialKpiData", "description": "Data for Financial KPI report."}