{"properties": {"vehicle_year": {"anyOf": [{"type": "integer", "maximum": 2026, "minimum": 1900}, {"type": "null"}], "title": "Vehicle Year", "description": "Manufacturing year of the vehicle"}, "vehicle_make": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Make", "description": "Make/manufacturer of the vehicle"}, "vehicle_model": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Model", "description": "Model of the vehicle"}, "vehicle_vin": {"anyOf": [{"type": "string", "maxLength": 17, "minLength": 17}, {"type": "null"}], "title": "Vehicle Vin", "description": "Vehicle Identification Number (17 characters)"}, "vehicle_license_plate": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Vehicle License Plate", "description": "License plate number of the vehicle"}, "vehicle_state": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "State where the vehicle is registered"}, "driver_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Driver Name", "description": "Full name of the driver at the time of incident"}, "driver_license_number": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver License Number", "description": "Driver's license number"}, "driver_state": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "State that issued the driver's license"}, "driver_relationship": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver Relationship", "description": "Relationship of driver to the insured (e.g., OWNER, FAMILY_MEMBER, FRIEND)"}, "point_of_impact": {"anyOf": [{"$ref": "./PointOfImpact.json"}, {"type": "null"}], "description": "Area of the vehicle that was impacted (e.g., FRONT, REAR, LEFT, RIGHT)"}, "airbags_deployed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Airbags Deployed", "description": "Whether any airbags deployed during the incident"}, "vehicle_driveable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Vehicle Driveable", "description": "Whether the vehicle was driveable after the incident"}, "towed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Towed", "description": "Whether the vehicle was towed from the scene"}, "tow_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Tow Location", "description": "Location where the vehicle was towed to"}, "incident_type": {"anyOf": [{"$ref": "./AutoIncidentType.json"}, {"type": "null"}], "description": "Type of auto incident (COLLIS<PERSON>, COMPREHENSIVE)"}, "collision_type": {"anyOf": [{"$ref": "./CollisionType.json"}, {"type": "null"}], "description": "Type of collision if incident_type is COLLISION"}, "passenger_count": {"anyOf": [{"type": "integer", "minimum": 0}, {"type": "null"}], "title": "Passenger Count", "description": "Number of passengers in the vehicle at time of incident"}, "passenger_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Passenger Details", "description": "Details about passengers including names, ages, positions, etc."}, "cargo_theft": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Cargo Theft", "description": "Whether cargo theft occurred during the incident"}, "cargo_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cargo Description", "description": "Description of stolen cargo, if applicable"}, "has_property_damage": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Has Property Damage", "description": "Whether there was property damage in the incident"}, "property_damage": {"anyOf": [{"$ref": "./AutoPropertyDamageResponse.json"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "bodily_injury_details": {"anyOf": [{"$ref": "./BodilyInjuryDetailsResponse.json"}, {"type": "null"}]}, "injured_persons": {"anyOf": [{"items": {"$ref": "./InjuredPersonResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Injured Persons"}, "damaged_property_assets": {"anyOf": [{"items": {"$ref": "./DamagedPropertyAssetResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Damaged Property Assets"}}, "type": "object", "required": ["id", "claim_id", "created_at", "updated_at"], "title": "AutoDetailsResponse", "description": "Schema for auto details in responses."}