{"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "./RecoveryStatus.json"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "type": {"$ref": "./ClaimType.json", "default": "GENERAL_LIABILITY"}, "status": {"anyOf": [{"$ref": "./ClaimStatus.json"}, {"type": "null"}], "description": "Current status of the claim"}, "gl_details": {"anyOf": [{"$ref": "./GeneralLiabilityDetailsUpdate.json"}, {"type": "null"}]}}, "type": "object", "title": "GeneralLiabilityClaimUpdate", "description": "Schema for updating general liability claims."}