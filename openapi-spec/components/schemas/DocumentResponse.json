{"properties": {"type": {"$ref": "./DocumentType.json", "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the document"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim the document belongs to"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this document"}, "file_path": {"type": "string", "title": "File Path", "description": "Server path where the file is stored"}, "file_size": {"type": "integer", "title": "File Size", "description": "Size of the file in bytes"}, "mime_type": {"type": "string", "title": "Mime Type", "description": "MIME type of the file (e.g., application/pdf)"}, "uploaded_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Uploaded By", "description": "ID of the user who uploaded the document"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the document was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the document was last updated"}}, "type": "object", "required": ["type", "name", "id", "claim_id", "file_path", "file_size", "mime_type", "created_at", "updated_at"], "title": "DocumentResponse", "description": "Schema for document response."}