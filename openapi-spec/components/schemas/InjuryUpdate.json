{"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury"}, "injury_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Type", "description": "Type of injury"}, "injury_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Severity", "description": "Severity of the injury"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "./MedicalTreatmentRequirements.json"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "./InsuranceBillingStatus.json"}, {"type": "null"}], "description": "Status of insurance billing"}}, "type": "object", "title": "InjuryUpdate", "description": "<PERSON><PERSON><PERSON> for updating an injury."}