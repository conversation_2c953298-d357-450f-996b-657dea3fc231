{"properties": {"period_start": {"type": "string", "format": "date", "title": "Period Start", "description": "Start date of the period"}, "new_claims_count": {"type": "integer", "title": "New Claims Count", "description": "Number of new claims in this period"}, "closed_claims_count": {"type": "integer", "title": "Closed Claims Count", "description": "Number of closed claims in this period"}}, "type": "object", "required": ["period_start", "new_claims_count", "closed_claims_count"], "title": "ClaimsOverTimeItem", "description": "Item in Claims Over Time report."}