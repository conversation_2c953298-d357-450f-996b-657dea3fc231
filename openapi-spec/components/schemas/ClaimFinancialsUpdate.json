{"properties": {"estimated_value": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated total value of the claim"}, "indemnity_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Indemnity Paid", "description": "Amount paid for indemnity"}, "expense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Expense <PERSON>", "description": "Amount paid for expenses"}, "defense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Defense Paid", "description": "Amount paid for defense costs"}, "recovery_expected": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Expected", "description": "Expected recovery amount"}, "recovery_received": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Received", "description": "Received recovery amount"}, "deductible_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Deductible Amount", "description": "Deductible amount"}, "coverage_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Coverage Limit", "description": "Coverage limit"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code", "default": "USD"}, "reserves": {"anyOf": [{"items": {"$ref": "./ReserveUpdate.json"}, "type": "array"}, {"type": "null"}], "title": "Reserves", "description": "Updated reserves"}}, "type": "object", "title": "ClaimFinancialsUpdate", "description": "Schema for updating claim financials."}