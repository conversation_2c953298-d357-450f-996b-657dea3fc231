{"properties": {"claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Unique identifier for the created claim"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Unique claim reference number"}, "claim_type": {"$ref": "./ClaimType.json", "description": "Type of the created claim"}, "status": {"$ref": "./ClaimStatus.json", "description": "Status of the created claim"}}, "type": "object", "required": ["claim_id", "claim_number", "claim_type", "status"], "title": "FNOLConversionResponse", "description": "Schema to use specifically for FNOL to claim conversion to avoid circular references.", "example": {"claim_id": "123e4567-e89b-12d3-a456-************", "claim_number": "SLCT-2024-0000001", "claim_type": "AUTO", "status": "DRAFT"}}