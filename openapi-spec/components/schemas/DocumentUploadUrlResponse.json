{"properties": {"upload_url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Upload Url", "description": "Pre-signed URL for uploading the file"}, "document_id": {"type": "string", "format": "uuid", "title": "Document Id", "description": "ID of the document record created"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the upload URL will expire"}}, "type": "object", "required": ["upload_url", "document_id", "expires_at"], "title": "DocumentUploadUrlResponse", "description": "Schema for document upload URL response."}