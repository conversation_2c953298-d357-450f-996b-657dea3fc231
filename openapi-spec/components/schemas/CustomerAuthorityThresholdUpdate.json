{"properties": {"reserve_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Reserve Limit"}, "payment_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "CustomerAuthorityThresholdUpdate", "description": "Schema for updating customer-specific authority thresholds."}