{"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"anyOf": [{"$ref": "./TaskPriority.json"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "./TaskStatus.json"}, {"type": "null"}]}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}}, "type": "object", "title": "TaskUpdate", "description": "Schema for updating an existing task.\n\nAll fields are optional."}