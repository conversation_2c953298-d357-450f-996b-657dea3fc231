{"properties": {"items": {"items": {"$ref": "./ClaimResponseSchema.json"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedClaimResponse", "description": "Schema for paginated claim response."}