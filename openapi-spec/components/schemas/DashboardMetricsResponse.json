{"properties": {"total_claims": {"type": "integer", "title": "Total Claims", "description": "Total number of claims"}, "open_claims": {"type": "integer", "title": "Open Claims", "description": "Number of open claims"}, "new_claims_last_period": {"type": "integer", "title": "New Claims Last Period", "description": "Number of new claims in the selected period"}, "closed_claims_last_period": {"type": "integer", "title": "Closed Claims Last Period", "description": "Number of closed claims in the selected period"}, "average_claim_lifecycle_days": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Claim Lifecycle Days", "description": "Average time to close claims in days"}, "average_claim_lifecycle_days_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in average claim lifecycle compared to previous period"}, "total_payments_last_period": {"type": "string", "title": "Total Payments Last Period", "description": "Total payments made in the selected period"}, "total_payments_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in total payments compared to previous period"}, "total_outstanding_reserves": {"type": "string", "title": "Total Outstanding Reserves", "description": "Total outstanding reserves"}, "total_outstanding_reserves_change": {"anyOf": [{"$ref": "./MetricChange.json"}, {"type": "null"}], "description": "Change in outstanding reserves compared to previous period"}, "tasks_pending": {"type": "integer", "title": "Tasks Pending", "description": "Number of pending tasks"}, "tasks_overdue": {"type": "integer", "title": "Tasks Overdue", "description": "Number of overdue tasks"}, "fnols_pending": {"type": "integer", "title": "Fnols Pending", "description": "Number of pending FNOLs"}}, "type": "object", "required": ["total_claims", "open_claims", "new_claims_last_period", "closed_claims_last_period", "total_payments_last_period", "total_outstanding_reserves", "tasks_pending", "tasks_overdue", "fnols_pending"], "title": "DashboardMetricsResponse", "description": "Schema for dashboard metrics response."}