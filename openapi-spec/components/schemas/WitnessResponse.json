{"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the witness"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the witness"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the witness"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the witness"}, "statement": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Statement", "description": "Statement provided by the witness"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this witness"}}, "type": "object", "required": ["name", "id", "claim_id", "created_at", "updated_at"], "title": "WitnessResponse", "description": "Schema for witness response data."}