{"properties": {"estimated_value": {"type": "string", "title": "Estimated Value", "description": "Estimated total value of the claim"}, "indemnity_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Indemnity Paid", "description": "Amount paid for indemnity"}, "expense_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expense <PERSON>", "description": "Amount paid for expenses"}, "defense_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defense Paid", "description": "Amount paid for defense costs"}, "recovery_expected": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recovery Expected", "description": "Expected recovery amount"}, "recovery_received": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recovery Received", "description": "Received recovery amount"}, "deductible_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deductible Amount", "description": "Deductible amount"}, "coverage_limit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Coverage Limit", "description": "Coverage limit"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code", "default": "USD"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Associated claim ID"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Associated claim number"}, "reserves": {"items": {"$ref": "./ReserveResponse.json"}, "type": "array", "title": "Reserves", "description": "Current reserves"}, "last_reserve_change": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Reserve Change", "description": "Last reserve change date"}, "last_payment_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Payment Date", "description": "Last payment date"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["estimated_value", "id", "claim_id", "claim_number", "reserves", "created_at", "updated_at"], "title": "ClaimFinancialsInDB", "description": "Schema for claim financials in database."}