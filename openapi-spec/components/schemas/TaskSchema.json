{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"type": "string", "title": "Priority"}, "status": {"type": "string", "title": "Status"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "assigned_to": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To"}, "created_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["id", "title", "priority", "status", "created_at", "updated_at"], "title": "TaskSchema", "description": "Schema for task responses."}