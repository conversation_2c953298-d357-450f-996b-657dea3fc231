{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "from_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "From Status"}, "to_status": {"type": "string", "title": "To Status"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "changed_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Changed By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this status history"}}, "type": "object", "required": ["id", "to_status", "created_at"], "title": "StatusHistorySchema", "description": "Schema for status history responses."}