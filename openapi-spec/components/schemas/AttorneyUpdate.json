{"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Full name of the attorney"}, "attorney_type": {"anyOf": [{"$ref": "./AttorneyType.json"}, {"type": "null"}], "description": "Type of attorney (PLAINTIFF, DEFENSE, etc.)"}, "firm_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Firm Name", "description": "Name of the law firm"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the attorney"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the attorney"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the attorney or law firm"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes about the attorney"}}, "type": "object", "title": "AttorneyUpdate", "description": "<PERSON><PERSON><PERSON> for updating an existing attorney."}