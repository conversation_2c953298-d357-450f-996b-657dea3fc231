{"properties": {"incident_type": {"$ref": "./GeneralLiabilityIncidentType.json", "description": "Type of general liability incident"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "premises_details": {"anyOf": [{"$ref": "./PremisesLiabilityDetailsResponse.json"}, {"type": "null"}]}, "products_details": {"anyOf": [{"$ref": "./ProductsLiabilityDetailsResponse.json"}, {"type": "null"}]}, "operations_details": {"anyOf": [{"$ref": "./CompletedOperationsDetailsResponse.json"}, {"type": "null"}]}, "advertising_details": {"anyOf": [{"$ref": "./PersonalAdvertisingInjuryDetailsResponse.json"}, {"type": "null"}]}, "bodily_injury_details": {"anyOf": [{"$ref": "./BodilyInjuryDetailsResponse.json"}, {"type": "null"}]}, "injured_persons": {"anyOf": [{"items": {"$ref": "./InjuredPersonResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Injured Persons"}, "damaged_property_assets": {"anyOf": [{"items": {"$ref": "./DamagedPropertyAssetResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Damaged Property Assets"}}, "type": "object", "required": ["incident_type", "id", "claim_id", "created_at", "updated_at"], "title": "GeneralLiabilityDetailsResponse", "description": "Schema for general liability details in responses."}