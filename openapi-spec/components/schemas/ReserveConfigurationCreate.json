{"properties": {"claim_type": {"$ref": "./ClaimType.json", "description": "Type of claim"}, "reserve_type": {"$ref": "./ReserveType.json", "description": "Type of reserve"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this reserve is required"}, "minimum_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Minimum Amount", "description": "Minimum required amount"}, "description": {"type": "string", "title": "Description", "description": "Description of the configuration"}}, "type": "object", "required": ["claim_type", "reserve_type", "is_required", "description"], "title": "ReserveConfigurationCreate", "description": "Schema for creating a reserve configuration."}