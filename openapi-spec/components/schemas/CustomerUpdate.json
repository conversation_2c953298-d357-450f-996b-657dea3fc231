{"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the customer/insurance company"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Whether the customer is active in the system"}}, "type": "object", "title": "CustomerUpdate", "description": "Update customer schema."}