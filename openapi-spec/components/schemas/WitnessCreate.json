{"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the witness"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the witness"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the witness"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the witness"}, "statement": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Statement", "description": "Statement provided by the witness"}}, "type": "object", "required": ["name"], "title": "WitnessCreate", "description": "<PERSON><PERSON><PERSON> for creating a new witness."}