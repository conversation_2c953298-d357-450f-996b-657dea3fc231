{"properties": {"payment_type": {"$ref": "./PaymentType.json", "description": "Type of payment (INDEMNITY, EXPENSE, or DEFENSE)"}, "amount": {"anyOf": [{"type": "number", "exclusiveMinimum": 0}, {"type": "string"}], "title": "Amount", "description": "Payment amount (must be positive)"}, "payee": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Payee", "description": "Recipient of the payment"}, "payment_date": {"type": "string", "format": "date-time", "title": "Payment Date", "description": "Date the payment was issued"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "Notes about the payment"}}, "type": "object", "required": ["payment_type", "amount", "payee", "payment_date"], "title": "PaymentCreate", "description": "Schema for creating a payment."}