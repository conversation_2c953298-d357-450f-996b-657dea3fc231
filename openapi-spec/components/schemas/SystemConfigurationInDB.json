{"properties": {"key": {"type": "string", "title": "Key", "description": "Configuration key"}, "value": {"type": "string", "title": "Value", "description": "Configuration value"}, "description": {"type": "string", "title": "Description", "description": "Configuration description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["key", "value", "description", "id", "created_at", "updated_at"], "title": "SystemConfigurationInDB", "description": "Schema for system configuration in DB."}