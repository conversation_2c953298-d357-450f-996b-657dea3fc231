{"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the injured person"}, "person_type": {"anyOf": [{"$ref": "./InjuredPersonType.json"}, {"type": "null"}], "description": "Type of injured person"}, "contact_info": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Contact Info", "description": "Contact information of the injured person"}, "age": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Age", "description": "Age of the injured person"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the incident occurred"}, "incident_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Incident Description", "description": "Description of the incident"}, "incident_report_status": {"anyOf": [{"$ref": "./IncidentReportStatus.json"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "injuries": {"anyOf": [{"items": {"$ref": "./InjuryResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Injuries"}}, "type": "object", "required": ["id", "created_at", "updated_at"], "title": "InjuredPersonResponse", "description": "Schema for injured person in responses."}