{"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the injured person"}, "person_type": {"anyOf": [{"$ref": "./InjuredPersonType.json"}, {"type": "null"}], "description": "Type of injured person"}, "contact_info": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Contact Info", "description": "Contact information of the injured person"}, "age": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Age", "description": "Age of the injured person"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the incident occurred"}, "incident_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Incident Description", "description": "Description of the incident"}, "incident_report_status": {"anyOf": [{"$ref": "./IncidentReportStatus.json"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}}, "type": "object", "title": "InjuredPersonCreate", "description": "<PERSON><PERSON><PERSON> for creating a new injured person."}