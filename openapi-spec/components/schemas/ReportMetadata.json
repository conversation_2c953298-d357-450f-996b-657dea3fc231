{"properties": {"report_name": {"type": "string", "title": "Report Name", "description": "Name of the report"}, "generated_at": {"type": "string", "format": "date-time", "title": "Generated At", "description": "Timestamp when the report was generated"}, "filters_applied": {"additionalProperties": true, "type": "object", "title": "Filters Applied", "description": "Filters that were applied to generate the report"}, "column_headers": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Column Headers", "description": "Headers for report data columns"}}, "type": "object", "required": ["report_name", "generated_at"], "title": "ReportMetadata", "description": "Common metadata for all report responses."}