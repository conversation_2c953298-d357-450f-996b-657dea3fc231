{"properties": {"type": {"anyOf": [{"$ref": "./DocumentType.json"}, {"type": "null"}], "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}}, "additionalProperties": false, "type": "object", "title": "DocumentUpdate", "description": "Schema for updating document metadata."}