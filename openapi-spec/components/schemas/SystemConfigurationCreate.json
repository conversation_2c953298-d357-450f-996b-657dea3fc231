{"properties": {"key": {"type": "string", "title": "Key", "description": "Configuration key"}, "value": {"type": "string", "title": "Value", "description": "Configuration value"}, "description": {"type": "string", "title": "Description", "description": "Configuration description"}}, "type": "object", "required": ["key", "value", "description"], "title": "SystemConfigurationCreate", "description": "Schema for creating a system configuration."}