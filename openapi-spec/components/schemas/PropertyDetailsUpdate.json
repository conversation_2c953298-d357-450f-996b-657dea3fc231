{"properties": {"property_type": {"anyOf": [{"$ref": "./PropertyType.json"}, {"type": "null"}], "description": "Type of property (e.g., RESIDENTIAL, COMMERCIAL, RENTAL)"}, "property_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Property Address", "description": "Full address of the property"}, "property_city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Property City", "description": "City where the property is located"}, "property_state": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "State where the property is located"}, "property_zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Property Zip", "description": "ZIP/postal code of the property"}, "damage_type": {"anyOf": [{"$ref": "./DamageType.json"}, {"type": "null"}], "description": "Type of damage (e.g., FIRE, WATER, WIND, HAIL, THEFT)"}, "damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Detailed description of the damage"}, "affected_areas": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Affected Areas", "description": "Specific areas of the property affected by the damage"}, "inhabitable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Inhabitable", "description": "Whether the property is currently inhabitable"}, "temporary_repairs_needed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Temporary Repairs Needed", "description": "Whether temporary repairs are needed to secure the property"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "emergency_services_called": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Emergency Services Called", "description": "Whether emergency services (fire, medical) were called"}, "bodily_injury_details": {"anyOf": [{"$ref": "./BodilyInjuryDetailsUpdate.json"}, {"type": "null"}]}}, "type": "object", "title": "PropertyDetailsUpdate", "description": "Schema for updating property details."}