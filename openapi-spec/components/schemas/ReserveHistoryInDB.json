{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Associated claim ID"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this reserve history"}, "financials_id": {"type": "string", "format": "uuid", "title": "Financials Id", "description": "Associated financials ID"}, "reserve_type": {"$ref": "./ReserveType.json", "description": "Type of reserve"}, "previous_amount": {"type": "string", "title": "Previous Amount", "description": "Previous reserve amount"}, "new_amount": {"type": "string", "title": "New Amount", "description": "New reserve amount"}, "changed_by_id": {"type": "string", "format": "uuid", "title": "Changed By Id", "description": "User who made the change"}, "changed_at": {"type": "string", "format": "date-time", "title": "Changed At", "description": "When the change was made"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Notes about the change"}}, "type": "object", "required": ["id", "claim_id", "financials_id", "reserve_type", "previous_amount", "new_amount", "changed_by_id", "changed_at"], "title": "ReserveHistoryInDB", "description": "Schema for reserve history in database."}