{"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"$ref": "./TaskPriority.json", "default": "MEDIUM"}, "status": {"$ref": "./TaskStatus.json", "default": "PENDING"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "assigned_to": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To"}}, "type": "object", "required": ["title"], "title": "TaskCreateRequestBody", "description": "Schema for the request body when creating a task.\n\nclaim_id is not included here as it's derived from the path."}