{"properties": {"name": {"type": "string", "maxLength": 200, "title": "Name", "description": "Name of the damaged property asset"}, "asset_type": {"$ref": "./PropertyAssetType.json", "description": "Type of property asset"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the property asset"}, "location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Location", "description": "Location of the property asset"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the property asset"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City", "description": "City where the property asset is located"}, "state": {"anyOf": [{"$ref": "./USState.json"}, {"type": "null"}], "description": "State where the property asset is located"}, "zip_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip Code", "description": "ZIP/postal code of the property asset"}, "owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Owner Type", "description": "Type of owner (Insured, Third Party, etc.)"}, "owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Owned By Insured", "description": "Whether the property is owned by the insured"}, "estimated_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated value of the property asset"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date when the property was purchased"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "damage_instances": {"anyOf": [{"items": {"$ref": "./DamageInstanceResponse.json"}, "type": "array"}, {"type": "null"}], "title": "Damage Instances"}}, "type": "object", "required": ["name", "asset_type", "id", "created_at", "updated_at"], "title": "DamagedPropertyAssetResponse", "description": "Schema for damaged property asset in responses."}