{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "content": {"type": "string", "title": "Content"}, "created_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "content", "created_at", "updated_at"], "title": "NoteSchema", "description": "Schema for note responses."}