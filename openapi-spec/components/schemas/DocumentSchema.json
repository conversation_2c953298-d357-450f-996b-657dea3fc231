{"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "type": {"type": "string", "title": "Type"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "file_path": {"type": "string", "title": "File Path"}, "file_size": {"type": "integer", "title": "File Size"}, "mime_type": {"type": "string", "title": "Mime Type"}, "uploaded_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Uploaded By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "name", "type", "file_path", "file_size", "mime_type", "created_at", "updated_at"], "title": "DocumentSchema", "description": "Schema for document responses."}