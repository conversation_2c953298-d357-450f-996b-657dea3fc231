{"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the customer/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the customer is active in the system", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the customer"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the customer was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the customer was last updated"}}, "type": "object", "required": ["name", "prefix", "id", "created_at", "updated_at"], "title": "CustomerResponse", "description": "Customer response schema."}