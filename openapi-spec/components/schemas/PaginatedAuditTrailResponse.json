{"properties": {"items": {"items": {"$ref": "./AuditTrailResponse.json"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedAuditTrailResponse", "description": "Schema for paginated audit trail response."}