{"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID of the adjuster"}, "user_name": {"type": "string", "title": "User Name", "description": "Name of the adjuster"}, "claims_handled": {"type": "integer", "title": "Claims Handled", "description": "Number of claims handled by this adjuster"}, "avg_resolution_time": {"type": "number", "title": "Avg Resolution Time", "description": "Average resolution time in days"}, "total_payments": {"type": "string", "title": "Total Payments", "description": "Total payments authorized"}, "pending_tasks": {"type": "integer", "title": "Pending Tasks", "description": "Number of pending tasks"}, "completed_tasks": {"type": "integer", "title": "Completed Tasks", "description": "Number of completed tasks"}}, "type": "object", "required": ["user_id", "user_name", "claims_handled", "avg_resolution_time", "total_payments", "pending_tasks", "completed_tasks"], "title": "AdjusterPerformanceItem", "description": "Item in Adjuster Performance report."}