{"properties": {"claim_type": {"$ref": "./ClaimType.json", "description": "Type of claim"}, "reserve_type": {"$ref": "./ReserveType.json", "description": "Type of reserve"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this reserve is required"}, "minimum_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Minimum Amount", "description": "Minimum required amount"}, "description": {"type": "string", "title": "Description", "description": "Description of the configuration"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["claim_type", "reserve_type", "is_required", "description", "id", "created_at", "updated_at"], "title": "ReserveConfigurationInDB", "description": "Schema for reserve configuration in database."}