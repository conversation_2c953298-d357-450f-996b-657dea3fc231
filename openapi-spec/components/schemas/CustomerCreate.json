{"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the customer/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the customer is active in the system", "default": true}}, "type": "object", "required": ["name", "prefix"], "title": "CustomerCreate", "description": "Create customer schema."}