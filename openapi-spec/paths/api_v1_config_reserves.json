{"get": {"tags": ["config", "config"], "summary": "List Reserve Configurations", "description": "List reserve configurations with optional filtering. Permissions are checked at the service layer.", "operationId": "list_reserve_configurations_api_v1_config_reserves_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "title": "Claim Type"}}, {"name": "reserve_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ReserveType.json"}, {"type": "null"}], "title": "Reserve Type"}}, {"name": "is_required", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Required"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/ReserveConfigurationInDB.json"}, "title": "Response List Reserve Configurations Api V1 Config Reserves Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["config", "config"], "summary": "Create Reserve Configuration", "description": "Create a new reserve configuration. Permissions are checked at the service layer.", "operationId": "create_reserve_configuration_api_v1_config_reserves_post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReserveConfigurationCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReserveConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}