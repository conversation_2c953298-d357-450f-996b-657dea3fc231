{"get": {"tags": ["users", "users"], "summary": "List Users", "description": "List users with optional filtering and search. Permissions are checked at the service layer.", "operationId": "list_users_api_v1_users_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in names, email, and department", "title": "Search"}, "description": "Search in names, email, and department"}, {"name": "role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/UserRole.json"}, {"type": "null"}], "title": "Role"}}, {"name": "authority_role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/AuthorityRole.json"}, {"type": "null"}], "title": "Authority Role"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/UserStatus.json"}, {"type": "null"}], "title": "Status"}}, {"name": "department", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department"}}, {"name": "email", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/UserResponse.json"}, "title": "Response List Users Api V1 Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["users", "users"], "summary": "Create User", "description": "Create a new user. Permissions are checked at the service layer.", "operationId": "create_user_api_v1_users_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}