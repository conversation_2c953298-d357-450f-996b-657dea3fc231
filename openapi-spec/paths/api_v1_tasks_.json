{"get": {"tags": ["tasks"], "summary": "List Tasks", "description": "Get a list of tasks, optionally filtered. Requires 'LIST_TASKS' permission.", "operationId": "list_tasks_api_v1_tasks__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter tasks by claim ID", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Filter tasks by claim ID"}, {"name": "assigned_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter tasks assigned to a specific user ID", "title": "Assigned To"}, "description": "Filter tasks assigned to a specific user ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/TaskStatus.json"}, {"type": "null"}], "description": "Filter tasks by status", "title": "Status"}, "description": "Filter tasks by status"}, {"name": "priority", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/TaskPriority.json"}, {"type": "null"}], "description": "Filter tasks by priority", "title": "Priority"}, "description": "Filter tasks by priority"}, {"name": "title", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter tasks by partial title match (case-insensitive)", "title": "Title"}, "description": "Filter tasks by partial title match (case-insensitive)"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/PaginatedTaskResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}