{"get": {"tags": ["claims", "documents", "documents"], "summary": "Get Document By Id", "description": "Get document by ID for a specific claim.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    document_id: Document ID\n    document_service: Document service\n\nReturns:\n    Document data", "operationId": "get_document_by_id_api_v1_claims__claim_id__documents__document_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["claims", "documents", "documents"], "summary": "Update Document", "description": "Update document metadata.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_data: Document data to update\n    document_service: Document service\n\nReturns:\n    Updated document data", "operationId": "update_document_api_v1_claims__claim_id__documents__document_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentUpdate.json", "description": "Document data to update"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "documents", "documents"], "summary": "Delete Claim Document", "description": "Delete document by ID.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_service: Document service", "operationId": "delete_claim_document_api_v1_claims__claim_id__documents__document_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}