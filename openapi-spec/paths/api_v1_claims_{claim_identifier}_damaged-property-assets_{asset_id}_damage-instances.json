{"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damage Instances", "description": "Get all damage instances for a damaged property asset.", "operationId": "get_damage_instances_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/DamageInstanceResponse.json"}, "title": "Response Get Damage Instances Api V1 Claims  Claim Identifier  Damaged Property Assets  Asset Id  Damage Instances Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "claims", "property-damage"], "summary": "Create Damage Instance", "description": "Create a damage instance for a damaged property asset.", "operationId": "create_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/DamageInstanceCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DamageInstanceResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}