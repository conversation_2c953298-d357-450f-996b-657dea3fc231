{"get": {"tags": ["claims", "documents", "documents"], "summary": "Get Document Download Url", "description": "Get a signed URL for downloading a document.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_service: Document service\n\nReturns:\n    Download URL and expiration time", "operationId": "get_document_download_url_api_v1_claims__claim_id__documents__document_id__download_url_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentDownloadUrlResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}