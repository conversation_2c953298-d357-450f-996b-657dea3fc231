{"get": {"tags": ["users", "users"], "summary": "Get Current User Details", "description": "Get current user details.", "operationId": "get_current_user_details_api_v1_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["users", "users"], "summary": "Update Current User", "description": "Update current user's own profile. Permissions are checked at the service layer.", "operationId": "update_current_user_api_v1_users_me_patch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "../components/schemas/UserUpdate.json"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}}