{"get": {"tags": ["reports", "reports"], "summary": "Get Claims Kpi Report", "description": "Get Claims KPI Report.\n\nRetrieves key performance indicators for claims, including:\n- Total claims\n- Open claims percentage\n- Closed claims percentage\n- Average resolution time\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_claims_kpi_report_api_v1_reports_claims_kpis_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": false, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReportResponseClaimsKpis.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}