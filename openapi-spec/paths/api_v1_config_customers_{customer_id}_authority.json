{"get": {"tags": ["config", "config"], "summary": "List Customer Authority Thresholds", "description": "List authority thresholds for a specific customer. Permissions are checked at the service layer.", "operationId": "list_customer_authority_thresholds_api_v1_config_customers__customer_id__authority_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/CustomerAuthorityThresholdInDB.json"}, "title": "Response List Customer Authority Thresholds Api V1 Config Customers  Customer Id  Authority Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["config", "config"], "summary": "Create Customer Authority Threshold", "description": "Create a new authority threshold for a customer. Permissions are checked at the service layer.", "operationId": "create_customer_authority_threshold_api_v1_config_customers__customer_id__authority_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerAuthorityThresholdCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}