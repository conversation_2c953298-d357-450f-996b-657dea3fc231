{"post": {"tags": ["auth", "auth"], "summary": "Cleanup Sessions", "description": "Clean up expired sessions.\n\nRequires <PERSON><PERSON>GE_SECURITY_SETTINGS permission.\nOnly removes sessions that have expired based on their expires_at timestamp.", "operationId": "cleanup_sessions_api_v1_auth_sessions_cleanup_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Cleanup Sessions Api V1 Auth Sessions Cleanup Post"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}