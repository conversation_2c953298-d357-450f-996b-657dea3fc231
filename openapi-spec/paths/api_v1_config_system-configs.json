{"get": {"tags": ["config", "config"], "summary": "List System Configurations", "description": "List all system configurations. Permissions are checked at the service layer.", "operationId": "list_system_configurations_api_v1_config_system_configs_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "../components/schemas/SystemConfigurationInDB.json"}, "type": "array", "title": "Response List System Configurations Api V1 Config System Configs Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["config", "config"], "summary": "Create System Configuration", "description": "Create a new system configuration. Permissions are checked at the service layer.", "operationId": "create_system_configuration_api_v1_config_system_configs_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "../components/schemas/SystemConfigurationCreate.json"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/SystemConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}