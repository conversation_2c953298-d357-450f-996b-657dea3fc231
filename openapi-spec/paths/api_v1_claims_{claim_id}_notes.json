{"get": {"tags": ["notes", "notes"], "summary": "List Notes For Claim", "description": "List all notes associated with a specific claim.", "operationId": "list_notes_for_claim_api_v1_claims__claim_id__notes_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/NoteResponse.json"}, "title": "Response List Notes For Claim Api V1 Claims  Claim Id  Notes Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}