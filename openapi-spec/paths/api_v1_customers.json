{"get": {"tags": ["customers", "customers"], "summary": "List Customers", "description": "List all customers.", "operationId": "list_customers_api_v1_customers_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Active Only"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/CustomerResponse.json"}, "title": "Response List Customers Api V1 Customers Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["customers", "customers"], "summary": "Create Customer", "description": "Create a new customer.", "operationId": "create_customer_api_v1_customers_post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}