{"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injured Person", "description": "Get an injured person for a claim identified by ID or Number.", "operationId": "get_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuredPersonResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "put": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Update Injured Person", "description": "Update an injured person for a claim identified by ID or Number.", "operationId": "update_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuredPersonUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuredPersonResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Delete Injured Person", "description": "Delete an injured person for a claim identified by ID or Number.", "operationId": "delete_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}