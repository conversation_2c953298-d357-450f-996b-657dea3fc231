{"options": {"summary": "Options Handler", "description": "Global OPTIONS request handler for any path.\n\nThis is a fallback to handle OPTIONS requests for any path\nthat doesn't have a specific OPTIONS handler.\nCORS middleware will add appropriate headers.", "operationId": "options_handler_api_v1__path__options", "parameters": [{"name": "path", "in": "path", "required": true, "schema": {"type": "string", "title": "Path"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}