{"get": {"tags": ["claims", "documents", "documents"], "summary": "List All Documents", "description": "List all documents across all claims with optional filtering.\nRequires MANAGE_DOCUMENTS permission.\n\nArgs:\n    skip: Number of records to skip\n    limit: Maximum number of records to return\n    document_type: Filter by document type\n    keyword: Search in document name, description, or filename\n    document_service: Document service\n\nReturns:\n    List of documents", "operationId": "list_all_documents_api_v1_claims__claim_id__documents_all_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/DocumentType.json"}, {"type": "null"}], "title": "Document Type"}}, {"name": "keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Keyword"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentList.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}