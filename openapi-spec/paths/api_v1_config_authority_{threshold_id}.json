{"get": {"tags": ["config", "config"], "summary": "Get Customer Authority Threshold", "description": "Get a specific customer authority threshold. Permissions are checked at the service layer.", "operationId": "get_customer_authority_threshold_api_v1_config_authority__threshold_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["config", "config"], "summary": "Update Customer Authority Threshold", "description": "Update a customer authority threshold. Permissions are checked at the service layer.", "operationId": "update_customer_authority_threshold_api_v1_config_authority__threshold_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerAuthorityThresholdUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete Customer Authority Threshold", "description": "Delete a customer authority threshold. Permissions are checked at the service layer.", "operationId": "delete_customer_authority_threshold_api_v1_config_authority__threshold_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}