{"post": {"tags": ["auth", "auth"], "summary": "<PERSON><PERSON>", "description": "Login endpoint to get access and refresh tokens.", "operationId": "login_api_v1_auth_token_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "../components/schemas/Body_login_api_v1_auth_token_post.json"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TokenResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}