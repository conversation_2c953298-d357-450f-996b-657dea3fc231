{"get": {"tags": ["fnols", "fnols"], "summary": "Get Fnol", "description": "Get FNOL by ID.", "operationId": "get_fnol_api_v1_fnols__fnol_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (customer, claims)", "default": ["customer", "claims"], "title": "Include"}, "description": "Include related items (customer, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["fnols", "fnols"], "summary": "Update Fnol", "description": "Update FNOL by ID.", "operationId": "update_fnol_api_v1_fnols__fnol_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["fnols", "fnols"], "summary": "Delete Fnol", "description": "Delete FNOL by ID.", "operationId": "delete_fnol_api_v1_fnols__fnol_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}