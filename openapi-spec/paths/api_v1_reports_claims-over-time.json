{"get": {"tags": ["reports", "reports"], "summary": "Get Claims Over Time Report", "description": "Get Claims Over Time Report.\n\nTracks new and closed claims over time periods (weekly or monthly).\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_claims_over_time_report_api_v1_reports_claims_over_time_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReportResponseClaimsOverTime.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}