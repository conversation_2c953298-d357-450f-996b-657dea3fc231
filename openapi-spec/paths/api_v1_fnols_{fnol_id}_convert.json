{"post": {"tags": ["fnols", "fnols", "claims"], "summary": "Convert F<PERSON>l To Claim", "description": "Convert an FNOL to a claim.\n\nTakes an FNOL ID and creates a new claim with the FNOL linked to it.\nReturns a simplified response to avoid circular references.", "operationId": "convert_fnol_to_claim_api_v1_fnols__fnol_id__convert_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}, {"name": "claim_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Type of claim to create (case-insensitive)", "title": "Claim Type"}, "description": "Type of claim to create (case-insensitive)"}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLConversionResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}