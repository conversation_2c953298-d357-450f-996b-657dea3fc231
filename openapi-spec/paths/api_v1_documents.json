{"get": {"tags": ["documents", "documents"], "summary": "List All Documents", "description": "List all documents irrespective of their associated claims.\n\nThis endpoint returns the latest N documents across all claims in the system.\n\nArgs:\n    skip: Number of records to skip (for pagination)\n    limit: Maximum number of records to return (for pagination)\n    document_type: Optional filter by document type\n    document_service: Document service\n\nReturns:\n    A list of documents with metadata including their associated claim IDs", "operationId": "list_all_documents_api_v1_documents_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records to return", "default": 100, "title": "Limit"}, "description": "Maximum number of records to return"}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/DocumentType.json"}, {"type": "null"}], "description": "Filter by document type", "title": "Document Type"}, "description": "Filter by document type"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentList.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}