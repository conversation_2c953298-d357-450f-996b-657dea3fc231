{"post": {"tags": ["auth", "auth"], "summary": "Refresh <PERSON>", "description": "Get a new access token using a refresh token.", "operationId": "refresh_token_api_v1_auth_refresh_post", "parameters": [{"name": "refresh_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}, {"name": "refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TokenResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}