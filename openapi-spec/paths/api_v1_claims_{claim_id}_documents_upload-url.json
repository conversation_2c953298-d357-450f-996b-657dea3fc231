{"post": {"tags": ["claims", "documents", "documents"], "summary": "Get Claim Document Upload Url", "description": "Get a signed URL for uploading a document to a claim.\n\nArgs:\n    upload_request: Upload request data\n    claim_id: Claim ID or Claim Number\n    document_service: Document service\n\nReturns:\n    Upload URL, document ID, and expiration time", "operationId": "get_claim_document_upload_url_api_v1_claims__claim_id__documents_upload_url_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentUploadUrlRequest.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentUploadUrlResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}