{"patch": {"tags": ["config", "config"], "summary": "Update System Configuration", "description": "Update a system configuration. Permissions are checked at the service layer.", "operationId": "update_system_configuration_api_v1_config_system_configs__config_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Config Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/SystemConfigurationUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/SystemConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete System Configuration", "description": "Delete a system configuration.", "operationId": "delete_system_configuration_api_v1_config_system_configs__config_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Config Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}