{"get": {"tags": ["config", "config"], "summary": "Get System Configuration By Key", "description": "Get a system configuration by key. Permissions are checked at the service layer.", "operationId": "get_system_configuration_by_key_api_v1_config_system_configs__key__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "title": "Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/SystemConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}