{"patch": {"tags": ["claims", "claims", "recovery"], "summary": "Update Claim Recovery Status", "description": "Update recovery status for a claim identified by ID or Number.", "operationId": "update_claim_recovery_status_api_v1_claims__claim_identifier__recovery_status_patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "recovery_status", "in": "query", "required": true, "schema": {"$ref": "../components/schemas/RecoveryStatus.json", "description": "New recovery status"}, "description": "New recovery status"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClaimResponseSchema.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}