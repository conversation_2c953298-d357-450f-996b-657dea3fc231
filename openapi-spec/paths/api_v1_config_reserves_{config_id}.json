{"get": {"tags": ["config", "config"], "summary": "Get Reserve Configuration", "description": "Get a specific reserve configuration. Permissions are checked at the service layer.", "operationId": "get_reserve_configuration_api_v1_config_reserves__config_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReserveConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["config", "config"], "summary": "Update Reserve Configuration", "description": "Update a reserve configuration. Permissions are checked at the service layer.", "operationId": "update_reserve_configuration_api_v1_config_reserves__config_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReserveConfigurationUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReserveConfigurationInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete Reserve Configuration", "description": "Delete a reserve configuration. Permissions are checked at the service layer.", "operationId": "delete_reserve_configuration_api_v1_config_reserves__config_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}