{"post": {"tags": ["users", "users"], "summary": "Change Password", "description": "Change the current user's password.", "operationId": "change_password_api_v1_users_password_change_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "../components/schemas/PasswordChange.json"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}}