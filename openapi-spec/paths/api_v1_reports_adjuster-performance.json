{"get": {"tags": ["reports", "reports"], "summary": "Get Adjuster Performance Report", "description": "Get Adjuster Performance Report.\n\nProvides performance metrics for adjusters, including:\n- Claims handled\n- Average resolution time\n- Total payments authorized\n- Pending tasks\n- Overdue tasks\n\nThe report can be filtered by customer_id or claim_type.", "operationId": "get_adjuster_performance_report_api_v1_reports_adjuster_performance_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReportResponseAdjusterPerformance.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}