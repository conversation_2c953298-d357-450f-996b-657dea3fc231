{"get": {"tags": ["tasks"], "summary": "Get Task", "description": "Get details of a specific task by its UUID or HR ID. Requires 'VIEW_TASKS' permission.", "operationId": "get_task_api_v1_tasks__task_identifier__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "put": {"tags": ["tasks"], "summary": "Update Task", "description": "Update details of an existing task. Requires 'UPDATE_TASKS' permission.", "operationId": "update_task_api_v1_tasks__task_identifier__put", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["tasks"], "summary": "Delete Task", "description": "Delete a task by its UUID or HR ID. Requires 'DELETE_TASK' permission.", "operationId": "delete_task_api_v1_tasks__task_identifier__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}