{"get": {"tags": ["users", "users"], "summary": "Get User", "description": "Get user by ID. Permissions are checked at the service layer.", "operationId": "get_user_api_v1_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["users", "users"], "summary": "Update User", "description": "Update user by ID. Permissions are checked at the service layer.", "operationId": "update_user_api_v1_users__user_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/UserResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["users", "users"], "summary": "Delete User", "description": "Delete user by ID. Permissions are checked at the service layer.", "operationId": "delete_user_api_v1_users__user_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}