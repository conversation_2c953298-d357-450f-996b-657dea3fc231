{"post": {"tags": ["claims", "documents", "documents"], "summary": "Direct Upload Document", "description": "Upload a file directly and create a document record.\n\nThis endpoint handles both file upload and record creation in one request.\nMaximum file size: 100MB.\n\nArgs:\n    request: Request object\n    claim_id: Claim ID or Claim Number\n    file: File to upload\n    document_type: Document type\n    document_name: Document display name (optional)\n    document_description: Document description (optional)\n    current_user: Current authenticated user\n    document_service: Document service\n\nReturns:\n    Created document", "operationId": "direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "../components/schemas/Body_direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}