{"patch": {"tags": ["tasks"], "summary": "Change Task Status", "description": "Change the status of a task. Requires 'CHANGE_TASK_STATUS' permission.", "operationId": "change_task_status_api_v1_tasks__task_identifier__status_patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskStatusSchema.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}