{"delete": {"tags": ["auth", "auth"], "summary": "Revoke Session", "description": "Revoke a specific session.", "operationId": "revoke_session_api_v1_auth_sessions__session_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Session Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}