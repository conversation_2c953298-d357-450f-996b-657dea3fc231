{"get": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Get Attorney", "description": "Get a specific attorney by ID.", "operationId": "get_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/AttorneyResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Update Attorney", "description": "Update an existing attorney.", "operationId": "update_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/AttorneyUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/AttorneyResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Delete Attorney", "description": "Delete an attorney.", "operationId": "delete_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}