{"get": {"tags": ["claims", "audit", "audit"], "summary": "Get Audit Entry", "description": "Get a specific audit entry by ID.", "operationId": "get_audit_entry_api_v1_claims__claim_identifier__audit__audit_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "audit_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Audit entry ID", "title": "Audit Id"}, "description": "Audit entry ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/AuditTrailResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}