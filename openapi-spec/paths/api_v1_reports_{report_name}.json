{"get": {"tags": ["reports", "reports"], "summary": "Get Report By Name", "description": "Get Report by Name.\n\nGeneric endpoint to retrieve any report by its name.\n\nFor most use cases, you should use the specific report endpoints\ninstead of this generic one.", "operationId": "get_report_by_name_api_v1_reports__report_name__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "report_name", "in": "path", "required": true, "schema": {"type": "string", "description": "Name of the report", "title": "Report Name"}, "description": "Name of the report"}, {"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": false, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Report By Name Api V1 Reports  Report Name  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}