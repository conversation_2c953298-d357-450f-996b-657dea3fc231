{"patch": {"tags": ["tasks"], "summary": "Assign Task", "description": "Assign or unassign a task to a specific user. Requires 'ASSIGN_TASK' permission.", "operationId": "assign_task_api_v1_tasks__task_identifier__assign_patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskAssignSchema.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}