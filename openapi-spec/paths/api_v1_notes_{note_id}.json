{"get": {"tags": ["notes", "notes"], "summary": "Get Note", "description": "Retrieve a specific note by its ID.", "operationId": "get_note_api_v1_notes__note_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/NoteResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["notes", "notes"], "summary": "Update Note", "description": "Update the content of a specific note.", "operationId": "update_note_api_v1_notes__note_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/NoteUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/NoteResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["notes", "notes"], "summary": "Delete Note", "description": "Delete a specific note.", "operationId": "delete_note_api_v1_notes__note_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}