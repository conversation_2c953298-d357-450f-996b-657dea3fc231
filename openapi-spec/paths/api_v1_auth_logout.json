{"post": {"tags": ["auth", "auth"], "summary": "Logout", "description": "<PERSON><PERSON><PERSON> and invalidate the refresh token.", "operationId": "logout_api_v1_auth_logout_post", "parameters": [{"name": "refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Logout Api V1 Auth Logout Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}