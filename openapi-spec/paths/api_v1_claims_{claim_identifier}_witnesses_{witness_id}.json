{"get": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Get Witness", "description": "Get a specific witness by ID.", "operationId": "get_witness_api_v1_claims__claim_identifier__witnesses__witness_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/WitnessResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Update Witness", "description": "Update an existing witness.", "operationId": "update_witness_api_v1_claims__claim_identifier__witnesses__witness_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/WitnessUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/WitnessResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Delete Witness", "description": "Delete a witness.", "operationId": "delete_witness_api_v1_claims__claim_identifier__witnesses__witness_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}