{"post": {"tags": ["config", "config"], "summary": "Initialize Database", "description": "Initialize the database if not already initialized.\n\nThis endpoint creates all necessary tables and inserts initial data.\nIt will do nothing if the database is already initialized, unless\nthe force parameter is set to true.\n\nThis endpoint does not require authentication to allow initial setup.", "operationId": "initialize_database_api_v1_config_initialize_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Force reinitialization even if already initialized", "default": false, "title": "Force"}, "description": "Force reinitialization even if already initialized"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "Response Initialize Database Api V1 Config Initialize Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}