{"get": {"tags": ["claims", "documents", "documents"], "summary": "List Claim Documents", "description": "List documents for a specific claim.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    skip: Number of records to skip\n    limit: Maximum number of records to return\n    document_type: Filter by document type\n    document_service: Document service\n\nReturns:\n    List of documents", "operationId": "list_claim_documents_api_v1_claims__claim_id__documents_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/DocumentType.json"}, {"type": "null"}], "title": "Document Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentList.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "documents", "documents"], "summary": "Create Claim Document", "description": "Create a new document record for a claim.\n\nThis endpoint expects the document to have already been uploaded to the storage.\nUse the upload-url endpoint first to get a URL for uploading the file.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    document_data: Document data\n    current_user: Current authenticated user\n    document_service: Document service\n\nReturns:\n    Created document", "operationId": "create_claim_document_api_v1_claims__claim_id__documents_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentCreate.json", "description": "Document data"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DocumentResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}