{"get": {"tags": ["customers", "customers"], "summary": "Get Customer", "description": "Get a customer by ID.", "operationId": "get_customer_api_v1_customers__customer_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["customers", "customers"], "summary": "Update Customer", "description": "Update a customer.", "operationId": "update_customer_api_v1_customers__customer_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/CustomerResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["customers", "customers"], "summary": "Delete Customer", "description": "Delete a customer.", "operationId": "delete_customer_api_v1_customers__customer_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}