{"get": {"tags": ["config", "config"], "summary": "Is Database Initialized", "description": "Check if the database has been initialized.\n\nReturns a simple boolean response indicating whether the database has been\ninitialized with the required initial data.\n\nThis endpoint does not require authentication to allow initial status check.", "operationId": "is_database_initialized_api_v1_config_is_initialized_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Response Is Database Initialized Api V1 Config Is Initialized Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}