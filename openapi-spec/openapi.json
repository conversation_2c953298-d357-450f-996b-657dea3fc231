{"openapi": "3.1.0", "info": {"title": "Claimentine", "version": "0.1.0"}, "paths": {"/health": {"$ref": "paths/health.json"}, "/api/v1/auth/token": {"$ref": "paths/api_v1_auth_token.json"}, "/api/v1/auth/refresh": {"$ref": "paths/api_v1_auth_refresh.json"}, "/api/v1/auth/logout": {"$ref": "paths/api_v1_auth_logout.json"}, "/api/v1/auth/sessions": {"$ref": "paths/api_v1_auth_sessions.json"}, "/api/v1/auth/sessions/{session_id}": {"$ref": "paths/api_v1_auth_sessions_{session_id}.json"}, "/api/v1/auth/sessions/cleanup": {"$ref": "paths/api_v1_auth_sessions_cleanup.json"}, "/api/v1/users/me": {"$ref": "paths/api_v1_users_me.json"}, "/api/v1/users": {"$ref": "paths/api_v1_users.json"}, "/api/v1/users/{user_id}": {"$ref": "paths/api_v1_users_{user_id}.json"}, "/api/v1/users/password-change": {"$ref": "paths/api_v1_users_password-change.json"}, "/api/v1/claims/{claim_id}/documents/{document_id}": {"$ref": "paths/api_v1_claims_{claim_id}_documents_{document_id}.json"}, "/api/v1/claims/{claim_id}/documents": {"$ref": "paths/api_v1_claims_{claim_id}_documents.json"}, "/api/v1/claims/{claim_id}/documents/{document_id}/download-url": {"$ref": "paths/api_v1_claims_{claim_id}_documents_{document_id}_download-url.json"}, "/api/v1/claims/{claim_id}/documents/upload-url": {"$ref": "paths/api_v1_claims_{claim_id}_documents_upload-url.json"}, "/api/v1/claims/{claim_id}/documents/direct-upload": {"$ref": "paths/api_v1_claims_{claim_id}_documents_direct-upload.json"}, "/api/v1/claims/{claim_id}/documents/all": {"$ref": "paths/api_v1_claims_{claim_id}_documents_all.json"}, "/api/v1/claims/{claim_identifier}/tasks/": {"$ref": "paths/api_v1_claims_{claim_identifier}_tasks_.json"}, "/api/v1/claims/{claim_identifier}/witnesses": {"$ref": "paths/api_v1_claims_{claim_identifier}_witnesses.json"}, "/api/v1/claims/{claim_identifier}/witnesses/{witness_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_witnesses_{witness_id}.json"}, "/api/v1/claims/{claim_identifier}/attorneys": {"$ref": "paths/api_v1_claims_{claim_identifier}_attorneys.json"}, "/api/v1/claims/{claim_identifier}/attorneys/{attorney_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_attorneys_{attorney_id}.json"}, "/api/v1/claims/{claim_identifier}/audit": {"$ref": "paths/api_v1_claims_{claim_identifier}_audit.json"}, "/api/v1/claims/{claim_identifier}/audit/summary": {"$ref": "paths/api_v1_claims_{claim_identifier}_audit_summary.json"}, "/api/v1/claims/{claim_identifier}/audit/{audit_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_audit_{audit_id}.json"}, "/api/v1/claims": {"$ref": "paths/api_v1_claims.json"}, "/api/v1/claims/{claim_identifier}": {"$ref": "paths/api_v1_claims_{claim_identifier}.json"}, "/api/v1/claims/{claim_identifier}/close": {"$ref": "paths/api_v1_claims_{claim_identifier}_close.json"}, "/api/v1/claims/{claim_identifier}/financials": {"$ref": "paths/api_v1_claims_{claim_identifier}_financials.json"}, "/api/v1/claims/{claim_identifier}/financials/reserve": {"$ref": "paths/api_v1_claims_{claim_identifier}_financials_reserve.json"}, "/api/v1/claims/{claim_identifier}/financials/reserve-history": {"$ref": "paths/api_v1_claims_{claim_identifier}_financials_reserve-history.json"}, "/api/v1/claims/{claim_identifier}/financials/payments": {"$ref": "paths/api_v1_claims_{claim_identifier}_financials_payments.json"}, "/api/v1/claims/{claim_identifier}/recovery/status": {"$ref": "paths/api_v1_claims_{claim_identifier}_recovery_status.json"}, "/api/v1/claims/{claim_identifier}/recovery": {"$ref": "paths/api_v1_claims_{claim_identifier}_recovery.json"}, "/api/v1/claims/{claim_identifier}/carrier": {"$ref": "paths/api_v1_claims_{claim_identifier}_carrier.json"}, "/api/v1/claims/{claim_identifier}/recovery/amounts": {"$ref": "paths/api_v1_claims_{claim_identifier}_recovery_amounts.json"}, "/api/v1/claims/{claim_identifier}/bodily_injury": {"$ref": "paths/api_v1_claims_{claim_identifier}_bodily_injury.json"}, "/api/v1/claims/{claim_identifier}/injured-persons": {"$ref": "paths/api_v1_claims_{claim_identifier}_injured-persons.json"}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_injured-persons_{person_id}.json"}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}/injuries": {"$ref": "paths/api_v1_claims_{claim_identifier}_injured-persons_{person_id}_injuries.json"}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}/injuries/{injury_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_injured-persons_{person_id}_injuries_{injury_id}.json"}, "/api/v1/claims/{claim_identifier}/damaged-property-assets": {"$ref": "paths/api_v1_claims_{claim_identifier}_damaged-property-assets.json"}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_damaged-property-assets_{asset_id}.json"}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances": {"$ref": "paths/api_v1_claims_{claim_identifier}_damaged-property-assets_{asset_id}_damage-instances.json"}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances/{instance_id}": {"$ref": "paths/api_v1_claims_{claim_identifier}_damaged-property-assets_{asset_id}_damage-instances_{instance_id}.json"}, "/api/v1/claims/{claim_id}/tasks/": {"$ref": "paths/api_v1_claims_{claim_id}_tasks_.json"}, "/api/v1/customers": {"$ref": "paths/api_v1_customers.json"}, "/api/v1/customers/{customer_id}": {"$ref": "paths/api_v1_customers_{customer_id}.json"}, "/api/v1/fnols": {"$ref": "paths/api_v1_fnols.json"}, "/api/v1/fnols/number/{fnol_number}": {"$ref": "paths/api_v1_fnols_number_{fnol_number}.json"}, "/api/v1/fnols/{fnol_id}": {"$ref": "paths/api_v1_fnols_{fnol_id}.json"}, "/api/v1/fnols/{fnol_id}/convert": {"$ref": "paths/api_v1_fnols_{fnol_id}_convert.json"}, "/api/v1/config/reserves": {"$ref": "paths/api_v1_config_reserves.json"}, "/api/v1/config/reserves/{config_id}": {"$ref": "paths/api_v1_config_reserves_{config_id}.json"}, "/api/v1/config/customers/{customer_id}/authority": {"$ref": "paths/api_v1_config_customers_{customer_id}_authority.json"}, "/api/v1/config/authority/{threshold_id}": {"$ref": "paths/api_v1_config_authority_{threshold_id}.json"}, "/api/v1/config/initialize": {"$ref": "paths/api_v1_config_initialize.json"}, "/api/v1/config/is-initialized": {"$ref": "paths/api_v1_config_is-initialized.json"}, "/api/v1/config/system-configs": {"$ref": "paths/api_v1_config_system-configs.json"}, "/api/v1/config/system-configs/{config_id}": {"$ref": "paths/api_v1_config_system-configs_{config_id}.json"}, "/api/v1/config/system-configs/{key}": {"$ref": "paths/api_v1_config_system-configs_{key}.json"}, "/api/v1/metrics/dashboard": {"$ref": "paths/api_v1_metrics_dashboard.json"}, "/api/v1/reports/claims-kpis": {"$ref": "paths/api_v1_reports_claims-kpis.json"}, "/api/v1/reports/claims-by-type": {"$ref": "paths/api_v1_reports_claims-by-type.json"}, "/api/v1/reports/claims-by-status": {"$ref": "paths/api_v1_reports_claims-by-status.json"}, "/api/v1/reports/claims-over-time": {"$ref": "paths/api_v1_reports_claims-over-time.json"}, "/api/v1/reports/financial-kpis": {"$ref": "paths/api_v1_reports_financial-kpis.json"}, "/api/v1/reports/payments-vs-reserves": {"$ref": "paths/api_v1_reports_payments-vs-reserves.json"}, "/api/v1/reports/adjuster-performance": {"$ref": "paths/api_v1_reports_adjuster-performance.json"}, "/api/v1/reports/{report_name}": {"$ref": "paths/api_v1_reports_{report_name}.json"}, "/api/v1/claims/{claim_identifier}/notes": {"$ref": "paths/api_v1_claims_{claim_identifier}_notes.json"}, "/api/v1/claims/{claim_id}/notes": {"$ref": "paths/api_v1_claims_{claim_id}_notes.json"}, "/api/v1/notes/{note_id}": {"$ref": "paths/api_v1_notes_{note_id}.json"}, "/api/v1/tasks/": {"$ref": "paths/api_v1_tasks_.json"}, "/api/v1/tasks/{task_identifier}": {"$ref": "paths/api_v1_tasks_{task_identifier}.json"}, "/api/v1/tasks/{task_identifier}/assign": {"$ref": "paths/api_v1_tasks_{task_identifier}_assign.json"}, "/api/v1/tasks/{task_identifier}/status": {"$ref": "paths/api_v1_tasks_{task_identifier}_status.json"}, "/api/v1/documents": {"$ref": "paths/api_v1_documents.json"}, "/api/v1/{path}": {"$ref": "paths/api_v1_{path}.json"}}, "components": {"securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/auth/token"}}}}}}