# Claimentine Permissions and Roles

## Permission Structure

### Claims Management
| Operation                | Customer | Agent | Adjuster | Manager | Admin |
|-------------------------|----------|--------|----------|---------|--------|
| View Own Claims         | ✓        | ✓     | ✓        | ✓       | ✓      |
| View Assigned Claims    | -        | ✓     | ✓        | ✓       | ✓      |
| View All Claims         | -        | -     | ✓        | ✓       | ✓      |
| Create Own Claims       | ✓        | -     | -        | -       | -      |
| Create Claims           | -        | ✓     | ✓        | ✓       | ✓      |
| Edit Assigned Claims    | -        | ✓     | -        | -       | -      |
| Edit Claims             | -        | -     | ✓        | ✓       | ✓      |
| Delete Claims           | -        | -     | -        | ✓       | ✓      |
| Assign Claims           | -        | -     | ✓        | ✓       | ✓      |
| Close Claims            | -        | -     | ✓        | ✓       | ✓      |

### User Management
| Operation              | Customer | Agent | Adjuster | Manager | Admin |
|-----------------------|----------|--------|----------|---------|--------|
| View Own Profile      | ✓        | ✓     | ✓        | ✓       | ✓      |
| Edit Own Profile      | ✓        | ✓     | ✓        | ✓       | ✓      |
| View Users            | -        | -     | ✓        | ✓       | ✓      |
| Create Users          | -        | -     | -        | ✓       | ✓      |
| Edit Users            | -        | -     | -        | ✓       | ✓      |
| Delete Users          | -        | -     | -        | ✓       | ✓      |
| Manage Roles          | -        | -     | -        | -       | ✓      |

### Document Management
| Operation                  | Customer | Agent | Adjuster | Manager | Admin |
|---------------------------|----------|--------|----------|---------|--------|
| View Own Documents        | ✓        | ✓     | ✓        | ✓       | ✓      |
| View Assigned Documents   | -        | ✓     | ✓        | ✓       | ✓      |
| View All Documents        | -        | -     | ✓        | ✓       | ✓      |
| Upload Own Documents      | ✓        | -     | -        | -       | -      |
| Upload Documents          | -        | ✓     | ✓        | ✓       | ✓      |
| Delete Documents          | -        | -     | ✓        | ✓       | ✓      |
| Manage Categories         | -        | -     | -        | ✓       | ✓      |

### Financial Operations
| Operation                  | Customer | Agent | Adjuster | Manager | Admin |
|---------------------------|----------|--------|----------|---------|--------|
| View Financials           | -        | ✓     | ✓        | ✓       | ✓      |
| Set Initial Reserve       | -        | -     | ✓        | ✓       | ✓      |
| Update Reserves           | -        | -     | ✓        | ✓       | ✓      |
| Process Payments          | -        | -     | ✓        | ✓       | ✓      |
| Override Financial Limits | -        | -     | -        | ✓       | ✓      |

### System Operations
| Operation                  | Customer | Agent | Adjuster | Manager | Admin |
|---------------------------|----------|--------|----------|---------|--------|
| View Reports              | -        | -     | -        | ✓       | ✓      |
| View Basic Analytics      | -        | -     | -        | ✓       | ✓      |
| View Advanced Analytics   | -        | -     | -        | -       | ✓      |
| Configure Reports         | -        | -     | -        | -       | ✓      |
| Manage Workflows          | -        | -     | -        | ✓       | ✓      |
| System Configuration      | -        | -     | -        | -       | ✓      |
| View Audit Logs           | -        | -     | -        | -       | ✓      |
| Manage API Keys           | -        | -     | -        | -       | ✓      |
| Manage Security Settings  | -        | -     | -        | -       | ✓      |
| View Security Logs        | -        | -     | -        | -       | ✓      |

## Role Hierarchy

```
ADMIN
  └── MANAGER
        └── ADJUSTER
              └── AGENT
                    └── CUSTOMER
```

Each role inherits all permissions from the roles below it in the hierarchy. 

## Permission Inheritance

Roles inherit permissions from roles beneath them in the hierarchy. For example:
- An ADMIN has all permissions that any role has
- A MANAGER has all permissions of ADJUSTER, AGENT, and CUSTOMER
- An ADJUSTER has all permissions of AGENT and CUSTOMER
- The lowest role (CUSTOMER) has no inherited permissions

The inheritance is implemented in the `get_inherited_roles` function, which recursively builds the set of all roles that a given role inherits from.

## Standard Permission Checking Patterns

### 1. Service-Level Permission Checking

All permission checks are consolidated in the service layer using the `BaseService.check_permission` method:

```python
# Example of direct permission check
self.check_permission("VIEW_CLAIM", resource_type="claim", resource_id=claim_id)
```

### 2. Using the Permission Decorator

The preferred way to implement permission checks is the `@require_service_permission` decorator:

```python
@require_service_permission("VIEW_CLAIM", resource_id_param="claim_id")
def get_claim(self, claim_id: UUID):
    # Method implementation without explicit permission checks
```

The decorator automatically:
- Extracts resource IDs from method parameters
- Handles appropriate error creation
- Works with both single permissions and permission lists
- Supports checking for "any of" multiple permissions

#### Multiple Permissions (ALL)

```python
@require_service_permission(["VIEW_CLAIM", "EDIT_CLAIM"], resource_id_param="claim_id")
def update_claim(self, claim_id: UUID, data: dict):
    # All permissions are required
```

#### Multiple Permissions (ANY)

```python
@require_service_permission(
    ["VIEW_OWN_CLAIMS", "VIEW_ASSIGNED_CLAIMS", "VIEW_ALL_CLAIMS"],
    resource_id_param="claim_id",
    any_permission=True
)
def get_claim(self, claim_id: UUID):
    # Any of the permissions is sufficient
```

### 3. Permission Caching

The `BaseService` implements permission caching to avoid redundant checks:

```python
# First call performs the actual check
result1 = service.check_permission("VIEW_CLAIM", resource_id=claim_id)

# Subsequent calls with same parameters use the cached result
result2 = service.check_permission("VIEW_CLAIM", resource_id=claim_id)  # Uses cache
```

### 4. Resource-Specific Permission Checking

For specialized resource access patterns, implement custom permission checks by extending the pattern:

```python
# Example of a resource-specific permission check
@require_service_permission("VIEW_ASSIGNED_CLAIMS", resource_id_param="claim_id")
def get_assigned_claim(self, claim_id: UUID):
    # Check if the claim is assigned to the current user
    if not self._is_claim_assigned_to_user(claim_id, self.current_user.id):
        raise AuthorizationError("Not authorized to access this claim")
    
    # Rest of the implementation
```

## Error Handling

All permission denials generate a standardized error response using the `create_permission_error` function, which ensures:
- Consistent error messages
- Proper logging with contextual information
- Appropriate HTTP status codes (403)
- Detailed error context for debugging 