# Project
PROJECT_NAME=Claimentine
VERSION=0.1.0
API_V1_STR=/api/v1

# Database
POSTGRES_HOST=dpg-d0502chr0fns73ctk2ig-a
POSTGRES_PORT=5432
POSTGRES_USER=claimentine_k0ii_user
POSTGRES_PASSWORD=HUK4K47ZEyaHL7CDaqKNc8xqNb3JIUYs
POSTGRES_DB=claimentine_k0ii
DB_ECHO_LOG=false
DB_POOL_SIZE=5
DB_MAX_OVERFLOW=10

# Security
JWT_SECRET_KEY=dev-secret-key-change-in-production
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=24
EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=48

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000"]

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=json 