{"openapi": "3.1.0", "info": {"title": "Claimentine", "version": "0.1.0"}, "paths": {"/health": {"get": {"tags": ["health"], "summary": "Get Health Status", "description": "Get system health status.", "operationId": "get_health_status_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "boolean"}, {"additionalProperties": true, "type": "object"}]}, "type": "object", "title": "Response Get Health Status Health Get"}}}}}}}, "/api/v1/auth/token": {"post": {"tags": ["auth", "auth"], "summary": "<PERSON><PERSON>", "description": "Login endpoint to get access and refresh tokens.", "operationId": "login_api_v1_auth_token_post", "requestBody": {"content": {"application/x-www-form-urlencoded": {"schema": {"$ref": "#/components/schemas/Body_login_api_v1_auth_token_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/refresh": {"post": {"tags": ["auth", "auth"], "summary": "Refresh <PERSON>", "description": "Get a new access token using a refresh token.", "operationId": "refresh_token_api_v1_auth_refresh_post", "parameters": [{"name": "refresh_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}, {"name": "refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/logout": {"post": {"tags": ["auth", "auth"], "summary": "Logout", "description": "<PERSON><PERSON><PERSON> and invalidate the refresh token.", "operationId": "logout_api_v1_auth_logout_post", "parameters": [{"name": "refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Refresh <PERSON>"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Logout Api V1 Auth Logout Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/sessions": {"get": {"tags": ["auth", "auth"], "summary": "List Sessions", "description": "List active sessions for the current user.", "operationId": "list_sessions_api_v1_auth_sessions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserSessionResponse"}, "type": "array", "title": "Response List Sessions Api V1 Auth Sessions Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/auth/sessions/{session_id}": {"delete": {"tags": ["auth", "auth"], "summary": "Revoke Session", "description": "Revoke a specific session.", "operationId": "revoke_session_api_v1_auth_sessions__session_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Session Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/auth/sessions/cleanup": {"post": {"tags": ["auth", "auth"], "summary": "Cleanup Sessions", "description": "Clean up expired sessions.\n\nRequires <PERSON><PERSON>GE_SECURITY_SETTINGS permission.\nOnly removes sessions that have expired based on their expires_at timestamp.", "operationId": "cleanup_sessions_api_v1_auth_sessions_cleanup_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": true, "type": "object", "title": "Response Cleanup Sessions Api V1 Auth Sessions Cleanup Post"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/users/me": {"get": {"tags": ["users", "users"], "summary": "Get Current User Details", "description": "Get current user details.", "operationId": "get_current_user_details_api_v1_users_me_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}, "patch": {"tags": ["users", "users"], "summary": "Update Current User", "description": "Update current user's own profile. Permissions are checked at the service layer.", "operationId": "update_current_user_api_v1_users_me_patch", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}}, "/api/v1/users": {"get": {"tags": ["users", "users"], "summary": "List Users", "description": "List users with optional filtering and search. Permissions are checked at the service layer.", "operationId": "list_users_api_v1_users_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in names, email, and department", "title": "Search"}, "description": "Search in names, email, and department"}, {"name": "role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}], "title": "Role"}}, {"name": "authority_role", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AuthorityRole"}, {"type": "null"}], "title": "Authority Role"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "department", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Department"}}, {"name": "email", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserResponse"}, "title": "Response List Users Api V1 Users Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["users", "users"], "summary": "Create User", "description": "Create a new user. Permissions are checked at the service layer.", "operationId": "create_user_api_v1_users_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/{user_id}": {"get": {"tags": ["users", "users"], "summary": "Get User", "description": "Get user by ID. Permissions are checked at the service layer.", "operationId": "get_user_api_v1_users__user_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["users", "users"], "summary": "Update User", "description": "Update user by ID. Permissions are checked at the service layer.", "operationId": "update_user_api_v1_users__user_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["users", "users"], "summary": "Delete User", "description": "Delete user by ID. Permissions are checked at the service layer.", "operationId": "delete_user_api_v1_users__user_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "User Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/users/password-change": {"post": {"tags": ["users", "users"], "summary": "Change Password", "description": "Change the current user's password.", "operationId": "change_password_api_v1_users_password_change_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordChange"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}]}}, "/api/v1/claims/{claim_id}/documents/{document_id}": {"get": {"tags": ["claims", "documents", "documents"], "summary": "Get Document By Id", "description": "Get document by ID for a specific claim.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    document_id: Document ID\n    document_service: Document service\n\nReturns:\n    Document data", "operationId": "get_document_by_id_api_v1_claims__claim_id__documents__document_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["claims", "documents", "documents"], "summary": "Update Document", "description": "Update document metadata.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_data: Document data to update\n    document_service: Document service\n\nReturns:\n    Updated document data", "operationId": "update_document_api_v1_claims__claim_id__documents__document_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUpdate", "description": "Document data to update"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "documents", "documents"], "summary": "Delete Claim Document", "description": "Delete document by ID.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_service: Document service", "operationId": "delete_claim_document_api_v1_claims__claim_id__documents__document_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/documents": {"get": {"tags": ["claims", "documents", "documents"], "summary": "List Claim Documents", "description": "List documents for a specific claim.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    skip: Number of records to skip\n    limit: Maximum number of records to return\n    document_type: Filter by document type\n    document_service: Document service\n\nReturns:\n    List of documents", "operationId": "list_claim_documents_api_v1_claims__claim_id__documents_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentType"}, {"type": "null"}], "title": "Document Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "documents", "documents"], "summary": "Create Claim Document", "description": "Create a new document record for a claim.\n\nThis endpoint expects the document to have already been uploaded to the storage.\nUse the upload-url endpoint first to get a URL for uploading the file.\n\nArgs:\n    claim_id: Claim ID or Claim Number\n    document_data: Document data\n    current_user: Current authenticated user\n    document_service: Document service\n\nReturns:\n    Created document", "operationId": "create_claim_document_api_v1_claims__claim_id__documents_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentCreate", "description": "Document data"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/documents/{document_id}/download-url": {"get": {"tags": ["claims", "documents", "documents"], "summary": "Get Document Download Url", "description": "Get a signed URL for downloading a document.\nValidation ensures document exists and user has permission.\n\nArgs:\n    document_id: Document ID\n    document_service: Document service\n\nReturns:\n    Download URL and expiration time", "operationId": "get_document_download_url_api_v1_claims__claim_id__documents__document_id__download_url_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number (used for context, validation done by service)", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number (used for context, validation done by service)"}, {"name": "document_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Document ID", "title": "Document Id"}, "description": "Document ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentDownloadUrlResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/documents/upload-url": {"post": {"tags": ["claims", "documents", "documents"], "summary": "Get Claim Document Upload Url", "description": "Get a signed URL for uploading a document to a claim.\n\nArgs:\n    upload_request: Upload request data\n    claim_id: Claim ID or Claim Number\n    document_service: Document service\n\nReturns:\n    Upload URL, document ID, and expiration time", "operationId": "get_claim_document_upload_url_api_v1_claims__claim_id__documents_upload_url_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadUrlRequest"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentUploadUrlResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/documents/direct-upload": {"post": {"tags": ["claims", "documents", "documents"], "summary": "Direct Upload Document", "description": "Upload a file directly and create a document record.\n\nThis endpoint handles both file upload and record creation in one request.\nMaximum file size: 100MB.\n\nArgs:\n    request: Request object\n    claim_id: Claim ID or Claim Number\n    file: File to upload\n    document_type: Document type\n    document_name: Document display name (optional)\n    document_description: Document description (optional)\n    current_user: Current authenticated user\n    document_service: Document service\n\nReturns:\n    Created document", "operationId": "direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID or Claim Number", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Claim ID or Claim Number"}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/documents/all": {"get": {"tags": ["claims", "documents", "documents"], "summary": "List All Documents", "description": "List all documents across all claims with optional filtering.\nRequires MANAGE_DOCUMENTS permission.\n\nArgs:\n    skip: Number of records to skip\n    limit: Maximum number of records to return\n    document_type: Filter by document type\n    keyword: Search in document name, description, or filename\n    document_service: Document service\n\nReturns:\n    List of documents", "operationId": "list_all_documents_api_v1_claims__claim_id__documents_all_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentType"}, {"type": "null"}], "title": "Document Type"}}, {"name": "keyword", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Keyword"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/tasks/": {"post": {"tags": ["claims", "tasks"], "summary": "Create Task for C<PERSON>m", "description": "Create a new task associated with a specific claim. Requires 'CREATE_TASK' permission.", "operationId": "create_task_api_v1_claims__claim_identifier__tasks__post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCreateRequestBody"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/witnesses": {"get": {"tags": ["claims", "witnesses", "witnesses"], "summary": "List Witnesses", "description": "Get all witnesses for a claim.", "operationId": "list_witnesses_api_v1_claims__claim_identifier__witnesses_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WitnessResponse"}, "title": "Response List Witnesses Api V1 Claims  Claim Identifier  Witnesses Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Create Witness", "description": "Create a new witness for a claim.", "operationId": "create_witness_api_v1_claims__claim_identifier__witnesses_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WitnessCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WitnessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/witnesses/{witness_id}": {"get": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Get Witness", "description": "Get a specific witness by ID.", "operationId": "get_witness_api_v1_claims__claim_identifier__witnesses__witness_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WitnessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Update Witness", "description": "Update an existing witness.", "operationId": "update_witness_api_v1_claims__claim_identifier__witnesses__witness_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WitnessUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WitnessResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "witnesses", "witnesses"], "summary": "Delete Witness", "description": "Delete a witness.", "operationId": "delete_witness_api_v1_claims__claim_identifier__witnesses__witness_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "witness_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Witness ID", "title": "Witness Id"}, "description": "Witness ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/attorneys": {"get": {"tags": ["claims", "attorneys", "attorneys"], "summary": "List Attorneys", "description": "Get all attorneys for a claim.", "operationId": "list_attorneys_api_v1_claims__claim_identifier__attorneys_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttorneyResponse"}, "title": "Response List Attorneys Api V1 Claims  Claim Identifier  Attorneys Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Create Attorney", "description": "Create a new attorney for a claim.", "operationId": "create_attorney_api_v1_claims__claim_identifier__attorneys_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttorneyCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttorneyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/attorneys/{attorney_id}": {"get": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Get Attorney", "description": "Get a specific attorney by ID.", "operationId": "get_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttorneyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Update Attorney", "description": "Update an existing attorney.", "operationId": "update_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttorneyUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AttorneyResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "attorneys", "attorneys"], "summary": "Delete Attorney", "description": "Delete an attorney.", "operationId": "delete_attorney_api_v1_claims__claim_identifier__attorneys__attorney_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "attorney_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Attorney ID", "title": "Attorney Id"}, "description": "Attorney ID"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/audit": {"get": {"tags": ["claims", "audit", "audit"], "summary": "List Audit Entries", "description": "Get audit trail entries for a claim with optional filtering and pagination.", "operationId": "list_audit_entries_api_v1_claims__claim_identifier__audit_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "entity_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/EntityType"}, {"type": "null"}], "description": "Filter by entity type", "title": "Entity Type"}, "description": "Filter by entity type"}, {"name": "change_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ChangeType"}, {"type": "null"}], "description": "Filter by change type", "title": "Change Type"}, "description": "Filter by change type"}, {"name": "from_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by date range start", "title": "From Date"}, "description": "Filter by date range start"}, {"name": "to_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by date range end", "title": "To Date"}, "description": "Filter by date range end"}, {"name": "changed_by_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by user who made the change", "title": "Changed By Id"}, "description": "Filter by user who made the change"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of items to skip (pagination)", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of items to skip (pagination)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Maximum number of items to return (pagination)", "default": 50, "title": "Limit"}, "description": "Maximum number of items to return (pagination)"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedAuditTrailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "audit", "audit"], "summary": "Create Audit Entry", "description": "Create a manual audit entry.\n\nThis is typically used for administrative purposes or to document\nactions that occurred outside the system.", "operationId": "create_audit_entry_api_v1_claims__claim_identifier__audit_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditTrailCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditTrailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/audit/summary": {"get": {"tags": ["claims", "audit", "audit"], "summary": "Get Audit Summary", "description": "Get a summary of audit activity for a claim.", "operationId": "get_audit_summary_api_v1_claims__claim_identifier__audit_summary_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "entries_limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of recent entries to include in summary", "default": 5, "title": "Entries Limit"}, "description": "Number of recent entries to include in summary"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditSummaryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/audit/{audit_id}": {"get": {"tags": ["claims", "audit", "audit"], "summary": "Get Audit Entry", "description": "Get a specific audit entry by ID.", "operationId": "get_audit_entry_api_v1_claims__claim_identifier__audit__audit_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "audit_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Audit entry ID", "title": "Audit Id"}, "description": "Audit entry ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuditTrailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims": {"get": {"tags": ["claims", "claims"], "summary": "List Claims", "description": "List claims with filtering based on user's permissions.", "operationId": "list_claims_api_v1_claims_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "title": "Type"}}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "assigned_to_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id"}}, {"name": "supervisor_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id"}}, {"name": "team", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search across claim number, claimant name, and description", "title": "Search"}, "description": "Search across claim number, claimant name, and description"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer"], "title": "Include"}, "description": "Include related items (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedClaimResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "Create a new claim.", "operationId": "create_claim_api_v1_claims_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_type", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/ClaimType", "description": "Type of claim to create"}, "description": "Type of claim to create"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Claim Data"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AutoClaimResponse"}, {"$ref": "#/components/schemas/PropertyClaimResponse"}, {"$ref": "#/components/schemas/GeneralLiabilityClaimResponse"}], "title": "Response Create Claim Api V1 Claims Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}": {"get": {"tags": ["claims", "claims"], "summary": "<PERSON>", "description": "Get claim by ID (UUID) or Claim Number.\n\nThe response model will be determined by the claim's type.\nOptionally include related items via the 'include' query parameter:\n- customer: Claim customer (included by default if not specified otherwise)\n- documents: Claim documents\n- notes: Claim notes\n- tasks: Claim tasks\n- status_history: Claim status history\n- financials: Claim financials\n- assigned_to: Assigned user details\n- supervisor: Supervisor user details\n- created_by: Creator user details\n- auto_details: Auto claim details (for auto claims)\n- property_details: Property claim details (for property claims)\n- gl_details: General liability claim details (for general liability claims)", "operationId": "get_claim_api_v1_claims__claim_identifier__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AutoClaimResponse"}, {"$ref": "#/components/schemas/PropertyClaimResponse"}, {"$ref": "#/components/schemas/GeneralLiabilityClaimResponse"}], "title": "Response Get Claim Api V1 Claims  Claim Identifier  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["claims", "claims"], "summary": "Update Claim", "description": "Update claim by ID or Number.", "operationId": "update_claim_api_v1_claims__claim_identifier__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "Claim ID (UUID) or Claim Number", "title": "Claim Identifier"}, "description": "Claim ID (UUID) or Claim Number"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (documents, notes, tasks, status_history, auto_details, property_details)", "default": ["status_history"], "title": "Include"}, "description": "Include related items (documents, notes, tasks, status_history, auto_details, property_details)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AutoClaimUpdate"}, {"$ref": "#/components/schemas/PropertyClaimUpdate"}, {"$ref": "#/components/schemas/GeneralLiabilityClaimUpdate"}], "title": "Claim Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/AutoClaimResponse"}, {"$ref": "#/components/schemas/PropertyClaimResponse"}, {"$ref": "#/components/schemas/GeneralLiabilityClaimResponse"}], "title": "Response Update Claim Api V1 Claims  Claim Identifier  Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims"], "summary": "Delete Claim", "description": "Delete claim by ID or Number.", "operationId": "delete_claim_api_v1_claims__claim_identifier__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/close": {"post": {"tags": ["claims", "claims"], "summary": "Close Claim Endpoint", "description": "Close a claim identified by ID or Number.", "operationId": "close_claim_endpoint_api_v1_claims__claim_identifier__close_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CloseClaimSchema"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/financials": {"get": {"tags": ["claims", "claims"], "summary": "Get Claim Financials", "description": "Get financials for a claim identified by ID or Number.", "operationId": "get_claim_financials_api_v1_claims__claim_identifier__financials_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimFinancialsInDB"}, {"type": "null"}], "title": "Response Get Claim Financials Api V1 Claims  Claim Identifier  Financials Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims"], "summary": "Create Claim Financials", "description": "Create financials for a claim identified by ID or Number.", "operationId": "create_claim_financials_api_v1_claims__claim_identifier__financials_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFinancialsCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFinancialsInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims"], "summary": "Update Claim Financials", "description": "Update financial details for a claim identified by ID or Number.", "operationId": "update_claim_financials_api_v1_claims__claim_identifier__financials_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFinancialsUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFinancialsInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/financials/reserve": {"put": {"tags": ["claims", "claims"], "summary": "Update Claim Reserve", "description": "Update reserve for a claim identified by ID or Number.", "operationId": "update_claim_reserve_api_v1_claims__claim_identifier__financials_reserve_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimFinancialsInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/financials/reserve-history": {"get": {"tags": ["claims", "claims"], "summary": "Get Claim Reserve History", "description": "Get reserve history for a claim identified by ID or Number.", "operationId": "get_claim_reserve_history_api_v1_claims__claim_identifier__financials_reserve_history_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReserveHistoryInDB"}, "title": "Response Get Claim Reserve History Api V1 Claims  Claim Identifier  Financials Reserve History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/financials/payments": {"post": {"tags": ["claims", "claims", "payments"], "summary": "Add Claim Payment", "description": "Add a payment to a claim identified by ID or Number.", "operationId": "add_claim_payment_api_v1_claims__claim_identifier__financials_payments_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["claims", "claims", "payments"], "summary": "List Claim Payments", "description": "List payments for a claim identified by ID or Number.", "operationId": "list_claim_payments_api_v1_claims__claim_identifier__financials_payments_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/recovery/status": {"patch": {"tags": ["claims", "claims", "recovery"], "summary": "Update Claim Recovery Status", "description": "Update recovery status for a claim identified by ID or Number.", "operationId": "update_claim_recovery_status_api_v1_claims__claim_identifier__recovery_status_patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "recovery_status", "in": "query", "required": true, "schema": {"$ref": "#/components/schemas/RecoveryStatus", "description": "New recovery status"}, "description": "New recovery status"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/recovery": {"get": {"tags": ["claims", "claims", "recovery"], "summary": "Get Claim Recovery Details", "description": "Get recovery details for a claim identified by ID or Number.", "operationId": "get_claim_recovery_details_api_v1_claims__claim_identifier__recovery_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecoveryDetailsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/carrier": {"patch": {"tags": ["claims", "claims", "recovery"], "summary": "Update Claim Carrier Details", "description": "Update third-party carrier information for a claim identified by ID or Number.", "operationId": "update_claim_carrier_details_api_v1_claims__claim_identifier__carrier_patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "carrier_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Name of the third-party carrier", "title": "Carrier Name"}, "description": "Name of the third-party carrier"}, {"name": "carrier_contact", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Contact info for the third-party carrier", "title": "Carrier Contact"}, "description": "Contact info for the third-party carrier"}, {"name": "carrier_claim_number", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Third-party carrier's claim number", "title": "Carrier Claim Number"}, "description": "Third-party carrier's claim number"}, {"name": "carrier_adjuster", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Third-party carrier's adjuster name", "title": "Carrier Adjuster"}, "description": "Third-party carrier's adjuster name"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/recovery/amounts": {"patch": {"tags": ["claims", "claims", "recovery"], "summary": "Update Claim Recovery Amounts", "description": "Update recovery amounts for a claim identified by ID or Number.", "operationId": "update_claim_recovery_amounts_api_v1_claims__claim_identifier__recovery_amounts_patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "expected_amount", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Expected recovery amount", "title": "Expected Amount"}, "description": "Expected recovery amount"}, {"name": "received_amount", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Amount actually recovered", "title": "Received Amount"}, "description": "Amount actually recovered"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClaimResponseSchema"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/bodily_injury": {"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "<PERSON> Bo<PERSON>ly Injury Details", "description": "Get bodily injury details for a claim identified by ID or Number.", "operationId": "get_bodily_injury_details_api_v1_claims__claim_identifier__bodily_injury_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/BodilyInjuryDetailsResponse"}, {"type": "null"}], "title": "Response Get Bodily Injury Details Api V1 Claims  Claim Identifier  Bodily Injury Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Update <PERSON><PERSON><PERSON>", "description": "Update bodily injury details for a claim identified by ID or Number.", "operationId": "update_bodily_injury_details_api_v1_claims__claim_identifier__bodily_injury_put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BodilyInjuryDetailsUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BodilyInjuryDetailsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Delete Bodily Injury Details", "description": "Delete bodily injury details for a claim identified by ID or Number.", "operationId": "delete_bodily_injury_details_api_v1_claims__claim_identifier__bodily_injury_delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/injured-persons": {"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injured Persons", "description": "Get all injured persons for a claim identified by ID or Number.", "operationId": "get_injured_persons_api_v1_claims__claim_identifier__injured_persons_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InjuredPersonResponse"}, "title": "Response Get Injured Persons Api V1 Claims  Claim Identifier  Injured Persons Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Create Injured Person", "description": "Create an injured person for a claim identified by ID or Number.", "operationId": "create_injured_person_api_v1_claims__claim_identifier__injured_persons_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuredPersonCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuredPersonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}": {"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injured Person", "description": "Get an injured person for a claim identified by ID or Number.", "operationId": "get_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuredPersonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Update Injured Person", "description": "Update an injured person for a claim identified by ID or Number.", "operationId": "update_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuredPersonUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuredPersonResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Delete Injured Person", "description": "Delete an injured person for a claim identified by ID or Number.", "operationId": "delete_injured_person_api_v1_claims__claim_identifier__injured_persons__person_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}/injuries": {"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injuries", "description": "Get all injuries for an injured person.", "operationId": "get_injuries_api_v1_claims__claim_identifier__injured_persons__person_id__injuries_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/InjuryResponse"}, "title": "Response Get Injuries Api V1 Claims  Claim Identifier  Injured Persons  Person Id  Injuries Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Create Injury", "description": "Create an injury for an injured person.", "operationId": "create_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuryCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/injured-persons/{person_id}/injuries/{injury_id}": {"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injury", "description": "Get an injury for an injured person.", "operationId": "get_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Update Injury", "description": "Update an injury for an injured person.", "operationId": "update_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InjuryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Delete Injury", "description": "Delete an injury for an injured person.", "operationId": "delete_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries__injury_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "injury_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injury ID", "title": "Injury Id"}, "description": "Injury ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/damaged-property-assets": {"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damaged Property Assets", "description": "Get all damaged property assets for a claim identified by ID or Number.", "operationId": "get_damaged_property_assets_api_v1_claims__claim_identifier__damaged_property_assets_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}, "title": "Response Get Damaged Property Assets Api V1 Claims  Claim Identifier  Damaged Property Assets Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims", "property-damage"], "summary": "Create Damaged Property Asset", "description": "Create a damaged property asset for a claim identified by ID or Number.", "operationId": "create_damaged_property_asset_api_v1_claims__claim_identifier__damaged_property_assets_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamagedPropertyAssetCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}": {"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damaged Property Asset", "description": "Get a damaged property asset for a claim identified by ID or Number.", "operationId": "get_damaged_property_asset_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims", "property-damage"], "summary": "Update Damaged Property Asset", "description": "Update a damaged property asset for a claim identified by ID or Number.", "operationId": "update_damaged_property_asset_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__put", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamagedPropertyAssetUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims", "property-damage"], "summary": "Delete Damaged Property Asset", "description": "Delete a damaged property asset for a claim identified by ID or Number.", "operationId": "delete_damaged_property_asset_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances": {"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damage Instances", "description": "Get all damage instances for a damaged property asset.", "operationId": "get_damage_instances_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DamageInstanceResponse"}, "title": "Response Get Damage Instances Api V1 Claims  Claim Identifier  Damaged Property Assets  Asset Id  Damage Instances Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["claims", "claims", "property-damage"], "summary": "Create Damage Instance", "description": "Create a damage instance for a damaged property asset.", "operationId": "create_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageInstanceCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageInstanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances/{instance_id}": {"get": {"tags": ["claims", "claims", "property-damage"], "summary": "Get Damage Instance", "description": "Get a damage instance for a damaged property asset.", "operationId": "get_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageInstanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["claims", "claims", "property-damage"], "summary": "Update Damage Instance", "description": "Update a damage instance for a damaged property asset.", "operationId": "update_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__put", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageInstanceUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageInstanceResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["claims", "claims", "property-damage"], "summary": "Delete Damage Instance", "description": "Delete a damage instance for a damaged property asset.", "operationId": "delete_damage_instance_api_v1_claims__claim_identifier__damaged_property_assets__asset_id__damage_instances__instance_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damaged Property Asset ID", "title": "Asset Id"}, "description": "Damaged Property Asset ID"}, {"name": "instance_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Damage Instance ID", "title": "Instance Id"}, "description": "Damage Instance ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/tasks/": {"post": {"tags": ["claims", "tasks"], "summary": "Create Task for C<PERSON>m", "description": "Create a new task associated with a specific claim. Requires 'CREATE_TASK' permission.", "operationId": "create_task_api_v1_claims__claim_id__tasks__post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID or Number of the claim to associate the task with.", "title": "Claim Identifier"}, "description": "The ID or Number of the claim to associate the task with."}, {"name": "claim_identifier", "in": "query", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskCreateRequestBody"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/customers": {"get": {"tags": ["customers", "customers"], "summary": "List Customers", "description": "List all customers.", "operationId": "list_customers_api_v1_customers_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Active Only"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerResponse"}, "title": "Response List Customers Api V1 Customers Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["customers", "customers"], "summary": "Create Customer", "description": "Create a new customer.", "operationId": "create_customer_api_v1_customers_post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/customers/{customer_id}": {"get": {"tags": ["customers", "customers"], "summary": "Get Customer", "description": "Get a customer by ID.", "operationId": "get_customer_api_v1_customers__customer_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["customers", "customers"], "summary": "Update Customer", "description": "Update a customer.", "operationId": "update_customer_api_v1_customers__customer_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["customers", "customers"], "summary": "Delete Customer", "description": "Delete a customer.", "operationId": "delete_customer_api_v1_customers__customer_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/fnols": {"get": {"tags": ["fnols", "fnols"], "summary": "List Fnols", "description": "List FNOLs with filtering based on user's permissions.", "operationId": "list_fnols_api_v1_fnols_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (customer, claims)", "default": ["customer"], "title": "Include"}, "description": "Include related items (customer, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FNOLResponse"}, "title": "Response List Fnols Api V1 Fnols Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["fnols", "fnols"], "summary": "Create Fnol", "description": "Create a new FNOL.", "operationId": "create_fnol_api_v1_fnols_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/fnols/number/{fnol_number}": {"get": {"tags": ["fnols", "fnols"], "summary": "Get Fnol By Number", "description": "Get FNOL by number.", "operationId": "get_fnol_by_number_api_v1_fnols_number__fnol_number__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_number", "in": "path", "required": true, "schema": {"type": "string", "title": "Fnol Number"}}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (customer, claims)", "default": ["customer", "claims"], "title": "Include"}, "description": "Include related items (customer, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/fnols/{fnol_id}": {"get": {"tags": ["fnols", "fnols"], "summary": "Get Fnol", "description": "Get FNOL by ID.", "operationId": "get_fnol_api_v1_fnols__fnol_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (customer, claims)", "default": ["customer", "claims"], "title": "Include"}, "description": "Include related items (customer, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["fnols", "fnols"], "summary": "Update Fnol", "description": "Update FNOL by ID.", "operationId": "update_fnol_api_v1_fnols__fnol_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["fnols", "fnols"], "summary": "Delete Fnol", "description": "Delete FNOL by ID.", "operationId": "delete_fnol_api_v1_fnols__fnol_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/fnols/{fnol_id}/convert": {"post": {"tags": ["fnols", "fnols", "claims"], "summary": "Convert F<PERSON>l To Claim", "description": "Convert an FNOL to a claim.\n\nTakes an FNOL ID and creates a new claim with the FNOL linked to it.\nReturns a simplified response to avoid circular references.", "operationId": "convert_fnol_to_claim_api_v1_fnols__fnol_id__convert_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Fnol Id"}}, {"name": "claim_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Type of claim to create (case-insensitive)", "title": "Claim Type"}, "description": "Type of claim to create (case-insensitive)"}], "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FNOLConversionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/reserves": {"get": {"tags": ["config", "config"], "summary": "List Reserve Configurations", "description": "List reserve configurations with optional filtering. Permissions are checked at the service layer.", "operationId": "list_reserve_configurations_api_v1_config_reserves_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "title": "Claim Type"}}, {"name": "reserve_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ReserveType"}, {"type": "null"}], "title": "Reserve Type"}}, {"name": "is_required", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Required"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ReserveConfigurationInDB"}, "title": "Response List Reserve Configurations Api V1 Config Reserves Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["config", "config"], "summary": "Create Reserve Configuration", "description": "Create a new reserve configuration. Permissions are checked at the service layer.", "operationId": "create_reserve_configuration_api_v1_config_reserves_post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveConfigurationCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/reserves/{config_id}": {"get": {"tags": ["config", "config"], "summary": "Get Reserve Configuration", "description": "Get a specific reserve configuration. Permissions are checked at the service layer.", "operationId": "get_reserve_configuration_api_v1_config_reserves__config_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["config", "config"], "summary": "Update Reserve Configuration", "description": "Update a reserve configuration. Permissions are checked at the service layer.", "operationId": "update_reserve_configuration_api_v1_config_reserves__config_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveConfigurationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReserveConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete Reserve Configuration", "description": "Delete a reserve configuration. Permissions are checked at the service layer.", "operationId": "delete_reserve_configuration_api_v1_config_reserves__config_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Config Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/customers/{customer_id}/authority": {"get": {"tags": ["config", "config"], "summary": "List Customer Authority Thresholds", "description": "List authority thresholds for a specific customer. Permissions are checked at the service layer.", "operationId": "list_customer_authority_thresholds_api_v1_config_customers__customer_id__authority_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerAuthorityThresholdInDB"}, "title": "Response List Customer Authority Thresholds Api V1 Config Customers  Customer Id  Authority Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["config", "config"], "summary": "Create Customer Authority Threshold", "description": "Create a new authority threshold for a customer. Permissions are checked at the service layer.", "operationId": "create_customer_authority_threshold_api_v1_config_customers__customer_id__authority_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "customer_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Customer Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerAuthorityThresholdCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerAuthorityThresholdInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/authority/{threshold_id}": {"get": {"tags": ["config", "config"], "summary": "Get Customer Authority Threshold", "description": "Get a specific customer authority threshold. Permissions are checked at the service layer.", "operationId": "get_customer_authority_threshold_api_v1_config_authority__threshold_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerAuthorityThresholdInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["config", "config"], "summary": "Update Customer Authority Threshold", "description": "Update a customer authority threshold. Permissions are checked at the service layer.", "operationId": "update_customer_authority_threshold_api_v1_config_authority__threshold_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerAuthorityThresholdUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CustomerAuthorityThresholdInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete Customer Authority Threshold", "description": "Delete a customer authority threshold. Permissions are checked at the service layer.", "operationId": "delete_customer_authority_threshold_api_v1_config_authority__threshold_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/initialize": {"post": {"tags": ["config", "config"], "summary": "Initialize Database", "description": "Initialize the database if not already initialized.\n\nThis endpoint creates all necessary tables and inserts initial data.\nIt will do nothing if the database is already initialized, unless\nthe force parameter is set to true.\n\nThis endpoint does not require authentication to allow initial setup.", "operationId": "initialize_database_api_v1_config_initialize_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "force", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Force reinitialization even if already initialized", "default": false, "title": "Force"}, "description": "Force reinitialization even if already initialized"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "Response Initialize Database Api V1 Config Initialize Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/is-initialized": {"get": {"tags": ["config", "config"], "summary": "Is Database Initialized", "description": "Check if the database has been initialized.\n\nReturns a simple boolean response indicating whether the database has been\ninitialized with the required initial data.\n\nThis endpoint does not require authentication to allow initial status check.", "operationId": "is_database_initialized_api_v1_config_is_initialized_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Response Is Database Initialized Api V1 Config Is Initialized Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/config/system-configs": {"get": {"tags": ["config", "config"], "summary": "List System Configurations", "description": "List all system configurations. Permissions are checked at the service layer.", "operationId": "list_system_configurations_api_v1_config_system_configs_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/SystemConfigurationInDB"}, "type": "array", "title": "Response List System Configurations Api V1 Config System Configs Get"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}, "post": {"tags": ["config", "config"], "summary": "Create System Configuration", "description": "Create a new system configuration. Permissions are checked at the service layer.", "operationId": "create_system_configuration_api_v1_config_system_configs_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemConfigurationCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/v1/config/system-configs/{config_id}": {"patch": {"tags": ["config", "config"], "summary": "Update System Configuration", "description": "Update a system configuration. Permissions are checked at the service layer.", "operationId": "update_system_configuration_api_v1_config_system_configs__config_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Config Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemConfigurationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete System Configuration", "description": "Delete a system configuration.", "operationId": "delete_system_configuration_api_v1_config_system_configs__config_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "config_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Config Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/config/system-configs/{key}": {"get": {"tags": ["config", "config"], "summary": "Get System Configuration By Key", "description": "Get a system configuration by key. Permissions are checked at the service layer.", "operationId": "get_system_configuration_by_key_api_v1_config_system_configs__key__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string", "title": "Key"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemConfigurationInDB"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/metrics/dashboard": {"get": {"tags": ["metrics", "metrics"], "summary": "Get Dashboard Metrics", "description": "Get dashboard metrics.\n\nRetrieves key metrics for the dashboard, such as:\n- Total claims\n- Open claims\n- New claims in period\n- Closed claims in period\n- Average claim lifecycle\n- Total payments\n- Total reserves\n- Tasks pending\n- Tasks overdue\n- FNOLs pending\n\nThe metrics can be filtered by customer_id or user_id, and compared\nwith the previous period of equal length.", "operationId": "get_dashboard_metrics_api_v1_metrics_dashboard_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"type": "string", "description": "Time period for metrics", "default": "last_30_days", "title": "Period"}, "description": "Time period for metrics"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": true, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DashboardMetricsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/claims-kpis": {"get": {"tags": ["reports", "reports"], "summary": "Get Claims Kpi Report", "description": "Get Claims KPI Report.\n\nRetrieves key performance indicators for claims, including:\n- Total claims\n- Open claims percentage\n- Closed claims percentage\n- Average resolution time\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_claims_kpi_report_api_v1_reports_claims_kpis_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": false, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseClaimsKpis"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/claims-by-type": {"get": {"tags": ["reports", "reports"], "summary": "Get Claims By Type Report", "description": "Get Claims by Type Report.\n\nBreaks down claims by their type (AUTO, PROPERTY, GENERAL_LIABILITY, etc.)\nand provides count and percentage for each type.\n\nThe report can be filtered by customer_id or user_id.", "operationId": "get_claims_by_type_report_api_v1_reports_claims_by_type_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseClaimsByType"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/claims-by-status": {"get": {"tags": ["reports", "reports"], "summary": "Get Claims By Status Report", "description": "Get Claims by Status Report.\n\nBreaks down claims by their status (Open, Closed) and provides count\nand percentage for each status.\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_claims_by_status_report_api_v1_reports_claims_by_status_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseClaimsByStatus"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/claims-over-time": {"get": {"tags": ["reports", "reports"], "summary": "Get Claims Over Time Report", "description": "Get Claims Over Time Report.\n\nTracks new and closed claims over time periods (weekly or monthly).\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_claims_over_time_report_api_v1_reports_claims_over_time_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseClaimsOverTime"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/financial-kpis": {"get": {"tags": ["reports", "reports"], "summary": "Get Financial Kpi Report", "description": "Get Financial KPI Report.\n\nRetrieves key financial performance indicators, including:\n- Total reserves\n- Total payments\n- Average reserve per claim\n- Average payment per claim\n\nThe report can be filtered by customer_id, claim_type, or user_id,\nand compared with the previous period.", "operationId": "get_financial_kpi_report_api_v1_reports_financial_kpis_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": false, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseFinancialKpis"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/payments-vs-reserves": {"get": {"tags": ["reports", "reports"], "summary": "Get Payments Vs Reserves Report", "description": "Get Payments vs Reserves Report.\n\nCompares payments and reserves over time periods (weekly or monthly).\n\nThe report can be filtered by customer_id, claim_type, or user_id.", "operationId": "get_payments_vs_reserves_report_api_v1_reports_payments_vs_reserves_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponsePaymentsVsReserves"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/adjuster-performance": {"get": {"tags": ["reports", "reports"], "summary": "Get Adjuster Performance Report", "description": "Get Adjuster Performance Report.\n\nProvides performance metrics for adjusters, including:\n- Claims handled\n- Average resolution time\n- Total payments authorized\n- Pending tasks\n- Overdue tasks\n\nThe report can be filtered by customer_id or claim_type.", "operationId": "get_adjuster_performance_report_api_v1_reports_adjuster_performance_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportResponseAdjusterPerformance"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/reports/{report_name}": {"get": {"tags": ["reports", "reports"], "summary": "Get Report By Name", "description": "Get Report by Name.\n\nGeneric endpoint to retrieve any report by its name.\n\nFor most use cases, you should use the specific report endpoints\ninstead of this generic one.", "operationId": "get_report_by_name_api_v1_reports__report_name__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "report_name", "in": "path", "required": true, "schema": {"type": "string", "description": "Name of the report", "title": "Report Name"}, "description": "Name of the report"}, {"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": false, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "customer_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer ID", "title": "Customer Id"}, "description": "Filter by customer ID"}, {"name": "claim_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/ClaimType"}, {"type": "null"}], "description": "Filter by claim type", "title": "Claim Type"}, "description": "Filter by claim type"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Response Get Report By Name Api V1 Reports  Report Name  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_identifier}/notes": {"post": {"tags": ["notes", "notes"], "summary": "Create Note For Claim", "description": "Create a new note associated with a specific claim (identified by UUID or Number).", "operationId": "create_note_for_claim_api_v1_claims__claim_identifier__notes_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer", "bodily_injury_details", "premises_details", "gl_details"], "title": "Includes"}, "description": "Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteCreateRequestBody"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/claims/{claim_id}/notes": {"get": {"tags": ["notes", "notes"], "summary": "List Notes For Claim", "description": "List all notes associated with a specific claim.", "operationId": "list_notes_for_claim_api_v1_claims__claim_id__notes_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/NoteResponse"}, "title": "Response List Notes For Claim Api V1 Claims  Claim Id  Notes Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/notes/{note_id}": {"get": {"tags": ["notes", "notes"], "summary": "Get Note", "description": "Retrieve a specific note by its ID.", "operationId": "get_note_api_v1_notes__note_id__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["notes", "notes"], "summary": "Update Note", "description": "Update the content of a specific note.", "operationId": "update_note_api_v1_notes__note_id__patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NoteResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["notes", "notes"], "summary": "Delete Note", "description": "Delete a specific note.", "operationId": "delete_note_api_v1_notes__note_id__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "note_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Note Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/": {"get": {"tags": ["tasks"], "summary": "List Tasks", "description": "Get a list of tasks, optionally filtered. Requires 'LIST_TASKS' permission.", "operationId": "list_tasks_api_v1_tasks__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter tasks by claim ID", "title": "<PERSON><PERSON><PERSON>d"}, "description": "Filter tasks by claim ID"}, {"name": "assigned_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter tasks assigned to a specific user ID", "title": "Assigned To"}, "description": "Filter tasks assigned to a specific user ID"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TaskStatus"}, {"type": "null"}], "description": "Filter tasks by status", "title": "Status"}, "description": "Filter tasks by status"}, {"name": "priority", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TaskPriority"}, {"type": "null"}], "description": "Filter tasks by priority", "title": "Priority"}, "description": "Filter tasks by priority"}, {"name": "title", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter tasks by partial title match (case-insensitive)", "title": "Title"}, "description": "Filter tasks by partial title match (case-insensitive)"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedTaskResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_identifier}": {"get": {"tags": ["tasks"], "summary": "Get Task", "description": "Get details of a specific task by its UUID or HR ID. Requires 'VIEW_TASKS' permission.", "operationId": "get_task_api_v1_tasks__task_identifier__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["tasks"], "summary": "Update Task", "description": "Update details of an existing task. Requires 'UPDATE_TASKS' permission.", "operationId": "update_task_api_v1_tasks__task_identifier__put", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["tasks"], "summary": "Delete Task", "description": "Delete a task by its UUID or HR ID. Requires 'DELETE_TASK' permission.", "operationId": "delete_task_api_v1_tasks__task_identifier__delete", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_identifier}/assign": {"patch": {"tags": ["tasks"], "summary": "Assign Task", "description": "Assign or unassign a task to a specific user. Requires 'ASSIGN_TASK' permission.", "operationId": "assign_task_api_v1_tasks__task_identifier__assign_patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskAssignSchema"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/tasks/{task_identifier}/status": {"patch": {"tags": ["tasks"], "summary": "Change Task Status", "description": "Change the status of a task. Requires 'CHANGE_TASK_STATUS' permission.", "operationId": "change_task_status_api_v1_tasks__task_identifier__status_patch", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "task_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "UUID or HR_ID of the task", "title": "Task Identifier"}, "description": "UUID or HR_ID of the task"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskStatusSchema"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TaskRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/documents": {"get": {"tags": ["documents", "documents"], "summary": "List All Documents", "description": "List all documents irrespective of their associated claims.\n\nThis endpoint returns the latest N documents across all claims in the system.\n\nArgs:\n    skip: Number of records to skip (for pagination)\n    limit: Maximum number of records to return (for pagination)\n    document_type: Optional filter by document type\n    document_service: Document service\n\nReturns:\n    A list of documents with metadata including their associated claim IDs", "operationId": "list_all_documents_api_v1_documents_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "Number of records to skip", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of records to skip"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "description": "Maximum number of records to return", "default": 100, "title": "Limit"}, "description": "Maximum number of records to return"}, {"name": "document_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentType"}, {"type": "null"}], "description": "Filter by document type", "title": "Document Type"}, "description": "Filter by document type"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DocumentList"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/{path}": {"options": {"summary": "Options Handler", "description": "Global OPTIONS request handler for any path.\n\nThis is a fallback to handle OPTIONS requests for any path\nthat doesn't have a specific OPTIONS handler.\nCORS middleware will add appropriate headers.", "operationId": "options_handler_api_v1__path__options", "parameters": [{"name": "path", "in": "path", "required": true, "schema": {"type": "string", "title": "Path"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"AdjusterPerformanceItem": {"properties": {"user_id": {"type": "string", "title": "User Id", "description": "User ID of the adjuster"}, "user_name": {"type": "string", "title": "User Name", "description": "Name of the adjuster"}, "claims_handled": {"type": "integer", "title": "Claims Handled", "description": "Number of claims handled by this adjuster"}, "avg_resolution_time": {"type": "number", "title": "Avg Resolution Time", "description": "Average resolution time in days"}, "total_payments": {"type": "string", "title": "Total Payments", "description": "Total payments authorized"}, "pending_tasks": {"type": "integer", "title": "Pending Tasks", "description": "Number of pending tasks"}, "completed_tasks": {"type": "integer", "title": "Completed Tasks", "description": "Number of completed tasks"}}, "type": "object", "required": ["user_id", "user_name", "claims_handled", "avg_resolution_time", "total_payments", "pending_tasks", "completed_tasks"], "title": "AdjusterPerformanceItem", "description": "Item in Adjuster Performance report."}, "AttorneyCreate": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the attorney"}, "attorney_type": {"$ref": "#/components/schemas/AttorneyType", "description": "Type of attorney (PLAINTIFF, DEFENSE, etc.)"}, "firm_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Firm Name", "description": "Name of the law firm"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the attorney"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the attorney"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the attorney or law firm"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes about the attorney"}}, "type": "object", "required": ["name", "attorney_type"], "title": "AttorneyCreate", "description": "<PERSON><PERSON><PERSON> for creating a new attorney."}, "AttorneyResponse": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the attorney"}, "attorney_type": {"$ref": "#/components/schemas/AttorneyType", "description": "Type of attorney (PLAINTIFF, DEFENSE, etc.)"}, "firm_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Firm Name", "description": "Name of the law firm"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the attorney"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the attorney"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the attorney or law firm"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes about the attorney"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this attorney"}}, "type": "object", "required": ["name", "attorney_type", "id", "claim_id", "created_at", "updated_at"], "title": "AttorneyResponse", "description": "Schema for attorney response data."}, "AttorneyType": {"type": "string", "enum": ["PLAINTIFF", "DEFENSE", "COVERAGE", "MONITORING", "OTHER"], "title": "AttorneyType", "description": "Types of attorneys associated with claims."}, "AttorneyUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Full name of the attorney"}, "attorney_type": {"anyOf": [{"$ref": "#/components/schemas/AttorneyType"}, {"type": "null"}], "description": "Type of attorney (PLAINTIFF, DEFENSE, etc.)"}, "firm_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Firm Name", "description": "Name of the law firm"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the attorney"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the attorney"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the attorney or law firm"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes about the attorney"}}, "type": "object", "title": "AttorneyUpdate", "description": "<PERSON><PERSON><PERSON> for updating an existing attorney."}, "AuditSummaryResponse": {"properties": {"total_entries": {"type": "integer", "title": "Total Entries", "description": "Total number of audit entries"}, "by_entity": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By Entity", "description": "Count of entries by entity type"}, "by_change_type": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By Change Type", "description": "Count of entries by change type"}, "by_user": {"additionalProperties": {"type": "integer"}, "type": "object", "title": "By User", "description": "Count of entries by user"}, "recent_activity": {"items": {"$ref": "#/components/schemas/AuditTrailResponse"}, "type": "array", "title": "Recent Activity", "description": "Most recent audit entries", "default": []}}, "type": "object", "required": ["total_entries", "by_entity", "by_change_type", "by_user"], "title": "AuditSummaryResponse", "description": "Schema for audit summary response."}, "AuditTrailCreate": {"properties": {"entity_type": {"$ref": "#/components/schemas/EntityType", "description": "Type of entity being audited"}, "entity_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Entity Id", "description": "ID of the specific entity if applicable"}, "change_type": {"$ref": "#/components/schemas/ChangeType", "description": "Type of change (CREATE, UPDATE, DELETE)"}, "field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name", "description": "Name of the field that changed"}, "previous_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Previous Value", "description": "Previous value of the field"}, "new_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "New Value", "description": "New value of the field"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Human-readable description of the change"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim this audit entry belongs to"}, "changed_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Changed By Id", "description": "ID of the user who made the change"}}, "type": "object", "required": ["entity_type", "change_type", "claim_id"], "title": "AuditTrailCreate", "description": "Schema for creating a new audit trail entry."}, "AuditTrailResponse": {"properties": {"entity_type": {"$ref": "#/components/schemas/EntityType", "description": "Type of entity being audited"}, "entity_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Entity Id", "description": "ID of the specific entity if applicable"}, "change_type": {"$ref": "#/components/schemas/ChangeType", "description": "Type of change (CREATE, UPDATE, DELETE)"}, "field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name", "description": "Name of the field that changed"}, "previous_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Previous Value", "description": "Previous value of the field"}, "new_value": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "New Value", "description": "New value of the field"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Human-readable description of the change"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "changed_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Changed By Id"}, "changed_at": {"type": "string", "format": "date-time", "title": "Changed At"}}, "type": "object", "required": ["entity_type", "change_type", "id", "claim_id", "changed_at"], "title": "AuditTrailResponse", "description": "Schema for audit trail response data."}, "AuthorityRole": {"type": "string", "enum": ["NO_AUTHORITY", "BASIC", "INTERMEDIATE", "SENIOR", "SUPERVISOR", "MANAGER", "UNLIMITED"], "title": "AuthorityRole", "description": "Authority roles for financial operations."}, "AutoClaimResponse": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "claim_number": {"type": "string", "title": "Claim Number"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "AUTO"}, "status": {"$ref": "#/components/schemas/ClaimStatus"}, "created_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "customer": {"anyOf": [{"$ref": "#/components/schemas/CustomerResponse"}, {"type": "null"}]}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentSchema"}, "type": "array"}, {"type": "null"}], "title": "Documents"}, "notes": {"anyOf": [{"items": {"$ref": "#/components/schemas/NoteSchema"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "tasks": {"anyOf": [{"items": {"$ref": "#/components/schemas/TaskSchema"}, "type": "array"}, {"type": "null"}], "title": "Tasks"}, "status_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/StatusHistorySchema"}, "type": "array"}, {"type": "null"}], "title": "Status History"}, "financials": {"anyOf": [{"$ref": "#/components/schemas/ClaimFinancialsInDB"}, {"type": "null"}]}, "witnesses": {"anyOf": [{"items": {"$ref": "#/components/schemas/WitnessResponse"}, "type": "array"}, {"type": "null"}], "title": "Witnesses"}, "attorneys": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttorneyResponse"}, "type": "array"}, {"type": "null"}], "title": "Attorneys"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "supervisor": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "auto_details": {"anyOf": [{"$ref": "#/components/schemas/AutoDetailsResponse"}, {"type": "null"}]}}, "type": "object", "required": ["id", "customer_id", "claim_number", "status", "created_at", "updated_at"], "title": "AutoClaimResponse", "description": "Schema for auto claims in responses."}, "AutoClaimUpdate": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "AUTO"}, "status": {"anyOf": [{"$ref": "#/components/schemas/ClaimStatus"}, {"type": "null"}], "description": "Current status of the claim"}, "auto_details": {"anyOf": [{"$ref": "#/components/schemas/AutoDetailsUpdate"}, {"type": "null"}]}}, "type": "object", "title": "AutoClaimUpdate", "description": "Schema for updating auto claims."}, "AutoDetailsResponse": {"properties": {"vehicle_year": {"anyOf": [{"type": "integer", "maximum": 2026.0, "minimum": 1900.0}, {"type": "null"}], "title": "Vehicle Year", "description": "Manufacturing year of the vehicle"}, "vehicle_make": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Make", "description": "Make/manufacturer of the vehicle"}, "vehicle_model": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Model", "description": "Model of the vehicle"}, "vehicle_vin": {"anyOf": [{"type": "string", "maxLength": 17, "minLength": 17}, {"type": "null"}], "title": "Vehicle Vin", "description": "Vehicle Identification Number (17 characters)"}, "vehicle_license_plate": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Vehicle License Plate", "description": "License plate number of the vehicle"}, "vehicle_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the vehicle is registered"}, "driver_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Driver Name", "description": "Full name of the driver at the time of incident"}, "driver_license_number": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver License Number", "description": "Driver's license number"}, "driver_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State that issued the driver's license"}, "driver_relationship": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver Relationship", "description": "Relationship of driver to the insured (e.g., OWNER, FAMILY_MEMBER, FRIEND)"}, "point_of_impact": {"anyOf": [{"$ref": "#/components/schemas/PointOfImpact"}, {"type": "null"}], "description": "Area of the vehicle that was impacted (e.g., FRONT, REAR, LEFT, RIGHT)"}, "airbags_deployed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Airbags Deployed", "description": "Whether any airbags deployed during the incident"}, "vehicle_driveable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Vehicle Driveable", "description": "Whether the vehicle was driveable after the incident"}, "towed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Towed", "description": "Whether the vehicle was towed from the scene"}, "tow_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Tow Location", "description": "Location where the vehicle was towed to"}, "incident_type": {"anyOf": [{"$ref": "#/components/schemas/AutoIncidentType"}, {"type": "null"}], "description": "Type of auto incident (COLLIS<PERSON>, COMPREHENSIVE)"}, "collision_type": {"anyOf": [{"$ref": "#/components/schemas/CollisionType"}, {"type": "null"}], "description": "Type of collision if incident_type is COLLISION"}, "passenger_count": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Passenger Count", "description": "Number of passengers in the vehicle at time of incident"}, "passenger_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Passenger Details", "description": "Details about passengers including names, ages, positions, etc."}, "cargo_theft": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Cargo Theft", "description": "Whether cargo theft occurred during the incident"}, "cargo_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cargo Description", "description": "Description of stolen cargo, if applicable"}, "has_property_damage": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Has Property Damage", "description": "Whether there was property damage in the incident"}, "property_damage": {"anyOf": [{"$ref": "#/components/schemas/AutoPropertyDamageResponse"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "bodily_injury_details": {"anyOf": [{"$ref": "#/components/schemas/BodilyInjuryDetailsResponse"}, {"type": "null"}]}, "injured_persons": {"anyOf": [{"items": {"$ref": "#/components/schemas/InjuredPersonResponse"}, "type": "array"}, {"type": "null"}], "title": "Injured Persons"}, "damaged_property_assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}, "type": "array"}, {"type": "null"}], "title": "Damaged Property Assets"}}, "type": "object", "required": ["id", "claim_id", "created_at", "updated_at"], "title": "AutoDetailsResponse", "description": "Schema for auto details in responses."}, "AutoDetailsUpdate": {"properties": {"vehicle_year": {"anyOf": [{"type": "integer", "maximum": 2026.0, "minimum": 1900.0}, {"type": "null"}], "title": "Vehicle Year", "description": "Manufacturing year of the vehicle"}, "vehicle_make": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Make", "description": "Make/manufacturer of the vehicle"}, "vehicle_model": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Vehicle Model", "description": "Model of the vehicle"}, "vehicle_vin": {"anyOf": [{"type": "string", "maxLength": 17, "minLength": 17}, {"type": "null"}], "title": "Vehicle Vin", "description": "Vehicle Identification Number (17 characters)"}, "vehicle_license_plate": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Vehicle License Plate", "description": "License plate number of the vehicle"}, "vehicle_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the vehicle is registered"}, "driver_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Driver Name", "description": "Full name of the driver at the time of incident"}, "driver_license_number": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver License Number", "description": "Driver's license number"}, "driver_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State that issued the driver's license"}, "driver_relationship": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Driver Relationship", "description": "Relationship of driver to the insured (e.g., OWNER, FAMILY_MEMBER, FRIEND)"}, "point_of_impact": {"anyOf": [{"$ref": "#/components/schemas/PointOfImpact"}, {"type": "null"}], "description": "Area of the vehicle that was impacted (e.g., FRONT, REAR, LEFT, RIGHT)"}, "airbags_deployed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Airbags Deployed", "description": "Whether any airbags deployed during the incident"}, "vehicle_driveable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Vehicle Driveable", "description": "Whether the vehicle was driveable after the incident"}, "towed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Towed", "description": "Whether the vehicle was towed from the scene"}, "tow_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Tow Location", "description": "Location where the vehicle was towed to"}, "incident_type": {"anyOf": [{"$ref": "#/components/schemas/AutoIncidentType"}, {"type": "null"}], "description": "Type of auto incident (COLLIS<PERSON>, COMPREHENSIVE)"}, "collision_type": {"anyOf": [{"$ref": "#/components/schemas/CollisionType"}, {"type": "null"}], "description": "Type of collision if incident_type is COLLISION"}, "passenger_count": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Passenger Count", "description": "Number of passengers in the vehicle at time of incident"}, "passenger_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Passenger Details", "description": "Details about passengers including names, ages, positions, etc."}, "cargo_theft": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Cargo Theft", "description": "Whether cargo theft occurred during the incident"}, "cargo_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cargo Description", "description": "Description of stolen cargo, if applicable"}, "has_property_damage": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Has Property Damage", "description": "Whether there was property damage in the incident"}, "property_damage": {"anyOf": [{"$ref": "#/components/schemas/AutoPropertyDamageUpdate"}, {"type": "null"}]}}, "type": "object", "title": "AutoDetailsUpdate", "description": "Schema for updating auto details."}, "AutoIncidentType": {"type": "string", "enum": ["COLLISION", "COMPREHENSIVE", "THEFT", "VANDALISM", "FIRE", "FLOOD", "HAIL", "FALLEN_OBJECT", "ANIMAL_COLLISION", "CARGO_LOSS", "CARGO_THEFT", "CARGO_DAMAGE", "TRAILER_DAMAGE", "EQUIPMENT_DAMAGE", "THIRD_PARTY_INJURY", "THIRD_PARTY_PROPERTY_DAMAGE", "MECHANICAL_BREAKDOWN", "OTHER"], "title": "AutoIncidentType", "description": "Type of auto incident."}, "AutoPropertyDamageResponse": {"properties": {"damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Description of the property damage"}, "property_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Property Type", "description": "Type of property damaged"}, "property_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Property Address", "description": "Address of the damaged property"}, "property_owner": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Property Owner", "description": "Owner of the damaged property"}, "estimated_damage_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Damage Value", "description": "Estimated value of the property damage"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "auto_details_id": {"type": "string", "format": "uuid", "title": "Auto Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "auto_details_id", "created_at", "updated_at"], "title": "AutoPropertyDamageResponse", "description": "Schema for auto property damage in responses."}, "AutoPropertyDamageUpdate": {"properties": {"damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Description of the property damage"}, "property_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Property Type", "description": "Type of property damaged"}, "property_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Property Address", "description": "Address of the damaged property"}, "property_owner": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Property Owner", "description": "Owner of the damaged property"}, "estimated_damage_value": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}, {"type": "null"}], "title": "Estimated Damage Value", "description": "Estimated value of the property damage"}}, "type": "object", "title": "AutoPropertyDamageUpdate", "description": "<PERSON><PERSON>a for updating auto property damage."}, "BodilyInjuryDetailsResponse": {"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury occurrence"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the injury occurred"}, "injured_person_type": {"anyOf": [{"$ref": "#/components/schemas/InjuredPersonType"}, {"type": "null"}], "description": "Type of injured person"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "incident_report_status": {"anyOf": [{"$ref": "#/components/schemas/IncidentReportStatus"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "#/components/schemas/MedicalTreatmentRequirements"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "#/components/schemas/InsuranceBillingStatus"}, {"type": "null"}], "description": "Status of insurance billing"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "created_at", "updated_at"], "title": "BodilyInjuryDetailsResponse", "description": "Schema for bodily injury details in responses."}, "BodilyInjuryDetailsUpdate": {"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury occurrence"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the injury occurred"}, "injured_person_type": {"anyOf": [{"$ref": "#/components/schemas/InjuredPersonType"}, {"type": "null"}], "description": "Type of injured person"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "incident_report_status": {"anyOf": [{"$ref": "#/components/schemas/IncidentReportStatus"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "#/components/schemas/MedicalTreatmentRequirements"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "#/components/schemas/InsuranceBillingStatus"}, {"type": "null"}], "description": "Status of insurance billing"}}, "type": "object", "title": "BodilyInjuryDetailsUpdate", "description": "<PERSON><PERSON><PERSON> for updating bodily injury details."}, "Body_direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File", "description": "File to upload"}, "document_type": {"$ref": "#/components/schemas/DocumentType", "description": "Document type"}, "document_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Name", "description": "Document display name"}, "document_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Document Description", "description": "Document description"}}, "type": "object", "required": ["file", "document_type"], "title": "Body_direct_upload_document_api_v1_claims__claim_id__documents_direct_upload_post"}, "Body_login_api_v1_auth_token_post": {"properties": {"remember_me": {"type": "boolean", "title": "Remember Me", "default": false}, "grant_type": {"anyOf": [{"type": "string", "pattern": "password"}, {"type": "null"}], "title": "Grant Type"}, "username": {"type": "string", "title": "Username"}, "password": {"type": "string", "title": "Password"}, "scope": {"type": "string", "title": "<PERSON><PERSON>", "default": ""}, "client_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Id"}, "client_secret": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Client Secret"}}, "type": "object", "required": ["username", "password"], "title": "Body_login_api_v1_auth_token_post"}, "ChangeType": {"type": "string", "enum": ["CREATE", "UPDATE", "DELETE"], "title": "ChangeType", "description": "Types of changes tracked in the audit system."}, "ClaimFinancialsCreate": {"properties": {"estimated_value": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Estimated Value", "description": "Estimated total value of the claim"}, "indemnity_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Indemnity Paid", "description": "Amount paid for indemnity"}, "expense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Expense <PERSON>", "description": "Amount paid for expenses"}, "defense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Defense Paid", "description": "Amount paid for defense costs"}, "recovery_expected": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Expected", "description": "Expected recovery amount"}, "recovery_received": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Received", "description": "Received recovery amount"}, "deductible_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Deductible Amount", "description": "Deductible amount"}, "coverage_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Coverage Limit", "description": "Coverage limit"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code", "default": "USD"}, "reserves": {"items": {"$ref": "#/components/schemas/ReserveCreate"}, "type": "array", "title": "Reserves", "description": "Initial reserves"}}, "type": "object", "required": ["estimated_value", "reserves"], "title": "ClaimFinancialsCreate", "description": "Schema for creating claim financials."}, "ClaimFinancialsInDB": {"properties": {"estimated_value": {"type": "string", "title": "Estimated Value", "description": "Estimated total value of the claim"}, "indemnity_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Indemnity Paid", "description": "Amount paid for indemnity"}, "expense_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expense <PERSON>", "description": "Amount paid for expenses"}, "defense_paid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defense Paid", "description": "Amount paid for defense costs"}, "recovery_expected": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recovery Expected", "description": "Expected recovery amount"}, "recovery_received": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recovery Received", "description": "Received recovery amount"}, "deductible_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deductible Amount", "description": "Deductible amount"}, "coverage_limit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Coverage Limit", "description": "Coverage limit"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code", "default": "USD"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Associated claim ID"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Associated claim number"}, "reserves": {"items": {"$ref": "#/components/schemas/ReserveResponse"}, "type": "array", "title": "Reserves", "description": "Current reserves"}, "last_reserve_change": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Reserve Change", "description": "Last reserve change date"}, "last_payment_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Payment Date", "description": "Last payment date"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["estimated_value", "id", "claim_id", "claim_number", "reserves", "created_at", "updated_at"], "title": "ClaimFinancialsInDB", "description": "Schema for claim financials in database."}, "ClaimFinancialsUpdate": {"properties": {"estimated_value": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated total value of the claim"}, "indemnity_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Indemnity Paid", "description": "Amount paid for indemnity"}, "expense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Expense <PERSON>", "description": "Amount paid for expenses"}, "defense_paid": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Defense Paid", "description": "Amount paid for defense costs"}, "recovery_expected": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Expected", "description": "Expected recovery amount"}, "recovery_received": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Recovery Received", "description": "Received recovery amount"}, "deductible_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Deductible Amount", "description": "Deductible amount"}, "coverage_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Coverage Limit", "description": "Coverage limit"}, "currency": {"type": "string", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Currency code", "default": "USD"}, "reserves": {"anyOf": [{"items": {"$ref": "#/components/schemas/ReserveUpdate"}, "type": "array"}, {"type": "null"}], "title": "Reserves", "description": "Updated reserves"}}, "type": "object", "title": "ClaimFinancialsUpdate", "description": "Schema for updating claim financials."}, "ClaimResponseSchema": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "claim_number": {"type": "string", "title": "Claim Number"}, "type": {"$ref": "#/components/schemas/ClaimType"}, "status": {"$ref": "#/components/schemas/ClaimStatus"}, "created_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "customer": {"anyOf": [{"$ref": "#/components/schemas/CustomerResponse"}, {"type": "null"}]}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentSchema"}, "type": "array"}, {"type": "null"}], "title": "Documents"}, "notes": {"anyOf": [{"items": {"$ref": "#/components/schemas/NoteSchema"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "tasks": {"anyOf": [{"items": {"$ref": "#/components/schemas/TaskSchema"}, "type": "array"}, {"type": "null"}], "title": "Tasks"}, "status_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/StatusHistorySchema"}, "type": "array"}, {"type": "null"}], "title": "Status History"}, "financials": {"anyOf": [{"$ref": "#/components/schemas/ClaimFinancialsInDB"}, {"type": "null"}]}, "witnesses": {"anyOf": [{"items": {"$ref": "#/components/schemas/WitnessResponse"}, "type": "array"}, {"type": "null"}], "title": "Witnesses"}, "attorneys": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttorneyResponse"}, "type": "array"}, {"type": "null"}], "title": "Attorneys"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "supervisor": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}}, "type": "object", "required": ["id", "customer_id", "claim_number", "type", "status", "created_at", "updated_at"], "title": "ClaimResponseSchema", "description": "Schema for claim responses."}, "ClaimStatus": {"type": "string", "enum": ["INITIAL_REVIEW", "COVERAGE_EVALUATION", "INVESTIGATION", "DAMAGES", "NEGOTIATION_SETTLEMENT", "LITIGATION", "SUBROGATION", "CLOSURE", "REOPEN"], "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Status of a claim."}, "ClaimType": {"type": "string", "enum": ["AUTO", "PROPERTY", "GENERAL_LIABILITY"], "title": "ClaimType", "description": "Types of claims."}, "ClaimsByStatusItem": {"properties": {"status": {"type": "string", "title": "Status", "description": "Status of claim"}, "count": {"type": "integer", "title": "Count", "description": "Number of claims with this status"}, "percentage": {"type": "number", "title": "Percentage", "description": "Percentage of claims with this status"}, "color": {"type": "string", "title": "Color", "description": "Color for consistent chart rendering"}}, "type": "object", "required": ["status", "count", "percentage", "color"], "title": "ClaimsByStatusItem", "description": "Item in Claims by Status report."}, "ClaimsByTypeItem": {"properties": {"claim_type": {"type": "string", "title": "Claim Type", "description": "Type of claim"}, "count": {"type": "integer", "title": "Count", "description": "Number of claims of this type"}, "percentage": {"type": "number", "title": "Percentage", "description": "Percentage of claims of this type"}, "color": {"type": "string", "title": "Color", "description": "Color for consistent chart rendering"}}, "type": "object", "required": ["claim_type", "count", "percentage", "color"], "title": "ClaimsByTypeItem", "description": "Item in Claims by Type report."}, "ClaimsKpiData": {"properties": {"total_claims": {"type": "integer", "title": "Total Claims", "description": "Total number of claims"}, "total_claims_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in total claims compared to previous period"}, "open_claims": {"type": "integer", "title": "Open Claims", "description": "Number of open claims"}, "open_claims_percentage": {"type": "number", "title": "Open Claims Percentage", "description": "Percentage of open claims out of all claims"}, "closed_claims": {"type": "integer", "title": "Closed Claims", "description": "Number of closed claims"}, "closed_claims_percentage": {"type": "number", "title": "Closed Claims Percentage", "description": "Percentage of closed claims out of all claims"}, "avg_resolution_time_days": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Avg Resolution Time Days", "description": "Average time to close claims in days"}, "avg_resolution_time_days_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in average resolution time compared to previous period"}}, "type": "object", "required": ["total_claims", "open_claims", "open_claims_percentage", "closed_claims", "closed_claims_percentage"], "title": "ClaimsKpiData", "description": "Data for Claims KPI report."}, "ClaimsOverTimeItem": {"properties": {"period_start": {"type": "string", "format": "date", "title": "Period Start", "description": "Start date of the period"}, "new_claims_count": {"type": "integer", "title": "New Claims Count", "description": "Number of new claims in this period"}, "closed_claims_count": {"type": "integer", "title": "Closed Claims Count", "description": "Number of closed claims in this period"}}, "type": "object", "required": ["period_start", "new_claims_count", "closed_claims_count"], "title": "ClaimsOverTimeItem", "description": "Item in Claims Over Time report."}, "CloseClaimSchema": {"properties": {"status": {"$ref": "#/components/schemas/ClaimStatus", "description": "Closing status (CLOSURE)"}}, "type": "object", "required": ["status"], "title": "CloseClaimSchema", "description": "Schema for closing a claim."}, "CollisionType": {"type": "string", "enum": ["REAR_END", "TURNING", "HEAD_ON", "BACKING", "CONTROLLED_INTERSECTION", "NON_CONTROLLED_INTERSECTION", "SIDESWIPE", "LANE_CHANGE", "OTHER"], "title": "CollisionType", "description": "Type of collision."}, "CommunicationPreference": {"type": "string", "enum": ["EMAIL", "PHONE", "TEXT", "MAIL", "PORTAL", "NO_PREFERENCE"], "title": "CommunicationPreference", "description": "Preferred method of communication for responses."}, "CompletedOperationsDetailsResponse": {"properties": {"work_type": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Work Type", "description": "Type of work completed"}, "completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completion Date", "description": "Date work was completed"}, "customer_acceptance_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Customer Acceptance Date", "description": "Date customer accepted work"}, "contract_status": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contract Status", "description": "Status of contract"}, "contract_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contract Details", "description": "Details of the contract"}, "defect_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Defect Description", "description": "Description of the alleged defect in work"}, "problem_detection_timeline": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Problem Detection Timeline", "description": "Timeline of problem detection"}, "repair_attempts": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Attempts", "description": "Details of repair attempts made"}, "repair_descriptions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Descriptions", "description": "Descriptions of repairs performed"}, "applicable_warranties": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Applicable Warranties", "description": "Applicable warranties for the work"}, "subcontractor_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Subcontractor Involved", "description": "Whether subcontractors were involved"}, "subcontractor_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Subcontractor Details", "description": "Details about subcontractors"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "CompletedOperationsDetailsResponse", "description": "Schema for completed operations details in responses."}, "CustomerAuthorityThresholdCreate": {"properties": {"authority_role": {"$ref": "#/components/schemas/AuthorityRole"}, "reserve_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Reserve Limit"}, "payment_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}}, "type": "object", "required": ["authority_role", "reserve_limit", "payment_limit", "customer_id"], "title": "CustomerAuthorityThresholdCreate", "description": "Schema for creating customer-specific authority thresholds."}, "CustomerAuthorityThresholdInDB": {"properties": {"authority_role": {"$ref": "#/components/schemas/AuthorityRole"}, "reserve_limit": {"type": "string", "title": "Reserve Limit"}, "payment_limit": {"type": "string", "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}}, "type": "object", "required": ["authority_role", "reserve_limit", "payment_limit", "id", "customer_id", "created_at", "updated_at", "is_deleted"], "title": "CustomerAuthorityThresholdInDB", "description": "Schema for customer-specific authority thresholds in database."}, "CustomerAuthorityThresholdUpdate": {"properties": {"reserve_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Reserve Limit"}, "payment_limit": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "CustomerAuthorityThresholdUpdate", "description": "Schema for updating customer-specific authority thresholds."}, "CustomerCreate": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the customer/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the customer is active in the system", "default": true}}, "type": "object", "required": ["name", "prefix"], "title": "CustomerCreate", "description": "Create customer schema."}, "CustomerResponse": {"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the customer/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the customer is active in the system", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the customer"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the customer was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the customer was last updated"}}, "type": "object", "required": ["name", "prefix", "id", "created_at", "updated_at"], "title": "CustomerResponse", "description": "Customer response schema."}, "CustomerUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the customer/insurance company"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the customer"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Whether the customer is active in the system"}}, "type": "object", "title": "CustomerUpdate", "description": "Update customer schema."}, "DamageInstanceCreate": {"properties": {"damage_type": {"$ref": "#/components/schemas/DamageType", "description": "Type of damage (e.g., FIRE, WATER, WIND)"}, "damage_description": {"type": "string", "title": "Damage Description", "description": "Description of the damage"}, "damage_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Damage Severity", "description": "Severity of the damage (Minor, Moderate, Severe)"}, "affected_area": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Affected Area", "description": "Specific area affected by the damage"}, "damage_cause": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Damage Cause", "description": "Cause of the damage"}, "date_of_damage": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date Of Damage", "description": "Date when the damage occurred"}, "repair_status": {"anyOf": [{"$ref": "#/components/schemas/RepairStatus"}, {"type": "null"}], "description": "Status of repairs"}, "repair_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Description", "description": "Description of the repair work"}, "repair_vendor": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Vendor performing the repairs"}, "estimated_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost", "description": "Estimated cost of repairs"}, "actual_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost", "description": "Actual cost of repairs"}, "repair_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Start Date", "description": "Date when repairs started"}, "repair_completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Completion Date", "description": "Date when repairs were completed"}, "estimated_replacement_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Replacement Cost", "description": "Estimated cost to replace the item"}, "deductible_applied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Deductible Applied", "description": "Whether a deductible was applied"}, "depreciation_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Depreciation Amount", "description": "Amount of depreciation applied"}}, "type": "object", "required": ["damage_type", "damage_description"], "title": "DamageInstanceCreate", "description": "Schema for creating a damage instance."}, "DamageInstanceResponse": {"properties": {"damage_type": {"$ref": "#/components/schemas/DamageType", "description": "Type of damage (e.g., FIRE, WATER, WIND)"}, "damage_description": {"type": "string", "title": "Damage Description", "description": "Description of the damage"}, "damage_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Damage Severity", "description": "Severity of the damage (Minor, Moderate, Severe)"}, "affected_area": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Affected Area", "description": "Specific area affected by the damage"}, "damage_cause": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Damage Cause", "description": "Cause of the damage"}, "date_of_damage": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date Of Damage", "description": "Date when the damage occurred"}, "repair_status": {"anyOf": [{"$ref": "#/components/schemas/RepairStatus"}, {"type": "null"}], "description": "Status of repairs"}, "repair_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Description", "description": "Description of the repair work"}, "repair_vendor": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Vendor performing the repairs"}, "estimated_repair_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost", "description": "Estimated cost of repairs"}, "actual_repair_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost", "description": "Actual cost of repairs"}, "repair_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Start Date", "description": "Date when repairs started"}, "repair_completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Completion Date", "description": "Date when repairs were completed"}, "estimated_replacement_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Replacement Cost", "description": "Estimated cost to replace the item"}, "deductible_applied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Deductible Applied", "description": "Whether a deductible was applied"}, "depreciation_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Depreciation Amount", "description": "Amount of depreciation applied"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "damaged_property_asset_id": {"type": "string", "format": "uuid", "title": "Damaged Property Asset Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["damage_type", "damage_description", "id", "damaged_property_asset_id", "created_at", "updated_at"], "title": "DamageInstanceResponse", "description": "Schema for damage instance in responses."}, "DamageInstanceUpdate": {"properties": {"damage_type": {"anyOf": [{"$ref": "#/components/schemas/DamageType"}, {"type": "null"}], "description": "Type of damage (e.g., FIRE, WATER, WIND)"}, "damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Description of the damage"}, "damage_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Damage Severity", "description": "Severity of the damage (Minor, Moderate, Severe)"}, "affected_area": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Affected Area", "description": "Specific area affected by the damage"}, "damage_cause": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Damage Cause", "description": "Cause of the damage"}, "date_of_damage": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Date Of Damage", "description": "Date when the damage occurred"}, "repair_status": {"anyOf": [{"$ref": "#/components/schemas/RepairStatus"}, {"type": "null"}], "description": "Status of repairs"}, "repair_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Repair Description", "description": "Description of the repair work"}, "repair_vendor": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "<PERSON><PERSON>", "description": "Vendor performing the repairs"}, "estimated_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost", "description": "Estimated cost of repairs"}, "actual_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost", "description": "Actual cost of repairs"}, "repair_start_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Start Date", "description": "Date when repairs started"}, "repair_completion_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Completion Date", "description": "Date when repairs were completed"}, "estimated_replacement_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Replacement Cost", "description": "Estimated cost to replace the item"}, "deductible_applied": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Deductible Applied", "description": "Whether a deductible was applied"}, "depreciation_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Depreciation Amount", "description": "Amount of depreciation applied"}}, "type": "object", "title": "DamageInstanceUpdate", "description": "Schema for updating a damage instance."}, "DamageType": {"type": "string", "enum": ["WATER", "FIRE", "WIND", "HAIL", "THEFT", "VANDALISM", "STRUCTURAL", "ELECTRICAL", "SMOKE", "COLLISION", "EXPLOSION", "MACHINERY_BREAKDOWN", "FREEZING", "MOLD", "LANDSLIDE", "FLOOD", "EARTHQUAKE", "LIGHTNING", "WEIGHT_OF_ICE_SNOW", "CIVIL_DISTURBANCE", "OTHER"], "title": "DamageType", "description": "Types of property damage."}, "DamagedPropertyAssetCreate": {"properties": {"name": {"type": "string", "maxLength": 200, "title": "Name", "description": "Name of the damaged property asset"}, "asset_type": {"$ref": "#/components/schemas/PropertyAssetType", "description": "Type of property asset"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the property asset"}, "location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Location", "description": "Location of the property asset"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the property asset"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City", "description": "City where the property asset is located"}, "state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the property asset is located"}, "zip_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip Code", "description": "ZIP/postal code of the property asset"}, "owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Owner Type", "description": "Type of owner (Insured, Third Party, etc.)"}, "owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Owned By Insured", "description": "Whether the property is owned by the insured"}, "estimated_value": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated value of the property asset"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date when the property was purchased"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "damage_instances": {"anyOf": [{"items": {"$ref": "#/components/schemas/DamageInstanceCreate"}, "type": "array"}, {"type": "null"}], "title": "Damage Instances"}}, "type": "object", "required": ["name", "asset_type"], "title": "DamagedPropertyAssetCreate", "description": "Schema for creating a damaged property asset."}, "DamagedPropertyAssetResponse": {"properties": {"name": {"type": "string", "maxLength": 200, "title": "Name", "description": "Name of the damaged property asset"}, "asset_type": {"$ref": "#/components/schemas/PropertyAssetType", "description": "Type of property asset"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the property asset"}, "location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Location", "description": "Location of the property asset"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the property asset"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City", "description": "City where the property asset is located"}, "state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the property asset is located"}, "zip_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip Code", "description": "ZIP/postal code of the property asset"}, "owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Owner Type", "description": "Type of owner (Insured, Third Party, etc.)"}, "owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Owned By Insured", "description": "Whether the property is owned by the insured"}, "estimated_value": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated value of the property asset"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date when the property was purchased"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "damage_instances": {"anyOf": [{"items": {"$ref": "#/components/schemas/DamageInstanceResponse"}, "type": "array"}, {"type": "null"}], "title": "Damage Instances"}}, "type": "object", "required": ["name", "asset_type", "id", "created_at", "updated_at"], "title": "DamagedPropertyAssetResponse", "description": "Schema for damaged property asset in responses."}, "DamagedPropertyAssetUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the damaged property asset"}, "asset_type": {"anyOf": [{"$ref": "#/components/schemas/PropertyAssetType"}, {"type": "null"}], "description": "Type of property asset"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the property asset"}, "location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Location", "description": "Location of the property asset"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the property asset"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City", "description": "City where the property asset is located"}, "state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the property asset is located"}, "zip_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip Code", "description": "ZIP/postal code of the property asset"}, "owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Owner Type", "description": "Type of owner (Insured, Third Party, etc.)"}, "owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Owned By Insured", "description": "Whether the property is owned by the insured"}, "estimated_value": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Value", "description": "Estimated value of the property asset"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date when the property was purchased"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}}, "type": "object", "title": "DamagedPropertyAssetUpdate", "description": "Schema for updating a damaged property asset."}, "DashboardMetricsResponse": {"properties": {"total_claims": {"type": "integer", "title": "Total Claims", "description": "Total number of claims"}, "open_claims": {"type": "integer", "title": "Open Claims", "description": "Number of open claims"}, "new_claims_last_period": {"type": "integer", "title": "New Claims Last Period", "description": "Number of new claims in the selected period"}, "closed_claims_last_period": {"type": "integer", "title": "Closed Claims Last Period", "description": "Number of closed claims in the selected period"}, "average_claim_lifecycle_days": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Claim Lifecycle Days", "description": "Average time to close claims in days"}, "average_claim_lifecycle_days_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in average claim lifecycle compared to previous period"}, "total_payments_last_period": {"type": "string", "title": "Total Payments Last Period", "description": "Total payments made in the selected period"}, "total_payments_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in total payments compared to previous period"}, "total_outstanding_reserves": {"type": "string", "title": "Total Outstanding Reserves", "description": "Total outstanding reserves"}, "total_outstanding_reserves_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in outstanding reserves compared to previous period"}, "tasks_pending": {"type": "integer", "title": "Tasks Pending", "description": "Number of pending tasks"}, "tasks_overdue": {"type": "integer", "title": "Tasks Overdue", "description": "Number of overdue tasks"}, "fnols_pending": {"type": "integer", "title": "Fnols Pending", "description": "Number of pending FNOLs"}}, "type": "object", "required": ["total_claims", "open_claims", "new_claims_last_period", "closed_claims_last_period", "total_payments_last_period", "total_outstanding_reserves", "tasks_pending", "tasks_overdue", "fnols_pending"], "title": "DashboardMetricsResponse", "description": "Schema for dashboard metrics response."}, "DocumentCreate": {"properties": {"type": {"$ref": "#/components/schemas/DocumentType", "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}, "file_path": {"type": "string", "maxLength": 1024, "minLength": 1, "title": "File Path", "description": "Server path where the file is stored"}, "file_size": {"type": "integer", "maximum": 104857600.0, "exclusiveMinimum": 0.0, "title": "File Size", "description": "Size of the file in bytes, maximum 100MB"}, "mime_type": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Mime Type", "description": "MIME type of the file (e.g., application/pdf)"}}, "type": "object", "required": ["type", "name", "file_path", "file_size", "mime_type"], "title": "DocumentCreate", "description": "<PERSON><PERSON>a for creating a document after upload."}, "DocumentDownloadUrlResponse": {"properties": {"download_url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Download Url", "description": "Pre-signed URL for downloading the file"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the download URL will expire"}}, "type": "object", "required": ["download_url", "expires_at"], "title": "DocumentDownloadUrlResponse", "description": "Schema for document download URL response."}, "DocumentList": {"properties": {"items": {"items": {"$ref": "#/components/schemas/DocumentResponse"}, "type": "array", "title": "Items", "description": "List of document objects"}, "total": {"type": "integer", "minimum": 0.0, "title": "Total", "description": "Total number of documents matching the query"}}, "type": "object", "required": ["items", "total"], "title": "DocumentList", "description": "Schema for a list of documents."}, "DocumentResponse": {"properties": {"type": {"$ref": "#/components/schemas/DocumentType", "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the document"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim the document belongs to"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this document"}, "file_path": {"type": "string", "title": "File Path", "description": "Server path where the file is stored"}, "file_size": {"type": "integer", "title": "File Size", "description": "Size of the file in bytes"}, "mime_type": {"type": "string", "title": "Mime Type", "description": "MIME type of the file (e.g., application/pdf)"}, "uploaded_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Uploaded By", "description": "ID of the user who uploaded the document"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the document was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the document was last updated"}}, "type": "object", "required": ["type", "name", "id", "claim_id", "file_path", "file_size", "mime_type", "created_at", "updated_at"], "title": "DocumentResponse", "description": "Schema for document response."}, "DocumentSchema": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "type": {"type": "string", "title": "Type"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "file_path": {"type": "string", "title": "File Path"}, "file_size": {"type": "integer", "title": "File Size"}, "mime_type": {"type": "string", "title": "Mime Type"}, "uploaded_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Uploaded By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "name", "type", "file_path", "file_size", "mime_type", "created_at", "updated_at"], "title": "DocumentSchema", "description": "Schema for document responses."}, "DocumentType": {"type": "string", "enum": ["PHOTO", "REPORT", "INVOICE", "STATEMENT", "CONTRACT", "POLICY", "CORRESPONDENCE", "OTHER"], "title": "DocumentType", "description": "Types of documents."}, "DocumentUpdate": {"properties": {"type": {"anyOf": [{"$ref": "#/components/schemas/DocumentType"}, {"type": "null"}], "description": "Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)"}, "name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Display name of the document"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Optional description of the document contents"}}, "additionalProperties": false, "type": "object", "title": "DocumentUpdate", "description": "Schema for updating document metadata."}, "DocumentUploadUrlRequest": {"properties": {"file_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "File Name", "description": "Original name of the file being uploaded"}, "content_type": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Content Type", "description": "MIME type of the file being uploaded"}, "document_type": {"$ref": "#/components/schemas/DocumentType", "description": "Type of document being uploaded"}}, "additionalProperties": false, "type": "object", "required": ["file_name", "content_type", "document_type"], "title": "DocumentUploadUrlRequest", "description": "Schema for requesting a document upload URL."}, "DocumentUploadUrlResponse": {"properties": {"upload_url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Upload Url", "description": "Pre-signed URL for uploading the file"}, "document_id": {"type": "string", "format": "uuid", "title": "Document Id", "description": "ID of the document record created"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the upload URL will expire"}}, "type": "object", "required": ["upload_url", "document_id", "expires_at"], "title": "DocumentUploadUrlResponse", "description": "Schema for document upload URL response."}, "EntityType": {"type": "string", "enum": ["CLAIM", "CLAIM_DETAILS", "AUTO_DETAILS", "PROPERTY_DETAILS", "GL_DETAILS", "WITNESS", "ATTORNEY", "FINANCIAL", "RESERVE", "PAYMENT", "DOCUMENT", "NOTE", "TASK"], "title": "EntityType", "description": "Types of entities that can be audited."}, "FNOLConversionResponse": {"properties": {"claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Unique identifier for the created claim"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Unique claim reference number"}, "claim_type": {"$ref": "#/components/schemas/ClaimType", "description": "Type of the created claim"}, "status": {"$ref": "#/components/schemas/ClaimStatus", "description": "Status of the created claim"}}, "type": "object", "required": ["claim_id", "claim_number", "claim_type", "status"], "title": "FNOLConversionResponse", "description": "Schema to use specifically for FNOL to claim conversion to avoid circular references.", "example": {"claim_id": "123e4567-e89b-12d3-a456-************", "claim_number": "SLCT-2024-0000001", "claim_type": "AUTO", "status": "DRAFT"}}, "FNOLCreate": {"properties": {"customer_id": {"type": "string", "format": "uuid", "title": "Customer Id", "description": "ID of the customer the FNOL belongs to"}, "reported_by": {"type": "string", "maxLength": 200, "title": "Reported By", "description": "Name of the person reporting the loss"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the incident"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the FNOL"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_state": {"$ref": "#/components/schemas/USState", "description": "US state where the incident occurred"}, "incident_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Incident Time", "description": "Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"}, "reporter_relationship": {"anyOf": [{"$ref": "#/components/schemas/ReporterRelationship"}, {"type": "null"}], "description": "Relationship of the reporter to the claim/incident"}, "communication_preference": {"anyOf": [{"$ref": "#/components/schemas/CommunicationPreference"}, {"type": "null"}], "description": "Preferred method of communication for responses"}, "fnol_number": {"anyOf": [{"type": "string", "pattern": "^[A-Z0-9]{4}-FNOL-\\d{7}$"}, {"type": "null"}], "title": "Fnol Number", "description": "Unique FNOL identifier in format PREFIX-FNOL-NNNNNNN (auto-generated if not provided)"}}, "type": "object", "required": ["customer_id", "reported_by", "incident_state"], "title": "FNOLCreate", "description": "Create FNOL schema."}, "FNOLResponse": {"properties": {"customer_id": {"type": "string", "format": "uuid", "title": "Customer Id", "description": "ID of the customer the FNOL belongs to"}, "reported_by": {"type": "string", "maxLength": 200, "title": "Reported By", "description": "Name of the person reporting the loss"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the incident"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the FNOL"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_state": {"$ref": "#/components/schemas/USState", "description": "US state where the incident occurred"}, "incident_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Incident Time", "description": "Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"}, "reporter_relationship": {"anyOf": [{"$ref": "#/components/schemas/ReporterRelationship"}, {"type": "null"}], "description": "Relationship of the reporter to the claim/incident"}, "communication_preference": {"anyOf": [{"$ref": "#/components/schemas/CommunicationPreference"}, {"type": "null"}], "description": "Preferred method of communication for responses"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the FNOL"}, "fnol_number": {"type": "string", "title": "Fnol Number", "description": "Unique FNOL reference number"}, "reported_at": {"type": "string", "format": "date-time", "title": "Reported At", "description": "Timestamp when the loss was reported"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the FNOL was created in the system"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the FNOL was last updated"}, "customer": {"$ref": "#/components/schemas/CustomerResponse", "description": "Customer details"}, "claims": {"items": {"$ref": "#/components/schemas/ClaimResponseSchema"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Claims associated with this FNOL"}}, "type": "object", "required": ["customer_id", "reported_by", "incident_state", "id", "fnol_number", "reported_at", "created_at", "updated_at", "customer"], "title": "FNOLResponse", "description": "FNOL response schema.", "example": {"claims": [], "communication_preference": "EMAIL", "created_at": "2024-01-01T00:00:00Z", "customer": {"active": true, "created_at": "2024-01-01T00:00:00Z", "description": "Selective Insurance Company of America", "id": "123e4567-e89b-12d3-a456-426614174000", "name": "Selective Insurance", "prefix": "SLCT", "updated_at": "2024-01-01T00:00:00Z"}, "customer_id": "123e4567-e89b-12d3-a456-426614174000", "description": "Multi-vehicle accident on highway", "fnol_number": "SLCT-FNOL-0000001", "id": "123e4567-e89b-12d3-a456-426614174000", "incident_date": "2024-01-01", "incident_location": "I-<PERSON> <PERSON>, <PERSON> 123", "incident_state": "FL", "incident_time": "14:30:00", "policy_number": "POL123456789", "reported_at": "2024-01-01T00:00:00Z", "reported_by": "<PERSON>", "reporter_email": "<EMAIL>", "reporter_phone": "************", "reporter_relationship": "INSURED", "updated_at": "2024-01-01T00:00:00Z"}}, "FNOLUpdate": {"properties": {"reported_by": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Reported By", "description": "Name of the person reporting the loss"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the incident"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the FNOL"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "US state where the incident occurred"}, "incident_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Incident Time", "description": "Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"}, "reporter_relationship": {"anyOf": [{"$ref": "#/components/schemas/ReporterRelationship"}, {"type": "null"}], "description": "Relationship of the reporter to the claim/incident"}, "communication_preference": {"anyOf": [{"$ref": "#/components/schemas/CommunicationPreference"}, {"type": "null"}], "description": "Preferred method of communication for responses"}}, "type": "object", "title": "FNOLUpdate", "description": "Update FNOL schema."}, "FinancialKpiData": {"properties": {"total_reserves": {"type": "string", "title": "Total Reserves", "description": "Total reserves amount"}, "total_reserves_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in total reserves compared to previous period"}, "total_payments": {"type": "string", "title": "Total Payments", "description": "Total payments amount"}, "total_payments_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in total payments compared to previous period"}, "avg_claim_value": {"type": "string", "title": "Avg Claim Value", "description": "Average claim value"}, "avg_claim_value_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in average claim value compared to previous period"}, "recovery_amount": {"type": "string", "title": "Recovery Amount", "description": "Total recovery amount"}, "recovery_amount_change": {"anyOf": [{"$ref": "#/components/schemas/MetricChange"}, {"type": "null"}], "description": "Change in recovery amount compared to previous period"}}, "type": "object", "required": ["total_reserves", "total_payments", "avg_claim_value", "recovery_amount"], "title": "FinancialKpiData", "description": "Data for Financial KPI report."}, "GeneralLiabilityClaimResponse": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "claim_number": {"type": "string", "title": "Claim Number"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "GENERAL_LIABILITY"}, "status": {"$ref": "#/components/schemas/ClaimStatus"}, "created_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "customer": {"anyOf": [{"$ref": "#/components/schemas/CustomerResponse"}, {"type": "null"}]}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentSchema"}, "type": "array"}, {"type": "null"}], "title": "Documents"}, "notes": {"anyOf": [{"items": {"$ref": "#/components/schemas/NoteSchema"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "tasks": {"anyOf": [{"items": {"$ref": "#/components/schemas/TaskSchema"}, "type": "array"}, {"type": "null"}], "title": "Tasks"}, "status_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/StatusHistorySchema"}, "type": "array"}, {"type": "null"}], "title": "Status History"}, "financials": {"anyOf": [{"$ref": "#/components/schemas/ClaimFinancialsInDB"}, {"type": "null"}]}, "witnesses": {"anyOf": [{"items": {"$ref": "#/components/schemas/WitnessResponse"}, "type": "array"}, {"type": "null"}], "title": "Witnesses"}, "attorneys": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttorneyResponse"}, "type": "array"}, {"type": "null"}], "title": "Attorneys"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "supervisor": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "gl_details": {"anyOf": [{"$ref": "#/components/schemas/GeneralLiabilityDetailsResponse"}, {"type": "null"}]}}, "type": "object", "required": ["id", "customer_id", "claim_number", "status", "created_at", "updated_at"], "title": "GeneralLiabilityClaimResponse", "description": "Schema for general liability claims in responses."}, "GeneralLiabilityClaimUpdate": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "GENERAL_LIABILITY"}, "status": {"anyOf": [{"$ref": "#/components/schemas/ClaimStatus"}, {"type": "null"}], "description": "Current status of the claim"}, "gl_details": {"anyOf": [{"$ref": "#/components/schemas/GeneralLiabilityDetailsUpdate"}, {"type": "null"}]}}, "type": "object", "title": "GeneralLiabilityClaimUpdate", "description": "Schema for updating general liability claims."}, "GeneralLiabilityDetailsResponse": {"properties": {"incident_type": {"$ref": "#/components/schemas/GeneralLiabilityIncidentType", "description": "Type of general liability incident"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "premises_details": {"anyOf": [{"$ref": "#/components/schemas/PremisesLiabilityDetailsResponse"}, {"type": "null"}]}, "products_details": {"anyOf": [{"$ref": "#/components/schemas/ProductsLiabilityDetailsResponse"}, {"type": "null"}]}, "operations_details": {"anyOf": [{"$ref": "#/components/schemas/CompletedOperationsDetailsResponse"}, {"type": "null"}]}, "advertising_details": {"anyOf": [{"$ref": "#/components/schemas/PersonalAdvertisingInjuryDetailsResponse"}, {"type": "null"}]}, "bodily_injury_details": {"anyOf": [{"$ref": "#/components/schemas/BodilyInjuryDetailsResponse"}, {"type": "null"}]}, "injured_persons": {"anyOf": [{"items": {"$ref": "#/components/schemas/InjuredPersonResponse"}, "type": "array"}, {"type": "null"}], "title": "Injured Persons"}, "damaged_property_assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}, "type": "array"}, {"type": "null"}], "title": "Damaged Property Assets"}}, "type": "object", "required": ["incident_type", "id", "claim_id", "created_at", "updated_at"], "title": "GeneralLiabilityDetailsResponse", "description": "Schema for general liability details in responses."}, "GeneralLiabilityDetailsUpdate": {"properties": {"incident_type": {"$ref": "#/components/schemas/GeneralLiabilityIncidentType", "description": "Type of general liability incident"}}, "type": "object", "required": ["incident_type"], "title": "GeneralLiabilityDetailsUpdate", "description": "Schema for updating general liability details."}, "GeneralLiabilityIncidentType": {"type": "string", "enum": ["PREMISES_LIABILITY", "SLIP_AND_FALL", "TRIP_AND_FALL", "FALLING_OBJECT", "INADEQUATE_SECURITY", "NEGLIGENT_MAINTENANCE", "PRODUCTS_LIABILITY", "DEFECTIVE_DESIGN", "MANUFACTURING_DEFECT", "FAILURE_TO_WARN", "PRODUCT_RECALL", "COMPLETED_OPERATIONS", "ONGOING_OPERATIONS", "CONTRACTOR_ERROR", "PROFESSIONAL_NEGLIGENCE", "THIRD_PARTY_PROPERTY_DAMAGE", "THIRD_PARTY_BODILY_INJURY", "PERSONAL_ADVERTISING_INJURY", "LIBEL_SLANDER", "COPYRIGHT_INFRINGEMENT", "MEDICAL_PAYMENTS", "ENVIRONMENTAL_DAMAGE", "CYBER_LIABILITY", "FOOD_CONTAMINATION", "OTHER"], "title": "GeneralLiabilityIncidentType", "description": "General Liability incident types."}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "IncidentReportStatus": {"type": "string", "enum": ["FILED", "PENDING", "NOT_REQUIRED"], "title": "IncidentReportStatus", "description": "Status of incident reports."}, "InjuredPersonCreate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the injured person"}, "person_type": {"anyOf": [{"$ref": "#/components/schemas/InjuredPersonType"}, {"type": "null"}], "description": "Type of injured person"}, "contact_info": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Contact Info", "description": "Contact information of the injured person"}, "age": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Age", "description": "Age of the injured person"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the incident occurred"}, "incident_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Incident Description", "description": "Description of the incident"}, "incident_report_status": {"anyOf": [{"$ref": "#/components/schemas/IncidentReportStatus"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}}, "type": "object", "title": "InjuredPersonCreate", "description": "<PERSON><PERSON><PERSON> for creating a new injured person."}, "InjuredPersonResponse": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the injured person"}, "person_type": {"anyOf": [{"$ref": "#/components/schemas/InjuredPersonType"}, {"type": "null"}], "description": "Type of injured person"}, "contact_info": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Contact Info", "description": "Contact information of the injured person"}, "age": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Age", "description": "Age of the injured person"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the incident occurred"}, "incident_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Incident Description", "description": "Description of the incident"}, "incident_report_status": {"anyOf": [{"$ref": "#/components/schemas/IncidentReportStatus"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Gl Details Id"}, "auto_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Auto Details Id"}, "property_details_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Property Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "injuries": {"anyOf": [{"items": {"$ref": "#/components/schemas/InjuryResponse"}, "type": "array"}, {"type": "null"}], "title": "Injuries"}}, "type": "object", "required": ["id", "created_at", "updated_at"], "title": "InjuredPersonResponse", "description": "Schema for injured person in responses."}, "InjuredPersonType": {"type": "string", "enum": ["GUEST", "PATRON", "EMPLOYEE", "TENANT", "CONTRACTOR", "VENDOR", "TRESPASSER", "VISITOR", "CUSTOMER", "SUBCONTRACTOR", "DELIVERY_PERSON", "SERVICE_PROVIDER", "PROPERTY_OWNER", "STUDENT", "PATIENT", "DRIVER", "INSURED_DRIVER", "THIRD_PARTY_DRIVER", "PASSENGER", "PEDESTRIAN", "CYCLIST", "OTHER_DRIVER", "OTHER_PASSENGER", "COMMERCIAL_DRIVER", "TRUCKER", "BYSTANDER", "CHILD", "ELDERLY", "UNKNOWN", "OTHER"], "title": "InjuredPersonType", "description": "Types of injured persons."}, "InjuredPersonUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Name", "description": "Name of the injured person"}, "person_type": {"anyOf": [{"$ref": "#/components/schemas/InjuredPersonType"}, {"type": "null"}], "description": "Type of injured person"}, "contact_info": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Contact Info", "description": "Contact information of the injured person"}, "age": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Age", "description": "Age of the injured person"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location where the incident occurred"}, "incident_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Incident Description", "description": "Description of the incident"}, "incident_report_status": {"anyOf": [{"$ref": "#/components/schemas/IncidentReportStatus"}, {"type": "null"}], "description": "Status of the incident report"}, "report_filer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Name", "description": "Name of the person who filed the report"}, "report_filer_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Report Filer Contact", "description": "Contact details of the report filer"}}, "type": "object", "title": "InjuredPersonUpdate", "description": "<PERSON><PERSON><PERSON> for updating an injured person."}, "InjuryCreate": {"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury"}, "injury_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Type", "description": "Type of injury"}, "injury_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Severity", "description": "Severity of the injury"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "#/components/schemas/MedicalTreatmentRequirements"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "#/components/schemas/InsuranceBillingStatus"}, {"type": "null"}], "description": "Status of insurance billing"}}, "type": "object", "title": "InjuryCreate", "description": "<PERSON><PERSON><PERSON> for creating a new injury."}, "InjuryResponse": {"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury"}, "injury_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Type", "description": "Type of injury"}, "injury_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Severity", "description": "Severity of the injury"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "#/components/schemas/MedicalTreatmentRequirements"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "#/components/schemas/InsuranceBillingStatus"}, {"type": "null"}], "description": "Status of insurance billing"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "injured_person_id": {"type": "string", "format": "uuid", "title": "Injured Person Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "injured_person_id", "created_at", "updated_at"], "title": "InjuryResponse", "description": "<PERSON><PERSON>a for injury in responses."}, "InjuryUpdate": {"properties": {"injury_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Injury Description", "description": "Description of the injury"}, "injury_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Type", "description": "Type of injury"}, "injury_severity": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Injury Severity", "description": "Severity of the injury"}, "equipment_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Involved", "description": "Whether equipment was involved in the incident"}, "equipment_details": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Equipment Details", "description": "Details about the equipment involved"}, "equipment_owned_by_insured": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Equipment Owned By Insured", "description": "Whether the equipment is owned by the insured"}, "safety_measures_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Safety Measures Involved", "description": "Whether safety measures were involved"}, "safety_measures_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Safety Measures Description", "description": "Description of the safety measures"}, "medical_treatment_requirements": {"anyOf": [{"$ref": "#/components/schemas/MedicalTreatmentRequirements"}, {"type": "null"}], "description": "Type of medical treatment required"}, "treatment_nature": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Treatment Nature", "description": "Nature of the medical treatment"}, "medical_provider_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Medical Provider Name", "description": "Name of the medical provider"}, "medical_provider_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Medical Provider Address", "description": "Address of the medical provider"}, "estimated_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Cost", "description": "Estimated cost of medical treatment"}, "insurance_billing_status": {"anyOf": [{"$ref": "#/components/schemas/InsuranceBillingStatus"}, {"type": "null"}], "description": "Status of insurance billing"}}, "type": "object", "title": "InjuryUpdate", "description": "<PERSON><PERSON><PERSON> for updating an injury."}, "InsuranceBillingStatus": {"type": "string", "enum": ["NOT_SUBMITTED", "PENDING", "PARTIAL_PAYMENT", "PAID", "DENIED"], "title": "InsuranceBillingStatus", "description": "Insurance billing status."}, "MedicalTreatmentRequirements": {"type": "string", "enum": ["NONE", "FIRST_AID", "OUTPATIENT", "HOSPITALIZATION", "SURGERY", "PHYSICAL_THERAPY", "ONGOING_CARE"], "title": "MedicalTreatmentRequirements", "description": "Medical treatment requirements."}, "MetricChange": {"properties": {"value": {"anyOf": [{"type": "number"}, {"type": "integer"}, {"type": "string"}], "title": "Value", "description": "Absolute value of the change"}, "direction": {"type": "string", "enum": ["increase", "decrease", "neutral"], "title": "Direction", "description": "Direction of the change"}, "percentage": {"type": "number", "title": "Percentage", "description": "Percentage of change"}}, "type": "object", "required": ["value", "direction", "percentage"], "title": "MetricChange", "description": "Schema for representing metric changes with direction and percentage."}, "NoteCreateRequestBody": {"properties": {"content": {"type": "string", "title": "Content", "description": "The content of the note."}}, "type": "object", "required": ["content"], "title": "NoteCreateRequestBody", "description": "Schema for the request body when creating a note.\n\nclaim_id is not included here as it's derived from the path."}, "NoteResponse": {"properties": {"content": {"type": "string", "title": "Content", "description": "The content of the note."}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this note"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "author_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Author Id", "description": "ID of the user who created the note."}, "author_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Author <PERSON><PERSON>", "description": "Email of the user who created the note."}, "author": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Author", "description": "Name of the user who created the note."}}, "type": "object", "required": ["content", "id", "claim_id", "created_at", "updated_at"], "title": "NoteResponse", "description": "Schema for returning a note via the API."}, "NoteSchema": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "content": {"type": "string", "title": "Content"}, "created_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "content", "created_at", "updated_at"], "title": "NoteSchema", "description": "Schema for note responses."}, "NoteUpdate": {"properties": {"content": {"type": "string", "title": "Content", "description": "The updated content of the note."}}, "type": "object", "required": ["content"], "title": "NoteUpdate", "description": "Schema for updating an existing note. Only content can be updated."}, "PaginatedAuditTrailResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/AuditTrailResponse"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedAuditTrailResponse", "description": "Schema for paginated audit trail response."}, "PaginatedClaimResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ClaimResponseSchema"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedClaimResponse", "description": "Schema for paginated claim response."}, "PaginatedTaskResponse": {"properties": {"items": {"items": {"$ref": "#/components/schemas/TaskRead"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}, "skip": {"type": "integer", "title": "<PERSON><PERSON>"}, "limit": {"type": "integer", "title": "Limit"}}, "type": "object", "required": ["items", "total", "skip", "limit"], "title": "PaginatedTaskResponse", "description": "Paginated response for task listings."}, "PasswordChange": {"properties": {"current_password": {"type": "string", "title": "Current Password", "description": "Current password for verification", "examples": ["CurrentP@ssw0rd"]}, "new_password": {"type": "string", "maxLength": 64, "minLength": 8, "title": "New Password", "description": "New password", "examples": ["NewStrongP@ssw0rd!"]}}, "type": "object", "required": ["current_password", "new_password"], "title": "PasswordChange", "description": "<PERSON><PERSON>a for changing user password."}, "PaymentCreate": {"properties": {"payment_type": {"$ref": "#/components/schemas/PaymentType", "description": "Type of payment (INDEMNITY, EXPENSE, or DEFENSE)"}, "amount": {"anyOf": [{"type": "number", "exclusiveMinimum": 0.0}, {"type": "string"}], "title": "Amount", "description": "Payment amount (must be positive)"}, "payee": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Payee", "description": "Recipient of the payment"}, "payment_date": {"type": "string", "format": "date-time", "title": "Payment Date", "description": "Date the payment was issued"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "Notes about the payment"}}, "type": "object", "required": ["payment_type", "amount", "payee", "payment_date"], "title": "PaymentCreate", "description": "Schema for creating a payment."}, "PaymentList": {"properties": {"items": {"items": {"$ref": "#/components/schemas/PaymentResponse"}, "type": "array", "title": "Items"}, "total": {"type": "integer", "title": "Total"}}, "type": "object", "required": ["items", "total"], "title": "PaymentList", "description": "Schema for listing payments."}, "PaymentReserveTimeItem": {"properties": {"period": {"type": "string", "format": "date", "title": "Period", "description": "Date period (month/week)"}, "payments": {"type": "string", "title": "Payments", "description": "Total payments in this period"}, "reserves": {"type": "string", "title": "Reserves", "description": "Total reserves in this period"}}, "type": "object", "required": ["period", "payments", "reserves"], "title": "PaymentReserveTimeItem", "description": "Item in Payments vs Reserves report."}, "PaymentResponse": {"properties": {"payment_type": {"$ref": "#/components/schemas/PaymentType", "description": "Type of payment (INDEMNITY, EXPENSE, or DEFENSE)"}, "amount": {"type": "string", "title": "Amount", "description": "Payment amount (must be positive)"}, "payee": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Payee", "description": "Recipient of the payment"}, "payment_date": {"type": "string", "format": "date-time", "title": "Payment Date", "description": "Date the payment was issued"}, "notes": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Notes", "description": "Notes about the payment"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "financials_id": {"type": "string", "format": "uuid", "title": "Financials Id", "description": "Associated financials ID"}, "created_by_id": {"type": "string", "format": "uuid", "title": "Created By Id", "description": "User who created the payment"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["payment_type", "amount", "payee", "payment_date", "id", "financials_id", "created_by_id", "created_at", "updated_at"], "title": "PaymentResponse", "description": "Schema for payment response."}, "PaymentType": {"type": "string", "enum": ["INDEMNITY", "EXPENSE", "DEFENSE"], "title": "PaymentType", "description": "Types of payments."}, "PersonalAdvertisingInjuryDetailsResponse": {"properties": {"injury_nature": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Injury Nature", "description": "Nature of alleged injury"}, "offense_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Offense Date", "description": "Date of alleged offense"}, "offensive_material_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Offensive Material Description", "description": "Description of alleged offensive material/action"}, "publication_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Publication Location", "description": "Location of publication/distribution"}, "material_creator": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Material Creator", "description": "Identification of material creator"}, "material_removed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Material Removed", "description": "Whether the infringing material was removed"}, "removal_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Removal Date", "description": "Date of material removal"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "PersonalAdvertisingInjuryDetailsResponse", "description": "Schema for personal and advertising injury details in responses."}, "PointOfImpact": {"type": "string", "enum": ["FRONT", "REAR", "DRIVER_SIDE", "PASSENGER_SIDE", "ROOF", "UNDERCARRIAGE", "MULTIPLE"], "title": "PointOfImpact", "description": "Vehicle impact location."}, "PremisesLiabilityDetailsResponse": {"properties": {"owner_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Owner Name", "description": "Name of the property owner"}, "owner_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Owner Address", "description": "Address of the property owner"}, "premises_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Premises Type", "description": "Type of premises"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Location of incident on premises"}, "is_inside": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Inside", "description": "Whether the incident occurred inside or outside"}, "weather_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Weather Type", "description": "Weather conditions at time of incident"}, "third_party_property_involved": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Third Party Property Involved", "description": "Whether third party property was involved"}, "hazard_signs_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Hazard Signs Present", "description": "Whether signs/warnings about hazards were present"}, "claimant_activity_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claimant Activity Description", "description": "Description of claimant's activity at time of incident"}, "security_cameras_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Security Cameras Present", "description": "Whether security cameras were present"}, "insured_relationship": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Insured Relationship", "description": "Insured relationship to premises (owner, tenant, other, unknown)"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "PremisesLiabilityDetailsResponse", "description": "Schema for premises liability details in responses."}, "ProductsLiabilityDetailsResponse": {"properties": {"manufacturer_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Manufacturer Name", "description": "Name of the manufacturer"}, "manufacturer_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Manufacturer Address", "description": "Address of the manufacturer"}, "product_type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Product Type", "description": "Type of product"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date", "description": "Date of purchase"}, "installation_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Installation Date", "description": "Date of installation if applicable"}, "usage_complies_with_intent": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Usage Complies With Intent", "description": "Whether product was used as intended"}, "product_modified": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Product Modified", "description": "Whether product was modified"}, "serial_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Serial Number", "description": "Product serial number"}, "warnings_present": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Warnings Present", "description": "Whether warnings were present on product"}, "third_party_materials_used": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Third Party Materials Used", "description": "Whether third party materials were used"}, "similar_incidents_history": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Similar Incidents History", "description": "History of similar incidents or complaints"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "gl_details_id": {"type": "string", "format": "uuid", "title": "Gl Details Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "gl_details_id", "created_at", "updated_at"], "title": "ProductsLiabilityDetailsResponse", "description": "Schema for products liability details in responses."}, "PropertyAssetType": {"type": "string", "enum": ["BUILDING", "STRUCTURE", "ROOF", "WALL", "FLOOR", "FOUNDATION", "HVAC", "PLUMBING", "ELECTRICAL_SYSTEM", "FURNITURE", "APPLIANCE", "ELECTRONICS", "MACHINERY", "EQUIPMENT", "INVENTORY", "TOOLS", "RAW_MATERIALS", "FINISHED_GOODS", "OFFICE_EQUIPMENT", "VEHICLE", "CARGO", "TRAILER", "FLEET_VEHICLE", "SPECIALIZED_VEHICLE", "THIRD_PARTY_STRUCTURE", "THIRD_PARTY_VEHICLE", "THIRD_PARTY_PROPERTY", "CUSTOMER_PROPERTY", "LANDSCAPING", "FIXTURE", "SIGNAGE", "PARKING_LOT", "FENCING", "MANUFACTURING_EQUIPMENT", "RESTAURANT_EQUIPMENT", "MEDICAL_EQUIPMENT", "IT_INFRASTRUCTURE", "DATA", "OTHER"], "title": "PropertyAssetType", "description": "Types of property assets that can be damaged."}, "PropertyClaimResponse": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "customer_id": {"type": "string", "format": "uuid", "title": "Customer Id"}, "claim_number": {"type": "string", "title": "Claim Number"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "PROPERTY"}, "status": {"$ref": "#/components/schemas/ClaimStatus"}, "created_by_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "customer": {"anyOf": [{"$ref": "#/components/schemas/CustomerResponse"}, {"type": "null"}]}, "documents": {"anyOf": [{"items": {"$ref": "#/components/schemas/DocumentSchema"}, "type": "array"}, {"type": "null"}], "title": "Documents"}, "notes": {"anyOf": [{"items": {"$ref": "#/components/schemas/NoteSchema"}, "type": "array"}, {"type": "null"}], "title": "Notes"}, "tasks": {"anyOf": [{"items": {"$ref": "#/components/schemas/TaskSchema"}, "type": "array"}, {"type": "null"}], "title": "Tasks"}, "status_history": {"anyOf": [{"items": {"$ref": "#/components/schemas/StatusHistorySchema"}, "type": "array"}, {"type": "null"}], "title": "Status History"}, "financials": {"anyOf": [{"$ref": "#/components/schemas/ClaimFinancialsInDB"}, {"type": "null"}]}, "witnesses": {"anyOf": [{"items": {"$ref": "#/components/schemas/WitnessResponse"}, "type": "array"}, {"type": "null"}], "title": "Witnesses"}, "attorneys": {"anyOf": [{"items": {"$ref": "#/components/schemas/AttorneyResponse"}, "type": "array"}, {"type": "null"}], "title": "Attorneys"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "supervisor": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "created_by": {"anyOf": [{"$ref": "#/components/schemas/UserResponse"}, {"type": "null"}]}, "property_details": {"anyOf": [{"$ref": "#/components/schemas/PropertyDetailsResponse"}, {"type": "null"}]}}, "type": "object", "required": ["id", "customer_id", "claim_number", "status", "created_at", "updated_at"], "title": "PropertyClaimResponse", "description": "Schema for property claims in responses."}, "PropertyClaimUpdate": {"properties": {"description": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Description", "description": "Detailed description of the claim"}, "claimant_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Claimant Name", "description": "Full name of the claimant"}, "claimant_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Email address of the claimant"}, "claimant_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Claimant Phone", "description": "Contact phone number of the claimant"}, "insured_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Insured Name", "description": "Full name of the insured"}, "insured_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Insured Email", "description": "Email address of the insured"}, "insured_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Insured Phone", "description": "Contact phone number of the insured"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "jurisdiction": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "Legal jurisdiction of the claim (US state)"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the claim"}, "assigned_to_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id", "description": "ID of the adjuster assigned to the claim"}, "supervisor_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id", "description": "ID of the supervisor overseeing the claim"}, "recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "type": {"$ref": "#/components/schemas/ClaimType", "default": "PROPERTY"}, "status": {"anyOf": [{"$ref": "#/components/schemas/ClaimStatus"}, {"type": "null"}], "description": "Current status of the claim"}, "property_details": {"anyOf": [{"$ref": "#/components/schemas/PropertyDetailsUpdate"}, {"type": "null"}]}}, "type": "object", "title": "PropertyClaimUpdate", "description": "Schema for updating property claims."}, "PropertyDetailsResponse": {"properties": {"property_type": {"anyOf": [{"$ref": "#/components/schemas/PropertyType"}, {"type": "null"}], "description": "Type of property (e.g., RESIDENTIAL, COMMERCIAL, RENTAL)"}, "property_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Property Address", "description": "Full address of the property"}, "property_city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Property City", "description": "City where the property is located"}, "property_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the property is located"}, "property_zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Property Zip", "description": "ZIP/postal code of the property"}, "damage_type": {"anyOf": [{"$ref": "#/components/schemas/DamageType"}, {"type": "null"}], "description": "Type of damage (e.g., FIRE, WATER, WIND, HAIL, THEFT)"}, "damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Detailed description of the damage"}, "affected_areas": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Affected Areas", "description": "Specific areas of the property affected by the damage"}, "inhabitable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Inhabitable", "description": "Whether the property is currently inhabitable"}, "temporary_repairs_needed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Temporary Repairs Needed", "description": "Whether temporary repairs are needed to secure the property"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "emergency_services_called": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Emergency Services Called", "description": "Whether emergency services (fire, medical) were called"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "damaged_property_assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/DamagedPropertyAssetResponse"}, "type": "array"}, {"type": "null"}], "title": "Damaged Property Assets"}, "bodily_injury_details": {"anyOf": [{"$ref": "#/components/schemas/BodilyInjuryDetailsResponse"}, {"type": "null"}]}, "injured_persons": {"anyOf": [{"items": {"$ref": "#/components/schemas/InjuredPersonResponse"}, "type": "array"}, {"type": "null"}], "title": "Injured Persons"}}, "type": "object", "required": ["id", "claim_id", "created_at", "updated_at"], "title": "PropertyDetailsResponse", "description": "Schema for property details in responses."}, "PropertyDetailsUpdate": {"properties": {"property_type": {"anyOf": [{"$ref": "#/components/schemas/PropertyType"}, {"type": "null"}], "description": "Type of property (e.g., RESIDENTIAL, COMMERCIAL, RENTAL)"}, "property_address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Property Address", "description": "Full address of the property"}, "property_city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Property City", "description": "City where the property is located"}, "property_state": {"anyOf": [{"$ref": "#/components/schemas/USState"}, {"type": "null"}], "description": "State where the property is located"}, "property_zip": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Property Zip", "description": "ZIP/postal code of the property"}, "damage_type": {"anyOf": [{"$ref": "#/components/schemas/DamageType"}, {"type": "null"}], "description": "Type of damage (e.g., FIRE, WATER, WIND, HAIL, THEFT)"}, "damage_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Damage Description", "description": "Detailed description of the damage"}, "affected_areas": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Affected Areas", "description": "Specific areas of the property affected by the damage"}, "inhabitable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Inhabitable", "description": "Whether the property is currently inhabitable"}, "temporary_repairs_needed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Temporary Repairs Needed", "description": "Whether temporary repairs are needed to secure the property"}, "police_report_filed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Police Report Filed", "description": "Whether a police report was filed"}, "police_report_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Police Report Number", "description": "Police report reference number"}, "emergency_services_called": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Emergency Services Called", "description": "Whether emergency services (fire, medical) were called"}, "bodily_injury_details": {"anyOf": [{"$ref": "#/components/schemas/BodilyInjuryDetailsUpdate"}, {"type": "null"}]}}, "type": "object", "title": "PropertyDetailsUpdate", "description": "Schema for updating property details."}, "PropertyType": {"type": "string", "enum": ["RESIDENTIAL", "SINGLE_FAMILY", "MULTI_FAMILY", "CONDOMINIUM", "APARTMENT_BUILDING", "COMMERCIAL", "OFFICE_BUILDING", "RETAIL_STORE", "SHOPPING_CENTER", "RESTAURANT", "HOTEL_MOTEL", "WAREHOUSE", "MANUFACTURING_FACILITY", "MEDICAL_FACILITY", "EDUCATIONAL_FACILITY", "INDUSTRIAL", "FACTORY", "DISTRIBUTION_CENTER", "PROCESSING_FACILITY", "VACANT_LAND", "AGRICULTURAL", "MIXED_USE", "OTHER"], "title": "PropertyType", "description": "Type of property."}, "RecoveryDetailsResponse": {"properties": {"recovery_status": {"anyOf": [{"$ref": "#/components/schemas/RecoveryStatus"}, {"type": "null"}], "description": "Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"}, "carrier_name": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Name", "description": "Third-party carrier name"}, "carrier_contact": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Contact", "description": "Third-party carrier contact information"}, "carrier_claim_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Carrier Claim Number", "description": "Third-party carrier's claim number"}, "carrier_adjuster": {"anyOf": [{"type": "string", "maxLength": 200}, {"type": "null"}], "title": "Carrier Adjuster", "description": "Third-party carrier's adjuster name"}, "expected_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Expected Amount", "description": "Expected recovery amount"}, "received_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Received Amount", "description": "Amount actually recovered"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "ID of the claim this recovery data belongs to"}, "claim_number": {"type": "string", "title": "Claim Number", "description": "Claim number for reference"}}, "type": "object", "required": ["claim_id", "claim_number"], "title": "RecoveryDetailsResponse", "description": "Schema for recovery details response."}, "RecoveryStatus": {"type": "string", "enum": ["NOT_STARTED", "INITIATED", "IN_PROGRESS", "COMPLETED"], "title": "RecoveryStatus", "description": "Status of recovery efforts."}, "RepairStatus": {"type": "string", "enum": ["NOT_STARTED", "ASSESSMENT_PENDING", "ESTIMATED", "WAITING_FOR_PARTS", "WAITING_FOR_CONTRACTOR", "PERMITS_PENDING", "SCHEDULED", "IN_PROGRESS", "PARTIALLY_COMPLETED", "INSPECTION_PENDING", "COMPLETED", "REPLACED", "NOT_REPAIRABLE", "REPAIRS_DECLINED", "ON_HOLD"], "title": "RepairStatus", "description": "Status of repairs for damaged property."}, "ReportMetadata": {"properties": {"report_name": {"type": "string", "title": "Report Name", "description": "Name of the report"}, "generated_at": {"type": "string", "format": "date-time", "title": "Generated At", "description": "Timestamp when the report was generated"}, "filters_applied": {"additionalProperties": true, "type": "object", "title": "Filters Applied", "description": "Filters that were applied to generate the report"}, "column_headers": {"items": {"additionalProperties": true, "type": "object"}, "type": "array", "title": "Column Headers", "description": "Headers for report data columns"}}, "type": "object", "required": ["report_name", "generated_at"], "title": "ReportMetadata", "description": "Common metadata for all report responses."}, "ReportResponseAdjusterPerformance": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"items": {"$ref": "#/components/schemas/AdjusterPerformanceItem"}, "type": "array", "title": "Data", "description": "Adjuster performance data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseAdjusterPerformance", "description": "Response schema for Adjuster Performance report."}, "ReportResponseClaimsByStatus": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"items": {"$ref": "#/components/schemas/ClaimsByStatusItem"}, "type": "array", "title": "Data", "description": "Claims by status data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseClaimsByStatus", "description": "Response schema for Claims by Status report."}, "ReportResponseClaimsByType": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"items": {"$ref": "#/components/schemas/ClaimsByTypeItem"}, "type": "array", "title": "Data", "description": "Claims by type data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseClaimsByType", "description": "Response schema for Claims by Type report."}, "ReportResponseClaimsKpis": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"$ref": "#/components/schemas/ClaimsKpiData", "description": "Claims KPI data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseClaimsKpis", "description": "Response schema for Claims KPI report."}, "ReportResponseClaimsOverTime": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"items": {"$ref": "#/components/schemas/ClaimsOverTimeItem"}, "type": "array", "title": "Data", "description": "Claims over time data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseClaimsOverTime", "description": "Response schema for Claims Over Time report."}, "ReportResponseFinancialKpis": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"$ref": "#/components/schemas/FinancialKpiData", "description": "Financial KPI data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponseFinancialKpis", "description": "Response schema for Financial KPI report."}, "ReportResponsePaymentsVsReserves": {"properties": {"report_metadata": {"$ref": "#/components/schemas/ReportMetadata", "description": "Report metadata"}, "data": {"items": {"$ref": "#/components/schemas/PaymentReserveTimeItem"}, "type": "array", "title": "Data", "description": "Payments vs reserves data"}}, "type": "object", "required": ["report_metadata", "data"], "title": "ReportResponsePaymentsVsReserves", "description": "Response schema for Payments vs Reserves report."}, "ReporterRelationship": {"type": "string", "enum": ["INSURED", "CLAIMANT", "ATTORNEY", "AGENT", "OTHER"], "title": "ReporterRelationship", "description": "Relationship of the reporter to the claim/incident."}, "ReserveConfigurationCreate": {"properties": {"claim_type": {"$ref": "#/components/schemas/ClaimType", "description": "Type of claim"}, "reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this reserve is required"}, "minimum_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Minimum Amount", "description": "Minimum required amount"}, "description": {"type": "string", "title": "Description", "description": "Description of the configuration"}}, "type": "object", "required": ["claim_type", "reserve_type", "is_required", "description"], "title": "ReserveConfigurationCreate", "description": "Schema for creating a reserve configuration."}, "ReserveConfigurationInDB": {"properties": {"claim_type": {"$ref": "#/components/schemas/ClaimType", "description": "Type of claim"}, "reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "is_required": {"type": "boolean", "title": "Is Required", "description": "Whether this reserve is required"}, "minimum_amount": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Minimum Amount", "description": "Minimum required amount"}, "description": {"type": "string", "title": "Description", "description": "Description of the configuration"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["claim_type", "reserve_type", "is_required", "description", "id", "created_at", "updated_at"], "title": "ReserveConfigurationInDB", "description": "Schema for reserve configuration in database."}, "ReserveConfigurationUpdate": {"properties": {"is_required": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Required", "description": "Whether this reserve is required"}, "minimum_amount": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Minimum Amount", "description": "Minimum required amount"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Description of the configuration"}}, "type": "object", "title": "ReserveConfigurationUpdate", "description": "Schema for updating a reserve configuration."}, "ReserveCreate": {"properties": {"reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "amount": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Amount", "description": "Reserve amount"}}, "type": "object", "required": ["reserve_type", "amount"], "title": "ReserveCreate", "description": "Schema for creating a reserve."}, "ReserveHistoryInDB": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d", "description": "Associated claim ID"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this reserve history"}, "financials_id": {"type": "string", "format": "uuid", "title": "Financials Id", "description": "Associated financials ID"}, "reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "previous_amount": {"type": "string", "title": "Previous Amount", "description": "Previous reserve amount"}, "new_amount": {"type": "string", "title": "New Amount", "description": "New reserve amount"}, "changed_by_id": {"type": "string", "format": "uuid", "title": "Changed By Id", "description": "User who made the change"}, "changed_at": {"type": "string", "format": "date-time", "title": "Changed At", "description": "When the change was made"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Notes about the change"}}, "type": "object", "required": ["id", "claim_id", "financials_id", "reserve_type", "previous_amount", "new_amount", "changed_by_id", "changed_at"], "title": "ReserveHistoryInDB", "description": "Schema for reserve history in database."}, "ReserveResponse": {"properties": {"reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "amount": {"type": "string", "title": "Amount", "description": "Reserve amount"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "Creation timestamp"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "Last update timestamp"}}, "type": "object", "required": ["reserve_type", "amount", "id", "created_at", "updated_at"], "title": "ReserveResponse", "description": "Schema for reserve in responses."}, "ReserveType": {"type": "string", "enum": ["BODILY_INJURY", "PROPERTY_DAMAGE", "MEDICAL_PAYMENTS", "LOSS_OF_USE", "BUSINESS_INTERRUPTION", "DEFENSE_COST", "ALLOCATED_EXPENSE", "UNALLOCATED_EXPENSE"], "title": "ReserveType", "description": "Types of reserves."}, "ReserveUpdate": {"properties": {"reserve_type": {"$ref": "#/components/schemas/ReserveType", "description": "Type of reserve"}, "amount": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Amount", "description": "Reserve amount"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Notes about the change"}}, "type": "object", "required": ["reserve_type", "amount"], "title": "ReserveUpdate", "description": "Schema for updating a reserve."}, "StatusHistorySchema": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "from_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "From Status"}, "to_status": {"type": "string", "title": "To Status"}, "reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Reason"}, "changed_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Changed By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this status history"}}, "type": "object", "required": ["id", "to_status", "created_at"], "title": "StatusHistorySchema", "description": "Schema for status history responses."}, "SystemConfigurationCreate": {"properties": {"key": {"type": "string", "title": "Key", "description": "Configuration key"}, "value": {"type": "string", "title": "Value", "description": "Configuration value"}, "description": {"type": "string", "title": "Description", "description": "Configuration description"}}, "type": "object", "required": ["key", "value", "description"], "title": "SystemConfigurationCreate", "description": "Schema for creating a system configuration."}, "SystemConfigurationInDB": {"properties": {"key": {"type": "string", "title": "Key", "description": "Configuration key"}, "value": {"type": "string", "title": "Value", "description": "Configuration value"}, "description": {"type": "string", "title": "Description", "description": "Configuration description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["key", "value", "description", "id", "created_at", "updated_at"], "title": "SystemConfigurationInDB", "description": "Schema for system configuration in DB."}, "SystemConfigurationUpdate": {"properties": {"value": {"type": "string", "title": "Value", "description": "Configuration value"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Configuration description"}}, "type": "object", "required": ["value"], "title": "SystemConfigurationUpdate", "description": "Schema for updating a system configuration."}, "TaskAssignSchema": {"properties": {"assignee_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assignee <PERSON>", "description": "UUID of the user to assign the task to, or null to unassign"}}, "type": "object", "required": ["assignee_id"], "title": "TaskAssignSchema"}, "TaskCreateRequestBody": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"$ref": "#/components/schemas/TaskPriority", "default": "MEDIUM"}, "status": {"$ref": "#/components/schemas/TaskStatus", "default": "PENDING"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "assigned_to": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To"}}, "type": "object", "required": ["title"], "title": "TaskCreateRequestBody", "description": "Schema for the request body when creating a task.\n\nclaim_id is not included here as it's derived from the path."}, "TaskPriority": {"type": "string", "enum": ["LOW", "MEDIUM", "HIGH", "URGENT"], "title": "TaskPriority", "description": "Task priority levels."}, "TaskRead": {"properties": {"title": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"$ref": "#/components/schemas/TaskPriority", "default": "MEDIUM"}, "status": {"$ref": "#/components/schemas/TaskStatus", "default": "PENDING"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "hr_id": {"type": "string", "title": "Hr Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this task"}, "assignee": {"anyOf": [{"$ref": "#/components/schemas/UserReadBasic"}, {"type": "null"}]}, "creator": {"anyOf": [{"$ref": "#/components/schemas/UserReadBasic"}, {"type": "null"}]}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["title", "id", "hr_id", "claim_id", "created_at", "updated_at"], "title": "TaskRead", "description": "Schema for representing a task in API responses."}, "TaskSchema": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "title": {"type": "string", "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"type": "string", "title": "Priority"}, "status": {"type": "string", "title": "Status"}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}, "assigned_to": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To"}, "created_by": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Created By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["id", "title", "priority", "status", "created_at", "updated_at"], "title": "TaskSchema", "description": "Schema for task responses."}, "TaskStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "COMPLETED", "BLOCKED", "CANCELLED"], "title": "TaskStatus", "description": "Task status values."}, "TaskStatusSchema": {"properties": {"status": {"$ref": "#/components/schemas/TaskStatus", "description": "The new status for the task"}}, "type": "object", "required": ["status"], "title": "TaskStatusSchema"}, "TaskUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Title"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "priority": {"anyOf": [{"$ref": "#/components/schemas/TaskPriority"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "#/components/schemas/TaskStatus"}, {"type": "null"}]}, "due_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Due Date"}}, "type": "object", "title": "TaskUpdate", "description": "Schema for updating an existing task.\n\nAll fields are optional."}, "TokenResponse": {"properties": {"access_token": {"type": "string", "title": "Access Token", "description": "JWT access token for authentication"}, "refresh_token": {"type": "string", "title": "Refresh <PERSON>", "description": "<PERSON><PERSON> used to refresh the access token"}, "token_type": {"type": "string", "title": "Token Type", "description": "Type of token (usually 'bearer')"}}, "type": "object", "required": ["access_token", "refresh_token", "token_type"], "title": "TokenResponse", "description": "Token response schema."}, "USState": {"type": "string", "enum": ["AL", "AK", "AZ", "AR", "CA", "CO", "CT", "DE", "FL", "GA", "HI", "ID", "IL", "IN", "IA", "KS", "KY", "LA", "ME", "MD", "MA", "MI", "MN", "MS", "MO", "MT", "NE", "NV", "NH", "NJ", "NM", "NY", "NC", "ND", "OH", "OK", "OR", "PA", "RI", "SC", "SD", "TN", "TX", "UT", "VT", "VA", "WA", "WV", "WI", "WY", "UNKNOWN"], "title": "USState", "description": "US States."}, "UserCreate": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "first_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Last Name"}, "role": {"$ref": "#/components/schemas/UserRole"}, "authority_role": {"$ref": "#/components/schemas/AuthorityRole", "default": "NO_AUTHORITY"}, "status": {"$ref": "#/components/schemas/UserStatus", "default": "PENDING"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"type": "string", "maxLength": 50, "title": "Timezone", "default": "UTC"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "password": {"type": "string", "maxLength": 64, "minLength": 8, "title": "Password", "description": "User password in plain text", "examples": ["StrongP@ssw0rd!"]}}, "type": "object", "required": ["email", "first_name", "last_name", "role", "password"], "title": "UserCreate", "description": "Schema for creating a new user."}, "UserReadBasic": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "email": {"type": "string", "title": "Email"}}, "type": "object", "required": ["id", "email"], "title": "UserReadBasic", "description": "Minimal user representation for embedding in other schemas."}, "UserResponse": {"properties": {"email": {"type": "string", "format": "email", "title": "Email"}, "first_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Last Name"}, "role": {"$ref": "#/components/schemas/UserRole"}, "authority_role": {"$ref": "#/components/schemas/AuthorityRole", "default": "NO_AUTHORITY"}, "status": {"$ref": "#/components/schemas/UserStatus", "default": "PENDING"}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"type": "string", "maxLength": 50, "title": "Timezone", "default": "UTC"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "last_login_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Login At"}, "email_verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Email Verified At"}, "last_password_change_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Password Change At"}, "force_password_change": {"type": "boolean", "title": "Force Password Change"}, "locked_until": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Locked Until"}, "failed_login_attempts": {"type": "integer", "title": "Failed Login Attempts"}, "permissions": {"items": {"type": "string"}, "type": "array", "title": "Permissions"}}, "type": "object", "required": ["email", "first_name", "last_name", "role", "id", "created_at", "updated_at", "force_password_change", "failed_login_attempts"], "title": "UserResponse", "description": "Schema for user data in API responses.", "example": {"authority_role": "BASIC", "created_at": "2024-01-01T00:00:00Z", "department": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "first_name": "<PERSON>", "id": "123e4567-e89b-12d3-a456-426614174000", "job_title": "Senior Adjuster", "last_name": "<PERSON><PERSON>", "permissions": ["VIEW_OWN_CLAIMS", "CREATE_CLAIMS", "EDIT_ASSIGNED_CLAIMS"], "phone_number": "+1234567890", "role": "ADJUSTER", "status": "ACTIVE", "timezone": "America/New_York", "updated_at": "2024-01-01T00:00:00Z"}}, "UserRole": {"type": "string", "enum": ["ADMIN", "MANAGER", "ADJUSTER", "AGENT", "CUSTOMER"], "title": "UserRole", "description": "User role values."}, "UserSessionResponse": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the session"}, "ip_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Ip Address", "description": "IP address the session was created from"}, "user_agent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Agent", "description": "Browser/client user agent information"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the session was created"}, "last_active_at": {"type": "string", "format": "date-time", "title": "Last Active At", "description": "When the session was last active"}, "expires_at": {"type": "string", "format": "date-time", "title": "Expires At", "description": "When the session will expire"}}, "type": "object", "required": ["id", "created_at", "last_active_at", "expires_at"], "title": "UserSessionResponse", "description": "User session response schema."}, "UserStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE", "PENDING", "SUSPENDED"], "title": "UserStatus", "description": "User status values."}, "UserUpdate": {"properties": {"first_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Last Name"}, "role": {"anyOf": [{"$ref": "#/components/schemas/UserRole"}, {"type": "null"}]}, "authority_role": {"anyOf": [{"$ref": "#/components/schemas/AuthorityRole"}, {"type": "null"}]}, "status": {"anyOf": [{"$ref": "#/components/schemas/UserStatus"}, {"type": "null"}]}, "department": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Department"}, "job_title": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Job Title"}, "phone_number": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone Number"}, "timezone": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Timezone"}, "preferences": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Preferences"}, "password": {"anyOf": [{"type": "string", "maxLength": 64, "minLength": 8}, {"type": "null"}], "title": "Password", "description": "New password in plain text", "examples": ["NewStrongP@ssw0rd!"]}}, "type": "object", "title": "UserUpdate", "description": "Schema for updating user data."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "WitnessCreate": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the witness"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the witness"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the witness"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the witness"}, "statement": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Statement", "description": "Statement provided by the witness"}}, "type": "object", "required": ["name"], "title": "WitnessCreate", "description": "<PERSON><PERSON><PERSON> for creating a new witness."}, "WitnessResponse": {"properties": {"name": {"type": "string", "maxLength": 200, "minLength": 1, "title": "Name", "description": "Full name of the witness"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the witness"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the witness"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the witness"}, "statement": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Statement", "description": "Statement provided by the witness"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "claim_id": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON>d"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Claim Number", "description": "Claim number associated with this witness"}}, "type": "object", "required": ["name", "id", "claim_id", "created_at", "updated_at"], "title": "WitnessResponse", "description": "Schema for witness response data."}, "WitnessUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 200, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Full name of the witness"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email", "description": "Email address of the witness"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone", "description": "Contact phone number of the witness"}, "address": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Address", "description": "Address of the witness"}, "statement": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Statement", "description": "Statement provided by the witness"}}, "type": "object", "title": "WitnessUpdate", "description": "<PERSON><PERSON><PERSON> for updating an existing witness."}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "/api/v1/auth/token"}}}}}}