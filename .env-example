# Claimentine Environment Variables Example
# Copy this file to .env and fill in your local configuration.

# --- Application Settings ---
# Comma-separated list of allowed CORS origins (e.g., "http://localhost:3000,https://yourdomain.com")
# CLAIMENTINE_BACKEND_CORS_ORIGINS=["http://localhost:3000"]

# --- Database Settings ---
# Ensure this database exists on your PostgreSQL server
CLAIMENTINE_POSTGRES_HOST=localhost
CLAIMENTINE_POSTGRES_PORT=5432
CLAIMENTINE_POSTGRES_USER=postgres
CLAIMENTINE_POSTGRES_PASSWORD=postgres
CLAIMENTINE_POSTGRES_DB=claimentine
# Optional: uncomment and set if you need a full DSN override
# CLAIMENTINE_POSTGRES_URL=postgresql://user:password@host:port/db

# Database connection pool (defaults are usually fine)
# CLAIMENTINE_DB_POOL_SIZE=5
# CLAIMENTINE_DB_MAX_OVERFLOW=10
# CLAIMENTINE_DB_ECHO_LOG=false

# --- JWT Authentication Settings ---
# <PERSON><PERSON><PERSON> THIS IN PRODUCTION to a strong, randomly generated secret!
CLAIMENTINE_JWT_SECRET_KEY=secret
CLAIMENTINE_JWT_ALGORITHM=HS256
CLAIMENTINE_JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
CLAIMENTINE_JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# --- Token Expiry Settings ---
CLAIMENTINE_PASSWORD_RESET_TOKEN_EXPIRE_HOURS=24
CLAIMENTINE_EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=48

# --- Google Cloud Storage (Optional - for document storage) ---
# Path to your Google Cloud service account key file
# CLAIMENTINE_GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/gcp-key.json
# Name of the GCS bucket for storing documents
CLAIMENTINE_GCS_BUCKET_NAME=claimentine-documents-dev

# --- Logging Settings ---
# Where logs are stored relative to the backend directory
# CLAIMENTINE_LOG_PATH=backend/logs
# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
CLAIMENTINE_LOG_LEVEL=INFO
# Maximum log file size before rotation
# CLAIMENTINE_LOG_FILE_SIZE_MB=10
# How many days to keep rotated log files
# CLAIMENTINE_LOG_RETENTION_DAYS=7 