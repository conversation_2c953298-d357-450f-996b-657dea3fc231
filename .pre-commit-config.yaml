repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: debug-statements

-   repo: https://github.com/psf/black
    rev: 24.1.1
    hooks:
    -   id: black
        args: [--line-length=120]

-   repo: https://github.com/pycqa/isort
    rev: 5.13.2
    hooks:
    -   id: isort
        args: ["--profile", "black", "--line-length=120"]

-   repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.3.2
    hooks:
    -   id: ruff
        args: [--line-length=120]

-   repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.9.0
    hooks:
    -   id: mypy
        additional_dependencies: [types-all] 