# Claimentine CLI Tutorial

## Introduction

This tutorial provides a comprehensive guide to using the Claimentine Command Line Interface (CLI). The Claimentine CLI is a powerful tool for administering the Elysian Claims Management System. It allows users to perform a variety of administrative tasks, including:

*   Managing users and their permissions.
*   Overseeing claims and customer data.
*   Configuring system-wide settings.
*   Interacting with various other modules of the system.

### Prerequisites

Before you begin, ensure you have the following prerequisites in place:

*   **Python 3.11+:** The CLI is built with Python and requires version 3.11 or higher.
*   **Poetry:** Dependency management is handled by Poetry.
*   **CLI Installation:** You need to have the Claimentine CLI installed. Please refer to the `cli/README.md` file for detailed instructions on how to install the CLI.
*   **Running Claimentine Backend:** The CLI interacts with a Claimentine backend server. Ensure that a backend server instance is running and accessible from your network.
*   **Permissions/Roles:** To perform administrative actions, your user account must have the necessary permissions and roles assigned within the Elysian Claims Management System.

## Authentication and Session Management (`auth`)

The `auth` command group is your gateway to the Claimentine system. It handles logging in, managing your sessions, and ensuring your CLI interactions are secure.

### Logging In

To start using the Claimentine CLI, you first need to log in. This authenticates your identity with the backend server.

**Command:**
```bash
poetry run clm auth login --email <your-email> --password <your-password>
```

**Example:**
```bash
poetry run clm auth login --email <EMAIL> --password "securepassword123"
```
Upon successful login, the CLI will store your authentication tokens securely for subsequent commands.

### Checking Authentication Status

You can check your current authentication status at any time. This is useful to confirm if you are logged in and which user you are logged in as.

**Command:**
```bash
poetry run clm auth status
```

**Example Output (if logged in):**
```
Logged in as: <EMAIL>
Access token valid until: 2023-10-27 10:30:00
Refresh token valid until: 2023-11-26 09:30:00
```

**Example Output (if not logged in):**
```
Not logged in. Please use 'clm auth login' to authenticate.
```

### Listing Active Sessions

You might have multiple active sessions if you've logged in from different devices or multiple times. You can list all your active sessions.

**Command:**
```bash
poetry run clm auth session list
```

**Example Output:**
```
Active Sessions:
- Session ID: abc123xyz789, Created: 2023-10-26 09:30:00, Last Used: 2023-10-26 14:15:00, User Agent: Claimentine CLI v0.1.0
- Session ID: def456uvw012, Created: 2023-10-25 11:00:00, Last Used: 2023-10-25 11:05:00, User Agent: Claimentine CLI v0.1.0
```
Each session will have a unique ID, creation time, last used time, and information about the client.

### Revoking a Specific Session

If you want to end a specific session (e.g., a session on a device you no longer use), you can revoke it using its Session ID.

**Command:**
```bash
poetry run clm auth session revoke <session-id>
```

**Example:**
```bash
poetry run clm auth session revoke abc123xyz789
```
This will invalidate the session, requiring a new login from that client.

### Logging Out

When you're finished using the CLI, it's good practice to log out. This will clear your local authentication tokens.

**Command:**
```bash
poetry run clm auth logout
```

**Example:**
```bash
poetry run clm auth logout
```
You will receive a confirmation message upon successful logout.

### Refreshing Access Token

Access tokens have a limited lifespan for security reasons. If your access token expires while you are still working, you can use your refresh token to get a new access token without needing to log in again with your credentials. The CLI often handles this automatically, but you can also trigger it manually.

**Command:**
```bash
poetry run clm auth refresh
```

**Example:**
```bash
poetry run clm auth refresh
```
This command will attempt to use your stored refresh token to obtain a new access token.

## CLI Configuration (`config`)

The Claimentine CLI stores its configuration locally, allowing you to customize its behavior, such as the backend API URL it connects to. The `config` command group helps you manage these settings.

### Showing Current Configuration

To see all current configuration settings, including the active environment and its API URL:

**Command:**
```bash
poetry run clm config show
```

**Example Output:**
```
Current Environment: dev
API URL (dev): http://localhost:8000
API URL (staging): https://staging-api.claimentine.com
API URL (prod): https://api.claimentine.com
Default Log Level: INFO
```
This output shows you the currently active environment (`dev`) and the API URLs configured for all available environments.

### Setting a Specific Configuration Value

You can set or update individual configuration values. The most common one to set is the API URL (using the key `api-url`) for the currently active environment.

**Command:**
```bash
poetry run clm config set <key> <value>
```

**Example (setting API URL for the current environment):**
```bash
poetry run clm config set api-url http://localhost:8080
```
If you are in the `dev` environment, this command will change the `api-url` for `dev` to `http://localhost:8080`.

**Note:** For managing environment-specific URLs, the `config env-url` command (explained below) is often more convenient.

### Initializing Configuration

If you haven't used the CLI before or if your configuration file is missing, you'll need to initialize it. This creates a default configuration file.

**Command:**
```bash
poetry run clm config init
```

**Example:**
```bash
poetry run clm config init
```
This will create a configuration file with default settings, usually in a standard user configuration directory.

### Resetting Configuration

You can reset the configuration to its default state.

**To reset all configuration values:**

**Command:**
```bash
poetry run clm config reset
```
You will be prompted for confirmation before all settings are wiped.

**To reset a specific configuration value to its default:**

**Command:**
```bash
poetry run clm config reset <key> [--yes]
```

**Example (resetting the API URL for the current environment):**
```bash
poetry run clm config reset api-url --yes
```
The `--yes` flag automatically confirms the reset, skipping the prompt.

### Managing Environments

The CLI supports multiple environments (e.g., `dev`, `staging`, `prod`) to easily switch between different backend servers.

#### Switching Between Environments

To switch the active environment:

**Command:**
```bash
poetry run clm config env <env_name>
```

**Examples:**
```bash
# Switch to the development environment
poetry run clm config env dev

# Switch to the staging environment
poetry run clm config env staging

# Switch to the production environment
poetry run clm config env prod
```
After switching, subsequent CLI commands will use the API URL and other settings configured for that environment.

#### Setting API URLs for Different Environments

You can define the API URL for each specific environment. This is useful when you work with multiple backend instances (development, testing, production).

**Command:**
```bash
poetry run clm config env-url <env_name> <url>
```

**Examples:**
```bash
# Set the API URL for the 'dev' environment
poetry run clm config env-url dev http://localhost:8000

# Set the API URL for the 'staging' environment
poetry run clm config env-url staging https://staging-api.claimentine.com

# Set the API URL for the 'prod' environment
poetry run clm config env-url prod https://api.claimentine.com
```
These URLs are stored in the configuration, and the CLI uses the URL corresponding to the currently active environment (set via `poetry run clm config env <env_name>`).

## User Management (`users`)

The `users` command group allows administrators to manage user accounts in the Elysian Claims Management System. This includes creating, viewing, updating, and deleting users, as well as assigning roles.

**Permissions:** Most `users` commands require `SYSTEM_ADMIN` or `TEAM_MANAGER` roles.

### Listing Users

You can list all users in the system or filter them based on criteria like role or status. The `[options]` part in the command below refers to flags like `--role` or `--status`.

**Command:**
```bash
poetry run clm users list [options]
```

**Examples:**
```bash
# List all users
poetry run clm users list

# List all users with the 'CLAIMS_ADJUSTER' role
poetry run clm users list --role CLAIMS_ADJUSTER

# List all 'ACTIVE' users
poetry run clm users list --status ACTIVE

# List all 'ACTIVE' users who are 'CLAIMS_ADJUSTER's
poetry run clm users list --role CLAIMS_ADJUSTER --status ACTIVE
```
The output will be a table listing user IDs, names, emails, roles, and statuses.

### Creating a New User

To create a new user, you need to provide their email, a temporary password, first name, last name, and assign them a role.

**Command:**
```bash
poetry run clm users create --email <email-address> --password <password> \
                            --first-name <first_name> --last-name <last_name> \
                            --role <role>
```

**Example:**
```bash
poetry run clm users create --email <EMAIL> --password "Password123!" \
                            --first-name Jane --last-name Doe \
                            --role CLAIMS_ADJUSTER
```
Upon successful creation, the system will output the details of the newly created user, including their system-generated User ID.

### Getting User Details

You can retrieve detailed information about a specific user either by their User ID or their email address.

**Command:**
```bash
poetry run clm users get (<user-id> | --email <email-address>)
```

**Examples:**
```bash
# Get user details by User ID
poetry run clm users get a1b2c3d4-e5f6-7890-1234-567890abcdef

# Get user details by email
poetry run clm users get --email <EMAIL>
```
This will display all available information for the specified user, such as their full name, email, role, status, department, job title, etc.

### Updating User Information

Existing user details can be modified. You can update various attributes such as their role, status, department, or job title. The `[options]` part refers to flags like `--department` or `--status`.

**Command:**
```bash
poetry run clm users update (<user-id> | --email <email-address>) [options]
```

**Examples:**
```bash
# Update user's department and job title by User ID
poetry run clm users update a1b2c3d4-e5f6-7890-1234-567890abcdef \
                            --department "Senior Claims" \
                            --job-title "Senior Claims Adjuster"

# Update user's status to 'INACTIVE' by email
poetry run clm users update --email <EMAIL> --status INACTIVE
```
The command will confirm the update and display the modified user details.

### Deleting a User

To remove a user from the system, you can delete them using their User ID or email address. This is a permanent action.

**Command:**
```bash
poetry run clm users delete (<user-id> | --email <email-address>) [--yes]
```

**Examples:**
```bash
# Delete a user by User ID (will prompt for confirmation)
poetry run clm users delete a1b2c3d4-e5f6-7890-1234-567890abcdef

# Delete a user by email, skipping confirmation
poetry run clm users delete --email <EMAIL> --yes
```
The `--yes` flag bypasses the confirmation prompt, deleting the user immediately.

### Available Roles

When creating or updating users, you will assign them a role. Roles define the user's permissions within the Elysian Claims Management System. The available roles are:

*   **`SYSTEM_ADMIN`**: Full system access. Can manage all aspects of the system, including users, system configurations, and all data.
*   **`TEAM_MANAGER`**: Allows management of teams and workflows. Can typically manage users within their own team or department.
*   **`CLAIMS_SUPERVISOR`**: Permits supervision and approval of claims. Can manage claims and related data, and often oversee a team of adjusters.
*   **`CLAIMS_ADJUSTER`**: Standard role for processing claims. Can manage assigned claims, documents, and notes related to those claims.
*   **`READ_ONLY`**: Provides view-only access to system data. Cannot make any changes. Useful for auditing or reporting purposes.

Choose the role that best fits the user's responsibilities.

## System Administration (`system`)

The `system` command group provides tools for high-level system administration tasks. These commands are typically used during initial setup or for managing global system settings.

**Permissions:** Most `system` commands require the `SYSTEM_ADMIN` role.

### Initializing the Database

This command is used to set up or initialize the database schema. It's often a one-time operation when deploying a new instance of the Elysian Claims Management System.

**Command:**
```bash
poetry run clm system initialize-db
```

**Example:**
```bash
poetry run clm system initialize-db
```
You will typically be prompted for confirmation before the database initialization proceeds. This command ensures all necessary tables and structures are created in the database.

### Listing System Configurations

The system stores various global configurations (e.g., document retention policies, API integration keys). You can list all such configurations.

**Command:**
```bash
poetry run clm system config list
```

**Example Output:**
```
System Configurations:
- ID: cfg_abc123, Key: DOCUMENT_RETENTION_DAYS, Value: 180, Description: Number of days documents are retained.
- ID: cfg_def456, Key: MAX_UPLOAD_SIZE_MB, Value: 100, Description: Maximum file upload size in Megabytes.
- ID: cfg_ghi789, Key: MAINTENANCE_MODE, Value: false, Description: System maintenance status.
```
The output will show a list of configuration items, each with an ID, a key (name), its current value, and a description.

### Showing Details of a Specific System Configuration

To view the details of a single system configuration item, you can use its key.

**Command:**
```bash
poetry run clm system config show --key <key_name>
```

**Example:**
```bash
poetry run clm system config show --key "DOCUMENT_RETENTION_DAYS"
```

**Example Output:**
```
ID: cfg_abc123
Key: DOCUMENT_RETENTION_DAYS
Value: 180
Description: Number of days documents are retained.
Last Modified: 2023-05-01 10:00:00
```

### Updating a System Configuration

System administrators can modify existing system configurations. You'll need the ID of the configuration item to update it.

**Command:**
```bash
poetry run clm system config update <config-id> --value <new-value> [--description <new-description>]
```

**Example:**
```bash
# Update the document retention period to 90 days
poetry run clm system config update cfg_abc123 --value "90"

# Update the max upload size and its description
poetry run clm system config update cfg_def456 --value "200" --description "Maximum file upload size in Megabytes (increased)"
```
The command will confirm the update. The `--description` parameter is optional; if omitted, the existing description will be retained.
It's crucial to understand the impact of changing system configurations, as they can affect the overall behavior and operation of the Elysian Claims Management System.

## Other Management Commands

Beyond authentication, configuration, user, and system management, the Claimentine CLI provides a rich set of commands for managing various aspects of the claims process and related data. This section provides a brief overview and examples for some of these other important command groups. These include dedicated command groups for:

*   Claims (`claims`)
*   Customers (`customers`)
*   First Notice of Loss (`fnols`)
*   Documents (`documents`)
*   Notes (`notes`)
*   Tasks (`tasks`)
*   Recovery (`recovery`)
*   Witnesses (`witnesses`)
*   Attorneys (`attorneys`)
*   Audit Logs (`audit`)
*   Metrics (`metrics`)
*   Reports (`reports`)

This tutorial section will highlight a few common operations for some of these groups.

### Claims Management (`claims`)

The `claims` group is used to manage insurance claims.

**Listing Claims:**
```bash
# List all claims
poetry run clm claims list

# List claims with a specific status and type, limiting the output
poetry run clm claims list --status OPEN --type PROPERTY --limit 10
```

**Getting Claim Details:**
```bash
# Get details for a specific claim by its ID
poetry run clm claims get <claim-id>

# Get claim details by its claim number
poetry run clm claims get --number "CLM-2023-00123"
```

### Customer Management (`customers`)

The `customers` group handles operations related to customer information.

**Listing Customers:**
```bash
# List all customers
poetry run clm customers list

# Search for customers by name and limit the results
poetry run clm customers list --search "Smith" --limit 20
```

**Getting Customer Details:**
```bash
# Get details for a specific customer by their ID
poetry run clm customers get <customer-id>

# Get customer details by their email address
poetry run clm customers get --email <EMAIL>
```

### Document Management (`documents`)

The `documents` group allows for management of documents associated with claims or other entities. *(Specific commands for `documents` are not detailed in the main `cli/README.md`, but would typically include uploading, downloading, listing, and deleting documents.)*

**Example (Conceptual - actual commands may vary):**
```bash
# List documents for a specific claim
# poetry run clm documents list --claim-id <claim-id>

# Download a specific document
# poetry run clm documents download <document-id> --output-path /path/to/save
```

### Discovering More Commands and Options

The examples above are just a small sample of what's available. For a more comprehensive list of all command groups and their primary subcommands, please refer to the `cli/README.md` file.

To explore the full capabilities of each command group and their specific subcommands, including all available options and parameters, the `--help` flag is invaluable.

**Using `--help`:**

```bash
# Get help for the 'claims' command group
poetry run clm claims --help

# Get help for the 'claims list' subcommand
poetry run clm claims list --help

# Get help for the 'customers create' subcommand
poetry run clm customers create --help
```
Using `--help` will provide you with the most up-to-date and detailed information about how to use each command.

## Troubleshooting

This section provides guidance on common issues you might encounter while using the Claimentine CLI.

### Authentication Errors

*   **Login Fails:**
    *   Double-check your email and password for typos.
    *   Ensure the Claimentine backend server is running and accessible from your machine.
    *   Verify that the API URL is correctly configured (see "Connection Issues" below).
*   **Token Expired:**
    *   If commands start failing with "token expired" or similar errors, your session may have timed out.
    *   Try refreshing your access token: `poetry run clm auth refresh`
    *   If refreshing doesn't work or isn't supported, log in again: `poetry run clm auth login --email <your-email> --password <your-password>`

### Command Not Found / Incorrect Usage

*   **Check Syntax:** Carefully review the command syntax. Typos or incorrect command names are common.
*   **Use `--help`:** The most reliable way to find the correct syntax and available options for any command or subcommand is to use the `--help` flag.
    *   Example: `poetry run clm users --help` or `poetry run clm users list --help`.

### Connection Issues

*   **Verify API URL:**
    *   Check the currently configured API URL: `poetry run clm config show`
    *   Ensure it points to the correct address and port of your Claimentine backend server.
    *   If incorrect, set it for the current environment: `poetry run clm config set api-url http://<your-backend-host>:<port>`
    *   Alternatively, set it for a specific environment: `poetry run clm config env-url <env_name> http://<your-backend-host>:<port>`
*   **Backend Server Accessibility:**
    *   Confirm that the backend server is running.
    *   Check for any network issues (firewalls, VPNs, etc.) that might be preventing the CLI from reaching the server.
    *   Try pinging the server or accessing its API URL in a web browser if applicable.

### Permission Denied Errors

*   **Check User Roles:** Many CLI operations require specific user roles (e.g., `SYSTEM_ADMIN`, `TEAM_MANAGER`).
*   If you receive an "access denied," "forbidden," or similar error, your user account may not have the necessary permissions for that action.
*   Use `poetry run clm auth status` to see your current user. Verify their roles within the Elysian Claims Management System.
*   Consult the "Available Roles" section in this tutorial or the system's documentation for more details on permissions.

### Installation / Dependency Problems

*   **Python Version:** Ensure you are using Python 3.11 or higher.
*   **Poetry Setup:** Verify that Poetry is correctly installed and that dependencies were installed without errors using `poetry install`.
*   **Refer to README:** For detailed installation instructions and troubleshooting for initial setup, please consult the `cli/README.md` file.
*   If you encounter persistent issues, try removing the virtual environment (if one exists and you know its name) and reinstalling dependencies:
    ```bash
    # Optional: remove existing virtual environment (be cautious; replace <env_name> with actual environment name if different from default)
    # poetry env remove <env_name> 
    poetry install
    ```

If problems persist after checking these common issues, consider reviewing any error messages in detail, checking logs if available, or seeking assistance from your system administrator or development team.
