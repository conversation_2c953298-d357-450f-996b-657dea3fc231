[tool.poetry]
name = "clm"
version = "0.1.0"
description = "CLI for Claimentine Claims Management System"
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "claimentine_cli", from = "src"}]

[tool.poetry.scripts]
clm = "claimentine_cli.main:app"

[tool.poetry.dependencies]
python = "^3.11"
typer = {extras = ["all"], version = "^0.9.0"}
rich = "^13.7.0"
httpx = "^0.26.0"
pydantic = "^2.0.0"
pydantic-settings = "^2.1.0"
python-dotenv = "^1.0.0"
autoflake = "^2.3.1"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-cov = "^4.1.0"
pytest-order = "^1.2.1"
black = "^24.1.0"
isort = "^5.13.0"
mypy = "^1.8.0"
ruff = "^0.1.0"
pre-commit = "^3.5.0"
flake8 = "^7.2.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 100
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=claimentine_cli --cov-report=term-missing"

[tool.coverage.run]
source = ["claimentine_cli"]
omit = ["tests/*"]

[tool.ruff]
line-length = 100
target-version = "py311"
