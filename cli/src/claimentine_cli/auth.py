"""Authentication utilities for the CLI."""

from claimentine_cli.config import Config


def get_auth_headers() -> dict:
    """
    Get authentication headers for API requests.

    Returns:
        dict: Headers dictionary with Authorization if token is available
    """
    config = Config()
    headers = {
        "Accept": "application/json",
    }

    if config.access_token:
        headers["Authorization"] = f"Bearer {config.access_token}"

    return headers
