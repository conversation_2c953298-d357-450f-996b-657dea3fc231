"""Constants used across the CLI."""

import os

from dotenv import load_dotenv

load_dotenv()  # Load environment variables from .env file


# API Configuration
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
API_V1_PREFIX = "/api/v1"
API_TIMEOUT = 10  # seconds


# Authentication
AUTH_URL = "/auth/token"
REFRESH_URL = "/auth/refresh"
SESSIONS_URL = "/auth/sessions"
TOKEN_FILE = "claimentine_cli_token.json"


# Resource Paths
CLIENTS_PATH = "/clients"
USERS_PATH = "/users"
CLAIMS_PATH = "/claims"
CLAIMS_NUMBER_PATH = f"{CLAIMS_PATH}/number"
FNOLS_PATH = "/fnols"
FNOLS_NUMBER_PATH = f"{FNOLS_PATH}/number"
CONFIG_PATH = "/config"

# Metrics and Reports
METRICS_PATH = "/metrics"
DASHBOARD_METRICS_PATH = f"{METRICS_PATH}/dashboard"
REPORTS_PATH = "/reports"
REPORT_CLAIMS_KPIS_PATH = f"{REPORTS_PATH}/claims-kpis"
REPORT_CLAIMS_BY_TYPE_PATH = f"{REPORTS_PATH}/claims-by-type"
REPORT_CLAIMS_BY_STATUS_PATH = f"{REPORTS_PATH}/claims-by-status"
REPORT_CLAIMS_OVER_TIME_PATH = f"{REPORTS_PATH}/claims-over-time"
REPORT_FINANCIAL_KPIS_PATH = f"{REPORTS_PATH}/financial-kpis"
REPORT_PAYMENTS_VS_RESERVES_PATH = f"{REPORTS_PATH}/payments-vs-reserves"
REPORT_ADJUSTER_PERFORMANCE_PATH = f"{REPORTS_PATH}/adjuster-performance"

# Formatting
DECIMAL_PLACES = 2  # Default decimal places for currency formatting
