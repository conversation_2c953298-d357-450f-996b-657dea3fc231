"""API client for making requests to the backend."""

import os
from typing import Any, Dict, Optional
from urllib.parse import urljoin

import httpx
import typer
from rich.console import Console

console = Console()


class APIClient:
    """HTTP client for API requests."""

    def __init__(self):
        """Initialize API client."""
        self.base_url = os.getenv("CLAIMENTINE_API_URL", "http://localhost:8000")
        self.api_prefix = "/api/v1/"
        self.token = os.getenv("CLAIMENTINE_API_TOKEN")
        self.client = httpx.Client(timeout=30.0)

    def _get_headers(self) -> Dict[str, str]:
        """Get request headers."""
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
        }
        if self.token:
            headers["Authorization"] = f"Bearer {self.token}"
        return headers

    def _handle_response(self, response: httpx.Response) -> Any:
        """Handle API response."""
        try:
            response.raise_for_status()
            return response
        except httpx.HTTPStatusError as e:
            try:
                error = e.response.json()
                message = error.get("message", str(e))
                details = error.get("details", {})
                console.print(f"❌ Error: {message}", style="red")
                if details:
                    console.print("Details:", style="yellow")
                    for error in details.get("errors", []):
                        loc = " -> ".join(str(x) for x in error["loc"])
                        console.print(f"  {loc}: {error['msg']}", style="yellow")
            except Exception:
                message = str(e)
                console.print(f"❌ Error: {message}", style="red")
            console.print(f"URL: {e.request.url}", style="yellow")
            raise typer.Exit(1)
        except Exception as e:
            console.print(f"❌ Error: {str(e)}", style="red")
            raise typer.Exit(1)

    def get(self, path: str, params: Optional[Dict[str, Any]] = None) -> Any:
        """Send GET request."""
        url = urljoin(self.base_url, self.api_prefix + path.lstrip("/"))
        response = self.client.get(url, params=params, headers=self._get_headers())
        return self._handle_response(response)

    def post(self, path: str, json: Optional[Dict[str, Any]] = None) -> Any:
        """Send POST request."""
        url = urljoin(self.base_url, self.api_prefix + path.lstrip("/"))
        response = self.client.post(url, json=json, headers=self._get_headers())
        return self._handle_response(response)

    def patch(self, path: str, json: Optional[Dict[str, Any]] = None) -> Any:
        """Send PATCH request."""
        url = urljoin(self.base_url, self.api_prefix + path.lstrip("/"))
        response = self.client.patch(url, json=json, headers=self._get_headers())
        return self._handle_response(response)

    def delete(self, path: str) -> Any:
        """Send DELETE request."""
        url = urljoin(self.base_url, self.api_prefix + path.lstrip("/"))
        response = self.client.delete(url, headers=self._get_headers())
        return self._handle_response(response)


# Create global API client instance
api_client = APIClient()
