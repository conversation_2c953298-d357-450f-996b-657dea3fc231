"""Formatting utilities for the CLI."""

from decimal import Decimal
from typing import Optional, Union


def format_decimal(
    value: Optional[Union[float, Decimal]], decimal_places: int = 2, prefix: str = "$"
) -> str:
    """Format a decimal value as currency.

    Args:
        value: The decimal value to format
        decimal_places: Number of decimal places to show
        prefix: Currency symbol prefix, default "$"

    Returns:
        A formatted string representation
    """
    if value is None:
        return "N/A"

    if isinstance(value, str):
        try:
            value = float(value)
        except ValueError:
            return value

    # Format with commas as thousand separators and fixed decimal places
    formatted = f"{prefix}{value:,.{decimal_places}f}"
    return formatted


def format_percentage(value: Optional[float], decimal_places: int = 1) -> str:
    """Format a value as a percentage.

    Args:
        value: The value to format (0.5 for 50%)
        decimal_places: Number of decimal places to show

    Returns:
        A formatted percentage string
    """
    if value is None:
        return "N/A"

    # Multiply by 100 and add % sign
    return f"{value * 100:.{decimal_places}f}%"


def format_date(date_str: Optional[str], format_str: str = "%Y-%m-%d") -> str:
    """Format a date string to a more readable format.

    Args:
        date_str: ISO format date string
        format_str: Output format string

    Returns:
        A formatted date string
    """
    if not date_str:
        return "N/A"

    from datetime import datetime

    try:
        # Parse ISO format date and reformat
        dt = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
        return dt.strftime(format_str)
    except ValueError:
        # Return original if parsing fails
        return date_str
