"""Utilities for managing the .env configuration file."""

import os
from pathlib import Path


def get_config_dir() -> Path:
    """Get the configuration directory path."""
    config_dir = Path.home() / ".config" / "claimentine"
    config_dir.mkdir(parents=True, exist_ok=True)
    return config_dir


def get_env_file() -> Path:
    """Get the .env file path."""
    return get_config_dir() / ".env"


def is_debug_mode() -> bool:
    """Check if debug mode is enabled via environment variable."""
    return os.environ.get("CLAIMENTINE_DEBUG", "0").lower() in ("1", "true", "yes", "y", "on")


def read_existing_env_vars() -> dict:
    """Read all existing environment variables from the .env file except API_URL."""
    env_file = get_env_file()
    env_vars = {}

    if not env_file.exists():
        return env_vars

    lines = env_file.read_text().splitlines()
    for line in lines:
        line = line.strip()
        if line and "=" in line and not line.startswith("#"):
            key, value = line.split("=", 1)
            # Preserve all variables except API_URL (which we'll regenerate)
            if key != "CLAIMENTINE_API_URL":
                env_vars[key] = value

    return env_vars


def regenerate_env_file(env_config: dict) -> None:
    """Completely regenerate the .env file from environment configuration.

    This approach eliminates synchronization bugs by treating the .env file
    as a generated artifact derived from environments.json.
    """
    env_file = get_env_file()

    # 1. Preserve all existing environment variables except API_URL
    existing_vars = read_existing_env_vars()

    # 2. Generate new .env content from environment config
    active_env = env_config["active"]
    active_url = env_config["environments"].get(active_env)

    new_env_lines = []

    # Add the active environment's API URL first
    if active_url:
        new_env_lines.append(f"CLAIMENTINE_API_URL={active_url}")

    # Add all other preserved variables
    for key, value in existing_vars.items():
        new_env_lines.append(f"{key}={value}")

    # 3. Write the complete new .env file atomically
    env_file.write_text("\n".join(new_env_lines) + "\n" if new_env_lines else "")


def save_env_var(key: str, value: str) -> None:
    """Save or update a configuration value in the .env file."""
    env_file = get_env_file()
    env_key = f"CLAIMENTINE_{key.upper()}"

    # Create or update .env file
    if not env_file.exists():
        env_file.touch()

    # Read existing content
    lines = []
    if env_file.exists():
        lines = env_file.read_text().splitlines()

    # Update or add the key-value pair
    found = False
    for i, line in enumerate(lines):
        if line.startswith(f"{env_key}="):
            lines[i] = f"{env_key}={value}"
            found = True
            break

    if not found:
        lines.append(f"{env_key}={value}")

    # Write back to file
    env_file.write_text("\n".join(lines) + "\n")


def delete_env_var(key: str) -> bool:
    """Delete a configuration value from the .env file."""
    env_file = get_env_file()
    env_key = f"CLAIMENTINE_{key.upper()}"

    if not env_file.exists():
        return False

    # Read existing content
    lines = env_file.read_text().splitlines()

    # Remove the key-value pair
    new_lines = [line for line in lines if not line.startswith(f"{env_key}=")]

    if len(new_lines) == len(lines):
        return False

    # Write back to file
    env_file.write_text("\n".join(new_lines) + "\n")
    return True


def set_tokens_in_env(access_token: str, refresh_token: str) -> None:
    """Save authentication tokens to the .env file."""
    save_env_var("access_token", access_token)
    save_env_var("refresh_token", refresh_token)


def clear_tokens_from_env() -> None:
    """Clear authentication tokens from the .env file."""
    delete_env_var("access_token")
    delete_env_var("refresh_token")
