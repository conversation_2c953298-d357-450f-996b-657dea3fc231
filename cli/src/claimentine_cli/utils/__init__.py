"""Utility functions for the CLI."""

import json
import uuid
from datetime import datetime
from typing import Optional, Union

import httpx
from rich.console import Console

console = Console()


def is_uuid4(s: str) -> bool:
    """Check if a string is a valid UUID4."""
    try:
        uuid_obj = uuid.UUID(s)
        return uuid_obj.version == 4
    except (ValueError, AttributeError, TypeError):
        return False


def format_datetime(dt: Optional[Union[str, datetime]], date_only: bool = False) -> str:
    """Format a datetime string or object for display.

    Args:
        dt: The datetime to format, either as string or datetime object
        date_only: If True, only format the date part (without time)

    Returns:
        Formatted datetime string
    """
    if not dt:
        return ""
    if isinstance(dt, str):
        dt = datetime.fromisoformat(dt.replace("Z", "+00:00"))

    # Use different format based on date_only flag
    if date_only:
        return dt.strftime("%Y-%m-%d")
    return dt.strftime("%Y-%m-%d %H:%M:%S")


def format_currency(amount: Optional[float]) -> str:
    """Format a currency amount for display."""
    if amount is None:
        return "$0.00"
    return f"${amount:,.2f}"


def format_bool(value: Optional[bool]) -> str:
    """Format a boolean value for display."""
    if value is None:
        return ""
    return "✓" if value else "✗"


def handle_api_error(response: httpx.Response) -> None:
    """Handle API error responses."""
    if response.status_code >= 400:
        console.print(f"[red]Error: {response.status_code} {response.reason_phrase}[/red]")
        try:
            error_data = response.json()
            if isinstance(error_data, dict):
                if "detail" in error_data:
                    console.print(f"[red]Detail: {error_data['detail']}[/red]")
                else:
                    console.print("[red]Response:[/red]")
                    console.print(json.dumps(error_data, indent=2))
            else:
                console.print(f"[red]Response: {error_data}[/red]")
        except Exception:
            console.print(f"[red]Response: {response.text}[/red]")
        exit(1)
