"""Output formatting utilities."""

import json
from typing import Any, Dict, List, Union

import typer
from rich.console import Console

console = Console()


def output_formatter(
    data: Union[Dict[str, Any], List[Dict[str, Any]]],
    format_type: str = "console",
) -> None:
    """Format and output data according to the specified format.

    Args:
        data: The data to format
        format_type: The format to output (json or console)
    """
    if format_type.lower() == "json":
        # Output JSON format
        try:
            print(json.dumps(data, indent=2, default=str))
        except Exception as e:
            console.print(f"[red]Error formatting JSON: {str(e)}[/red]")
            raise typer.Exit(code=1)
    else:
        # For console format, the calling function handles the formatting
        # This is just a pass-through for consistency
        return
