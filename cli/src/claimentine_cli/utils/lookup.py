"""Utility functions for resolving resource identifiers."""

import re
from typing import Optional
from uuid import UUID

import httpx
import typer
from rich.console import Console

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH, FNOLS_NUMBER_PATH, USERS_PATH

console = Console()

# Regex for basic UUID format validation (adjust if stricter validation needed)
UUID_PATTERN = re.compile(
    r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", re.IGNORECASE
)


def is_uuid4(uuid_string: str) -> bool:
    """Check if a string is a valid UUID4."""
    return bool(UUID_PATTERN.match(uuid_string))


def resolve_claim_id(identifier: str, use_id_flag: Optional[bool] = None) -> UUID:
    """Resolve a claim identifier (UUID or number) to a UUID."""
    if (use_id_flag is None and is_uuid4(identifier)) or use_id_flag:
        try:
            return UUID(identifier)
        except ValueError:
            console.print(
                f"[red]Error: Invalid Claim ID format: '{identifier}'. Expected UUID.[/red]"
            )
            raise typer.Exit(code=1)
    else:
        # Identifier is a claim number, look it up
        try:
            # The API client's get method already handles errors via _handle_api_error
            # Use the main get endpoint which handles both ID and number via get_claim_by_identifier dependency
            response = api_client.get(f"{CLAIMS_PATH}/{identifier}")
            claim_detail = response.json()
            return UUID(claim_detail["id"])
        except httpx.HTTPStatusError as e:
            # Let the handler in api_client manage the exit
            raise
        except (KeyError, ValueError) as e:
            console.print(
                f"[red]Error processing API response for claim number '{identifier}': {e}[/red]"
            )
            raise typer.Exit(code=1)


def resolve_user_id(identifier: str, use_id_flag: bool) -> UUID:
    """Resolve a user identifier (UUID or email) to a UUID."""
    if use_id_flag:
        try:
            return UUID(identifier)
        except ValueError:
            console.print(
                f"[red]Error: Invalid User ID format: '{identifier}'. Expected UUID.[/red]"
            )
            raise typer.Exit(code=1)
    else:
        # Identifier is an email, look it up
        try:
            response = api_client.get(
                USERS_PATH, params={"email": identifier}
            )  # Uses central error handler
            users = response.json()
            if not users:
                console.print(f"[red]Error: User with email '{identifier}' not found.[/red]")
                raise typer.Exit(code=1)
            if len(users) > 1:
                console.print(
                    f"[yellow]Warning: Multiple users found for email '{identifier}'. Using the first one.[/yellow]"
                )
            return UUID(users[0]["id"])
        except httpx.HTTPStatusError as e:
            raise  # Let the handler in api_client manage the exit
        except (KeyError, ValueError, IndexError) as e:
            console.print(
                f"[red]Error processing API response for user email '{identifier}': {e}[/red]"
            )
            raise typer.Exit(code=1)


def resolve_user_id_by_email(email: str) -> UUID:
    """Resolve a user email to a UUID."""
    try:
        response = api_client.get(USERS_PATH, params={"email": email})
        users = response.json()
        if not users:
            console.print(f"[red]Error: User with email '{email}' not found.[/red]")
            raise typer.Exit(code=1)
        if len(users) > 1:
            # This case should ideally not happen if email is unique, but handle defensively
            console.print(
                f"[yellow]Warning: Multiple users found for email '{email}'. Using the first one.[/yellow]"
            )
        # Extract UUID from the first user found
        return UUID(users[0]["id"])
    except httpx.HTTPStatusError:
        # Error is handled by api_client, re-raise to let it exit
        raise
    except (KeyError, ValueError, IndexError) as e:
        console.print(f"[red]Error processing API response for user email '{email}': {e}[/red]")
        raise typer.Exit(code=1)


def resolve_fnol_id(identifier: str, use_id_flag: bool) -> UUID:
    """Resolve an FNOL identifier (UUID or number) to a UUID."""
    if use_id_flag:
        try:
            return UUID(identifier)
        except ValueError:
            console.print(
                f"[red]Error: Invalid FNOL ID format: '{identifier}'. Expected UUID.[/red]"
            )
            raise typer.Exit(code=1)
    else:
        # Identifier is an FNOL number, look it up
        try:
            response = api_client.get(
                f"{FNOLS_NUMBER_PATH}/{identifier}"
            )  # Uses central error handler
            fnol_detail = response.json()
            return UUID(fnol_detail["id"])
        except httpx.HTTPStatusError as e:
            raise  # Let the handler in api_client manage the exit
        except (KeyError, ValueError) as e:
            console.print(
                f"[red]Error processing API response for FNOL number '{identifier}': {e}[/red]"
            )
            raise typer.Exit(code=1)
