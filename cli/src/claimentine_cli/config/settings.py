"""CLI Settings and Configuration."""

from pathlib import Path
from typing import Optional

from pydantic import BaseModel, HttpUrl
from pydantic_settings import BaseSettings, SettingsConfigDict


class CliConfig(BaseModel):
    """CLI Configuration Model."""

    api_url: HttpUrl
    timeout: int = 30
    debug: bool = False
    format: str = "table"


class Settings(BaseSettings):
    """Global Settings."""

    # CLI Config
    config_dir: Path = Path.home() / ".cadence"
    config_file: Path = config_dir / "config.json"
    cache_dir: Path = config_dir / "cache"

    # API Settings
    api_url: HttpUrl = HttpUrl("http://localhost:8000")
    api_timeout: int = 30

    # Auth Settings
    auth_token: Optional[str] = None

    model_config = SettingsConfigDict(
        env_prefix="CADENCE_",
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )


# Global settings instance
settings = Settings()
