"""API client for the Claimentine API."""

import json
from typing import Any, Dict, Optional

import httpx
from rich.console import <PERSON>sole

from claimentine_cli.config import Config

console = Console()


def _handle_api_error(response: httpx.Response, error: httpx.HTTPStatusError) -> None:
    """Provide user-friendly feedback for specific API errors."""
    status_code = response.status_code
    url = str(response.request.url)

    console.print(f"\n[bold red]API Error ({status_code}) for {url}[/bold red]")

    try:
        error_data = response.json()
        detail = error_data.get("detail", str(error))
    except json.JSONDecodeError:
        detail = response.text or str(error)

    if status_code == 400:
        console.print(
            "Reason: [yellow]Bad Request[/yellow] - The server could not understand the request."
        )
        if isinstance(detail, dict):
            console.print("Details:", style="yellow")
            console.print(json.dumps(detail, indent=2), style="yellow")
        else:
            console.print(f"Details: [yellow]{detail}[/yellow]")
    elif status_code == 401:
        console.print(
            "Reason: [yellow]Unauthorized[/yellow] - Authentication failed or token is invalid/expired."
        )
        console.print("Hint: Try running 'clm auth login' or check your token.", style="cyan")
    elif status_code == 403:
        console.print(
            "Reason: [yellow]Forbidden[/yellow] - You do not have permission to perform this action."
        )
        console.print(f"Details: [yellow]{detail}[/yellow]")
    elif status_code == 404:
        console.print(
            "Reason: [yellow]Not Found[/yellow] - The requested resource could not be found."
        )
        console.print(f"Details: [yellow]{detail}[/yellow]")
    elif status_code == 422:
        console.print("Reason: [yellow]Unprocessable Entity[/yellow] - Validation error.")
        if isinstance(detail, list):  # FastAPI validation errors
            console.print("Validation Errors:", style="yellow")
            for err in detail:
                loc = " -> ".join(map(str, err.get("loc", [])))
                msg = err.get("msg", "Unknown validation error")
                console.print(f"  - {loc}: {msg}", style="yellow")
        elif isinstance(detail, dict) and "detail" in detail:  # Handle nested detail
            console.print("Details:", style="yellow")
            console.print(json.dumps(detail["detail"], indent=2), style="yellow")
        else:
            console.print(f"Details: [yellow]{detail}[/yellow]")
    elif status_code >= 500:
        console.print(
            "Reason: [red]Server Error[/red] - An unexpected error occurred on the server."
        )
        console.print(f"Details: [red]{detail}[/red]")
    else:
        console.print(f"Reason: [yellow]Unexpected HTTP Error[/yellow]")
        console.print(f"Details: [yellow]{detail}[/yellow]")

    console.print(f"URL: {error.request.url}", style="yellow")
    raise error


class APIClient:
    """HTTP client for making requests to the Claimentine API."""

    API_VERSION = "/api/v1"

    def __init__(self, config: Config):
        """Initialize API client with configuration."""
        self.config = config
        self.client = httpx.Client(timeout=30.0)

    def _get_base_url(self) -> str:
        """Get base URL from latest config."""
        config = Config()
        return f"{config.api_url.rstrip('/')}{self.API_VERSION}"

    def _get_headers(self, is_form_data: bool = False) -> Dict[str, str]:
        """Get request headers including auth token if available."""
        # Reload config to get latest tokens
        config = Config()
        headers = {}
        if not is_form_data:
            headers["Accept"] = "application/json"
        if config.access_token:
            headers["Authorization"] = f"Bearer {config.access_token}"
        return headers

    def get(self, path: str, **kwargs: Any) -> httpx.Response:
        """Make GET request to API endpoint."""
        url = f"{self._get_base_url()}{path}"
        response = self.client.get(url, headers=self._get_headers(), **kwargs)
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            _handle_api_error(e.response, e)
        return response

    def post(self, path: str, **kwargs: Any) -> httpx.Response:
        """Make POST request to API endpoint."""
        url = f"{self._get_base_url()}{path}"
        # Check if form data is being sent
        is_form_data = "data" in kwargs and isinstance(kwargs["data"], dict)
        response = self.client.post(url, headers=self._get_headers(is_form_data), **kwargs)
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            _handle_api_error(e.response, e)
        return response

    def put(self, path: str, **kwargs: Any) -> httpx.Response:
        """Make PUT request to API endpoint."""
        url = f"{self._get_base_url()}{path}"
        response = self.client.put(url, headers=self._get_headers(), **kwargs)
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            _handle_api_error(e.response, e)
        return response

    def patch(self, path: str, **kwargs: Any) -> httpx.Response:
        """Make PATCH request to API endpoint."""
        url = f"{self._get_base_url()}{path}"
        console = Console(stderr=True)

        try:
            response = self.client.patch(url, headers=self._get_headers(), **kwargs)
            response.raise_for_status()
            return response
        except httpx.HTTPStatusError as e:
            _handle_api_error(e.response, e)
        except Exception as e:
            console.print(f"API Error for {url}\nError: {str(e)}", style="red")
            raise

    def delete(self, path: str, **kwargs: Any) -> httpx.Response:
        """Make DELETE request to API endpoint."""
        url = f"{self._get_base_url()}{path}"
        response = self.client.delete(url, headers=self._get_headers(), **kwargs)
        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:
            _handle_api_error(e.response, e)
        return response

    def get_silent(
        self, path: str, suppress_errors: Optional[list] = None, **kwargs: Any
    ) -> Optional[httpx.Response]:
        """Make GET request to API endpoint with optional error suppression.

        Args:
            path: The API endpoint path
            suppress_errors: List of HTTP status codes to suppress (defaults to [404])
            **kwargs: Additional arguments passed to httpx

        Returns:
            Response object if successful, None if error was suppressed
        """
        if suppress_errors is None:
            suppress_errors = [404]

        url = f"{self._get_base_url()}{path}"
        response = self.client.get(url, headers=self._get_headers(), **kwargs)
        try:
            response.raise_for_status()
            return response
        except httpx.HTTPStatusError as e:
            # Suppress specific error codes silently
            if e.response.status_code in suppress_errors:
                return None
            # Let other errors go through normal error handling
            _handle_api_error(e.response, e)
        except Exception:
            # Handle any other errors silently when suppression is requested
            return None


# Create global API client instance
config = Config()
api_client = APIClient(config)
