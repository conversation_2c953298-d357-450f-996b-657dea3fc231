"""Configuration management."""

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from claimentine_cli.utils.env_config import get_env_file  # Import helper


class Config(BaseSettings):
    """Application configuration. Reads settings from .env file."""

    api_url: str = Field(default="http://localhost:8000")
    access_token: str | None = Field(default=None)
    refresh_token: str | None = Field(default=None)

    model_config = SettingsConfigDict(
        env_prefix="CLAIMENTINE_",
        env_file=str(get_env_file()),  # Use imported helper
        env_file_encoding="utf-8",
        case_sensitive=False,
    )
