"""CLI commands for managing claim tasks."""

import json
import sys
from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

import httpx
import typer

# Replace backend import with local enum definitions
# from claimentine.models.task import TaskPriority, TaskStatus
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.utils import format_datetime, handle_api_error


# Define task-related enums locally to avoid backend dependency
class TaskPriority(str, Enum):
    """Task priority levels."""

    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    URGENT = "URGENT"  # Add URGENT to match test expectations
    CRITICAL = "CRITICAL"


class TaskStatus(str, Enum):
    """Task status values."""

    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    BLOCKED = "BLOCKED"
    CANCELED = "CANCELED"


# Define OutputFormat locally
class OutputFormat(str, Enum):
    TEXT = "text"
    JSON = "json"


tasks_app = typer.Typer(name="tasks", help="Manage claim tasks.", no_args_is_help=True)
console = Console()


@tasks_app.command("create")
def create_task(
    claim_identifier: str = typer.Option(
        ...,
        "--claim-identifier",
        "-c",
        help="The ID (UUID) or Number of the claim to associate the task with.",
    ),
    title: str = typer.Option(..., "--title", "-t", help="The title of the task."),
    description: Optional[str] = typer.Option(
        None, "--description", "-d", help="Optional description for the task."
    ),
    priority: TaskPriority = typer.Option(
        TaskPriority.MEDIUM.value,  # Use string value for default
        "--priority",
        "-p",
        help="Priority of the task.",
        case_sensitive=False,
    ),
    status: TaskStatus = typer.Option(
        TaskStatus.PENDING.value,  # Use string value for default
        "--status",
        "-s",
        help="Initial status of the task.",
        case_sensitive=False,
    ),
    due_date: Optional[datetime] = typer.Option(
        None, "--due-date", help="Optional due date (YYYY-MM-DD or ISO format)."
    ),
    assignee_id: Optional[UUID] = typer.Option(
        None, "--assignee", "-a", help="Optional UUID of the user to assign the task to."
    ),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Create a new task associated with a claim (identified by UUID or Number)."""
    try:
        payload = {
            "title": title,
            "priority": priority.value,
            "status": status.value,
        }
        if description is not None:
            payload["description"] = description
        if due_date is not None:
            payload["due_date"] = due_date.isoformat()
        if assignee_id is not None:
            payload["assigned_to"] = str(assignee_id)

        response = api_client.post(f"/claims/{claim_identifier}/tasks/", json=payload)
        response.raise_for_status()  # Check for 4xx/5xx errors
        task_data = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(task_data, indent=2))
        else:
            console.print(f"[green]Task '{task_data.get('hr_id')}' created successfully.[/green]")
            console.print(f"  ID: {task_data.get('id')}")
            console.print(f"  Claim ID (resolved): {task_data.get('claim_id')}")
            console.print(f"  Assigned To: {task_data.get('assignee', {}).get('id') or 'None'}")

    except httpx.HTTPStatusError as e:
        handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to create task: {e}[/red]")
        sys.exit(1)


@tasks_app.command("list")
def list_tasks(
    claim_id: Optional[UUID] = typer.Option(
        None, "--claim-id", "-c", help="Filter tasks by claim ID."
    ),
    assignee_id: Optional[UUID] = typer.Option(
        None, "--assignee", "-a", help="Filter tasks by assignee ID."
    ),
    status: Optional[TaskStatus] = typer.Option(
        None, "--status", "-s", help="Filter tasks by status.", case_sensitive=False
    ),
    title: Optional[str] = typer.Option(
        None, "--title", "-t", help="Filter tasks by partial title match (case-insensitive)."
    ),
    page: int = typer.Option(1, help="Page number", min=1),
    page_size: int = typer.Option(10, help="Items per page", min=1, max=100),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """List tasks with optional filters."""
    try:
        params = {"skip": (page - 1) * page_size, "limit": page_size}
        if claim_id:
            params["claim_id"] = str(claim_id)
        if assignee_id:
            params["assigned_to"] = str(assignee_id)  # API uses assigned_to
        if status:
            params["status"] = status.value
        if title:
            params["title"] = title

        # Use the correct endpoint: GET /tasks/
        response = api_client.get("/tasks/", params=params)
        response.raise_for_status()
        response_data = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(response_data, indent=2))
            return

        # Handle paginated response format
        if isinstance(response_data, dict) and "items" in response_data:
            tasks = response_data["items"]
            total = response_data.get("total", 0)
            skip = response_data.get("skip", 0)
            limit = response_data.get("limit", 10)

            # Show pagination info
            current_page = (skip // limit) + 1
            total_pages = (total + limit - 1) // limit if total > 0 else 1

            if not tasks:
                console.print("No tasks found matching the criteria.")
                return

            table = Table(
                title=f"Tasks (Page {current_page} of {total_pages}, {total} total)",
                show_header=True,
                header_style="bold magenta",
            )
        else:
            # Fallback for old format (shouldn't happen anymore)
            tasks = response_data if isinstance(response_data, list) else []
            if not tasks:
                console.print("No tasks found matching the criteria.")
                return
            table = Table(
                title=f"Tasks (Page {page})", show_header=True, header_style="bold magenta"
            )

        table.add_column("HR_ID", style="dim", width=15)
        table.add_column("Status", width=12)
        table.add_column("Priority", width=8)
        table.add_column("Due Date", width=12)
        table.add_column("Assignee", width=30)
        table.add_column("Title")
        # table.add_column("ID", style="dim", width=36) # Optionally show full UUID

        for task in tasks:
            due_date_display = format_datetime(task.get("due_date"), date_only=True) or "-"
            # Safely handle null assignee
            assignee = task.get("assignee")
            assignee_email = assignee.get("email") if assignee else "Unassigned"
            title_snippet = (
                task.get("title", "")[:50] + "..."
                if len(task.get("title", "")) > 50
                else task.get("title", "")
            )

            table.add_row(
                task.get("hr_id", "N/A"),
                task.get("status", "N/A"),
                task.get("priority", "N/A"),
                due_date_display,
                assignee_email,
                title_snippet,
                # str(task.get("id")) # Optionally show full UUID
            )

        console.print(table)

    except httpx.HTTPStatusError as e:
        handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to list tasks: {e}[/red]")
        sys.exit(1)


@tasks_app.command("view")
def view_task(
    task_identifier: str = typer.Argument(..., help="The UUID or HR_ID of the task to view."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """View the full details of a specific task."""
    try:
        response = api_client.get(f"/tasks/{task_identifier}")
        response.raise_for_status()
        task = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(task, indent=2))
            return

        # Text/Panel Output
        # Safely handle null assignee and creator
        assignee = task.get("assignee")
        creator = task.get("creator")
        assignee_email = assignee.get("email") if assignee else "Unassigned"
        creator_email = creator.get("email") if creator else "N/A"
        details = (
            f"[bold]HR_ID:[/bold]      {task.get('hr_id')}\n"
            f"[bold]UUID:[/bold]       {task.get('id')}\n"
            f"[bold]Claim ID:[/bold]   {task.get('claim_id')}\n"
            f"[bold]Status:[/bold]     {task.get('status')}\n"
            f"[bold]Priority:[/bold]   {task.get('priority')}\n"
            f"[bold]Due Date:[/bold]   {format_datetime(task.get('due_date'), date_only=True) or '-'}\n"
            f"[bold]Assignee:[/bold]   {assignee_email}\n"
            f"[bold]Creator:[/bold]    {creator_email}\n"
            f"[bold]Created:[/bold]    {format_datetime(task.get('created_at'))}\n"
            f"[bold]Updated:[/bold]    {format_datetime(task.get('updated_at'))}\n"
            f"[bold]Completed:[/bold]  {format_datetime(task.get('completed_at')) or '-'}"
        )
        console.print(
            Panel(
                details,
                title=f"Task Details: {task.get('title')}",
                border_style="blue",
            )
        )
        if task.get("description"):
            console.print(Panel(task.get("description"), title="Description", border_style="green"))

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            console.print(f"[red]Error: Task '{task_identifier}' not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to view task: {e}[/red]")
        sys.exit(1)


@tasks_app.command("update")
def update_task(
    task_identifier: str = typer.Argument(..., help="The UUID or HR_ID of the task to update."),
    title: Optional[str] = typer.Option(None, "--title", "-t", help="New title for the task."),
    description: Optional[str] = typer.Option(None, "--description", "-d", help="New description."),
    priority: Optional[TaskPriority] = typer.Option(
        None, "--priority", "-p", help="New priority.", case_sensitive=False
    ),
    status: Optional[TaskStatus] = typer.Option(
        None, "--status", "-s", help="New status.", case_sensitive=False
    ),  # Note: API PUT allows status update
    due_date: Optional[datetime] = typer.Option(None, "--due-date", help="New due date."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Update details of an existing task.

    Assignment should be done using 'tasks assign'.
    """
    payload = {}
    if title is not None:
        payload["title"] = title
    if description is not None:
        payload["description"] = description
    if priority is not None:
        payload["priority"] = priority.value
    if status is not None:
        payload["status"] = status.value
    if due_date is not None:
        # Handle potential timezone issues if backend expects aware datetimes
        payload["due_date"] = due_date.isoformat()

    if not payload:
        console.print("[yellow]No update parameters provided.[/yellow]")
        sys.exit(0)

    try:
        response = api_client.put(f"/tasks/{task_identifier}", json=payload)
        response.raise_for_status()
        updated_task = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(updated_task, indent=2))
        else:
            console.print(
                f"[green]Task '{updated_task.get('hr_id')}' updated successfully.[/green]"
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            console.print(f"[red]Error: Task '{task_identifier}' not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to update task: {e}[/red]")
        sys.exit(1)


@tasks_app.command("assign")
def assign_task(
    task_identifier: str = typer.Argument(..., help="The UUID or HR_ID of the task to assign."),
    assignee: Optional[str] = typer.Option(
        ...,
        "--assignee",
        "-a",
        help="UUID of the user to assign, or 'none'/'unassign' to clear.",
    ),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Assign or unassign a task."""
    assignee_id: Optional[UUID] = None
    if assignee.lower() not in ["none", "unassign", ""]:
        try:
            assignee_id = UUID(assignee)
        except ValueError:
            console.print(f"[red]Invalid UUID format for assignee: {assignee}[/red]")
            sys.exit(1)

    payload = {"assignee_id": str(assignee_id) if assignee_id else None}

    try:
        response = api_client.patch(f"/tasks/{task_identifier}/assign", json=payload)
        response.raise_for_status()
        updated_task = response.json()

        assignee_display = f"to user {assignee_id}" if assignee_id else "(unassigned)"
        if output == OutputFormat.JSON:
            console.print(json.dumps(updated_task, indent=2))
        else:
            console.print(
                f"[green]Task '{updated_task.get('hr_id')}' assigned {assignee_display} successfully.[/green]"
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            # Could be task not found or assignee user not found
            error_detail = e.response.json().get("detail", "Resource not found")
            console.print(f"[red]Error: {error_detail}[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to assign task: {e}[/red]")
        sys.exit(1)


@tasks_app.command("status")
def change_status(
    task_identifier: str = typer.Argument(..., help="The UUID or HR_ID of the task."),
    status: TaskStatus = typer.Option(
        ..., "--status", "-s", help="The new status for the task.", case_sensitive=False
    ),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Change the status of a task."""
    payload = {"status": status.value}

    try:
        response = api_client.patch(f"/tasks/{task_identifier}/status", json=payload)
        response.raise_for_status()
        updated_task = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(updated_task, indent=2))
        else:
            console.print(
                f"[green]Task '{updated_task.get('hr_id')}' status changed to {status.value} successfully.[/green]"
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            console.print(f"[red]Error: Task '{task_identifier}' not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to change task status: {e}[/red]")
        sys.exit(1)


@tasks_app.command("delete")
def delete_task(
    task_identifier: str = typer.Argument(..., help="The UUID or HR_ID of the task to delete."),
    force: bool = typer.Option(False, "--force", "-f", help="Force deletion without confirmation."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Delete a specific task."""
    if output == OutputFormat.TEXT and not force:
        typer.confirm(
            f"Are you sure you want to delete task '{task_identifier}'? This cannot be undone.",
            abort=True,
        )

    try:
        response = api_client.delete(f"/tasks/{task_identifier}")
        response.raise_for_status()
        # API returns the deleted task data
        deleted_task = response.json()

        if output == OutputFormat.JSON:
            # Indicate success along with deleted data
            console.print(json.dumps({"success": True, "deleted_task": deleted_task}, indent=2))
        else:
            console.print(
                f"[green]Task '{deleted_task.get('hr_id', task_identifier)}' deleted successfully.[/green]"
            )

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            msg = f"Error: Task '{task_identifier}' not found."
            if output == OutputFormat.JSON:
                console.print(json.dumps({"success": False, "error": msg}))
            else:
                console.print(f"[red]{msg}[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        msg = f"Failed to delete task: {e}"
        if output == OutputFormat.JSON:
            console.print(json.dumps({"success": False, "error": msg}))
        else:
            console.print(f"[red]{msg}[/red]")
        sys.exit(1)


# --- End of task commands --- #
