"""User management commands."""

import json
from typing import Optional
from uuid import <PERSON>UID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import USERS_PATH
from claimentine_cli.utils.lookup import resolve_user_id

app = typer.Typer(help="User management commands")
console = Console()


@app.command()
def list(
    role: Optional[str] = typer.Option(None, help="Filter by role"),
    authority_role: Optional[str] = typer.Option(
        None,
        help="Filter by authority role (NO_AUTHORITY, BASIC, INTERMEDIATE, SENIOR, SUPERVISOR, MANAGER, UNLIMITED)",
    ),
    status: Optional[str] = typer.Option(
        None, help="Filter by status (ACTIVE, INACTIVE, PENDING, SUSPENDED)"
    ),
    department: Optional[str] = typer.Option(None, help="Filter by department"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """List users with optional filtering."""
    params = {}
    if role:
        params["role"] = role
    if authority_role:
        params["authority_role"] = authority_role
    if status:
        params["status"] = status
    if department:
        params["department"] = department

    response = api_client.get(USERS_PATH, params=params)
    users = response.json()

    if output == "json":
        print(json.dumps(users, indent=2))
        return

    table = Table(title="Users")
    table.add_column("ID", style="cyan")
    table.add_column("Email", style="green")
    table.add_column("Name", style="blue")
    table.add_column("Role", style="yellow")
    table.add_column("Authority", style="magenta")
    table.add_column("Status", style="red")
    table.add_column("Department", style="dim")

    for user in users:
        table.add_row(
            str(user["id"]),
            user["email"],
            f"{user['first_name']} {user['last_name']}",
            user["role"],
            user["authority_role"],
            user["status"],
            user.get("department", ""),
        )

    console.print(table)


@app.command()
def create(
    email: str = typer.Option(..., help="User email"),
    password: str = typer.Option(..., help="User password", prompt=True, hide_input=True),
    first_name: str = typer.Option(..., help="User first name"),
    last_name: str = typer.Option(..., help="User last name"),
    role: str = typer.Option(..., help="User role (ADMIN, MANAGER, ADJUSTER, AGENT, CUSTOMER)"),
    authority_role: str = typer.Option(
        "NO_AUTHORITY",
        help="Authority role (NO_AUTHORITY, BASIC, INTERMEDIATE, SENIOR, SUPERVISOR, MANAGER, UNLIMITED)",
    ),
    department: Optional[str] = typer.Option(None, help="User department"),
    job_title: Optional[str] = typer.Option(None, help="User job title"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Create a new user."""
    data = {
        "email": email,
        "password": password,
        "first_name": first_name,
        "last_name": last_name,
        "role": role,
        "authority_role": authority_role,
        "department": department,
        "job_title": job_title,
    }

    response = api_client.post(USERS_PATH, json=data)
    user = response.json()

    if output == "json":
        print(json.dumps(user, indent=2))
        return

    console.print(f"Created user {user['email']} with ID {user['id']}", style="green")


@app.command()
def update(
    user_identifier: str = typer.Argument(..., help="User email or UUID (if --uuid flag is used)"),
    uuid: bool = typer.Option(False, "--uuid", help="Treat identifier as UUID instead of email"),
    email: Optional[str] = typer.Option(None, help="New email"),
    first_name: Optional[str] = typer.Option(None, help="New first name"),
    last_name: Optional[str] = typer.Option(None, help="New last name"),
    role: Optional[str] = typer.Option(
        None, help="New role (ADMIN, MANAGER, ADJUSTER, AGENT, CUSTOMER)"
    ),
    authority_role: Optional[str] = typer.Option(
        None,
        help="New authority role (NO_AUTHORITY, BASIC, INTERMEDIATE, SENIOR, SUPERVISOR, MANAGER, UNLIMITED)",
    ),
    status: Optional[str] = typer.Option(
        None, help="Set user status (ACTIVE, INACTIVE, PENDING, SUSPENDED)"
    ),
    department: Optional[str] = typer.Option(None, help="New department"),
    job_title: Optional[str] = typer.Option(None, help="New job title"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Update user details."""
    # Get user ID based on identifier type
    user_id = resolve_user_id(user_identifier, use_id_flag=uuid)

    data = {
        k: v
        for k, v in {
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
            "role": role,
            "authority_role": authority_role,
            "status": status,
            "department": department,
            "job_title": job_title,
        }.items()
        if v is not None
    }

    response = api_client.patch(f"{USERS_PATH}/{user_id}", json=data)
    user = response.json()

    if output == "json":
        print(json.dumps(user, indent=2))
        return

    console.print(f"Updated user {user['email']}", style="green")


@app.command()
def get(
    user_id: UUID = typer.Argument(..., help="User ID to get details for"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Get detailed user information."""
    response = api_client.get(f"{USERS_PATH}/{user_id}")
    user = response.json()

    if output == "json":
        print(json.dumps(user, indent=2))
        return

    table = Table(title=f"User Details - {user['email']}")
    table.add_row("ID", str(user["id"]))
    table.add_row("Email", user["email"])
    table.add_row("First Name", user["first_name"])
    table.add_row("Last Name", user["last_name"])
    table.add_row("Role", user["role"])
    table.add_row("Authority Role", user["authority_role"])
    table.add_row("Status", user["status"])
    table.add_row("Department", user.get("department", ""))
    table.add_row("Job Title", user.get("job_title", ""))
    table.add_row("Created At", str(user["created_at"]))
    table.add_row("Updated At", str(user["updated_at"]))

    console.print(table)

    # Display permissions in a separate table
    if user.get("permissions"):
        permissions_table = Table(title="User Permissions")
        permissions_table.add_column("Permission", style="green")
        for permission in sorted(user["permissions"]):
            permissions_table.add_row(permission)
        console.print("\n")
        console.print(permissions_table)


@app.command()
def delete(
    user_identifier: str = typer.Argument(..., help="User email or UUID (if --uuid flag is used)"),
    uuid: bool = typer.Option(False, "--uuid", help="Treat identifier as UUID instead of email"),
    yes: bool = typer.Option(False, "--yes", "-y", help="Bypass confirmation prompt"),
) -> None:
    """Delete a user."""
    # Get user ID based on identifier type
    user_id = resolve_user_id(user_identifier, use_id_flag=uuid)

    # Get user details first for confirmation message
    try:
        get_response = api_client.get(f"{USERS_PATH}/{user_id}")
        user_data = get_response.json()
        user_email = user_data.get("email", user_identifier)

        # Confirmation prompt
        if not yes:
            typer.confirm(
                f"Are you sure you want to delete user {user_email} (ID: {user_id})?",
                abort=True,
            )

        # Call the delete API endpoint
        api_client.delete(f"{USERS_PATH}/{user_id}")
        console.print(f"Successfully deleted user {user_email} (ID: {user_id})", style="green")

    except Exception as e:
        # API client handles HTTP errors, catch others just in case
        console.print(f"[red]Error deleting user: {e}[/red]")
        raise typer.Exit(1)


# Create a subcommand group for 'me' commands
me_app = typer.Typer(help="Current user subcommands")
app.add_typer(me_app, name="me")


# Move the existing 'me' command to be the default for the me_app
@me_app.callback(invoke_without_command=True)
def me_callback(
    ctx: typer.Context,
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Get current logged-in user's details."""
    # Only run this if no subcommand was invoked
    if ctx.invoked_subcommand is not None:
        return

    response = api_client.get(f"{USERS_PATH}/me")
    user = response.json()

    if output == "json":
        print(json.dumps(user, indent=2))
        return

    table = Table(title=f"Current User Details - {user['email']}")
    table.add_row("ID", str(user["id"]))
    table.add_row("Email", user["email"])
    table.add_row("First Name", user["first_name"])
    table.add_row("Last Name", user["last_name"])
    table.add_row("Role", user["role"])
    table.add_row("Authority Role", user["authority_role"])
    table.add_row("Status", user["status"])
    table.add_row("Department", user.get("department", ""))
    table.add_row("Job Title", user.get("job_title", ""))
    table.add_row("Created At", str(user["created_at"]))
    table.add_row("Updated At", str(user["updated_at"]))

    console.print(table)

    # Display permissions in a separate table
    if user.get("permissions"):
        permissions_table = Table(title="User Permissions")
        permissions_table.add_column("Permission", style="green")
        for permission in sorted(user["permissions"]):
            permissions_table.add_row(permission)
        console.print("\n")
        console.print(permissions_table)


@me_app.command("update")
def me_update(
    first_name: Optional[str] = typer.Option(None, help="Update first name"),
    last_name: Optional[str] = typer.Option(None, help="Update last name"),
    timezone: Optional[str] = typer.Option(None, help="Update timezone"),
    department: Optional[str] = typer.Option(None, help="Update department"),
    job_title: Optional[str] = typer.Option(None, help="Update job title"),
    phone_number: Optional[str] = typer.Option(None, help="Update phone number"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Update current user's profile."""
    data = {
        k: v
        for k, v in {
            "first_name": first_name,
            "last_name": last_name,
            "timezone": timezone,
            "department": department,
            "job_title": job_title,
            "phone_number": phone_number,
        }.items()
        if v is not None
    }

    if not data:
        console.print("[yellow]No fields provided to update[/yellow]")
        raise typer.Exit(1)

    try:
        response = api_client.patch(f"{USERS_PATH}/me", json=data)
        user = response.json()

        if output == "json":
            print(json.dumps(user, indent=2))
            return

        console.print(f"Updated profile for {user['email']}", style="green")
    except Exception as e:
        console.print(f"[red]Error updating profile: {e}[/red]")
        raise typer.Exit(1)


@app.command("change-password")
def change_password(
    current_password: str = typer.Option(
        ..., "--current-password", help="Current password", prompt=True, hide_input=True
    ),
    new_password: str = typer.Option(
        ..., "--new-password", help="New password", prompt=True, hide_input=True
    ),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Change your password."""
    try:
        # Prepare password data
        password_data = {"current_password": current_password, "new_password": new_password}

        # Make the API call
        response = api_client.post(f"{USERS_PATH}/password-change", json=password_data)
        user = response.json()

        # Handle output based on format
        if output == "json":
            print(json.dumps(user, indent=2))
        else:
            console.print("Password changed successfully.", style="green")

    except Exception as e:
        console.print(f"[red]Error changing password: {e}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
