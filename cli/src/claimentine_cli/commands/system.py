"""System management commands."""

import typer
from rich.console import <PERSON>sole
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.utils.output import output_formatter

app = typer.Typer(help="System management commands")
console = Console()


@app.command("initialize-db")
def initialize_database(
    force: bool = typer.Option(False, "--force", "-f", help="Force reinitialization"),
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """Initialize the database if not already initialized."""
    try:
        with console.status("Initializing database..."):
            response = api_client.post(
                "/config/initialize",
                params={"force": force},
            )
            # Convert the response to a dict if it's not already
            if hasattr(response, "json"):
                response_data = response.json()
            else:
                response_data = response

        # Format the output based on user preference
        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        if response_data.get("initialized"):
            console.print("[green]Database initialized successfully![/green]")
        elif response_data.get("already_initialized"):
            console.print("[yellow]Database was already initialized, no action taken.[/yellow]")
        else:
            console.print("[yellow]No action was required.[/yellow]")

    except Exception as e:
        console.print(f"[red]Error initializing database: {str(e)}[/red]")
        raise typer.Exit(code=1)


@app.command("is-initialized")
def is_database_initialized(
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """Check if the database has been initialized."""
    try:
        with console.status("Checking database initialization status..."):
            response = api_client.get("/config/is-initialized")
            # Convert the response to a dict if it's not already
            if hasattr(response, "json"):
                response_data = response.json()
            else:
                response_data = response

        # Format the output based on user preference
        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        if response_data.get("initialized"):
            console.print("[green]Database is initialized.[/green]")
        else:
            console.print("[yellow]Database is not initialized.[/yellow]")

    except Exception as e:
        console.print(f"[red]Error checking database initialization: {str(e)}[/red]")
        raise typer.Exit(code=1)


# System config sub-commands
config_app = typer.Typer(help="System configuration commands")
app.add_typer(config_app, name="config")


@config_app.command("list")
def list_system_configs(
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """List all system configurations."""
    try:
        with console.status("Fetching system configurations..."):
            response = api_client.get("/config/system-configs")

        # Parse the JSON response
        response_data = response.json()

        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        # Handle the case where response might be structured data or a direct list
        if isinstance(response_data, dict) and "items" in response_data:
            configs = response_data["items"]
        elif isinstance(response_data, list):
            configs = response_data
        else:
            configs = []

        # Create a table for console output
        table = Table("ID", "Key", "Value", "Description")
        for config in configs:
            table.add_row(str(config["id"]), config["key"], config["value"], config["description"])

        console.print(table)

    except Exception as e:
        console.print(f"[red]Error listing system configurations: {str(e)}[/red]")
        raise typer.Exit(code=1)


@config_app.command("show")
def get_system_config(
    key: str = typer.Option(..., "--key", "-k", help="Configuration key"),
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """Get a system configuration by key."""
    try:
        with console.status(f"Fetching system configuration '{key}'..."):
            response = api_client.get(f"/config/system-configs/{key}")

        # Parse the JSON response
        response_data = response.json()

        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        # Print details for console output
        console.print(f"[bold]Configuration Details:[/bold]")
        console.print(f"ID: {response_data['id']}")
        console.print(f"Key: {response_data['key']}")
        console.print(f"Value: {response_data['value']}")
        console.print(f"Description: {response_data['description']}")
        console.print(f"Created: {response_data['created_at']}")
        console.print(f"Updated: {response_data['updated_at']}")

    except Exception as e:
        console.print(f"[red]Error getting system configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)


@config_app.command("create")
def create_system_config(
    key: str = typer.Option(..., "--key", "-k", help="Configuration key"),
    value: str = typer.Option(..., "--value", "-v", help="Configuration value"),
    description: str = typer.Option(..., "--description", "-d", help="Configuration description"),
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """Create a new system configuration."""
    try:
        data = {"key": key, "value": value, "description": description}

        with console.status("Creating system configuration..."):
            response = api_client.post("/config/system-configs", json=data)

        # Parse the JSON response
        response_data = response.json()

        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        console.print(
            f"[green]Successfully created system configuration {response_data['id']}[/green]"
        )

    except Exception as e:
        console.print(f"[red]Error creating system configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)


@config_app.command("update")
def update_system_config(
    config_id: str = typer.Argument(..., help="Configuration ID"),
    value: str = typer.Option(..., "--value", "-v", help="New configuration value"),
    description: str = typer.Option(
        None, "--description", "-d", help="New configuration description"
    ),
    output: str = typer.Option("console", "--output", "-o", help="Output format: console or json"),
):
    """Update an existing system configuration."""
    try:
        data = {
            "value": value,
        }
        if description:
            data["description"] = description

        with console.status(f"Updating system configuration {config_id}..."):
            response = api_client.patch(f"/config/system-configs/{config_id}", json=data)

        # Parse the JSON response
        response_data = response.json()

        if output.lower() == "json":
            output_formatter(response_data, output)
            return

        console.print(f"[green]Successfully updated system configuration {config_id}[/green]")

    except Exception as e:
        console.print(f"[red]Error updating system configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)


@config_app.command("delete")
def delete_system_config(
    config_id: str = typer.Argument(..., help="Configuration ID"),
    force: bool = typer.Option(False, "--force", "-f", help="Force deletion without confirmation"),
):
    """Delete a system configuration."""
    try:
        if not force:
            if not typer.confirm(f"Are you sure you want to delete configuration {config_id}?"):
                console.print("Deletion cancelled.")
                return

        with console.status(f"Deleting system configuration {config_id}..."):
            api_client.delete(f"/config/system-configs/{config_id}")

        console.print(f"[green]Deleted system configuration {config_id}[/green]")

    except Exception as e:
        console.print(f"[red]Error deleting system configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)
