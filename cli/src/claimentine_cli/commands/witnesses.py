"""CLI commands for managing witnesses."""

import json
from typing import Optional
from uuid import UUID

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_claim_id

app = typer.Typer(help="Manage witnesses for claims")
console = Console()


@app.command()
def list(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List all witnesses for a claim."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/witnesses")
        witnesses = response.json()

        if output == "json":
            print(json.dumps(witnesses, indent=2))
            return

        if not witnesses:
            console.print("No witnesses found for this claim.")
            return

        table = Table(title=f"Witnesses for Claim {claim_identifier}")
        table.add_column("ID", style="dim")
        table.add_column("Name", style="green")
        table.add_column("Email", style="blue")
        table.add_column("Phone", style="cyan")
        table.add_column("Created", style="dim")

        for witness in witnesses:
            table.add_row(
                str(witness["id"]),
                witness["name"],
                witness.get("email", ""),
                witness.get("phone", ""),
                format_datetime(witness["created_at"]),
            )

        console.print(table)

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def get(
    witness_id: UUID = typer.Argument(..., help="Witness ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Get details of a specific witness."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/witnesses/{witness_id}")
        witness = response.json()

        if output == "json":
            print(json.dumps(witness, indent=2))
            return

        console.print(f"[bold]Witness Details:[/bold]")
        console.print(f"ID: {witness['id']}")
        console.print(f"Name: {witness['name']}")

        if witness.get("email"):
            console.print(f"Email: {witness['email']}")

        if witness.get("phone"):
            console.print(f"Phone: {witness['phone']}")

        if witness.get("address"):
            console.print(f"Address: {witness['address']}")

        if witness.get("statement"):
            console.print("\n[bold]Statement:[/bold]")
            console.print(witness["statement"])

        console.print(f"\nCreated: {format_datetime(witness['created_at'])}")
        console.print(f"Last Updated: {format_datetime(witness['updated_at'])}")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def add(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    name: str = typer.Option(..., help="Full name of the witness"),
    email: Optional[str] = typer.Option(None, help="Email address of the witness"),
    phone: Optional[str] = typer.Option(None, help="Phone number of the witness"),
    address: Optional[str] = typer.Option(None, help="Address of the witness"),
    statement: Optional[str] = typer.Option(None, help="Statement provided by the witness"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Add a new witness to a claim."""
    claim_id = resolve_claim_id(claim_identifier)

    witness_data = {
        "name": name,
        "email": email,
        "phone": phone,
        "address": address,
        "statement": statement,
    }

    # Remove None values
    witness_data = {k: v for k, v in witness_data.items() if v is not None}

    try:
        response = api_client.post(f"{CLAIMS_PATH}/{claim_id}/witnesses", json=witness_data)
        witness = response.json()

        if output == "json":
            print(json.dumps(witness, indent=2))
            return

        console.print(f"Witness [green]{witness['name']}[/green] added successfully!", style="bold")
        console.print(f"Witness ID: {witness['id']}")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def update(
    witness_id: UUID = typer.Argument(..., help="Witness ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    name: Optional[str] = typer.Option(None, help="Full name of the witness"),
    email: Optional[str] = typer.Option(None, help="Email address of the witness"),
    phone: Optional[str] = typer.Option(None, help="Phone number of the witness"),
    address: Optional[str] = typer.Option(None, help="Address of the witness"),
    statement: Optional[str] = typer.Option(None, help="Statement provided by the witness"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Update an existing witness."""
    claim_id = resolve_claim_id(claim_identifier)

    # Collect only provided fields
    witness_data = {}
    if name:
        witness_data["name"] = name
    if email is not None:  # Allow empty string to clear email
        witness_data["email"] = email
    if phone is not None:
        witness_data["phone"] = phone
    if address is not None:
        witness_data["address"] = address
    if statement is not None:
        witness_data["statement"] = statement

    if not witness_data:
        console.print("No fields provided to update.", style="yellow")
        raise typer.Exit(code=1)

    try:
        response = api_client.patch(
            f"{CLAIMS_PATH}/{claim_id}/witnesses/{witness_id}", json=witness_data
        )
        witness = response.json()

        if output == "json":
            print(json.dumps(witness, indent=2))
            return

        console.print(
            f"Witness [green]{witness['name']}[/green] updated successfully!", style="bold"
        )

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def delete(
    witness_id: UUID = typer.Argument(..., help="Witness ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    force: bool = typer.Option(False, "--force", "-f", help="Skip confirmation prompt"),
) -> None:
    """Delete a witness."""
    claim_id = resolve_claim_id(claim_identifier)

    if not force:
        try:
            # Get witness details for confirmation
            response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/witnesses/{witness_id}")
            witness = response.json()

            confirm = typer.confirm(
                f"Are you sure you want to delete witness '{witness['name']}' (ID: {witness_id})?"
            )

            if not confirm:
                console.print("Operation cancelled.")
                raise typer.Exit()

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                console.print(f"Witness {witness_id} not found.", style="red")
            else:
                console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
            raise typer.Exit(code=1)

    try:
        api_client.delete(f"{CLAIMS_PATH}/{claim_id}/witnesses/{witness_id}")
        console.print("Witness deleted successfully!", style="green")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)
