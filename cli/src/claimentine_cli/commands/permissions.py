"""Permission management commands."""

from typing import List
from uuid import UUID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client

app = typer.Typer(help="Permission management commands")
console = Console()


@app.command()
def list() -> None:
    """List all available permissions."""
    response = api_client.get("/api/v1/permissions")
    permissions = response.json()

    table = Table(title="Available Permissions")
    table.add_column("Name", style="cyan")
    table.add_column("Description", style="green")

    for perm in permissions:
        table.add_row(perm["name"], perm["description"])

    console.print(table)


@app.command()
def grant(
    role_id: UUID = typer.Argument(..., help="Role ID to grant permissions to"),
    permissions: List[str] = typer.Argument(..., help="Permission names to grant"),
) -> None:
    """Grant permissions to a role."""
    data = {"permissions": permissions}
    api_client.post(f"/api/v1/roles/{role_id}/permissions", json=data)
    console.print(f"Granted permissions {', '.join(permissions)} to role {role_id}", style="green")


@app.command()
def revoke(
    role_id: UUID = typer.Argument(..., help="Role ID to revoke permissions from"),
    permissions: List[str] = typer.Argument(..., help="Permission names to revoke"),
) -> None:
    """Revoke permissions from a role."""
    data = {"permissions": permissions}
    api_client.delete(f"/api/v1/roles/{role_id}/permissions", json=data)
    console.print(
        f"Revoked permissions {', '.join(permissions)} from role {role_id}", style="green"
    )


if __name__ == "__main__":
    app()
