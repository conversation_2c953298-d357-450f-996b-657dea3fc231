"""Authentication commands."""

from typing import Optional
from uuid import <PERSON>UID

import typer
from rich.console import Console

from claimentine_cli.api import api_client
from claimentine_cli.config import Config
from claimentine_cli.constants import AUTH_URL, REFRESH_URL, SESSIONS_URL
from claimentine_cli.utils.env_config import clear_tokens_from_env, set_tokens_in_env

app = typer.Typer(help="Authentication commands")
console = Console()

# Create a session subcommand group
session_app = typer.Typer(help="Session management commands")
app.add_typer(session_app, name="session")


@app.command()
def login(
    email: str = typer.Option(..., "--email", help="User email"),
    password: str = typer.Option(..., "--password", help="User password"),
) -> None:
    """Login to get access token."""
    response = api_client.post(
        AUTH_URL,
        data={"username": email, "password": password},
    )
    data = response.json()
    set_tokens_in_env(data["access_token"], data["refresh_token"])
    console.print("Successfully logged in", style="green")


@app.command()
def refresh(
    refresh_token: Optional[str] = typer.Option(None, help="Refresh token to use"),
) -> None:
    """Refresh access token using refresh token."""
    config = Config()
    token = refresh_token or config.refresh_token
    if not token:
        console.print("No refresh token found", style="red")
        raise typer.Exit(1)

    response = api_client.post(REFRESH_URL, json={"refresh_token": token})
    data = response.json()
    set_tokens_in_env(data["access_token"], data["refresh_token"])
    console.print("Successfully refreshed tokens", style="green")


@app.command()
def logout() -> None:
    """Logout and clear tokens."""
    config = Config()
    if config.refresh_token:
        try:
            # No standard backend logout endpoint defined, just clear local tokens
            pass
            # api_client.post(AUTH_LOGOUT_PATH, json={"token": config.refresh_token})
        except Exception:
            pass  # Ignore errors during logout
    clear_tokens_from_env()
    console.print("Successfully logged out.", style="green")
    console.print("Tokens cleared.", style="green")


@session_app.command(name="list")
def list_sessions() -> None:
    """List active sessions."""
    response = api_client.get(SESSIONS_URL)
    sessions = response.json()

    if not sessions:
        console.print("No active sessions found")
        return

    for session in sessions:
        console.print(
            f"Session {session['id']}: Agent=\"{session.get('user_agent', 'N/A')}\", IP={session.get('ip_address', 'N/A')}, Last Active={session.get('last_active_at', 'N/A')}, Expires={session.get('expires_at', 'N/A')}"
        )


@session_app.command(name="revoke")
def revoke_session(
    session_id: UUID = typer.Argument(..., help="ID of the session to revoke"),
) -> None:
    """Revoke a specific session by ID."""
    # Construct the URL to the specific session
    session_url = f"{SESSIONS_URL}/{session_id}"

    # Send the DELETE request
    api_client.delete(session_url)

    # If no exception was raised, the session was successfully revoked
    console.print(f"Session {session_id} successfully revoked.", style="green")


@app.command()
def cleanup() -> None:
    """Clean up expired sessions."""
    # This endpoint is not defined in constants.py or backend API
    # response = api_client.post(AUTH_SESSIONS_CLEANUP_PATH)
    # data = response.json()
    # console.print(data["message"], style="green")
    console.print("[yellow]Session cleanup command is not implemented in the backend.[/yellow]")


if __name__ == "__main__":
    app()
