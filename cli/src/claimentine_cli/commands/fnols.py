"""FNOL CLI commands."""

import json
from datetime import datetime, time
from enum import Enum
from typing import Optional
from uuid import UUID

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import FN<PERSON><PERSON>_PATH
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_fnol_id


# Add enum classes for CLI
class ReporterRelationship(str, Enum):
    """Relationship of the reporter to the claim/incident."""

    INSURED = "INSURED"
    CLAIMANT = "CLAIMANT"
    ATTORNEY = "ATTORNEY"
    AGENT = "AGENT"
    OTHER = "OTHER"


class USState(str, Enum):
    """US States enum for incident_state."""

    AL = "AL"
    AK = "AK"
    AZ = "AZ"
    AR = "AR"
    CA = "CA"
    CO = "CO"
    CT = "CT"
    DE = "DE"
    FL = "FL"
    GA = "GA"
    HI = "HI"
    ID = "ID"
    IL = "IL"
    IN = "IN"
    IA = "IA"
    KS = "KS"
    KY = "KY"
    LA = "LA"
    ME = "ME"
    MD = "MD"
    MA = "MA"
    MI = "MI"
    MN = "MN"
    MS = "MS"
    MO = "MO"
    MT = "MT"
    NE = "NE"
    NV = "NV"
    NH = "NH"
    NJ = "NJ"
    NM = "NM"
    NY = "NY"
    NC = "NC"
    ND = "ND"
    OH = "OH"
    OK = "OK"
    OR = "OR"
    PA = "PA"
    RI = "RI"
    SC = "SC"
    SD = "SD"
    TN = "TN"
    TX = "TX"
    UT = "UT"
    VT = "VT"
    VA = "VA"
    WA = "WA"
    WV = "WV"
    WI = "WI"
    WY = "WY"
    UNKNOWN = "UNKNOWN"


class CommunicationPreference(str, Enum):
    """Preferred method of communication for responses."""

    EMAIL = "EMAIL"
    PHONE = "PHONE"
    TEXT = "TEXT"
    MAIL = "MAIL"
    PORTAL = "PORTAL"
    NO_PREFERENCE = "NO_PREFERENCE"


app = typer.Typer(help="FNOL management commands")
console = Console()


@app.command()
def list(
    customer_id: Optional[UUID] = typer.Option(None, help="Filter by customer ID"),
    output: str = typer.Option("table", help="Output format: table or json"),
    page: int = typer.Option(1, help="Page number", min=1),
    page_size: int = typer.Option(10, help="Items per page", min=1, max=100),
) -> None:
    """List FNOLs with filtering options."""
    params = {
        "skip": (page - 1) * page_size,
        "limit": page_size,
    }

    # Add optional filters
    if customer_id:
        params["customer_id"] = str(customer_id)

    response = api_client.get(FNOLS_PATH, params=params)
    fnols = response.json()

    if not fnols:
        if output == "json":
            print(json.dumps([]))
        else:
            console.print("No FNOLs found")
        return

    if output == "json":
        print(json.dumps(fnols, indent=2))
        return

    table = Table(title="FNOLs")
    table.add_column("Number", style="green")
    table.add_column("Policy #", style="yellow")
    table.add_column("Customer", style="blue")
    table.add_column("Reported By", style="magenta")
    table.add_column("Reported At", style="yellow")
    table.add_column("Claims", style="cyan")
    table.add_column("Created", style="dim")

    for fnol in fnols:
        # Format claims count
        claims_count = len(fnol.get("claims", []))
        claims_display = str(claims_count) if claims_count > 0 else "-"

        # Get customer name
        customer_display = f"{fnol['customer']['name']} ({fnol['customer']['prefix']})"

        table.add_row(
            fnol["fnol_number"],
            fnol.get("policy_number", "-"),
            customer_display,
            fnol["reported_by"],
            format_datetime(fnol["reported_at"]),
            claims_display,
            format_datetime(fnol["created_at"]),
        )

    console.print(table)


@app.command()
def create(
    customer_id: UUID = typer.Option(..., help="Customer ID"),
    reported_by: str = typer.Option(..., help="Name of person reporting"),
    description: Optional[str] = typer.Option(None, help="FNOL description"),
    incident_date: Optional[str] = typer.Option(None, help="Incident date (YYYY-MM-DD)"),
    incident_location: Optional[str] = typer.Option(None, help="Incident location"),
    policy_number: Optional[str] = typer.Option(
        None, help="Policy number associated with the FNOL", show_default=False
    ),
    # Enhanced fields for ELY-1008
    incident_time: Optional[str] = typer.Option(None, help="Incident time (HH:MM)"),
    incident_state: Optional[USState] = typer.Option(None, help="US state where incident occurred"),
    reporter_relationship: Optional[ReporterRelationship] = typer.Option(
        None, help="Relationship of reporter to claim"
    ),
    communication_preference: Optional[CommunicationPreference] = typer.Option(
        None, help="Preferred method of communication"
    ),
    reporter_phone: Optional[str] = typer.Option(
        None, help="Reporter phone number (US format: ************)", show_default=False
    ),
    reporter_email: Optional[str] = typer.Option(
        None, help="Reporter email address", show_default=False
    ),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Create a new FNOL."""
    # Validate incident_date format if provided
    if incident_date:
        try:
            datetime.strptime(incident_date, "%Y-%m-%d")
        except ValueError:
            if output != "json":
                console.print("Error: Incident date must be in YYYY-MM-DD format", style="red")
            raise typer.Exit(1)

    # Validate incident_time format if provided
    incident_time_value = None
    if incident_time:
        try:
            time_obj = datetime.strptime(incident_time, "%H:%M").time()
            incident_time_value = time_obj.strftime("%H:%M:%S")  # Format for API
        except ValueError:
            if output != "json":
                console.print("Error: Incident time must be in HH:MM format", style="red")
            raise typer.Exit(1)

    # Validate at least one reporter contact method is provided
    if not reporter_phone and not reporter_email:
        if output != "json":
            console.print(
                "Error: At least one reporter contact method (phone or email) is required",
                style="red",
            )
        raise typer.Exit(1)

    data = {
        "customer_id": str(customer_id),
        "reported_by": reported_by,
        "description": description,
        "incident_date": incident_date,
        "incident_location": incident_location,
        "policy_number": policy_number,
        # Enhanced fields for ELY-1008
        "incident_time": incident_time_value,
        "incident_state": incident_state.value if incident_state else None,
        "reporter_relationship": reporter_relationship.value if reporter_relationship else None,
        "communication_preference": (
            communication_preference.value if communication_preference else None
        ),
        "reporter_phone": reporter_phone,
        "reporter_email": reporter_email,
    }

    response = api_client.post(FNOLS_PATH, json=data)
    fnol = response.json()

    if output == "json":
        print(json.dumps(fnol, indent=2))
        return

    console.print("FNOL created successfully!", style="green")
    console.print(f"Customer: {fnol['customer']['name']} ({fnol['customer']['prefix']})")
    console.print(f"FNOL Number: {fnol['fnol_number']}")
    console.print(f"Reported By: {fnol['reported_by']}")
    console.print(f"Reported At: {format_datetime(fnol['reported_at'])}")
    if fnol.get("policy_number"):
        console.print(f"Policy Number: {fnol['policy_number']}")

    # Add enhanced fields to output if available
    if fnol.get("incident_state"):
        console.print(f"Incident State: {fnol['incident_state']}")
    if fnol.get("reporter_relationship"):
        console.print(f"Reporter Relationship: {fnol['reporter_relationship']}")
    if fnol.get("communication_preference"):
        console.print(f"Communication Preference: {fnol['communication_preference']}")
    if fnol.get("reporter_phone"):
        console.print(f"Reporter Phone: {fnol['reporter_phone']}")
    if fnol.get("reporter_email"):
        console.print(f"Reporter Email: {fnol['reporter_email']}")


@app.command()
def update(
    fnol_identifier: str = typer.Argument(..., help="FNOL number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use FNOL ID instead of FNOL number"),
    reported_by: Optional[str] = typer.Option(None, help="Updated reporter name"),
    description: Optional[str] = typer.Option(None, help="Updated description"),
    incident_date: Optional[str] = typer.Option(None, help="Updated incident date (YYYY-MM-DD)"),
    incident_location: Optional[str] = typer.Option(None, help="Updated incident location"),
    policy_number: Optional[str] = typer.Option(
        None, help="Updated policy number", show_default=False
    ),
    # Enhanced fields for ELY-1008
    incident_time: Optional[str] = typer.Option(None, help="Updated incident time (HH:MM)"),
    incident_state: Optional[USState] = typer.Option(
        None, help="Updated US state where incident occurred"
    ),
    reporter_relationship: Optional[ReporterRelationship] = typer.Option(
        None, help="Updated relationship of reporter to claim"
    ),
    communication_preference: Optional[CommunicationPreference] = typer.Option(
        None, help="Updated preferred method of communication"
    ),
    reporter_phone: Optional[str] = typer.Option(
        None, help="Updated reporter phone number (US format: ************)", show_default=False
    ),
    reporter_email: Optional[str] = typer.Option(
        None, help="Updated reporter email address", show_default=False
    ),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Update a FNOL."""
    # Validate incident_date format if provided
    if incident_date:
        try:
            datetime.strptime(incident_date, "%Y-%m-%d")
        except ValueError:
            if output != "json":
                console.print("Error: Incident date must be in YYYY-MM-DD format", style="red")
            raise typer.Exit(1)

    # Process incident_time if provided
    incident_time_value = None
    if incident_time:
        try:
            time_obj = datetime.strptime(incident_time, "%H:%M").time()
            incident_time_value = time_obj.strftime("%H:%M:%S")
        except ValueError:
            if output != "json":
                console.print("Error: Incident time must be in HH:MM format", style="red")
            raise typer.Exit(1)

    # First get the FNOL to ensure it exists
    fnol_id = resolve_fnol_id(fnol_identifier, use_id_flag=use_id)

    # Prepare update data
    data = {
        k: v
        for k, v in {
            "reported_by": reported_by,
            "description": description,
            "incident_date": incident_date,
            "incident_location": incident_location,
            "policy_number": policy_number,
            # Enhanced fields for ELY-1008
            "incident_time": incident_time_value,
            "incident_state": incident_state.value if incident_state else None,
            "reporter_relationship": reporter_relationship.value if reporter_relationship else None,
            "communication_preference": (
                communication_preference.value if communication_preference else None
            ),
            "reporter_phone": reporter_phone,
            "reporter_email": reporter_email,
        }.items()
        if v is not None
    }

    response = api_client.patch(f"{FNOLS_PATH}/{fnol_id}", json=data)
    fnol = response.json()

    if output == "json":
        print(json.dumps(fnol, indent=2))
        return

    console.print("FNOL updated successfully!", style="green")
    console.print(f"FNOL Number: {fnol['fnol_number']}")
    console.print(f"Reported By: {fnol['reported_by']}")
    console.print(f"Reported At: {format_datetime(fnol['reported_at'])}")
    if fnol.get("policy_number"):
        console.print(f"Policy Number: {fnol['policy_number']}")

    # Add enhanced fields to output if available
    if fnol.get("incident_state"):
        console.print(f"Incident State: {fnol['incident_state']}")
    if fnol.get("reporter_relationship"):
        console.print(f"Reporter Relationship: {fnol['reporter_relationship']}")
    if fnol.get("communication_preference"):
        console.print(f"Communication Preference: {fnol['communication_preference']}")
    if fnol.get("reporter_phone"):
        console.print(f"Reporter Phone: {fnol['reporter_phone']}")
    if fnol.get("reporter_email"):
        console.print(f"Reporter Email: {fnol['reporter_email']}")


@app.command()
def get(
    fnol_identifier: str = typer.Argument(..., help="FNOL number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use FNOL ID instead of FNOL number"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Get FNOL details."""
    try:
        fnol_id = resolve_fnol_id(fnol_identifier, use_id_flag=use_id)

        # Fetch FNOL details using the resolved ID
        response = api_client.get(f"{FNOLS_PATH}/{fnol_id}")

    except httpx.HTTPStatusError as e:
        # Let HTTP errors (like 404) propagate for run_command to handle
        raise e
    except Exception as e:
        # Catch other unexpected errors
        console.print(f"[red]Unexpected error getting FNOL details: {e}[/red]")
        raise typer.Exit(1)

    fnol = response.json()

    if output == "json":
        print(json.dumps(fnol, indent=2))
        return

    table = Table(title=f"FNOL Details - {fnol['fnol_number']}", show_header=False)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="green")

    # Base fields
    table.add_row("ID", str(fnol["id"]))
    table.add_row("Number", fnol["fnol_number"])
    table.add_row("Reported By", fnol["reported_by"])
    table.add_row("Reported At", format_datetime(fnol["reported_at"]))

    # Customer information
    table.add_row("", "")
    table.add_row("Customer Information", "")
    table.add_row("Customer Name", fnol["customer"]["name"])
    table.add_row("Customer Prefix", fnol["customer"]["prefix"])
    table.add_row("Customer Description", fnol["customer"].get("description", ""))

    # FNOL details
    table.add_row("", "")
    table.add_row("FNOL Details", "")
    table.add_row("Description", fnol.get("description", ""))
    table.add_row("Policy Number", fnol.get("policy_number", ""))
    table.add_row("Incident Date", format_datetime(fnol.get("incident_date", "")))
    table.add_row("Incident Location", fnol.get("incident_location", ""))
    # Add new fields to the display
    table.add_row("Incident Time", fnol.get("incident_time", ""))
    table.add_row("Reporter Relationship", fnol.get("reporter_relationship", ""))
    table.add_row("Communication Preference", fnol.get("communication_preference", ""))
    table.add_row("Reporter Contact", fnol.get("reporter_contact", ""))
    table.add_row("Created At", format_datetime(fnol["created_at"]))
    table.add_row("Updated At", format_datetime(fnol["updated_at"]))

    # Claims information
    table.add_row("", "")
    table.add_row("Claims", "")
    if fnol.get("claims"):
        for claim in fnol["claims"]:
            table.add_row(
                claim["claim_number"],
                f"{claim['type']} - {claim['status']} - {claim['claimant_name']}",
            )
    else:
        table.add_row("No claims", "")

    console.print(table)


@app.command()
def delete(
    fnol_identifier: str = typer.Argument(..., help="FNOL number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use FNOL ID instead of FNOL number"),
    yes: bool = typer.Option(False, "--yes", "-y", help="Bypass confirmation prompt"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Delete a FNOL (soft delete)."""
    try:
        fnol_id = resolve_fnol_id(fnol_identifier, use_id_flag=use_id)
        # Get FNOL details first for confirmation message
        get_response = api_client.get(f"{FNOLS_PATH}/{fnol_id}")
        fnol_data = get_response.json()
        fnol_number = fnol_data.get("fnol_number", fnol_identifier)
        customer_name = fnol_data.get("customer", {}).get("name", "Unknown Customer")

        # Confirmation prompt (skip if output is json)
        if not yes and output != "json":
            typer.confirm(
                f"Are you sure you want to delete FNOL {fnol_number} for {customer_name} (ID: {fnol_id})?",
                abort=True,
            )

        # Call the delete API endpoint
        api_client.delete(f"{FNOLS_PATH}/{fnol_id}")

        # Only show success message if not in JSON mode
        if output != "json":
            console.print(f"Successfully deleted FNOL {fnol_number} (ID: {fnol_id})", style="green")
        else:
            # For JSON output, return empty JSON object
            print("{}")

    except httpx.HTTPStatusError as e:
        # Only print error messages if not in json mode
        if output != "json":
            console.print(f"[red]Error deleting FNOL (API Error): {e}[/red]")
        raise e  # Re-raise for run_command to handle if allow_error=True
    except Exception as e:
        # Only print error messages if not in json mode
        if output != "json":
            console.print(f"[red]Unexpected error deleting FNOL: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def convert(
    fnol_identifier: str = typer.Argument(..., help="FNOL number or ID (if --use-id is set)"),
    claim_type: str = typer.Option(
        ..., help="Type of claim to create (case-insensitive: AUTO, PROPERTY, GENERAL_LIABILITY)"
    ),
    use_id: bool = typer.Option(False, help="Use FNOL ID instead of FNOL number"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Convert an FNOL to a claim.

    Creates a new claim with the FNOL linked to it.
    """
    # Normalize claim type to uppercase for validation and API call
    claim_type = claim_type.upper()

    # Validate claim type
    if claim_type not in ["AUTO", "PROPERTY", "GENERAL_LIABILITY"]:
        if output != "json":
            console.print(
                f"[red]Error: Invalid claim type '{claim_type}'. Must be one of: AUTO, PROPERTY, GENERAL_LIABILITY[/red]"
            )
        raise typer.Exit(1)

    # First get the FNOL ID
    fnol_id = resolve_fnol_id(fnol_identifier, use_id_flag=use_id)

    # Make the API call to convert the FNOL
    try:
        response = api_client.post(
            f"{FNOLS_PATH}/{fnol_id}/convert", params={"claim_type": claim_type}
        )
        claim = response.json()

        if output == "json":
            # When output is json, print only the JSON with no other output
            print(json.dumps(claim, indent=2))
            return

        # Display result in table format
        console.print("FNOL converted successfully!", style="green")
        console.print(f"Claim ID: {claim['claim_id']}")
        print(f"Claim Number: {claim['claim_number']}")  # Use plain print to avoid color formatting
        console.print(f"Claim Type: {claim['claim_type']}")
        console.print(f"Status: {claim['status']}")

    except httpx.HTTPStatusError as e:
        # Only print error messages if not in json mode
        if output != "json":
            console.print(f"[red]Error converting FNOL to claim: {e}[/red]")
        raise e  # Re-raise for the caller to handle
    except Exception as e:
        if output != "json":
            console.print(f"[red]Unexpected error converting FNOL to claim: {e}[/red]")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
