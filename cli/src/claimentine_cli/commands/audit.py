"""CLI commands for viewing audit trail."""

import json
from datetime import datetime
from typing import Optional
from uuid import UUID

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_claim_id

app = typer.Typer(help="View audit trail for claims")
console = Console()


@app.command()
def list(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    entity_type: Optional[str] = typer.Option(
        None, help="Filter by entity type (CLAIM, WITNESS, ATTORNEY, etc.)"
    ),
    change_type: Optional[str] = typer.Option(
        None, help="Filter by change type (CREATE, UPDATE, DELETE)"
    ),
    from_date: Optional[datetime] = typer.Option(
        None, help="Filter by date range start (YYYY-MM-DD)"
    ),
    to_date: Optional[datetime] = typer.Option(None, help="Filter by date range end (YYYY-MM-DD)"),
    skip: int = typer.Option(0, help="Number of items to skip (pagination)"),
    limit: int = typer.Option(50, help="Maximum items to return (pagination)"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List audit trail entries for a claim with optional filtering."""
    claim_id = resolve_claim_id(claim_identifier)

    # Build query parameters
    params = {}
    if entity_type:
        params["entity_type"] = entity_type
    if change_type:
        params["change_type"] = change_type
    if from_date:
        params["from_date"] = from_date.isoformat()
    if to_date:
        params["to_date"] = to_date.isoformat()
    if skip:
        params["skip"] = skip
    if limit:
        params["limit"] = limit

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/audit", params=params)
        response_data = response.json()

        # Handle paginated format
        audit_entries = response_data["items"]
        total = response_data.get("total", len(audit_entries))
        current_skip = response_data.get("skip", 0)
        current_limit = response_data.get("limit", 50)

        if output == "json":
            print(json.dumps(response_data, indent=2))
            return

        if not audit_entries:
            console.print("No audit entries found for this claim.")
            return

        # Show pagination info
        console.print(
            f"Showing entries {current_skip+1}-{min(current_skip+len(audit_entries), total)} "
            f"of {total} total entries"
        )

        table = Table(title=f"Audit Trail for Claim {claim_identifier}")
        table.add_column("Timestamp", style="dim")
        table.add_column("Entity", style="magenta")
        table.add_column("Action", style="green")
        table.add_column("Field", style="blue")
        table.add_column("Description", style="cyan")

        for entry in audit_entries:
            table.add_row(
                format_datetime(entry["changed_at"]),
                entry["entity_type"],
                entry["change_type"],
                entry.get("field_name", ""),
                entry.get("description", ""),
            )

        console.print(table)

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def get(
    audit_id: UUID = typer.Argument(..., help="Audit entry ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Get details of a specific audit entry."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/audit/{audit_id}")
        audit_entry = response.json()

        if output == "json":
            print(json.dumps(audit_entry, indent=2))
            return

        console.print(f"[bold]Audit Entry Details:[/bold]")
        console.print(f"ID: {audit_entry['id']}")
        console.print(f"Timestamp: {format_datetime(audit_entry['changed_at'])}")
        console.print(f"Entity Type: {audit_entry['entity_type']}")

        if audit_entry.get("entity_id"):
            console.print(f"Entity ID: {audit_entry['entity_id']}")

        console.print(f"Change Type: {audit_entry['change_type']}")

        if audit_entry.get("field_name"):
            console.print(f"Field: {audit_entry['field_name']}")

        if audit_entry.get("description"):
            console.print(f"Description: {audit_entry['description']}")

        if audit_entry.get("previous_value"):
            console.print("\n[bold]Previous Value:[/bold]")
            console.print(json.dumps(audit_entry["previous_value"], indent=2))

        if audit_entry.get("new_value"):
            console.print("\n[bold]New Value:[/bold]")
            console.print(json.dumps(audit_entry["new_value"], indent=2))

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def summary(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Get a summary of audit activity for a claim."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/audit/summary")
        summary_data = response.json()

        if output == "json":
            print(json.dumps(summary_data, indent=2))
            return

        console.print(f"[bold]Audit Summary for Claim {claim_identifier}[/bold]")
        console.print(f"Total entries: {summary_data['total_entries']}")

        console.print("\n[bold]Changes by Entity Type:[/bold]")
        for entity_type, count in summary_data.get("by_entity", {}).items():
            console.print(f"{entity_type}: {count}")

        console.print("\n[bold]Changes by Action:[/bold]")
        for change_type, count in summary_data.get("by_change_type", {}).items():
            console.print(f"{change_type}: {count}")

        if summary_data.get("recent_activity"):
            console.print("\n[bold]Recent Activity:[/bold]")
            for entry in summary_data["recent_activity"]:
                console.print(
                    f"{format_datetime(entry['changed_at'])} - {entry['entity_type']} {entry['change_type']}: "
                    f"{entry.get('description', '')}"
                )

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)
