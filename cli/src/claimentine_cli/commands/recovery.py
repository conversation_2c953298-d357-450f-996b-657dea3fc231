"""Recovery management commands."""

import json
from enum import Enum
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH
from claimentine_cli.utils.lookup import resolve_claim_id

console = Console()
app = typer.Typer(help="Recovery management commands")


class RecoveryStatus(str, Enum):
    """Recovery workflow statuses matching backend model."""

    NOT_STARTED = "NOT_STARTED"
    INITIATED = "INITIATED"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"


class OutputFormat(str, Enum):
    """Output format options."""

    TEXT = "text"
    JSON = "json"


@app.command(name="status")
def update_status(
    claim_identifier: str = typer.Argument(..., help="Claim number or ID (if --use-id is set)"),
    status: RecoveryStatus = typer.Option(..., help="New recovery status"),
    use_id: bool = typer.Option(False, help="Use claim ID instead of claim number"),
    output: OutputFormat = typer.Option(
        OutputFormat.TEXT, "--output", "-o", help="Output format (text or json)"
    ),
) -> None:
    """Update the recovery status of a claim."""
    try:
        # Resolve identifier to ID first
        claim_id = resolve_claim_id(claim_identifier, use_id_flag=use_id)

        # Use the resolved ID for the API endpoint
        endpoint = f"{CLAIMS_PATH}/{claim_id}/recovery/status"

        # Make the API request
        payload = {"recovery_status": status.value}
        response = api_client.patch(endpoint, params=payload)

        # Process the response
        if response.status_code == 200:
            updated_claim = response.json()

            if output == OutputFormat.JSON:
                # Return a properly formatted JSON response
                print(json.dumps(updated_claim, indent=2))
            else:
                console.print(
                    f"Recovery status updated successfully to [green]{status.value}[/green] for claim {updated_claim['claim_number']}."
                )
        else:
            if output == OutputFormat.JSON:
                error = {
                    "status": "error",
                    "message": f"Failed to update recovery status: {response.text}",
                }
                print(json.dumps(error))
            else:
                console.print(f"Failed to update recovery status: {response.text}", style="red")

    except Exception as e:
        if output == OutputFormat.JSON:
            error = {
                "status": "error",
                "message": f"Error: {str(e)}",
            }
            print(json.dumps(error))
        else:
            console.print(f"Error: {str(e)}", style="red")
        raise typer.Exit(code=1)


@app.command(name="carrier")
def update_carrier_details(
    claim_identifier: str = typer.Argument(..., help="Claim number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use claim ID instead of claim number"),
    carrier_name: Optional[str] = typer.Option(None, help="Third-party carrier name"),
    carrier_contact: Optional[str] = typer.Option(
        None, help="Third-party carrier contact information"
    ),
    carrier_claim_number: Optional[str] = typer.Option(
        None, help="Third-party carrier's claim number"
    ),
    carrier_adjuster: Optional[str] = typer.Option(
        None, help="Third-party carrier's adjuster name"
    ),
    output: OutputFormat = typer.Option(
        OutputFormat.TEXT, "--output", "-o", help="Output format (text or json)"
    ),
) -> None:
    """Update third-party carrier details of a claim for recovery."""
    try:
        # Resolve identifier
        claim_id = resolve_claim_id(claim_identifier, use_id_flag=use_id)

        # First get the current carrier details
        get_response = api_client.get(f"{CLAIMS_PATH}/{claim_id}", params={"output": "json"})
        current_claim = get_response.json()

        # Prepare payload - use current values if not specified
        payload = {
            "carrier_name": (
                carrier_name if carrier_name is not None else current_claim.get("carrier_name", "")
            ),
            "carrier_contact": (
                carrier_contact
                if carrier_contact is not None
                else current_claim.get("carrier_contact", "")
            ),
            "carrier_claim_number": (
                carrier_claim_number
                if carrier_claim_number is not None
                else current_claim.get("carrier_claim_number", "")
            ),
            "carrier_adjuster": (
                carrier_adjuster
                if carrier_adjuster is not None
                else current_claim.get("carrier_adjuster", "")
            ),
        }

        # Make the API request - use query params as the API expects
        response = api_client.patch(f"{CLAIMS_PATH}/{claim_id}/carrier", params=payload)
        updated_claim = response.json()

        if output == OutputFormat.JSON:
            print(json.dumps(updated_claim, indent=2))
        else:
            console.print(
                f"Carrier details updated for claim {updated_claim['claim_number']}.", style="green"
            )

    except Exception as e:
        if output == OutputFormat.JSON:
            error = {
                "status": "error",
                "message": f"Error: {str(e)}",
            }
            print(json.dumps(error))
        else:
            console.print(f"Error updating carrier details: {str(e)}", style="red")
        raise typer.Exit(code=1)


@app.command(name="amounts")
def update_recovery_amounts(
    claim_identifier: str = typer.Argument(..., help="Claim number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use claim ID instead of claim number"),
    expected_amount: Optional[str] = typer.Option(None, help="Expected recovery amount"),
    received_amount: Optional[str] = typer.Option(None, help="Amount actually recovered"),
    output: OutputFormat = typer.Option(
        OutputFormat.TEXT, "--output", "-o", help="Output format (text or json)"
    ),
) -> None:
    """Update recovery amounts for a claim."""
    try:
        # Resolve identifier
        claim_id = resolve_claim_id(claim_identifier, use_id_flag=use_id)

        # Prepare payload
        payload = {}
        if expected_amount is not None:
            payload["expected_amount"] = expected_amount
        if received_amount is not None:
            payload["received_amount"] = received_amount

        if not payload:
            if output == OutputFormat.JSON:
                error = {
                    "status": "error",
                    "message": "At least one amount (expected or received) must be specified",
                }
                print(json.dumps(error))
            else:
                console.print(
                    "At least one amount (expected or received) must be specified.", style="red"
                )
            raise typer.Exit(code=1)

        # Make the API request - use query params as the API expects
        response = api_client.patch(f"{CLAIMS_PATH}/{claim_id}/recovery/amounts", params=payload)
        updated_claim = response.json()

        if output == OutputFormat.JSON:
            print(json.dumps(updated_claim, indent=2))
        else:
            console.print(
                f"Recovery amounts updated for claim {updated_claim['claim_number']}.",
                style="green",
            )

    except Exception as e:
        if output == OutputFormat.JSON:
            error = {
                "status": "error",
                "message": f"Error: {str(e)}",
            }
            print(json.dumps(error))
        else:
            console.print(f"Error updating recovery amounts: {str(e)}", style="red")
        raise typer.Exit(code=1)


@app.command(name="get")
def get_recovery_details(
    claim_identifier: str = typer.Argument(..., help="Claim number or ID (if --use-id is set)"),
    use_id: bool = typer.Option(False, help="Use claim ID instead of claim number"),
    output: OutputFormat = typer.Option(
        OutputFormat.TEXT, "--output", "-o", help="Output format (text or json)"
    ),
) -> None:
    """Get recovery details for a claim."""
    try:
        # Resolve identifier to ID first
        claim_id = resolve_claim_id(claim_identifier, use_id_flag=use_id)

        # Use the resolved ID for the API endpoint
        endpoint = f"{CLAIMS_PATH}/{claim_id}/recovery"

        # Make the API request
        response = api_client.get(endpoint)

        if response.status_code == 200:
            recovery_data = response.json()

            if output == OutputFormat.JSON:
                print(json.dumps(recovery_data, indent=2))
            else:
                # Display recovery details in a rich table
                table = Table(title=f"Recovery Details for Claim {recovery_data['claim_number']}")
                table.add_column("Field", style="cyan")
                table.add_column("Value", style="green")

                # Add recovery status with appropriate styling
                status = recovery_data.get("recovery_status", "Not Set")
                status_style = "green" if status != "Not Set" else "dim"
                table.add_row("Recovery Status", f"[{status_style}]{status}[/{status_style}]")

                # Add recovery amounts
                expected_amount = recovery_data.get("expected_amount") or "[dim]Not Set[/dim]"
                table.add_row("Expected Amount", expected_amount)

                received_amount = recovery_data.get("received_amount") or "[dim]Not Set[/dim]"
                table.add_row("Received Amount", received_amount)

                # Add carrier information
                carrier_name = recovery_data.get("carrier_name") or "[dim]Not Set[/dim]"
                table.add_row("Carrier Name", carrier_name)

                carrier_contact = recovery_data.get("carrier_contact") or "[dim]Not Set[/dim]"
                table.add_row("Carrier Contact", carrier_contact)

                carrier_claim_number = (
                    recovery_data.get("carrier_claim_number") or "[dim]Not Set[/dim]"
                )
                table.add_row("Carrier Claim Number", carrier_claim_number)

                carrier_adjuster = recovery_data.get("carrier_adjuster") or "[dim]Not Set[/dim]"
                table.add_row("Carrier Adjuster", carrier_adjuster)

                # Add claim identifiers for reference
                table.add_row("Claim ID", recovery_data["claim_id"])
                table.add_row("Claim Number", recovery_data["claim_number"])

                console.print(table)
        elif response.status_code == 404:
            if output == OutputFormat.JSON:
                error = {
                    "status": "not_found",
                    "message": "No recovery data found for this claim",
                }
                print(json.dumps(error))
            else:
                console.print("No recovery data found for this claim.", style="yellow")
        else:
            if output == OutputFormat.JSON:
                error = {
                    "status": "error",
                    "message": f"Failed to get recovery details: {response.text}",
                }
                print(json.dumps(error))
            else:
                console.print(f"Failed to get recovery details: {response.text}", style="red")

    except Exception as e:
        if output == OutputFormat.JSON:
            error = {
                "status": "error",
                "message": f"Error: {str(e)}",
            }
            print(json.dumps(error))
        else:
            console.print(f"Error: {str(e)}", style="red")
        raise typer.Exit(code=1)
