"""Document management commands."""

import json
import mimetypes
from pathlib import Path
from typing import Optional

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_claim_id

console = Console()

documents_group = typer.Typer(help="Document management commands")


@documents_group.command(name="list")
def list_documents(
    claim_identifier: str = typer.Option(
        ..., help="ID or number of the claim to list documents for"
    ),
    document_type: Optional[str] = typer.Option(None, "--type", help="Filter by document type"),
    output: str = typer.Option("table", help="Output format: table or json"),
    skip: int = typer.Option(0, help="Number of records to skip"),
    limit: int = typer.Option(100, help="Maximum number of records to return"),
):
    """List documents for a claim."""
    try:
        # Resolve claim identifier
        claim_id = resolve_claim_id(claim_identifier)

        # Build path and params
        path = f"/claims/{claim_id}/documents"
        params = {"skip": skip, "limit": limit}
        if document_type:
            params["document_type"] = document_type

        # Make API request using the central client
        response = api_client.get(path, params=params)

        # Process and print response - parse DocumentList structure
        response_data = response.json()
        documents = response_data.get("items", [])
        total = response_data.get("total", 0)

        if output == "json":
            print(json.dumps(response_data, indent=2))
            return

        if not documents:
            # Handle no documents based on output type
            console.print("No documents found for this claim.")
            return

        # Table output
        table = Table(title=f"Documents for Claim {claim_identifier} ({total} total)")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Type", style="blue")
        table.add_column("Size (KB)", style="magenta", justify="right")
        table.add_column("Uploaded At", style="yellow")

        for doc in documents:
            size_kb = doc.get("file_size", 0) / 1024 if doc.get("file_size") else 0
            table.add_row(
                doc["id"],
                doc["name"],
                doc.get("type", "-"),
                f"{size_kb:.2f}",
                format_datetime(doc.get("created_at")),
            )
        console.print(table)

    except Exception as e:
        console.print(f"[red]Error listing documents: {str(e)}[/red]")


@documents_group.command(name="get")
def get_document(
    document_id: str = typer.Argument(..., help="ID of the document to get"),
    claim_identifier: str = typer.Option(
        ..., help="ID or number of the claim the document belongs to"
    ),
    output: str = typer.Option("table", help="Output format: table or json"),
):
    """Get details of a specific document."""
    try:
        # Resolve claim identifier
        claim_id = resolve_claim_id(claim_identifier)

        # Build path
        path = f"/claims/{claim_id}/documents/{document_id}"

        # Make API request using the central client
        response = api_client.get(path)

        # Process and print response
        document = response.json()

        if output == "json":
            print(json.dumps(document, indent=2))
            return

        # Rich table output
        console.print(f"[bold blue]Document Details[/bold blue]")
        console.print(f"[bold]ID:[/bold] {document['id']}")
        console.print(f"[bold]Name:[/bold] {document['name']}")
        console.print(f"[bold]Type:[/bold] {document.get('type', 'Not specified')}")
        console.print(f"[bold]Description:[/bold] {document.get('description') or 'Not specified'}")

        # Format file size as KB
        size_kb = document.get("file_size", 0) / 1024 if document.get("file_size") else 0
        console.print(f"[bold]Size:[/bold] {size_kb:.2f} KB")

        console.print(f"[bold]MIME Type:[/bold] {document.get('mime_type', 'Not specified')}")
        console.print(f"[bold]Created:[/bold] {format_datetime(document.get('created_at'))}")
        console.print(f"[bold]Updated:[/bold] {format_datetime(document.get('updated_at'))}")

        # Additional information that might be useful
        console.print(f"[bold]File Path:[/bold] {document.get('file_path', 'Not specified')}")
        if document.get("uploaded_by"):
            console.print(f"[bold]Uploaded By:[/bold] {document.get('uploaded_by')}")

    except Exception as e:
        console.print(f"[red]Error getting document: {str(e)}[/red]")


@documents_group.command(name="upload")
def upload_document(
    claim_identifier: str = typer.Option(
        ..., help="ID or number of the claim to upload document to"
    ),
    file: Path = typer.Option(..., exists=True, help="Path to the file to upload"),
    document_type: str = typer.Option(..., "--type", help="Document type"),
    name: Optional[str] = typer.Option(None, help="Document name (defaults to filename)"),
    description: Optional[str] = typer.Option(None, help="Document description"),
    direct: bool = typer.Option(False, "--direct", help="Use direct upload instead of signed URLs"),
    output: str = typer.Option("table", help="Output format: table or json"),
):
    """Upload a document to a claim."""
    try:
        # Resolve claim identifier
        claim_id = resolve_claim_id(claim_identifier)

        # Use filename as name if not provided
        if not name:
            name = file.name

        # Get file size and mime type
        file_size = file.stat().st_size
        mime_type = (
            mimetypes.guess_type(file)[0] or "application/octet-stream"
        )  # Guess mime type based on extension

        if direct:
            # Direct upload - use multipart form data
            # Generate a document ID
            import uuid

            # Generate a document ID locally (Task 6 suggests this might need backend change)
            document_id = uuid.uuid4()

            # Create the file path (Task 6 suggests this might need backend change)
            file_path = f"claims/{claim_id}/documents/{document_id}/{name}"

            # Prepare document data
            document_data = {
                "name": name,
                "description": description,
                "type": document_type,
                "file_path": file_path,
                "file_size": file_size,
                "mime_type": mime_type,
            }

            # Prepare multipart form data
            with open(file, "rb") as f:
                files_data = {"file": (name, f, mime_type)}

                # Use central client for POST with multipart data
                # The APIClient.post handles headers and errors
                response = api_client.post(
                    f"/claims/{claim_id}/documents",
                    data={"document_data_json": json.dumps(document_data)},  # Form fields
                    files=files_data,  # File part
                )

            # Parse response
            document = response.json()

            # Handle output format
            if output == "json":
                print(json.dumps(document, indent=2))
            else:
                console.print(
                    f"[green]Document uploaded successfully with ID: {document['id']}[/green]"
                )

        else:
            # Signed URL upload - get a signed URL first
            # Use central client
            signed_url_response = api_client.post(
                f"/claims/{claim_id}/documents/upload-url",
                json={
                    "file_name": name,
                    "content_type": mime_type,
                    "document_type": document_type,
                },
            )

            # Parse response
            data = signed_url_response.json()
            upload_url = data.get("upload_url")
            document_id = data.get("document_id")

            if not upload_url:
                console.print("[red]Invalid response from server: missing upload URL[/red]")
                return

            # Ensure the backend provided the document ID
            if not document_id:
                console.print(
                    "[red]Error: Server did not provide a document ID for the upload.[/red]"
                )
                raise typer.Exit(code=1)

            # Upload file to storage using plain httpx for external URL
            with open(file, "rb") as f:
                try:
                    # Use a temporary synchronous httpx client for the external URL
                    with httpx.Client() as external_client:
                        upload_response = external_client.put(
                            upload_url, content=f.read(), headers={"Content-Type": mime_type}
                        )
                    upload_response.raise_for_status()  # Raise HTTP errors for the external PUT
                except httpx.RequestError as exc:
                    console.print(f"[red]Error during storage upload request: {exc}[/red]")
                    raise typer.Exit(1)
                except httpx.HTTPStatusError as exc:
                    # Handle status errors from the direct storage PUT separately
                    console.print(
                        f"[red]Storage upload failed: {exc.response.status_code} - {exc.response.text}[/red]"
                    )
                    raise typer.Exit(1)

            # Create document record using central client
            # Task 6: file_path construction here might be fragile
            create_response = api_client.post(
                f"/claims/{claim_id}/documents",
                json={
                    "name": name,
                    "description": description,
                    "type": document_type,
                    "file_path": f"claims/{claim_id}/documents/{document_id}/{name}",
                    "file_size": file_size,
                    "mime_type": mime_type,
                },
            )

            # Parse response (error handled by api_client)
            document = create_response.json()

            # Handle output format
            if output == "json":
                print(json.dumps(document, indent=2))
            else:
                console.print(
                    f"[green]Document uploaded successfully via signed URL. ID: {document['id']}[/green]"
                )

    except httpx.HTTPStatusError as e:
        # Already handled by api_client, just pass
        pass
    except Exception as e:
        # Catch other potential errors like file IO issues
        console.print(f"[red]An unexpected error occurred during upload: {str(e)}[/red]")


@documents_group.command(name="download")
def download_document(
    claim_identifier: str = typer.Option(
        ..., help="ID or number of the claim the document belongs to"
    ),
    document_id: str = typer.Option(..., help="ID of the document to download"),
    output: Optional[str] = typer.Option(
        None, "-o", help="Output file path (defaults to document name)"
    ),
    output_format: str = typer.Option("table", "--output", help="Output format: table or json"),
):
    """Download a document."""
    try:
        # Resolve claim identifier
        claim_id = resolve_claim_id(claim_identifier)

        # First get document metadata to get the name
        # Use central client
        metadata_response = api_client.get(f"/claims/{claim_id}/documents/{document_id}")

        document = metadata_response.json()
        file_name = document.get("name")

        # Get download URL
        # Use central client
        download_url_response = api_client.get(
            f"/claims/{claim_id}/documents/{document_id}/download-url"
        )

        # Parse response
        download_data = download_url_response.json()
        download_url = download_data.get("download_url")

        if not download_url:
            console.print("[red]Failed to get download URL[/red]")
            return

        # Download file from URL
        # Use a temporary httpx client for the external URL
        try:
            with httpx.Client() as external_client:
                download_response = external_client.get(download_url, follow_redirects=True)
            download_response.raise_for_status()
        except httpx.RequestError as exc:
            console.print(f"[red]Error during file download request: {exc}[/red]")
            raise typer.Exit(1)
        except httpx.HTTPStatusError as exc:
            console.print(
                f"[red]File download failed: {exc.response.status_code} - {exc.response.text}[/red]"
            )
            raise typer.Exit(1)

        # Determine output path
        output_path = output
        if not output_path:
            output_path = file_name

        # Save file
        with open(output_path, "wb") as f:
            f.write(download_response.content)

        # Prepare result data
        result = {
            "document_id": document_id,
            "file_name": file_name,
            "saved_to": output_path,
            "size": len(download_response.content),
            "status": "success",
        }

        # Handle output format
        if output_format == "json":
            print(json.dumps(result, indent=2))
        else:
            console.print(f"[green]Document downloaded to: {output_path}[/green]")

    except Exception as e:
        console.print(f"[red]An unexpected error occurred during download: {str(e)}[/red]")


@documents_group.command(name="delete")
def delete_document(
    claim_identifier: str = typer.Option(
        ..., help="ID or number of the claim the document belongs to"
    ),
    document_id: str = typer.Argument(..., help="ID of the document to delete"),
    output: str = typer.Option("table", help="Output format: table or json"),
    yes: bool = typer.Option(False, "--yes", "-y", help="Skip confirmation prompt"),
):
    """Delete a document."""
    try:
        # Resolve claim identifier
        claim_id = resolve_claim_id(claim_identifier)

        # Confirm deletion unless --yes flag is set
        if not yes:
            # Use rich for interactive confirmation
            confirm = console.input(
                f"[yellow]Are you sure you want to delete document {document_id}? [y/N]: [/yellow]"
            )
            if not confirm.lower().startswith("y"):
                console.print("[yellow]Deletion cancelled.[/yellow]")
                return

        # Build path
        path = f"/claims/{claim_id}/documents/{document_id}"

        # Make API request using the central client
        response = api_client.delete(path)

        if output == "json":
            print(json.dumps({"deleted": True, "document_id": document_id}))
        else:
            console.print(f"[green]Document {document_id} deleted successfully.[/green]")

    except Exception as e:
        console.print(f"[red]Error deleting document: {str(e)}[/red]")


@documents_group.command(name="list-all")
def list_all_documents(
    document_type: Optional[str] = typer.Option(None, "--type", help="Filter by document type"),
    output: str = typer.Option("table", help="Output format: table or json"),
    skip: int = typer.Option(0, help="Number of records to skip"),
    limit: int = typer.Option(100, help="Maximum number of records to return"),
):
    """List all documents across all claims."""
    try:
        # Build path and params
        path = "/documents"
        params = {"skip": skip, "limit": limit}
        if document_type:
            params["document_type"] = document_type

        # Make API request using the central client
        response = api_client.get(path, params=params)

        # Process and print response
        response_data = response.json()

        # Extract documents from the response
        if "items" in response_data:
            documents = response_data["items"]
            total = response_data.get("total", len(documents))
        else:
            documents = response_data
            total = len(documents)

        if output == "json":
            print(json.dumps(response_data, indent=2))
            return

        if not documents:
            # Handle no documents found
            if output == "json":
                print(json.dumps({"items": [], "total": 0}))
            else:
                console.print("No documents found.")
            return

        # Table output
        table = Table(title=f"All Documents (Total: {total})")
        table.add_column("ID", style="cyan")
        table.add_column("Name", style="green")
        table.add_column("Type", style="blue")
        table.add_column("Claim ID", style="yellow")
        table.add_column("Size (KB)", style="magenta", justify="right")
        table.add_column("Uploaded At", style="yellow")
        table.add_column("Uploaded By", style="green")

        for doc in documents:
            size_kb = doc.get("file_size", 0) / 1024 if doc.get("file_size") else 0
            uploaded_by = doc.get("uploaded_by", "-")
            table.add_row(
                doc["id"],
                doc["name"],
                doc.get("type", "-"),
                str(doc.get("claim_id", "-")),
                f"{size_kb:.2f}",
                format_datetime(doc.get("created_at")),
                str(uploaded_by),
            )
        console.print(table)

    except Exception as e:
        console.print(f"[red]Error listing all documents: {str(e)}[/red]")


@documents_group.command(name="update")
def update_document(
    claim_id: str = typer.Option(..., help="ID or number of the claim the document belongs to"),
    document_id: str = typer.Option(..., help="ID of the document to update"),
    name: Optional[str] = typer.Option(None, help="New document name"),
    document_type: Optional[str] = typer.Option(None, "--type", help="New document type"),
    description: Optional[str] = typer.Option(None, help="New document description"),
    output: str = typer.Option("table", help="Output format: table or json"),
):
    """Update document metadata."""
    try:
        # Check if any update fields provided
        if not any([name, document_type, description]):
            console.print(
                "[yellow]No update fields provided. Please specify at least one field to update.[/yellow]"
            )
            return

        # Build update data
        update_data = {}
        if name:
            update_data["name"] = name
        if document_type:
            update_data["type"] = document_type
        if description:
            update_data["description"] = description

        # Build URL
        url = f"/claims/{claim_id}/documents/{document_id}"

        # Make API request using the central client
        # Using PATCH instead of PUT as per API expectations
        response = api_client.patch(url, json=update_data)

        # Handle errors
        if response.status_code != 200:
            console.print(f"[red]Error: {response.text}[/red]")
            return

        # Display success message
        document = response.json()

        # Handle output format
        if output == "json":
            print(json.dumps(document, indent=2))
        else:
            console.print("[green]Document updated successfully[/green]")
            console.print(f"  ID: {document['id']}")
            console.print(f"  Name: {document['name']}")
            console.print(f"  Type: {document['type']}")
            if document.get("description"):
                console.print(f"  Description: {document['description']}")

    except Exception as e:
        console.print(f"[red]Error: {str(e)}[/red]")
