"""Metrics commands for the CLI."""

from decimal import Decima<PERSON>
from typing import Optional
from uuid import UUID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import DASHBOARD_METRICS_PATH
from claimentine_cli.utils.formatting import format_decimal

app = typer.Typer(help="Metrics and dashboard commands")
console = Console()


@app.command(name="dashboard")
def dashboard_metrics(
    client_id: Optional[UUID] = typer.Option(
        None, "--client-id", help="Filter metrics by client ID"
    ),
    user_id: Optional[UUID] = typer.Option(None, "--user-id", help="Filter metrics by user ID"),
    period: str = typer.Option(
        "last_30_days", help="Time period for metrics (last_30_days, last_6_months, last_1_year)"
    ),
    compare_period: bool = typer.Option(
        True, "--compare-period/--no-compare-period", help="Compare with previous period"
    ),
    output: str = typer.Option("table", "--output", "-o", help="Output format (table or json)"),
) -> None:
    """
    Get dashboard metrics showing overall performance and status.

    Displays key performance indicators including total claims, open claims,
    financial metrics, and task-related metrics.
    """
    # Prepare query parameters
    params = {
        "period": period,
        "compare_period": compare_period,
    }
    if client_id:
        params["client_id"] = str(client_id)
    if user_id:
        params["user_id"] = str(user_id)

    # Make API request
    response = api_client.get(DASHBOARD_METRICS_PATH, params=params)
    metrics = response.json()

    # If JSON output is requested, just print the JSON and return
    if output == "json":
        console.print_json(data=metrics)
        return

    # Display claims metrics
    claims_table = Table(title="Claims Metrics")
    claims_table.add_column("Metric", style="cyan")
    claims_table.add_column("Value", style="green")
    claims_table.add_column("Change", style="yellow")

    claims_table.add_row(
        "Total Claims",
        str(metrics["total_claims"]),
        _format_change(metrics.get("total_claims_change")),
    )
    claims_table.add_row("Open Claims", str(metrics["open_claims"]), "")
    claims_table.add_row("New Claims (Last Period)", str(metrics["new_claims_last_period"]), "")
    claims_table.add_row(
        "Closed Claims (Last Period)", str(metrics["closed_claims_last_period"]), ""
    )

    avg_lifecycle = metrics.get("average_claim_lifecycle_days")
    avg_lifecycle_str = f"{avg_lifecycle:.1f} days" if avg_lifecycle is not None else "N/A"
    claims_table.add_row(
        "Average Claim Lifecycle",
        avg_lifecycle_str,
        _format_change(metrics.get("average_claim_lifecycle_days_change")),
    )

    console.print(claims_table)

    # Display financial metrics
    financial_table = Table(title="Financial Metrics")
    financial_table.add_column("Metric", style="cyan")
    financial_table.add_column("Value", style="green")
    financial_table.add_column("Change", style="yellow")

    financial_table.add_row(
        "Total Payments (Last Period)",
        format_decimal(metrics["total_payments_last_period"]),
        _format_change(metrics.get("total_payments_change")),
    )
    financial_table.add_row(
        "Total Outstanding Reserves",
        format_decimal(metrics["total_outstanding_reserves"]),
        _format_change(metrics.get("total_outstanding_reserves_change")),
    )

    console.print(financial_table)

    # Display task metrics
    task_table = Table(title="Task Metrics")
    task_table.add_column("Metric", style="cyan")
    task_table.add_column("Value", style="green")

    task_table.add_row("Tasks Pending", str(metrics["tasks_pending"]))
    task_table.add_row("Tasks Overdue", str(metrics["tasks_overdue"]))

    console.print(task_table)


def _format_change(change_data: Optional[dict]) -> str:
    """Format metric change data for display."""
    if not change_data:
        return ""

    direction = change_data.get("direction", "neutral")
    percentage = change_data.get("percentage", 0)

    if direction == "increase":
        color = "green"
        arrow = "↑"
    elif direction == "decrease":
        color = "red"
        arrow = "↓"
    else:
        color = "yellow"
        arrow = "="

    return f"[{color}]{arrow} {percentage:.1f}%[/{color}]"


if __name__ == "__main__":
    app()
