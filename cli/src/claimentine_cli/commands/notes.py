"""CLI commands for managing claim notes."""

import json
import sys
from enum import Enum
from uuid import UUID

import httpx
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

from claimentine_cli.api import api_client  # Use the shared API client
from claimentine_cli.utils import format_datetime, handle_api_error


# Added OutputFormat Enum
class OutputFormat(str, Enum):
    TEXT = "text"
    JSON = "json"


notes_app = typer.Typer(name="notes", help="Manage claim notes.", no_args_is_help=True)
console = Console()


@notes_app.command("add")
def add_note(
    claim_identifier: str = typer.Option(
        ...,
        "--claim-identifier",
        "-c",
        help="The ID (UUID) or Number of the claim to add the note to.",
    ),
    content: str = typer.Option(..., "--content", "-m", help="The content of the note."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Add a new note to a claim (identified by UUID or Number)."""
    try:
        payload = {"content": content}
        response = api_client.post(f"/claims/{claim_identifier}/notes", json=payload)
        response.raise_for_status()  # Raise HTTPStatusError for bad responses (4xx or 5xx)
        note_data = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(note_data, indent=2))
        else:
            console.print(f"[green]Note added successfully to claim {claim_identifier}.[/green]")
            console.print(f"Note ID: {note_data.get('id')}")

    except httpx.HTTPStatusError as e:
        handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        # Catch other potential errors (network, json parsing, etc.)
        console.print(f"[red]Failed to add note: {e}[/red]")
        sys.exit(1)


@notes_app.command("list")
def list_notes(
    claim_id: UUID = typer.Option(
        ..., "--claim-id", "-c", help="The ID of the claim to list notes for."
    ),
    page: int = typer.Option(1, help="Page number", min=1),
    page_size: int = typer.Option(10, help="Items per page", min=1, max=100),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """List notes associated with a specific claim."""
    try:
        params = {"skip": (page - 1) * page_size, "limit": page_size}
        response = api_client.get(f"/claims/{claim_id}/notes", params=params)
        response.raise_for_status()
        notes = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(notes, indent=2))
            return

        # --- Text/Table Output (Default) ---
        if not notes:
            console.print(f"No notes found for claim {claim_id}.")
            return

        table = Table(
            title=f"Notes for Claim {claim_id} (Page {page})",
            show_header=True,
            header_style="bold magenta",
        )
        table.add_column("ID", style="dim", width=36)
        table.add_column("Created At", width=20)
        table.add_column("Author", width=30)
        table.add_column("Content Snippet")

        for note in notes:
            snippet = (
                note.get("content", "")[:75] + "..."
                if len(note.get("content", "")) > 75
                else note.get("content", "")
            )
            created_at_display = format_datetime(note.get("created_at"))
            table.add_row(
                str(note.get("id")),
                created_at_display,
                note.get("author_email", "N/A"),
                snippet,
            )

        console.print(table)

    except httpx.HTTPStatusError as e:
        # Handle specific 404 for the claim itself if needed, otherwise generic
        handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to list notes: {e}[/red]")
        sys.exit(1)


@notes_app.command("view")
def view_note(
    note_id: UUID = typer.Argument(..., help="The ID of the note to view."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """View the full details of a specific note."""
    try:
        response = api_client.get(f"/notes/{note_id}")
        response.raise_for_status()
        note = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(note, indent=2))
            return

        # --- Text/Panel Output (Default) ---
        # This check is likely redundant due to raise_for_status, but safe
        if not note:
            console.print(f"[red]Error: Note {note_id} not found (unexpected response).[/red]")
            raise typer.Exit(code=1)

        console.print(
            Panel(
                f"[bold]Claim ID:[/bold] {note.get('claim_id')}\n"
                f"[bold]Author:[/bold]   {note.get('author_email', 'N/A')} (ID: {note.get('author_id', 'N/A')})\n"
                f"[bold]Created:[/bold]  {format_datetime(note.get('created_at'))}\n"
                f"[bold]Updated:[/bold]  {format_datetime(note.get('updated_at'))}",
                title=f"Note Details (ID: {note.get('id')})",
                border_style="blue",
            )
        )
        console.print(Panel(note.get("content", ""), title="Content", border_style="green"))

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            console.print(f"[red]Error: Note {note_id} not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to view note: {e}[/red]")
        sys.exit(1)


@notes_app.command("edit")
def edit_note(
    note_id: UUID = typer.Argument(..., help="The ID of the note to edit."),
    content: str = typer.Option(..., "--content", "-m", help="The new content for the note."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Update the content of an existing note."""
    try:
        payload = {"content": content}
        response = api_client.patch(f"/notes/{note_id}", json=payload)
        response.raise_for_status()
        updated_note = response.json()

        if output == OutputFormat.JSON:
            console.print(json.dumps(updated_note, indent=2))
        else:
            console.print(f"[green]Note {note_id} updated successfully.[/green]")

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            console.print(f"[red]Error: Note {note_id} not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        console.print(f"[red]Failed to edit note: {e}[/red]")
        sys.exit(1)


@notes_app.command("delete")
def delete_note(
    note_id: UUID = typer.Argument(..., help="The ID of the note to delete."),
    force: bool = typer.Option(False, "--force", "-f", help="Force deletion without confirmation."),
    output: OutputFormat = typer.Option(OutputFormat.TEXT, "--output", "-o", help="Output format"),
):
    """Delete a specific note."""
    if output == OutputFormat.TEXT and not force:
        typer.confirm(
            f"Are you sure you want to delete note {note_id}? This cannot be undone.", abort=True
        )

    try:
        response = api_client.delete(f"/notes/{note_id}")
        response.raise_for_status()

        if output == OutputFormat.JSON:
            console.print(json.dumps({"success": True, "id": str(note_id)}))
        else:
            console.print(f"[green]Note {note_id} deleted successfully.[/green]")

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            if output == OutputFormat.JSON:
                console.print(json.dumps({"success": False, "error": f"Note {note_id} not found."}))
            else:
                console.print(f"[red]Error: Note {note_id} not found.[/red]")
        else:
            handle_api_error(e.response)
        sys.exit(1)
    except Exception as e:
        if output == OutputFormat.JSON:
            console.print(json.dumps({"success": False, "error": f"Failed to delete note: {e}"}))
        else:
            console.print(f"[red]Failed to delete note: {e}[/red]")
        sys.exit(1)
