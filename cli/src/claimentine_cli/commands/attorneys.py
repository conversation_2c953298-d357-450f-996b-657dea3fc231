"""CLI commands for managing attorneys."""

import json
from typing import Optional
from uuid import UUID

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_claim_id

app = typer.Typer(help="Manage attorneys for claims")
console = Console()


@app.command()
def list(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List all attorneys for a claim."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/attorneys")
        attorneys = response.json()

        if output == "json":
            print(json.dumps(attorneys, indent=2))
            return

        if not attorneys:
            console.print("No attorneys found for this claim.")
            return

        table = Table(title=f"Attorneys for Claim {claim_identifier}")
        table.add_column("ID", style="dim")
        table.add_column("Name", style="green")
        table.add_column("Type", style="magenta")
        table.add_column("Firm", style="yellow")
        table.add_column("Contact", style="blue")
        table.add_column("Created", style="dim")

        for attorney in attorneys:
            contact = attorney.get("email", "")
            if attorney.get("phone"):
                if contact:
                    contact += f", {attorney['phone']}"
                else:
                    contact = attorney["phone"]

            table.add_row(
                str(attorney["id"]),
                attorney["name"],
                attorney["attorney_type"],
                attorney.get("firm_name", ""),
                contact,
                format_datetime(attorney["created_at"]),
            )

        console.print(table)

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def get(
    attorney_id: UUID = typer.Argument(..., help="Attorney ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Get details of a specific attorney."""
    claim_id = resolve_claim_id(claim_identifier)

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/attorneys/{attorney_id}")
        attorney = response.json()

        if output == "json":
            print(json.dumps(attorney, indent=2))
            return

        console.print(f"[bold]Attorney Details:[/bold]")
        console.print(f"ID: {attorney['id']}")
        console.print(f"Name: {attorney['name']}")
        console.print(f"Type: {attorney['attorney_type']}")

        if attorney.get("firm_name"):
            console.print(f"Firm: {attorney['firm_name']}")

        if attorney.get("email"):
            console.print(f"Email: {attorney['email']}")

        if attorney.get("phone"):
            console.print(f"Phone: {attorney['phone']}")

        if attorney.get("address"):
            console.print(f"Address: {attorney['address']}")

        if attorney.get("notes"):
            console.print("\n[bold]Notes:[/bold]")
            console.print(attorney["notes"])

        console.print(f"\nCreated: {format_datetime(attorney['created_at'])}")
        console.print(f"Last Updated: {format_datetime(attorney['updated_at'])}")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def add(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    name: str = typer.Option(..., help="Full name of the attorney"),
    attorney_type: str = typer.Option(
        ..., help="Type of attorney (PLAINTIFF, DEFENSE, COVERAGE, MONITORING, OTHER)"
    ),
    firm_name: Optional[str] = typer.Option(None, help="Name of the law firm"),
    email: Optional[str] = typer.Option(None, help="Email address of the attorney"),
    phone: Optional[str] = typer.Option(None, help="Phone number of the attorney"),
    address: Optional[str] = typer.Option(None, help="Address of the attorney or law firm"),
    notes: Optional[str] = typer.Option(None, help="Additional notes about the attorney"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Add a new attorney to a claim."""
    claim_id = resolve_claim_id(claim_identifier)

    attorney_data = {
        "name": name,
        "attorney_type": attorney_type,
        "firm_name": firm_name,
        "email": email,
        "phone": phone,
        "address": address,
        "notes": notes,
    }

    # Remove None values
    attorney_data = {k: v for k, v in attorney_data.items() if v is not None}

    try:
        response = api_client.post(f"{CLAIMS_PATH}/{claim_id}/attorneys", json=attorney_data)
        attorney = response.json()

        if output == "json":
            print(json.dumps(attorney, indent=2))
            return

        console.print(
            f"{attorney['attorney_type']} attorney [green]{attorney['name']}[/green] added successfully!",
            style="bold",
        )
        console.print(f"Attorney ID: {attorney['id']}")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def update(
    attorney_id: UUID = typer.Argument(..., help="Attorney ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    name: Optional[str] = typer.Option(None, help="Full name of the attorney"),
    attorney_type: Optional[str] = typer.Option(
        None, help="Type of attorney (PLAINTIFF, DEFENSE, COVERAGE, MONITORING, OTHER)"
    ),
    firm_name: Optional[str] = typer.Option(None, help="Name of the law firm"),
    email: Optional[str] = typer.Option(None, help="Email address of the attorney"),
    phone: Optional[str] = typer.Option(None, help="Phone number of the attorney"),
    address: Optional[str] = typer.Option(None, help="Address of the attorney or law firm"),
    notes: Optional[str] = typer.Option(None, help="Additional notes about the attorney"),
    output: str = typer.Option("detail", help="Output format: detail or json"),
) -> None:
    """Update an existing attorney."""
    claim_id = resolve_claim_id(claim_identifier)

    # Collect only provided fields
    attorney_data = {}
    if name:
        attorney_data["name"] = name
    if attorney_type:
        attorney_data["attorney_type"] = attorney_type
    if firm_name is not None:
        attorney_data["firm_name"] = firm_name
    if email is not None:
        attorney_data["email"] = email
    if phone is not None:
        attorney_data["phone"] = phone
    if address is not None:
        attorney_data["address"] = address
    if notes is not None:
        attorney_data["notes"] = notes

    if not attorney_data:
        console.print("No fields provided to update.", style="yellow")
        raise typer.Exit(code=1)

    try:
        response = api_client.patch(
            f"{CLAIMS_PATH}/{claim_id}/attorneys/{attorney_id}", json=attorney_data
        )
        attorney = response.json()

        if output == "json":
            print(json.dumps(attorney, indent=2))
            return

        console.print(
            f"Attorney [green]{attorney['name']}[/green] updated successfully!", style="bold"
        )

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)


@app.command()
def delete(
    attorney_id: UUID = typer.Argument(..., help="Attorney ID"),
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    force: bool = typer.Option(False, "--force", "-f", help="Skip confirmation prompt"),
) -> None:
    """Delete an attorney."""
    claim_id = resolve_claim_id(claim_identifier)

    if not force:
        try:
            # Get attorney details for confirmation
            response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/attorneys/{attorney_id}")
            attorney = response.json()

            confirm = typer.confirm(
                f"Are you sure you want to delete attorney '{attorney['name']}' (ID: {attorney_id})?"
            )

            if not confirm:
                console.print("Operation cancelled.")
                raise typer.Exit()

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                console.print(f"Attorney {attorney_id} not found.", style="red")
            else:
                console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
            raise typer.Exit(code=1)

    try:
        api_client.delete(f"{CLAIMS_PATH}/{claim_id}/attorneys/{attorney_id}")
        console.print("Attorney deleted successfully!", style="green")

    except httpx.HTTPStatusError as e:
        console.print(f"Error: {e.response.status_code} - {e.response.text}", style="red")
        raise typer.Exit(code=1)
