"""CLI commands for reserve configurations."""

import json
from typing import Optional
from uuid import UUID

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import DECIMAL_PLACES
from claimentine_cli.utils import format_datetime

console = Console()

app = typer.Typer(
    name="reserves",
    help="Reserve configuration management commands",
    no_args_is_help=True,
)


@app.command()
def list(
    claim_type: Optional[str] = typer.Option(None, help="Filter by claim type"),
    reserve_type: Optional[str] = typer.Option(None, help="Filter by reserve type"),
    required: Optional[bool] = typer.Option(None, help="Filter by required status"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """List reserve configurations."""
    params = {}
    if claim_type:
        params["claim_type"] = claim_type
    if reserve_type:
        params["reserve_type"] = reserve_type
    if required is not None:
        params["is_required"] = required

    response = api_client.get("/config/reserves", params=params)
    configs = response.json()

    if output == "json":
        print(json.dumps(configs, indent=2))
        return

    if not configs:
        console.print("No configurations found.")
        return

    # Format data for display
    table = Table(title="Reserve Configurations")
    table.add_column("Claim Type", style="cyan")
    table.add_column("Reserve Type", style="green")
    table.add_column("Required", style="yellow")
    table.add_column("Min Amount", style="blue")
    table.add_column("Description", style="magenta")

    for config in configs:
        min_amount = config["minimum_amount"]
        min_amount_str = f"${float(min_amount):.{DECIMAL_PLACES}f}" if min_amount else "-"

        table.add_row(
            config["claim_type"],
            config["reserve_type"],
            "✓" if config["is_required"] else "✗",
            min_amount_str,
            config["description"],
        )

    console.print(table)


@app.command()
def show(
    config_id: UUID = typer.Argument(..., help="Configuration ID"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Show details of a specific reserve configuration."""
    response = api_client.get(f"/config/reserves/{config_id}")
    config = response.json()

    if output == "json":
        print(json.dumps(config, indent=2))
        return

    # Format data for display
    table = Table(title="Reserve Configuration Details", show_header=False)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="green")

    min_amount = config["minimum_amount"]
    min_amount_str = f"${float(min_amount):.{DECIMAL_PLACES}f}" if min_amount else "-"

    table.add_row("ID", str(config["id"]))
    table.add_row("Claim Type", config["claim_type"])
    table.add_row("Reserve Type", config["reserve_type"])
    table.add_row("Required", "✓" if config["is_required"] else "✗")
    table.add_row("Minimum Amount", min_amount_str)
    table.add_row("Description", config["description"])
    table.add_row("Created At", format_datetime(config["created_at"]))
    table.add_row("Updated At", format_datetime(config["updated_at"]))

    console.print(table)


@app.command()
def create(
    claim_type: str = typer.Option(..., help="Claim type (AUTO, PROPERTY, etc.)"),
    reserve_type: str = typer.Option(..., help="Reserve type (DEFENSE_COST, BODILY_INJURY, etc.)"),
    required: bool = typer.Option(
        True, "--required/--no-required", help="Whether this reserve is required"
    ),
    min_amount: float = typer.Option(..., "--min-amount", help="Minimum amount for this reserve"),
    description: str = typer.Option("", help="Description of this reserve configuration"),
    output: Optional[str] = typer.Option(None, help="Output format (json or text)"),
) -> None:
    """Create a new reserve configuration or update if exists."""
    data = {
        "claim_type": claim_type,
        "reserve_type": reserve_type,
        "is_required": required,
        "minimum_amount": min_amount,
        "description": description,
    }

    # First, check if this configuration already exists
    existing_configs = api_client.get(
        f"/config/reserves?claim_type={claim_type}&reserve_type={reserve_type}"
    )
    existing_configs_data = existing_configs.json()

    config_data = None
    action = "updated"

    if existing_configs_data:
        # Configuration already exists, update it
        config_id = existing_configs_data[0]["id"]
        try:
            config = api_client.patch(f"/config/reserves/{config_id}", json=data)
            config_data = config.json()
            action = "updated"
        except Exception as e:
            console.print(f"Error updating reserve configuration: {str(e)}", style="red")
            raise typer.Exit(1)

    else:
        # Create new configuration if it doesn't exist
        try:
            config = api_client.post("/config/reserves", json=data)
            config_data = config.json()
            action = "created"
        except Exception as e:
            error_msg = str(e)
            if "Configuration already exists" in error_msg:
                console.print(
                    f"Configuration already exists for {claim_type}:{reserve_type}", style="yellow"
                )
            else:
                console.print(f"Error creating reserve configuration: {error_msg}", style="red")
            raise typer.Exit(1)

    # Print output based on the flag
    if config_data:
        if output == "json":
            print(json.dumps(config_data, indent=2))
        else:
            console.print(f"Successfully {action} reserve configuration {config_data['id']}")


@app.command()
def update(
    config_id: UUID = typer.Argument(..., help="Configuration ID"),
    required: Optional[bool] = typer.Option(None, help="Whether the reserve is required"),
    min_amount: Optional[float] = typer.Option(None, help="Minimum required amount"),
    description: Optional[str] = typer.Option(None, help="Description of the configuration"),
) -> None:
    """Update an existing reserve configuration."""
    data = {}
    if required is not None:
        data["is_required"] = required
    if min_amount is not None:
        data["minimum_amount"] = min_amount
    if description is not None:
        data["description"] = description

    config = api_client.patch(f"/config/reserves/{config_id}", json=data)
    config_data = config.json()
    console.print(f"Updated reserve configuration {config_data['id']}")


@app.command()
def delete(
    config_id: UUID = typer.Argument(..., help="Configuration ID"),
    force: bool = typer.Option(False, help="Force deletion without confirmation"),
) -> None:
    """Delete a reserve configuration."""
    if not force:
        if not typer.confirm(f"Are you sure you want to delete reserve configuration {config_id}?"):
            console.print("Aborted.")
            return

    api_client.delete(f"/config/reserves/{config_id}")
    console.print(f"Deleted reserve configuration {config_id}")


@app.command()
def configure_requirement(
    claim_type: str = typer.Option(..., help="Claim type (e.g., AUTO, PROPERTY)"),
    reserve_type: str = typer.Option(
        ..., help="Reserve type (e.g., DEFENSE_COST, PROPERTY_DAMAGE)"
    ),
    is_required: bool = typer.Option(
        ..., help="Set to true if reserve is required, false otherwise"
    ),
) -> None:
    """Configure whether a reserve type is required for a specific claim type."""
    # First find the configuration ID
    params = {
        "claim_type": claim_type,
        "reserve_type": reserve_type,
    }

    response = api_client.get("/config/reserves", params=params)
    configs = response.json()

    if not configs:
        console.print(f"No configuration found for {claim_type}:{reserve_type}", style="red")
        console.print("Use the 'create' command to create a new configuration first.")
        return

    config = configs[0]
    config_id = config["id"]

    # Now update the configuration
    data = {"is_required": is_required}

    updated_config = api_client.patch(f"/config/reserves/{config_id}", json=data)
    updated_config_data = updated_config.json()

    console.print(
        f"Updated {claim_type}:{reserve_type} - Required: {'Yes' if is_required else 'No'}"
    )
    console.print(f"Configuration ID: {updated_config_data['id']}")
