"""Configuration command subpackage."""

import typer

# Create app for the config command
app = typer.Typer(name="config", help="Manage system configuration", no_args_is_help=True)

# Import subapps - we do this here to avoid circular imports
from claimentine_cli.commands.config.authority import app as authority_app

# Import basic commands and add their functions directly
from claimentine_cli.commands.config.basic import (
    init_config,
    reset_config,
    set_config,
    set_environment,
    set_environment_url,
    show_config,
)
from claimentine_cli.commands.config.reserve import app as reserve_app

# Register subcommands
app.add_typer(authority_app, name="authority")
app.add_typer(reserve_app, name="reserves")

# Register basic commands directly
app.command("set")(set_config)
app.command("show")(show_config)
app.command("reset")(reset_config)
app.command("init")(init_config)
app.command("env")(set_environment)
app.command("env-url")(set_environment_url)

# This is an empty __init__.py file to mark the directory as a package
# The main app is defined in commands/config.py
