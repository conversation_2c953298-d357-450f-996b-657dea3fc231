"""Basic configuration commands."""

import json
from enum import Enum
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.config import Config
from claimentine_cli.utils.env_config import (
    delete_env_var,
    get_env_file,
    regenerate_env_file,
    save_env_var,
)

app = typer.Typer(help="Basic configuration commands")
console = Console()


class Environment(str, Enum):
    """Available environments."""

    DEV = "dev"
    STAGING = "staging"
    PROD = "prod"


# Environment URL configuration file
def get_env_config_file() -> Path:
    """Get the environment configuration file path."""
    config_dir = get_env_file().parent
    return config_dir / "environments.json"


def load_environments() -> dict:
    """Load environment configurations."""
    file_path = get_env_config_file()
    if not file_path.exists():
        # Default environment configuration
        return {
            "active": "dev",
            "environments": {
                "dev": "http://localhost:8000",
                "staging": "http://staging-api.example.com",
                "prod": "http://api.example.com",
            },
        }

    try:
        return json.loads(file_path.read_text())
    except json.JSONDecodeError:
        console.print("[yellow]Warning: Could not parse environments file, using defaults[/yellow]")
        return {
            "active": "dev",
            "environments": {
                "dev": "http://localhost:8000",
                "staging": "http://staging-api.example.com",
                "prod": "http://api.example.com",
            },
        }


def save_environments(env_config: dict) -> None:
    """Save environment configurations and regenerate .env file.

    This function now uses complete .env file regeneration to eliminate
    synchronization bugs between environments.json and .env file.
    """
    file_path = get_env_config_file()
    file_path.parent.mkdir(parents=True, exist_ok=True)

    # Clean up any temporary flags before saving
    clean_config = env_config.copy()
    clean_config.pop("is_reset_operation", None)
    clean_config.pop("skip_env_update", None)

    # Save environments.json
    file_path.write_text(json.dumps(clean_config, indent=2))

    # Completely regenerate .env file from the environment configuration
    # This eliminates all synchronization issues
    regenerate_env_file(clean_config)


@app.command("set")
def set_config(
    key: str = typer.Argument(..., help="Configuration key to set"),
    value: str = typer.Argument(..., help="Value to set"),
):
    """Set a configuration value."""
    # Special case for api-url and api_url to make them both work
    if key in ["api-url", "api_url"]:
        key = "api_url"

        # When setting api_url directly, also update the current active environment
        env_config = load_environments()
        active_env = env_config["active"]
        env_config["environments"][active_env] = value

        # Use the new regeneration approach to ensure consistency
        save_environments(env_config)

        console.print(f"[green]Successfully set {key} to {value}[/green]")
        console.print(
            f"[green]Updated {active_env} environment URL and regenerated .env file[/green]"
        )
        return

    try:
        save_env_var(key, value)
        console.print(f"[green]Successfully set {key} to {value}[/green]")
    except Exception as e:
        console.print(f"[red]Error setting configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)


@app.command("show")
def show_config():
    """Show current configuration values."""
    config = Config()
    env_config = load_environments()
    active_env = env_config["active"]

    console.print("[bold]Current Configuration:[/bold]")
    console.print(f"Active Environment: [green]{active_env}[/green]")
    console.print(f"API URL: {config.api_url}")
    console.print(f"Config File: {get_env_file()}")

    # Create a table for environments
    table = Table("Environment", "URL", "Status")
    for env, url in env_config["environments"].items():
        status = "[green]ACTIVE[/green]" if env == active_env else ""
        table.add_row(env, url, status)

    console.print("\n[bold]Environment URLs:[/bold]")
    console.print(table)


@app.command("reset")
def reset_config(
    key: str = typer.Argument(
        None, help="Specific configuration key to reset (leave empty to reset all)"
    ),
    yes: bool = typer.Option(False, "--yes", "-y", help="Bypass confirmation prompt"),
):
    """Reset configuration to defaults."""
    if key:
        # Reset a specific config key
        if not yes:
            typer.confirm(f"Are you sure you want to reset {key} to its default value?", abort=True)
        deleted = delete_env_var(key)
        if deleted:
            console.print(f"[green]Successfully reset {key} to default value[/green]")
        else:
            console.print(f"[yellow]No configuration entry found for {key}[/yellow]")
    else:
        # Reset all config
        if not yes:
            typer.confirm(
                "Are you sure you want to reset ALL configuration to defaults?", abort=True
            )

        # Reset environments to defaults and regenerate .env file
        env_config = {
            "active": "dev",
            "environments": {
                "dev": "http://localhost:8000",
                "staging": "http://staging-api.example.com",
                "prod": "http://api.example.com",
            },
        }

        # Save environments and regenerate .env file
        # The regeneration will preserve existing tokens and other user settings
        save_environments(env_config)

        console.print("[green]Successfully reset configuration to defaults[/green]")


@app.command("init")
def init_config():
    """Initialize default configuration."""
    try:
        # Ensure the config directory and .env file exist
        env_file = get_env_file()
        env_file.parent.mkdir(parents=True, exist_ok=True)

        # Always create/touch the file, even if it exists (to ensure it's there)
        env_file.touch(exist_ok=True)

        if not env_file.exists():
            console.print(f"[red]Error: Failed to create config file at {env_file}[/red]")
            raise typer.Exit(code=1)
        else:
            console.print(f"[green]Ensured config file exists at {env_file}[/green]")

        # Initialize environment configuration if it doesn't exist
        env_config_file = get_env_config_file()
        if not env_config_file.exists():
            env_config = {
                "active": "dev",
                "environments": {
                    "dev": "http://localhost:8000",
                    "staging": "http://staging-api.example.com",
                    "prod": "http://api.example.com",
                },
            }
            save_environments(env_config)
            console.print(f"[green]Created environments file at {env_config_file}[/green]")

        # Set default API URL if not already set
        config = Config()
        if not config.api_url or config.api_url == "http://localhost:8000":
            # Explicitly set the default API URL
            save_env_var("api_url", "http://localhost:8000")
            console.print("[yellow]Using default API URL (http://localhost:8000)[/yellow]")
        else:
            console.print(f"[green]Using existing API URL ({config.api_url})[/green]")

        console.print("[green]Configuration initialized successfully[/green]")
    except Exception as e:
        console.print(f"[red]Error initializing configuration: {str(e)}[/red]")
        raise typer.Exit(code=1)


@app.command("env")
def set_environment(
    environment: Environment = typer.Argument(
        ..., help="Environment to activate (dev, staging, prod)"
    ),
):
    """Set the active environment."""
    env_config = load_environments()

    # Update the active environment
    env_config["active"] = environment

    # If this environment doesn't exist yet, set a default URL
    if environment not in env_config["environments"]:
        default_urls = {
            "dev": "http://localhost:8000",
            "staging": "http://staging-api.example.com",
            "prod": "http://api.example.com",
        }
        env_config["environments"][environment] = default_urls.get(
            environment, "http://localhost:8000"
        )

    # Save the configuration and update the active URL
    save_environments(env_config)

    console.print(f"[green]Activated {environment} environment[/green]")
    console.print(f"API URL set to: {env_config['environments'][environment]}")


@app.command("env-url")
def set_environment_url(
    environment: Environment = typer.Argument(..., help="Environment to configure"),
    url: str = typer.Argument(..., help="URL for the environment"),
):
    """Set the URL for a specific environment."""
    env_config = load_environments()

    # Update the URL for the specified environment
    previous_url = env_config["environments"].get(environment, "not set")
    env_config["environments"][environment] = url

    # Save the configuration (regeneration will handle .env file automatically)
    save_environments(env_config)

    console.print(f"[green]Updated {environment} URL from {previous_url} to {url}[/green]")
    if env_config["active"] == environment:
        console.print("[green]This is the active environment, .env file has been updated[/green]")
