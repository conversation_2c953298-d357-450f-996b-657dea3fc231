"""Financial management commands."""

import json
from datetime import datetime
from decimal import Decimal, InvalidOperation
from enum import Enum
from typing import List, Optional

import httpx
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLAIMS_PATH, CONFIG_PATH, DECIMAL_PLACES
from claimentine_cli.utils import format_datetime
from claimentine_cli.utils.lookup import resolve_claim_id

console = Console()
app = typer.Typer(help="Financial management commands")


class PaymentType(str, Enum):
    """Types of payments."""

    INDEMNITY = "INDEMNITY"
    EXPENSE = "EXPENSE"
    DEFENSE = "DEFENSE"


def _display_financials(financials: dict) -> None:
    """Display financial details."""
    DECIMAL_PLACES = 2
    # Basic financial information
    table = Table(title="Financial Details", show_header=False)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="green")

    # Helper function to convert string values to float and format them
    def format_value(value):
        if value is None:
            return "-"
        if isinstance(value, str):
            try:
                value = float(value)
            except ValueError:
                return value
        return f"${value:.{DECIMAL_PLACES}f}"

    table.add_row("Claim Number", financials["claim_number"])
    table.add_row("Estimated Value", format_value(financials["estimated_value"]))

    # Calculate total reserves
    total_reserves = 0
    if "reserves" in financials and financials["reserves"]:
        for r in financials["reserves"]:
            amount = r.get("amount", 0)
            if isinstance(amount, str):
                try:
                    amount = float(amount)
                except ValueError:
                    amount = 0
            total_reserves += amount

    table.add_row("Total Reserves", format_value(total_reserves))
    table.add_row("Indemnity Paid", format_value(financials.get("indemnity_paid")))
    table.add_row("Expense Paid", format_value(financials.get("expense_paid")))
    table.add_row("Defense Paid", format_value(financials.get("defense_paid")))

    # Calculate total paid
    total_paid = 0
    for field in ["indemnity_paid", "expense_paid", "defense_paid"]:
        if financials.get(field):
            try:
                total_paid += float(financials.get(field, 0))
            except (ValueError, TypeError):
                pass
    table.add_row("Total Paid", format_value(total_paid))

    table.add_row("Recovery Expected", format_value(financials.get("recovery_expected")))
    table.add_row("Recovery Received", format_value(financials.get("recovery_received")))
    table.add_row("Deductible", format_value(financials.get("deductible_amount")))
    table.add_row("Coverage Limit", format_value(financials.get("coverage_limit")))
    table.add_row("Currency", financials.get("currency", "-"))
    table.add_row(
        "Last Reserve Change",
        (
            format_datetime(financials.get("last_reserve_change"))
            if financials.get("last_reserve_change")
            else "-"
        ),
    )
    table.add_row(
        "Last Payment Date",
        (
            format_datetime(financials.get("last_payment_date"))
            if financials.get("last_payment_date")
            else "-"
        ),
    )

    console.print(table)

    # Reserve details
    if financials["reserves"]:
        reserve_table = Table(title="Reserves")
        reserve_table.add_column("Type", style="cyan")
        reserve_table.add_column("Amount", style="green")

        for reserve in financials["reserves"]:
            amount = reserve.get("amount", 0)
            reserve_table.add_row(reserve["reserve_type"], format_value(amount))

        console.print("\n")
        console.print(reserve_table)


@app.command()
def show(
    claim_id: str = typer.Argument(..., help="Claim ID or Claim Number"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Show financial details for a claim."""
    # Resolve identifier (potentially claim number) to UUID first
    resolved_claim_id = resolve_claim_id(claim_id)
    response = api_client.get(f"{CLAIMS_PATH}/{resolved_claim_id}/financials")
    financials = response.json()

    if output == "json":
        print(json.dumps(financials, indent=2))
        return

    _display_financials(financials)


@app.command()
def create(
    claim_identifier: str = typer.Argument(..., help="Claim ID or number"),
    estimated_value: float = typer.Option(..., help="Estimated total value of the claim"),
    reserve_type: List[str] = typer.Option(
        None, help="Type of reserve (optional, overrides default/required)"
    ),
    reserve_amount: List[float] = typer.Option(
        None, help="Amount of the reserve (must match --reserve-type)"
    ),
    currency: str = typer.Option("USD", help="Currency code (default USD)"),
    indemnity_paid: Optional[float] = typer.Option(
        None, help="Total indemnity paid (usually 0 at creation)"
    ),
    expense_paid: Optional[float] = typer.Option(
        None, help="Total expense paid (usually 0 at creation)"
    ),
    defense_paid: Optional[float] = typer.Option(
        None, help="Total defense costs paid (usually 0 at creation)"
    ),
    recovery_expected: Optional[float] = typer.Option(None, help="Total recovery expected"),
    recovery_received: Optional[float] = typer.Option(None, help="Total recovery received"),
    deductible: Optional[float] = typer.Option(None, help="Deductible amount"),
    coverage_limit: Optional[float] = typer.Option(None, help="Coverage limit"),
    output: str = typer.Option(None, help="Output format (json or table)"),
) -> None:
    """Create financial details for a claim, ensuring required reserves are set."""
    if reserve_type and reserve_amount and len(reserve_type) != len(reserve_amount):
        console.print(
            "[red]Error: Number of reserve types must match number of reserve amounts[/red]"
        )
        raise typer.Exit(1)

    claim_id = resolve_claim_id(claim_identifier)

    # 1. Get claim type to determine required reserves
    try:
        claim_response = api_client.get(f"{CLAIMS_PATH}/{claim_id}")
        claim_data = claim_response.json()
        claim_type = claim_data.get("type")
        if not claim_type:
            console.print(
                f"[red]Error: Could not determine type for claim {claim_identifier}[/red]"
            )
            raise typer.Exit(1)
    except Exception as e:
        console.print(f"[red]Error fetching claim details: {e}[/red]")
        raise typer.Exit(1)

    # 2. Fetch ALL reserve configurations for this claim type
    all_configs_for_type = []
    try:
        # Fetch all configs, not just required ones, to check requirements locally
        config_response = api_client.get(
            f"{CONFIG_PATH}/reserves", params={"claim_type": claim_type}
        )
        all_configs_for_type = config_response.json()
        if not isinstance(all_configs_for_type, list):  # Add type check
            console.print(
                f"[yellow]Warning: Expected list from /config/reserves, got {type(all_configs_for_type)}. Proceeding cautiously.[/yellow]"
            )
            all_configs_for_type = []  # Reset to empty list on unexpected type
    except Exception as e:
        console.print(
            f"[yellow]Warning: Could not fetch reserve configurations: {e}. Proceeding without defaults.[/yellow]"
        )

    # 3. Build initial reserves list, ensuring all *required* reserves are present
    initial_reserves = {}
    # Add required reserves with amount 0.00 based on fetched configs
    for config in all_configs_for_type:
        # Ensure config is a dictionary before accessing keys
        if isinstance(config, dict) and config.get("is_required"):
            reserve_type_key = config.get("reserve_type")
            if reserve_type_key:
                # Add required reserve if not already present (user might provide it)
                if reserve_type_key not in initial_reserves:
                    # Default amount to the configured minimum, or 0 if no minimum
                    min_amount_str = config.get("minimum_amount")
                    try:
                        default_amount = (
                            Decimal(min_amount_str)
                            if min_amount_str is not None
                            else Decimal("0.00")
                        )
                    except InvalidOperation:
                        console.print(
                            f"[yellow]Warning: Invalid minimum_amount '{min_amount_str}' for {reserve_type_key}. Defaulting to 0.00.[/yellow]"
                        )
                        default_amount = Decimal("0.00")

                    initial_reserves[reserve_type_key] = {
                        "reserve_type": reserve_type_key,
                        "amount": float(default_amount),  # Use configured minimum or 0
                    }
            else:
                console.print(
                    f"[yellow]Warning: Reserve config missing 'reserve_type': {config}[/yellow]"
                )
        elif not isinstance(config, dict):
            console.print(
                f"[yellow]Warning: Skipping non-dict item in reserve configs: {config}[/yellow]"
            )

    # Override/add with any user-provided reserves AFTER ensuring required ones exist
    if reserve_type and reserve_amount:
        # Create a lookup for minimum amounts of required reserves
        min_amounts = {}
        for config in all_configs_for_type:
            if isinstance(config, dict) and config.get("is_required"):
                rtype = config.get("reserve_type")
                min_amount_str = config.get("minimum_amount")
                if rtype and min_amount_str is not None:
                    try:
                        min_amounts[rtype] = Decimal(min_amount_str)
                    except InvalidOperation:
                        pass  # Ignore invalid minimums, already warned

        for rtype, user_amount_float in zip(reserve_type, reserve_amount):
            final_amount = Decimal(str(user_amount_float))  # Convert user input to Decimal
            # If this is a required reserve with a minimum, ensure user amount meets it
            if rtype in min_amounts:
                final_amount = max(final_amount, min_amounts[rtype])

            # Store as float in the payload dict
            initial_reserves[rtype] = {"reserve_type": rtype, "amount": float(final_amount)}

    # Final list of reserve objects
    final_reserves_list = list(initial_reserves.values())

    # 4. Prepare the data and call the API
    data = {
        "estimated_value": estimated_value,
        "currency": currency,
        "reserves": list(initial_reserves.values()),
    }

    if indemnity_paid is not None:
        data["indemnity_paid"] = indemnity_paid
    if expense_paid is not None:
        data["expense_paid"] = expense_paid
    if defense_paid is not None:
        data["defense_paid"] = defense_paid
    if recovery_expected is not None:
        data["recovery_expected"] = recovery_expected
    if recovery_received is not None:
        data["recovery_received"] = recovery_received
    if deductible is not None:
        data["deductible_amount"] = deductible
    if coverage_limit is not None:
        data["coverage_limit"] = coverage_limit

    # 5. Make API call
    try:
        response = api_client.post(f"{CLAIMS_PATH}/{claim_id}/financials", json=data)
        financials = response.json()

        if output == "json":
            print(json.dumps(financials, indent=2))
            return

        # Debug logging (only for table output)
        console.print("\nRequest data:", style="yellow")
        console.print(data)
        console.print("Created financial details:")
        _display_financials(financials)
    except httpx.HTTPStatusError:
        # Error handled by api_client, ensure the command exits with failure
        raise typer.Exit(code=1)
    except Exception as e:
        console.print(f"[red]An unexpected error occurred: {str(e)}[/red]")
        raise typer.Exit(code=1)


@app.command()
def update(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    estimated_value: Optional[float] = typer.Option(None, help="Estimated total value"),
    reserve_type: Optional[List[str]] = typer.Option(
        None, help="Reserve type (can be specified multiple times)"
    ),
    reserve_amount: Optional[List[float]] = typer.Option(
        None, help="Reserve amount (can be specified multiple times)"
    ),
    indemnity_paid: Optional[float] = typer.Option(None, help="Amount paid for indemnity"),
    expense_paid: Optional[float] = typer.Option(None, help="Amount paid for expenses"),
    defense_paid: Optional[float] = typer.Option(None, help="Amount paid for defense costs"),
    recovery_expected: Optional[float] = typer.Option(None, help="Expected recovery amount"),
    recovery_received: Optional[float] = typer.Option(None, help="Received recovery amount"),
    deductible: Optional[float] = typer.Option(None, help="Deductible amount"),
    coverage_limit: Optional[float] = typer.Option(None, help="Coverage limit"),
    currency: Optional[str] = typer.Option(None, help="Currency code"),
) -> None:
    """Update financial details for a claim."""
    if reserve_type and reserve_amount and len(reserve_type) != len(reserve_amount):
        console.print(
            "Error: Number of reserve types must match number of reserve amounts", style="red"
        )
        raise typer.Exit(1)

    # Resolve identifier to UUID
    claim_id = resolve_claim_id(claim_identifier)

    data = {}

    if estimated_value is not None:
        data["estimated_value"] = estimated_value
    if reserve_type and reserve_amount:
        data["reserves"] = [
            {"reserve_type": t, "amount": a} for t, a in zip(reserve_type, reserve_amount)
        ]
    if indemnity_paid is not None:
        data["indemnity_paid"] = indemnity_paid
    if expense_paid is not None:
        data["expense_paid"] = expense_paid
    if defense_paid is not None:
        data["defense_paid"] = defense_paid
    if recovery_expected is not None:
        data["recovery_expected"] = recovery_expected
    if recovery_received is not None:
        data["recovery_received"] = recovery_received
    if deductible is not None:
        data["deductible_amount"] = deductible
    if coverage_limit is not None:
        data["coverage_limit"] = coverage_limit
    if currency is not None:
        data["currency"] = currency

    # Debug logging
    console.print("\nRequest data:", style="yellow")
    console.print(data)

    try:
        response = api_client.put(f"{CLAIMS_PATH}/{claim_id}/financials", json=data)
        console.print("Updated financial details:")
        financials = response.json()
        _display_financials(financials)
    except Exception as e:
        console.print("\nError response:", style="red")
        console.print(str(e))
        if hasattr(e, "response"):
            console.print("\nResponse status code:", style="red")
            console.print(e.response.status_code)
            console.print("\nResponse content:", style="red")
            try:
                console.print(e.response.json())
            except:
                console.print(e.response.text)
            console.print("\nRequest URL:", style="red")
            console.print(e.response.request.url)
        raise


@app.command(name="update-reserve")
def update_reserve(
    claim_id: str = typer.Argument(..., help="Claim ID or Claim Number"),
    reserve_type: str = typer.Option(..., help="Reserve type"),
    amount: float = typer.Option(..., help="Reserve amount"),
    notes: Optional[str] = typer.Option(None, help="Notes about the change"),
) -> None:
    """Update a specific reserve."""
    data = {
        "reserve_type": reserve_type,
        "amount": amount,
    }
    if notes:
        data["notes"] = notes

    # Debug logging
    console.print("\nRequest data:", style="yellow")
    console.print(data)

    # Debug URL
    from claimentine_cli.api import APIClient
    from claimentine_cli.config import Config

    config = Config()
    url = f"{config.api_url.rstrip('/')}{APIClient.API_VERSION}{CLAIMS_PATH}/{claim_id}/financials/reserve"
    console.print(f"\nCalling API URL: {url}", style="cyan")
    console.print(f"Claim ID type: {type(claim_id)}", style="cyan")
    console.print(f"Reserve type: {reserve_type} (type: {type(reserve_type)})", style="cyan")
    console.print(f"Amount: {amount} (type: {type(amount)})", style="cyan")
    if notes:
        console.print(f"Notes: {notes} (type: {type(notes)})", style="cyan")

    try:
        response = api_client.put(f"{CLAIMS_PATH}/{claim_id}/financials/reserve", json=data)
        console.print("Updated reserve:")
        financials = response.json()
        _display_financials(financials)
    except httpx.HTTPStatusError as e:
        console.print("\nError response:", style="red")
        console.print(str(e))
        if (
            e.response.status_code == 403
            and "exceeds user's reserve limit" in e.response.text.lower()
        ):
            console.print(
                f"\n[bold red]Error: Reserve amount ${amount} exceeds your authority threshold limit.[/bold red]"
            )
            raise typer.Exit(code=1)
        if hasattr(e, "response"):
            try:
                error_data = e.response.json()
                console.print("\nResponse content:", style="red")
                console.print(error_data)
            except:
                console.print("\nResponse content:", style="red")
                console.print(e.response.text)
        raise typer.Exit(code=1)
    except Exception as e:
        console.print("\nError response:", style="red")
        console.print(str(e))
        raise typer.Exit(code=1)


@app.command()
def history(claim_id: str = typer.Argument(..., help="Claim ID or Claim Number")) -> None:
    """Show reserve history for a claim."""
    response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/financials/reserve-history")
    history = response.json()

    table = Table(title="Reserve History")
    table.add_column("Date", style="cyan")
    table.add_column("Type", style="green")
    table.add_column("Previous", style="yellow")
    table.add_column("New", style="blue")
    table.add_column("Changed By", style="magenta")
    table.add_column("Notes", style="dim")

    for entry in history:
        table.add_row(
            format_datetime(entry["changed_at"]),
            entry["reserve_type"],
            f"${float(entry['previous_amount']):.{DECIMAL_PLACES}f}",
            f"${float(entry['new_amount']):.{DECIMAL_PLACES}f}",
            entry["changed_by_id"],
            entry["notes"] or "-",
        )

    console.print(table)


@app.command(name="add-payment")
def add_payment(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    payment_type: PaymentType = typer.Option(
        ..., help="Type of payment (INDEMNITY, EXPENSE, or DEFENSE)"
    ),
    amount: float = typer.Option(..., help="Payment amount (must be positive)"),
    payee: str = typer.Option(..., help="Recipient of the payment"),
    payment_date: datetime = typer.Option(
        ..., formats=["%Y-%m-%d"], help="Payment date (YYYY-MM-DD)"
    ),
    notes: Optional[str] = typer.Option(None, help="Notes about the payment"),
) -> None:
    """Add a new payment to a claim."""
    # Resolve identifier to UUID
    try:
        claim_id = resolve_claim_id(claim_identifier)
    except Exception as e:
        console.print(f"[red]Error resolving claim identifier: {e}[/red]")
        raise typer.Exit(1)

    data = {
        "payment_type": payment_type.value,  # Use enum value
        "amount": amount,
        "payee": payee,
        "payment_date": payment_date.isoformat(),  # Send as ISO string
        "notes": notes,
    }

    # Debug logging
    console.print("\nRequest data:", style="yellow")
    console.print(data)

    try:
        response = api_client.post(f"{CLAIMS_PATH}/{claim_id}/financials/payments", json=data)
        payment = response.json()

        console.print("Payment added successfully:", style="green")
        # Display minimal payment details or reuse _display_financials after fetching updated financials
        table = Table(show_header=False, title="Payment Details")
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="green")
        table.add_row("Payment ID", payment["id"])
        table.add_row("Type", payment["payment_type"])
        table.add_row("Amount", f"${float(payment['amount']):.{DECIMAL_PLACES}f}")
        table.add_row("Payee", payment["payee"])
        table.add_row("Payment Date", format_datetime(payment["payment_date"]))
        table.add_row("Created By", payment["created_by_id"])
        console.print(table)

        # Optionally fetch and display updated financials
        # updated_financials_response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/financials")
        # updated_financials = updated_financials_response.json()
        # _display_financials(updated_financials)

    except httpx.HTTPStatusError:
        # Error handled and printed by api_client, just exit.
        pass
    except Exception as e:
        console.print(f"[red]An unexpected error occurred: {e}[/red]")
        raise typer.Exit(code=1)


@app.command(name="list-payments")
def list_payments(
    claim_identifier: str = typer.Argument(..., help="Claim ID or Claim Number"),
    page: int = typer.Option(1, help="Page number", min=1),
    page_size: int = typer.Option(10, help="Items per page", min=1, max=100),
    output_format: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List payments for a claim."""
    # Resolve identifier to UUID
    try:
        claim_id = resolve_claim_id(claim_identifier)
    except Exception as e:
        console.print(f"[red]Error resolving claim identifier: {e}[/red]")
        raise typer.Exit(1)

    params = {"skip": (page - 1) * page_size, "limit": page_size}

    try:
        response = api_client.get(f"{CLAIMS_PATH}/{claim_id}/financials/payments", params=params)
        data = response.json()
        payments = data.get("items", [])
        total = data.get("total", 0)

        if not payments:
            if output_format == "json":
                print(json.dumps({"items": [], "total": 0}))
            else:
                console.print(f"No payments found for claim {claim_identifier}.")
            return

        if output_format == "json":
            print(json.dumps(data, indent=2))
            return

        if payments:
            table = Table(title=f"Payments for Claim {claim_identifier}")
            table.add_column("ID", style="dim")
            table.add_column("Date", style="cyan")
            table.add_column("Type", style="green")
            table.add_column("Amount", style="yellow")
            table.add_column("Payee", style="blue")
            table.add_column("Notes", style="dim")

            for payment in payments:
                # Determine color based on payment type
                payment_type = payment.get("payment_type", "")
                payment_type_colors = {"INDEMNITY": "green", "EXPENSE": "yellow", "DEFENSE": "blue"}
                type_color = payment_type_colors.get(payment_type, "white")

                table.add_row(
                    payment["id"],
                    format_datetime(payment["payment_date"]),
                    payment_type,
                    f"${float(payment['amount']):.{DECIMAL_PLACES}f}",
                    payment["payee"],
                    payment.get("notes", "-"),
                    style=type_color,
                )
            console.print(table)

    except httpx.HTTPStatusError:
        # Error handled and printed by api_client, just exit.
        pass
    except Exception as e:
        console.print(f"[red]An unexpected error occurred: {e}[/red]")
        raise typer.Exit(code=1)
