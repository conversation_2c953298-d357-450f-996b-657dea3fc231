# Claimentine CLI

Command Line Interface for the Elysian Claims Management System.

## Requirements

- Python 3.11+
- Poetry for dependency management
- Running Claimentine backend server

## Installation

1. Install dependencies:
```bash
poetry install
```

2. Install pre-commit hooks:
```bash
poetry run pre-commit install
```

3. Initialize configuration:
```bash
poetry run clm config init
```

## Usage

The command line tool is invoked using `clm`:

```bash
poetry run clm [command] [subcommand] [options]
```

### Authentication

```bash
# Login to the system
poetry run clm auth login --email <email> --password <password>

# Check current auth status
poetry run clm auth status

# List active sessions
poetry run clm auth session list

# Revoke a specific session
poetry run clm auth session revoke <session-id>

# Logout
poetry run clm auth logout

# Refresh access token using your refresh token
poetry run clm auth refresh
```

### User Management

```bash
# List all users
poetry run clm users list

# List users with filters
poetry run clm users list --role CLAIMS_ADJUSTER --status ACTIVE

# Create new user
poetry run clm users create --email <EMAIL> --password userpass \
                            --first-name John --last-name Doe \
                            --role CLAIMS_ADJUSTER

# Get user details
poetry run clm users get <user-id>
poetry run clm users get --email <EMAIL>

# Update user
poetry run clm users update <user-id> --department "Claims" \
                            --job-title "Senior Adjuster" \
                            --status ACTIVE

# Delete user
poetry run clm users delete <user-id>
poetry run clm users delete --email <EMAIL> --yes
```

### Configuration

Basic configuration commands:

```bash
# Show current configuration
poetry run clm config show

# Set API URL
poetry run clm config set api-url http://localhost:8000

# Reset all configuration
poetry run clm config reset

# Reset specific configuration
poetry run clm config reset api_url --yes

# Initialize configuration
poetry run clm config init
```

Environment switching:

```bash
# View all configured environments
poetry run clm config show

# Switch between environments
poetry run clm config env dev     # Development
poetry run clm config env staging # Staging
poetry run clm config env prod    # Production

# Set URL for a specific environment 
poetry run clm config env-url dev http://localhost:8000
poetry run clm config env-url staging https://staging-api.claimentine.com
poetry run clm config env-url prod https://api.claimentine.com
```

### Claims Management

```bash
# List claims
poetry run clm claims list
poetry run clm claims list --status OPEN --type PROPERTY --limit 10

# Create a new claim
poetry run clm claims create --customer-id 123 --type PROPERTY \
                            --description "Water damage claim" \
                            --date-of-loss "2023-06-15"

# Get claim details
poetry run clm claims get <claim-id>
poetry run clm claims get --number "CLM-2023-00123"

# Update claim
poetry run clm claims update <claim-id> --status IN_PROGRESS \
                             --priority HIGH \
                             --assigned-to <EMAIL>

# Delete claim
poetry run clm claims delete <claim-id> --yes
```

### Customers

```bash
# List customers
poetry run clm customers list
poetry run clm customers list --search "Smith" --limit 20

# Create customer
poetry run clm customers create --first-name John --last-name Smith \
                               --email <EMAIL> \
                               --phone "+1234567890"

# Get customer details
poetry run clm customers get <customer-id>
poetry run clm customers get --email <EMAIL>

# Update customer
poetry run clm customers update <customer-id> --address "123 Main St" \
                               --city "New York" --state "NY" \
                               --postal-code "10001"

# Delete customer
poetry run clm customers delete <customer-id> --yes
```

### System Management

```bash
# Initialize database
poetry run clm system initialize-db

# List system configurations
poetry run clm system config list

# Get system configuration
poetry run clm system config show --key "DOCUMENT_RETENTION_DAYS"

# Update system configuration
poetry run clm system config update <config-id> --value "90" \
                                   --description "Document retention in days"
```

## Available Roles

- `SYSTEM_ADMIN`: Full system access
- `TEAM_MANAGER`: Team and workflow management
- `CLAIMS_SUPERVISOR`: Claims supervision and approval
- `CLAIMS_ADJUSTER`: Claims processing
- `READ_ONLY`: View-only access

## Development

- Format code: `poetry run black .`
- Sort imports: `poetry run isort .`
- Run linter: `poetry run ruff check .`
- Run type checker: `poetry run mypy .`
- Run tests: `poetry run pytest`

## Command Groups

- `auth`: Authentication and session management
- `users`: User management
- `config`: CLI configuration 
- `claims`: Claims management
- `customers`: Customer management
- `fnols`: First Notice of Loss management
- `documents`: Document management
- `notes`: Notes management
- `tasks`: Task management
- `system`: System management
- `recovery`: Recovery management
- `witnesses`: Witness management
- `attorneys`: Attorney management
- `audit`: Audit log management 