#!/usr/bin/env python3
"""Tests for injured persons and injuries CL<PERSON> commands."""

import json
import os
from typing import Dict

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_01_create_injured_person_auto(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict):
    """Test creating an injured person for an auto claim."""
    print("\nTesting Auto Claim Injured Person Creation...")

    claim_id = auto_claim.get("id")
    if not claim_id:
        pytest.fail("No auto claim ID available")

    # Create injured person
    command = [
        "claims",
        "create-injured-person",
        auto_claim["number"],
        "--name",
        "<PERSON>",
        "--person-type",
        "PASSENGER",
        "--contact-info",
        "<EMAIL>",
        "--age",
        "35",
        "--incident-location",
        "Front passenger seat",
        "--incident-description",
        "Passenger in vehicle during collision",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Officer <PERSON>",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("auto_details_id") is not None
    assert response.get("gl_details_id") is None  # Should be None for auto claim
    assert response.get("name") == "John Doe"
    assert response.get("person_type") == "PASSENGER"
    assert response.get("contact_info") == "<EMAIL>"
    assert response.get("age") == 35
    assert response.get("incident_location") == "Front passenger seat"
    assert response.get("incident_description") == "Passenger in vehicle during collision"

    # Store the injured person ID for later tests
    os.environ["AUTO_INJURED_PERSON_ID"] = str(response["id"])
    # print(f"AUTO_INJURED_PERSON_ID: {os.environ['AUTO_INJURED_PERSON_ID']}")


def test_02_create_injured_person_gl(runner: CliRunner, gl_claim: dict):
    """Test creating an injured person for a GL claim."""
    print("\nTesting GL Claim Injured Person Creation...")

    claim_id = gl_claim.get("id")
    if not claim_id:
        pytest.fail("No GL claim ID available")

    # Create injured person
    command = [
        "claims",
        "create-injured-person",
        gl_claim["number"],
        "--name",
        "Jane Smith",
        "--person-type",
        "GUEST",
        "--contact-info",
        "<EMAIL>",
        "--age",
        "42",
        "--incident-location",
        "Store entrance",
        "--incident-description",
        "Slipped on wet floor near entrance",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Manager Johnson",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("gl_details_id") is not None
    assert response.get("auto_details_id") is None  # Should be None for GL claim
    assert response.get("name") == "Jane Smith"
    assert response.get("person_type") == "GUEST"
    assert response.get("contact_info") == "<EMAIL>"
    assert response.get("age") == 42
    assert response.get("incident_location") == "Store entrance"

    # Store the injured person ID for later tests
    os.environ["GL_INJURED_PERSON_ID"] = str(response["id"])
    # print(f"GL_INJURED_PERSON_ID: {os.environ['GL_INJURED_PERSON_ID']}")


def test_03_list_injured_persons(runner: CliRunner, auto_claim: dict):
    """Test listing injured persons for a claim."""
    print("\nTesting List Injured Persons...")
    claim_number = auto_claim["number"]

    # Create first injured person for this test's claim
    person1_command = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        "John Doe Test03",
        "--person-type",
        "PASSENGER",
        "--contact-info",
        "<EMAIL>",
        "--age",
        "35",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Officer Smith",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]
    person1_result = runner.invoke(app, person1_command, catch_exceptions=True)
    print("\nSetup Step (adding first person for test_03):")
    print(f"  Exit code: {person1_result.exit_code}")
    print(f"  Exception: {person1_result.exception}")
    assert (
        person1_result.exit_code == 0
    ), f"Failed to create first injured person for test_03. Output: {person1_result.stdout}"
    person1_data = validate_json_response(person1_result.stdout)
    person1_id = str(person1_data["id"])

    # Create a second injured person for this test's claim
    person2_command = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        "Mary Johnson Test03",
        "--person-type",
        "DRIVER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Officer Jones",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]
    person2_result = runner.invoke(app, person2_command, catch_exceptions=True)
    print("\nSetup Step (adding second person for test_03):")
    print(f"  Exit code: {person2_result.exit_code}")
    print(f"  Exception: {person2_result.exception}")
    assert (
        person2_result.exit_code == 0
    ), f"Failed to create second injured person for test_03. Output: {person2_result.stdout}"
    person2_data = validate_json_response(person2_result.stdout)
    person2_id = str(person2_data["id"])

    # Test listing injured persons
    list_command_args = ["claims", "list-injured-persons", claim_number, "--output", "json"]
    result = runner.invoke(app, list_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response - expecting a list with at least 2 persons
    response_list = validate_json_response(result.stdout)
    assert isinstance(response_list, list)
    assert len(response_list) >= 2

    # Check if our created persons are in the list
    listed_ids = [p.get("id") for p in response_list]
    assert person1_id in listed_ids, "Person 1 (John Doe Test03) not found in list"
    assert person2_id in listed_ids, "Person 2 (Mary Johnson Test03) not found in list"

    for p in response_list:
        if p.get("id") == person1_id:
            assert p.get("name") == "John Doe Test03"
        elif p.get("id") == person2_id:
            assert p.get("name") == "Mary Johnson Test03"


def test_04_get_injured_person(runner: CliRunner, auto_claim: dict):
    """Test getting a specific injured person."""
    print("\nTesting Get Injured Person...")
    claim_number = auto_claim["number"]

    # Create an injured person specifically for this test
    person_name_to_get = "Get Me Person Test04"
    person_contact_to_get = "<EMAIL>"
    create_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        person_name_to_get,
        "--person-type",
        "PEDESTRIAN",
        "--contact-info",
        person_contact_to_get,
        "--age",
        "40",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Officer Test",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating person for get_injured_person test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create person for get_injured_person test. Output: {create_result.stdout}"
    created_person_data = validate_json_response(create_result.stdout)
    person_to_get_id = str(created_person_data["id"])

    # Get the created injured person
    get_command_args = [
        "claims",
        "get-injured-person",
        claim_number,
        person_to_get_id,
        "--json",
    ]
    result = runner.invoke(app, get_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == person_to_get_id
    assert response.get("name") == person_name_to_get
    assert response.get("person_type") == "PEDESTRIAN"
    assert response.get("contact_info") == person_contact_to_get
    assert "injuries" in response  # Should have injuries array, even if empty


def test_05_update_injured_person(runner: CliRunner, auto_claim: dict):
    """Test updating an injured person."""
    print("\nTesting Update Injured Person...")
    claim_id = auto_claim["id"]  # Get the claim ID
    claim_number = auto_claim["number"]

    # Update the claim with auto_details before creating an injured person
    update_claim_args = [
        "claims",
        "update",
        claim_number,
        "--vehicle-make",
        "TestMakeForT05",
        "--vehicle-model",
        "TestModelForT05",
        "--vehicle-year",
        "2023",
        "--output",
        "json",
    ]
    update_claim_result = runner.invoke(app, update_claim_args, catch_exceptions=True)
    print("\nSetup Step (updating claim with auto_details for test_05):")
    print(f"  Exit code: {update_claim_result.exit_code}")
    print(f"  Exception: {update_claim_result.exception}")
    print(f"  Output: {update_claim_result.stdout}")
    assert (
        update_claim_result.exit_code == 0
    ), f"Failed to update claim with auto_details. Output: {update_claim_result.stdout}"

    # Create an initial injured person for this test
    original_name = "Update Me Original Test05"
    original_contact = "<EMAIL>"
    original_age = 30
    original_person_type = "CYCLIST"
    original_incident_desc = "Original incident description for update test"

    create_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        original_name,
        "--person-type",
        original_person_type,
        "--contact-info",
        original_contact,
        "--age",
        str(original_age),
        "--incident-description",
        original_incident_desc,
        "--incident-report-status",
        "NOT_REQUIRED",  # Changed from NOT_APPLICABLE
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating person for update_injured_person test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create person for update_injured_person test. Output: {create_result.stdout}"
    created_person_data = validate_json_response(create_result.stdout)
    # Add assertion for auto_details_id
    assert (
        created_person_data.get("auto_details_id") is not None
    ), "auto_details_id is None on created person in test_05"
    person_to_update_id = str(created_person_data["id"])

    # Update the injured person
    updated_name = "Update Me Updated Test05"
    updated_contact = "<EMAIL>"
    updated_age = 31
    updated_incident_desc = "Updated incident description for test"

    update_command_args = [
        "claims",
        "update-injured-person",
        claim_number,
        person_to_update_id,
        "--name",
        updated_name,
        "--contact-info",
        updated_contact,
        "--age",
        str(updated_age),
        "--incident-description",
        updated_incident_desc,
        "--output",
        "json",
    ]
    result = runner.invoke(app, update_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == person_to_update_id
    assert response.get("name") == updated_name
    assert response.get("contact_info") == updated_contact
    assert response.get("age") == updated_age
    assert response.get("incident_description") == updated_incident_desc

    # Verify person_type was preserved from original
    assert response.get("person_type") == original_person_type


def test_06_create_injury(runner: CliRunner, auto_claim: dict):
    """Test creating an injury for an injured person."""
    print("\nTesting Create Injury...")
    claim_number = auto_claim["number"]

    # Create a parent injured person for this test
    parent_person_name = "Injured Parent Test06"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        parent_person_name,
        "--person-type",
        "BYSTANDER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating parent person for create_injury test):")
    print(f"  Exit code: {create_person_result.exit_code}")
    print(f"  Exception: {create_person_result.exception}")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create parent person for create_injury. Output: {create_person_result.stdout}"
    parent_person_data = validate_json_response(create_person_result.stdout)
    parent_person_id_test06 = str(parent_person_data["id"])

    # Create first injury for this parent person
    injury1_desc = "Fractured right wrist Test06"
    injury1_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test06,
        "--injury-description",
        injury1_desc,
        "--injury-type",
        "FRACTURE",
        "--injury-severity",
        "MODERATE",
        "--equipment-involved",
        "--equipment-details",
        "Airbag deployment Test06",
        "--medical-treatment-requirements",
        "HOSPITALIZATION",
        "--treatment-nature",
        "Emergency Room Visit and Casting",
        "--estimated-cost",
        "8500",
        "--output",
        "json",
    ]
    injury1_result = runner.invoke(app, injury1_command_args, catch_exceptions=True)
    assert (
        injury1_result.exit_code == 0
    ), f"Failed to create first injury. Output: {injury1_result.stdout}"
    injury1_response = validate_json_response(injury1_result.stdout)
    assert "id" in injury1_response
    # injury1_id_test06 = str(injury1_response["id"]) # No longer setting env var or needing to store for cross-test use
    assert injury1_response.get("injured_person_id") == parent_person_id_test06
    assert injury1_response.get("injury_description") == injury1_desc
    assert injury1_response.get("injury_type") == "FRACTURE"

    # Create a second injury for the same person
    injury2_desc = "Neck whiplash Test06"
    injury2_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test06,
        "--injury-description",
        injury2_desc,
        "--injury-type",
        "SOFT_TISSUE",
        "--injury-severity",
        "MILD",
        "--estimated-cost",
        "2500",
        "--output",
        "json",
    ]
    injury2_result = runner.invoke(app, injury2_command_args, catch_exceptions=True)
    print(f"DEBUG test_07: injury2_result.stdout for create: {injury2_result.stdout}")  # DEBUG
    print(
        f"DEBUG test_07: injury2_result.exit_code for create: {injury2_result.exit_code}"
    )  # DEBUG
    print(
        f"DEBUG test_07: injury2_result.exception for create: {injury2_result.exception}"
    )  # DEBUG
    assert (
        injury2_result.exit_code == 0
    ), f"Failed to create second injury. Output: {injury2_result.stdout}"
    injury2_response = validate_json_response(injury2_result.stdout)
    assert "id" in injury2_response
    # injury2_id_test06 = str(injury2_response["id"]) # No longer setting env var or needing to store for cross-test use
    assert injury2_response.get("injured_person_id") == parent_person_id_test06
    assert injury2_response.get("injury_description") == injury2_desc


def test_07_list_injuries(runner: CliRunner, auto_claim: dict):
    """Test listing injuries for an injured person."""
    print("\nTesting List Injuries...")
    claim_number = auto_claim["number"]

    # Create a parent injured person for this test
    parent_person_name = "Injured Parent Test07"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        parent_person_name,
        "--person-type",
        "BYSTANDER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating parent person for list_injuries test):")
    print(f"  Exit code: {create_person_result.exit_code}")
    print(f"  Exception: {create_person_result.exception}")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create parent person for list_injuries. Output: {create_person_result.stdout}"
    parent_person_data = validate_json_response(create_person_result.stdout)
    parent_person_id_test07 = str(parent_person_data["id"])

    # Create first injury for this parent person
    injury1_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test07,
        "--injury-description",
        "Listed Injury 1 Test07",
        "--injury-type",
        "BURN",
        "--injury-severity",
        "MILD",
        "--output",
        "json",
    ]
    injury1_result = runner.invoke(app, injury1_command_args, catch_exceptions=True)
    assert (
        injury1_result.exit_code == 0
    ), f"Failed to create first injury for list_injuries. Output: {injury1_result.stdout}"
    injury1_response = validate_json_response(injury1_result.stdout)
    injury1_id_test07 = str(injury1_response["id"])

    # Create a second injury for the same person
    injury2_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test07,
        "--injury-description",
        "Listed Injury 2 Test07",
        "--injury-type",
        "ABRASION",
        "--injury-severity",
        "MINOR",
        "--output",
        "json",
    ]
    injury2_result = runner.invoke(app, injury2_command_args, catch_exceptions=True)
    assert (
        injury2_result.exit_code == 0
    ), f"Failed to create second injury for list_injuries. Output: {injury2_result.stdout}"
    injury2_response = validate_json_response(injury2_result.stdout)
    injury2_id_test07 = str(injury2_response["id"])

    # List injuries for the parent person
    list_injuries_command_args = [
        "claims",
        "list-injuries",
        claim_number,
        parent_person_id_test07,
        "--output",
        "json",
    ]
    result = runner.invoke(app, list_injuries_command_args, catch_exceptions=True)

    assert result.exit_code == 0

    # Validate response
    response_list = validate_json_response(result.stdout)
    assert isinstance(response_list, list)
    assert len(response_list) >= 2  # Should have at least the two injuries we created

    # Check if both created injuries are in the list
    listed_injury_ids = [injury.get("id") for injury in response_list]
    assert (
        injury1_id_test07 in listed_injury_ids
    ), "Injury 1 (Listed Injury 1 Test07) not found in list"
    assert (
        injury2_id_test07 in listed_injury_ids
    ), "Injury 2 (Listed Injury 2 Test07) not found in list"


def test_08_get_injury(runner: CliRunner, auto_claim: dict):
    """Test getting a specific injury."""
    print("\nTesting Get Injury...")
    claim_number = auto_claim["number"]

    # Create a parent injured person for this test
    parent_person_name = "Injured Parent Test08"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        parent_person_name,
        "--person-type",
        "PEDESTRIAN",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating parent person for get_injury test):")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create parent person for get_injury. Output: {create_person_result.stdout}"
    parent_person_data = validate_json_response(create_person_result.stdout)
    parent_person_id_test08 = str(parent_person_data["id"])

    # Create an injury for this parent person
    injury_desc_to_get = "Injury To Get Test08"
    create_injury_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test08,
        "--injury-description",
        injury_desc_to_get,
        "--injury-type",
        "CONCUSSION",
        "--injury-severity",
        "MODERATE",
        "--output",
        "json",
    ]
    create_injury_result = runner.invoke(app, create_injury_command_args, catch_exceptions=True)
    print("\nSetup Step (creating injury for get_injury test):")
    assert (
        create_injury_result.exit_code == 0
    ), f"Failed to create injury for get_injury. Output: {create_injury_result.stdout}"
    created_injury_data = validate_json_response(create_injury_result.stdout)
    injury_to_get_id_test08 = str(created_injury_data["id"])

    # Get the created injury
    get_injury_command_args = [
        "claims",
        "get-injury",
        claim_number,
        parent_person_id_test08,
        injury_to_get_id_test08,
        "--json",
    ]
    result = runner.invoke(app, get_injury_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response["id"] == injury_to_get_id_test08
    assert response.get("injured_person_id") == parent_person_id_test08
    assert response.get("injury_description") == injury_desc_to_get


def test_09_update_injury(runner: CliRunner, auto_claim: dict):
    """Test updating an injury."""
    print("\nTesting Update Injury...")
    claim_number = auto_claim["number"]

    # Create a parent injured person for this test
    parent_person_name = "Injured Parent Test09"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        parent_person_name,
        "--person-type",
        "PASSENGER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating parent person for update_injury test):")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create parent person for update_injury. Output: {create_person_result.stdout}"
    parent_person_data = validate_json_response(create_person_result.stdout)
    parent_person_id_test09 = str(parent_person_data["id"])

    # Create an initial injury for this parent person
    original_injury_desc = "Original Injury for Update Test09"
    original_injury_severity = "MINOR"
    original_estimated_cost = 500.0
    create_injury_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test09,
        "--injury-description",
        original_injury_desc,
        "--injury-type",
        "SCRAPE",
        "--injury-severity",
        original_injury_severity,
        "--estimated-cost",
        str(original_estimated_cost),
        "--output",
        "json",
    ]
    create_injury_result = runner.invoke(app, create_injury_command_args, catch_exceptions=True)
    print("\nSetup Step (creating initial injury for update_injury test):")
    assert (
        create_injury_result.exit_code == 0
    ), f"Failed to create initial injury for update_injury. Output: {create_injury_result.stdout}"
    created_injury_data = validate_json_response(create_injury_result.stdout)
    injury_to_update_id_test09 = str(created_injury_data["id"])

    # Update the injury
    updated_description = "Updated Injury Description Test09"
    updated_severity = "SEVERE"
    updated_estimated_cost = 12500.0
    update_injury_command_args = [
        "claims",
        "update-injury",
        claim_number,
        parent_person_id_test09,
        injury_to_update_id_test09,
        "--injury-description",
        updated_description,
        "--injury-severity",
        updated_severity,
        "--estimated-cost",
        str(updated_estimated_cost),
        "--insurance-billing-status",
        "PARTIAL_PAYMENT",
        "--output",
        "json",
    ]
    result = runner.invoke(app, update_injury_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response["id"] == injury_to_update_id_test09
    assert response.get("injured_person_id") == parent_person_id_test09
    assert response.get("injury_description") == updated_description
    assert response.get("injury_severity") == updated_severity
    assert float(response.get("estimated_cost")) == updated_estimated_cost
    assert response.get("insurance_billing_status") == "PARTIAL_PAYMENT"


def test_10_delete_injury(runner: CliRunner, auto_claim: dict):
    """Test deleting an injury."""
    print("\nTesting Delete Injury...")
    claim_number = auto_claim["number"]

    # Create a parent injured person for this test
    parent_person_name = "Injured Parent Test10"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        parent_person_name,
        "--person-type",
        "DRIVER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating parent person for delete_injury test):")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create parent person for delete_injury. Output: {create_person_result.stdout}"
    parent_person_data = validate_json_response(create_person_result.stdout)
    parent_person_id_test10 = str(parent_person_data["id"])

    # Create an injury specifically for deletion
    injury_desc_to_delete = "Injury To Delete Test10"
    create_injury_command_args = [
        "claims",
        "create-injury",
        claim_number,
        parent_person_id_test10,
        "--injury-description",
        injury_desc_to_delete,
        "--injury-type",
        "DISLOCATION",
        "--injury-severity",
        "SEVERE",
        "--output",
        "json",
    ]
    create_injury_result = runner.invoke(app, create_injury_command_args, catch_exceptions=True)
    print("\nSetup Step (creating injury for deletion in delete_injury test):")
    assert (
        create_injury_result.exit_code == 0
    ), f"Failed to create injury for deletion. Output: {create_injury_result.stdout}"
    created_injury_data = validate_json_response(create_injury_result.stdout)
    injury_to_delete_id_test10 = str(created_injury_data["id"])

    # List injuries before deletion to confirm it exists (optional but good for sanity check)
    list_command = [
        "claims",
        "list-injuries",
        claim_number,
        parent_person_id_test10,
        "--output",
        "json",
    ]
    list_result = runner.invoke(app, list_command, catch_exceptions=True)
    assert list_result.exit_code == 0, "Failed to list injuries before deletion"
    injuries_before = validate_json_response(list_result.stdout)
    assert any(
        injury["id"] == injury_to_delete_id_test10 for injury in injuries_before
    ), "Injury to delete not found in list before deletion"

    # Now delete the injury
    delete_command_args = [
        "claims",
        "delete-injury",
        claim_number,
        parent_person_id_test10,
        injury_to_delete_id_test10,
        "--yes",
    ]
    delete_result = runner.invoke(app, delete_command_args, catch_exceptions=True)

    # Print debug info for delete operation
    print(f"Delete Exit code: {delete_result.exit_code}")
    print(f"Delete Exception: {delete_result.exception}")
    print(f"Delete Output: {delete_result.stdout}")

    assert delete_result.exit_code == 0, f"Failed to delete injury. Output: {delete_result.stdout}"

    # Verify the injury has been deleted by trying to get it
    verify_get_command_args = [
        "claims",
        "get-injury",
        claim_number,
        parent_person_id_test10,
        injury_to_delete_id_test10,
        "--json",
    ]
    verify_get_result = runner.invoke(app, verify_get_command_args, catch_exceptions=True)
    assert (
        verify_get_result.exit_code != 0
    ), "Injury was not deleted, get-injury command still found it."
    # Specifically, we expect a 404, which results in exit code 1 from the CLI helper
    assert "404 Not Found" in verify_get_result.stdout or verify_get_result.exit_code == 1


def test_11_delete_injured_person(runner: CliRunner, gl_claim: dict):
    """Test deleting an injured person and their injuries."""
    print("\nTesting Delete Injured Person...")
    claim_number = gl_claim["number"]

    # Create an injured person specifically for this test on the GL claim
    person_name_to_delete = "GL Person To Delete Test11"
    create_person_command_args = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        person_name_to_delete,
        "--person-type",
        "VISITOR",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "NOT_REQUIRED",
        "--output",
        "json",
    ]
    create_person_result = runner.invoke(app, create_person_command_args, catch_exceptions=True)
    print("\nSetup Step (creating person for delete_injured_person test):")
    assert (
        create_person_result.exit_code == 0
    ), f"Failed to create person for delete test. Output: {create_person_result.stdout}"
    created_person_data = validate_json_response(create_person_result.stdout)
    gl_person_to_delete_id_test11 = str(created_person_data["id"])

    # First create an injury for this GL injured person
    create_injury_command_args = [
        "claims",
        "create-injury",
        claim_number,
        gl_person_to_delete_id_test11,
        "--injury-description",
        "Sprained ankle for GL person Test11",
        "--injury-type",
        "SPRAIN",
        "--injury-severity",
        "MINOR",
        "--output",
        "json",
    ]
    create_injury_result = runner.invoke(app, create_injury_command_args, catch_exceptions=True)
    print("\nSetup Step (adding injury to GL person for delete_injured_person test):")
    assert (
        create_injury_result.exit_code == 0
    ), f"Failed to create injury for GL person. Output: {create_injury_result.stdout}"
    # injury_data = validate_json_response(create_injury_result.stdout)
    # gl_injury_id_test11 = str(injury_data["id"]) # Not strictly needed for this test logic

    # Delete the injured person
    delete_person_command_args = [
        "claims",
        "delete-injured-person",
        claim_number,
        gl_person_to_delete_id_test11,
        "--yes",
    ]
    result = runner.invoke(app, delete_person_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Delete Person Exit code: {result.exit_code}")
    print(f"Delete Person Exception: {result.exception}")
    print(f"Delete Person Output: {result.stdout}")

    assert result.exit_code == 0, f"Failed to delete injured person. Output: {result.stdout}"
    assert "deleted successfully" in result.stdout

    # Verify person was deleted by trying to get them
    verify_get_person_command_args = [
        "claims",
        "get-injured-person",
        claim_number,
        gl_person_to_delete_id_test11,
        "--json",
    ]
    verify_get_person_result = runner.invoke(
        app, verify_get_person_command_args, catch_exceptions=True
    )
    assert (
        verify_get_person_result.exit_code != 0
    ), "Injured person should no longer exist after deletion."
    # Specifically, we expect a 404, which results in exit code 1 from the CLI helper
    assert (
        "404 Not Found" in verify_get_person_result.stdout
        or verify_get_person_result.exit_code == 1
    )


def test_12_multiple_persons_with_multiple_injuries(runner: CliRunner, auto_claim: dict):
    """Test creating multiple injured persons, each with multiple injuries, and then listing them."""
    print("\nTesting Multiple Injured Persons with Multiple Injuries...")

    claim_number = auto_claim["number"]
    person_ids = []
    injury_ids_map = {}

    # Create first person
    person1_command = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        "First Multiple Test Person",
        "--person-type",
        "DRIVER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Officer Smith",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]
    person1_result = runner.invoke(app, person1_command, catch_exceptions=True)
    assert (
        person1_result.exit_code == 0
    ), f"Failed to create first person. Output: {person1_result.stdout}"
    person1_data = validate_json_response(person1_result.stdout)
    person1_id = str(person1_data["id"])
    # print(f"Created person1_id: {person1_id}")
    person_ids.append(person1_id)
    injury_ids_map[person1_id] = []

    # Create second person
    person2_command = [
        "claims",
        "create-injured-person",
        claim_number,
        "--name",
        "Second Multiple Test Person",
        "--person-type",
        "PASSENGER",
        "--contact-info",
        "<EMAIL>",
        "--incident-report-status",
        "PENDING",
        "--report-filer-name",
        "Witness A",
        "--report-filer-contact",
        "<EMAIL>",
        "--output",
        "json",
    ]
    person2_result = runner.invoke(app, person2_command, catch_exceptions=True)
    assert (
        person2_result.exit_code == 0
    ), f"Failed to create second person. Output: {person2_result.stdout}"
    person2_data = validate_json_response(person2_result.stdout)
    person2_id = str(person2_data["id"])
    # print(f"Created person2_id: {person2_id}")
    person_ids.append(person2_id)
    injury_ids_map[person2_id] = []

    # Add injuries to the first person
    injury1_person1_command = [
        "claims",
        "create-injury",
        claim_number,
        person1_id,
        "--injury-description",
        "First person's first injury",
        "--injury-type",
        "LACERATION",
        "--injury-severity",
        "MINOR",
        "--output",
        "json",
    ]
    injury1_person1_result = runner.invoke(app, injury1_person1_command, catch_exceptions=True)
    assert (
        injury1_person1_result.exit_code == 0
    ), f"Failed to create first injury for first person. Output: {injury1_person1_result.stdout}"
    injury1_person1_data = validate_json_response(injury1_person1_result.stdout)
    # print(f"Created injury1_id (for person1): {str(injury1_person1_data['id'])}")
    injury_ids_map[person1_id].append(str(injury1_person1_data["id"]))

    injury2_person1_command = [
        "claims",
        "create-injury",
        claim_number,
        person1_id,
        "--injury-description",
        "First person's second injury",
        "--injury-type",
        "CONTUSION",
        "--injury-severity",
        "MILD",
        "--output",
        "json",
    ]
    injury2_person1_result = runner.invoke(app, injury2_person1_command, catch_exceptions=True)
    assert (
        injury2_person1_result.exit_code == 0
    ), f"Failed to create second injury for first person. Output: {injury2_person1_result.stdout}"
    injury2_person1_data = validate_json_response(injury2_person1_result.stdout)
    # print(f"Created injury2_id (for person1): {str(injury2_person1_data['id'])}")
    injury_ids_map[person1_id].append(str(injury2_person1_data["id"]))

    # Add injuries to the second person
    injury1_person2_command = [
        "claims",
        "create-injury",
        claim_number,
        person2_id,
        "--injury-description",
        "Second person's injury",
        "--injury-type",
        "WHIPLASH",
        "--injury-severity",
        "MODERATE",
        "--output",
        "json",
    ]
    injury1_person2_result = runner.invoke(app, injury1_person2_command, catch_exceptions=True)
    assert (
        injury1_person2_result.exit_code == 0
    ), f"Failed to create injury for second person. Output: {injury1_person2_result.stdout}"
    injury1_person2_data = validate_json_response(injury1_person2_result.stdout)
    # print(f"Created injury3_id (for person2): {str(injury1_person2_data['id'])}")
    injury_ids_map[person2_id].append(str(injury1_person2_data["id"]))

    # List all injured persons for the claim
    list_persons_command = [
        "claims",
        "list-injured-persons",
        claim_number,
        "--output",
        "json",
    ]
    list_persons_result = runner.invoke(app, list_persons_command, catch_exceptions=True)
    assert list_persons_result.exit_code == 0
    persons = validate_json_response(list_persons_result.stdout)

    # Find our test persons
    test_persons = [
        p
        for p in persons
        if p.get("name").startswith("First Multiple") or p.get("name").startswith("Second Multiple")
    ]
    assert len(test_persons) == 2, f"Expected 2 test persons, found {len(test_persons)}"

    print("\nSuccessfully verified multiple persons with multiple injuries on a single claim")
