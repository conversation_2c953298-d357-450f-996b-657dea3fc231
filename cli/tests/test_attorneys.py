#!/usr/bin/env python3
import json
from datetime import datetime

# Import the main app
from claimentine_cli.main import app

# Import helper functions from conftest
from .conftest import validate_json_response

# --- Attorneys Tests (Integration) ---


def test_attorneys_add(runner, auto_claim):
    """Test adding an attorney to a claim via integration."""
    claim_id = auto_claim["id"]
    attorney_name = f"Test Attorney {datetime.now().isoformat()}"
    attorney_type = "DEFENSE"
    firm_name = "Test Law Firm LLC"
    attorney_email = "<EMAIL>"
    attorney_phone = "+1234567890"
    attorney_address = "123 Legal St, Test City"
    attorney_notes = "This is a test attorney."

    result = runner.invoke(
        app,
        [
            "attorneys",
            "add",
            claim_id,
            "--name",
            attorney_name,
            "--attorney-type",
            attorney_type,
            "--firm-name",
            firm_name,
            "--email",
            attorney_email,
            "--phone",
            attorney_phone,
            "--address",
            attorney_address,
            "--notes",
            attorney_notes,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result.exit_code == 0, f"attorneys add failed: {result.stdout}"
    attorney_data = validate_json_response(result.stdout)

    assert attorney_data.get("claim_id") == claim_id
    assert attorney_data.get("name") == attorney_name
    assert attorney_data.get("attorney_type") == attorney_type
    assert attorney_data.get("firm_name") == firm_name
    assert attorney_data.get("email") == attorney_email
    assert attorney_data.get("phone") == attorney_phone
    assert attorney_data.get("address") == attorney_address
    assert attorney_data.get("notes") == attorney_notes
    assert "id" in attorney_data
    assert "created_at" in attorney_data


def test_attorneys_list(runner, auto_claim):
    """Test listing attorneys for a claim via integration."""
    claim_id = auto_claim["id"]
    attorney_name_1 = f"List Test Attorney 1 {datetime.now().isoformat()}"
    attorney_name_2 = f"List Test Attorney 2 {datetime.now().isoformat()}"

    # Add a couple of attorneys first
    for i, name in enumerate([attorney_name_1, attorney_name_2]):
        # Use different attorney types for testing
        attorney_type = "PLAINTIFF" if i == 0 else "DEFENSE"
        add_result = runner.invoke(
            app,
            [
                "attorneys",
                "add",
                claim_id,
                "--name",
                name,
                "--attorney-type",
                attorney_type,
                "--email",
                f"attorney{i}@example.com",
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert (
            add_result.exit_code == 0
        ), f"Failed to add attorney for list test: {add_result.stdout}"
        # Validate the response
        added_attorney = validate_json_response(add_result.stdout)
        assert (
            added_attorney.get("name") == name
        ), f"Name mismatch for added attorney: {added_attorney.get('name')}"
        assert (
            added_attorney.get("claim_id") == claim_id
        ), f"Claim ID mismatch for added attorney: {added_attorney.get('claim_id')}"

    # Test listing (JSON)
    list_result_json = runner.invoke(
        app,
        ["attorneys", "list", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert list_result_json.exit_code == 0, f"attorneys list json failed: {list_result_json.stdout}"
    attorneys_list = validate_json_response(list_result_json.stdout)
    assert isinstance(attorneys_list, list)
    # Check if at least the attorneys we added are present
    found_1 = any(attorney.get("name") == attorney_name_1 for attorney in attorneys_list)
    found_2 = any(attorney.get("name") == attorney_name_2 for attorney in attorneys_list)
    assert found_1 and found_2, "Did not find added attorneys in list output"
    for attorney in attorneys_list:
        assert "id" in attorney
        assert attorney.get("claim_id") == claim_id


def test_attorneys_get(runner, auto_claim):
    """Test getting a specific attorney via integration."""
    claim_id = auto_claim["id"]
    attorney_name = f"Get Test Attorney {datetime.now().isoformat()}"
    attorney_type = "COVERAGE"
    attorney_email = "<EMAIL>"

    # Add an attorney first
    add_result = runner.invoke(
        app,
        [
            "attorneys",
            "add",
            claim_id,
            "--name",
            attorney_name,
            "--attorney-type",
            attorney_type,
            "--email",
            attorney_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add attorney for get test: {add_result.stdout}"
    added_attorney = validate_json_response(add_result.stdout)
    attorney_id = added_attorney["id"]

    # Test getting (JSON)
    get_result = runner.invoke(
        app,
        ["attorneys", "get", attorney_id, claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert get_result.exit_code == 0, f"attorneys get json failed: {get_result.stdout}"
    attorney_data = validate_json_response(get_result.stdout)
    assert attorney_data.get("id") == attorney_id
    assert attorney_data.get("claim_id") == claim_id
    assert attorney_data.get("name") == attorney_name
    assert attorney_data.get("attorney_type") == attorney_type
    assert attorney_data.get("email") == attorney_email


def test_attorneys_update(runner, auto_claim):
    """Test updating an attorney via integration."""
    claim_id = auto_claim["id"]
    original_name = f"Update Test Original {datetime.now().isoformat()}"
    updated_name = f"Update Test Updated {datetime.now().isoformat()}"
    original_type = "PLAINTIFF"
    updated_type = "DEFENSE"
    original_email = "<EMAIL>"
    updated_email = "<EMAIL>"

    # Add an attorney first
    add_result = runner.invoke(
        app,
        [
            "attorneys",
            "add",
            claim_id,
            "--name",
            original_name,
            "--attorney-type",
            original_type,
            "--email",
            original_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add attorney for update test: {add_result.stdout}"
    added_attorney = validate_json_response(add_result.stdout)
    attorney_id = added_attorney["id"]

    # Update the attorney
    update_result = runner.invoke(
        app,
        [
            "attorneys",
            "update",
            attorney_id,
            claim_id,
            "--name",
            updated_name,
            "--attorney-type",
            updated_type,
            "--email",
            updated_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert update_result.exit_code == 0, f"attorneys update failed: {update_result.stdout}"
    updated_attorney = validate_json_response(update_result.stdout)
    assert updated_attorney.get("id") == attorney_id
    assert updated_attorney.get("name") == updated_name
    assert updated_attorney.get("attorney_type") == updated_type
    assert updated_attorney.get("email") == updated_email
    assert updated_attorney.get("claim_id") == claim_id

    # Verify the update by getting the attorney again
    get_result = runner.invoke(
        app,
        ["attorneys", "get", attorney_id, claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert get_result.exit_code == 0
    verify_attorney = validate_json_response(get_result.stdout)
    assert verify_attorney.get("name") == updated_name
    assert verify_attorney.get("attorney_type") == updated_type
    assert verify_attorney.get("email") == updated_email


def test_attorneys_delete(runner, auto_claim):
    """Test deleting an attorney via integration."""
    claim_id = auto_claim["id"]
    attorney_name = f"Delete Test Attorney {datetime.now().isoformat()}"
    attorney_type = "OTHER"

    # Add an attorney first
    add_result = runner.invoke(
        app,
        [
            "attorneys",
            "add",
            claim_id,
            "--name",
            attorney_name,
            "--attorney-type",
            attorney_type,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add attorney for delete test: {add_result.stdout}"
    added_attorney = validate_json_response(add_result.stdout)
    attorney_id = added_attorney["id"]

    # Delete the attorney (using --force)
    delete_result = runner.invoke(
        app,
        ["attorneys", "delete", attorney_id, claim_id, "--force"],
        catch_exceptions=False,
    )
    assert delete_result.exit_code == 0, f"attorneys delete failed: {delete_result.stdout}"

    # Verify deletion by trying to get the attorney (should fail)
    get_result = runner.invoke(
        app,
        ["attorneys", "get", attorney_id, claim_id, "--output", "json"],
        catch_exceptions=True,  # Catch expected failure
    )
    # Expecting a non-zero exit code (e.g., 404 Not Found from API -> CLI error)
    assert get_result.exit_code != 0, "Getting a deleted attorney should fail"
    # Check for an error message (adjust based on actual CLI error output)
    assert (
        "not found" in get_result.stdout.lower() or "error" in get_result.stdout.lower()
    ), f"Expected error message for deleted attorney, got: {get_result.stdout}"


# --- End Attorneys Tests ---
