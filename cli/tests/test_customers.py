#!/usr/bin/env python3
import json
from datetime import datetime

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_19_customer_get(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, customer: str):
    """Test getting a specific customer's details by ID."""
    print("\nTesting Customer Get...")

    # Use the customer ID provided by the fixture
    exit_code, stdout = run_command(runner, ["customers", "get", customer, "--output", "json"])
    assert exit_code == 0

    # Parse and validate the customer details JSON response
    customer_details = validate_json_response(stdout)
    assert isinstance(customer_details, dict), "Expected a customer object"

    # Verify the response contains the expected customer ID
    assert customer_details["id"] == customer, "Customer ID should match the requested ID"

    # Check for required fields in the customer details
    required_fields = [
        "name",
        "prefix",
        "active",
        "description",
        "created_at",
        "updated_at",
    ]
    for field in required_fields:
        assert field in customer_details, f"Expected '{field}' field in customer details"

    print("✓ Customer get command successful (with <PERSON><PERSON><PERSON> validation)")


def test_20_customer_update(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, customer: str):
    """Test updating a customer's attributes."""
    print("\nTesting Customer Update...")

    # First, get the current customer details to store original values
    exit_code, stdout = run_command(runner, ["customers", "get", customer, "--output", "json"])
    assert exit_code == 0
    original_customer = validate_json_response(stdout)

    # Generate unique test values for the update
    timestamp = datetime.now().strftime("%H%M%S%f")
    new_name = f"Updated Customer {timestamp}"
    new_description = f"Updated description {timestamp}"
    new_active_status = not original_customer.get(
        "active", True
    )  # Toggle the current active status

    # Prepare args for update command
    update_args = [
        "customers",
        "update",
        customer,
        "--name",
        new_name,
        "--description",
        new_description,
        "--output",
        "json",
    ]

    # Add appropriate active flag based on the new status
    if new_active_status:
        update_args.append("--active")
    else:
        update_args.append("--no-active")

    # Execute the update command
    exit_code, update_stdout = run_command(runner, update_args)
    assert exit_code == 0, f"Update command failed with exit code {exit_code}"

    # Validate the update response
    updated_customer_response = validate_json_response(update_stdout)
    assert updated_customer_response["id"] == customer, "Customer ID should remain unchanged"
    assert (
        updated_customer_response["name"] == new_name
    ), f"Expected name to be updated to '{new_name}'"
    assert (
        updated_customer_response["description"] == new_description
    ), f"Expected description to be updated to '{new_description}'"
    assert (
        updated_customer_response["active"] == new_active_status
    ), f"Expected active status to be updated to {new_active_status}"

    # Verify the changes by getting the customer details again
    exit_code, get_stdout = run_command(runner, ["customers", "get", customer, "--output", "json"])
    assert exit_code == 0
    updated_customer = validate_json_response(get_stdout)

    # Verify all fields were properly updated
    assert (
        updated_customer["name"] == new_name
    ), f"Name not updated correctly. Expected: {new_name}, Got: {updated_customer['name']}"
    assert (
        updated_customer["description"] == new_description
    ), f"Description not updated correctly. Expected: {new_description}, Got: {updated_customer['description']}"
    assert (
        updated_customer["active"] == new_active_status
    ), f"Active status not updated correctly. Expected: {new_active_status}, Got: {updated_customer['active']}"

    # Verify that other fields remain unchanged
    assert (
        updated_customer["prefix"] == original_customer["prefix"]
    ), "Prefix should not have changed"

    print("✓ Customer update command successful (with verification)")


def test_21_customer_delete(runner: CliRunner, customer: str):
    """Tests deleting a customer via the CLI and verifies it's gone."""
    customer_id = customer  # Fixture provides the ID
    print(f"\nTesting: claimentine customers delete {customer_id} --output json")

    # 1. Delete the customer using --output json
    delete_args = ["customers", "delete", customer_id, "--output", "json"]
    exit_code, output = run_command(runner, delete_args)

    assert exit_code == 0, f"Delete command failed with output: {output}"
    print(f"✓ Delete command successful. Output:\n{output}")

    # Validate JSON output of delete command
    try:
        delete_data = json.loads(output)
        assert delete_data.get("message") == "Customer deleted successfully"
        assert delete_data.get("id") == customer_id
        print("✓ Delete JSON output validated.")
    except json.JSONDecodeError:
        pytest.fail(f"Delete command did not return valid JSON: {output}", pytrace=False)
    except AssertionError as e:
        pytest.fail(
            f"Delete command JSON output validation failed: {e}. Output was: {output}",
            pytrace=False,
        )

    # 2. Verify the customer is gone by trying to get it using --output json
    print(f"\nVerifying: claimentine customers get {customer_id} --output json (should fail)")
    get_args = ["customers", "get", customer_id, "--output", "json"]
    exit_code_get, output_get = run_command(runner, get_args, allow_error=True)

    assert (
        exit_code_get != 0
    ), f"Get command succeeded unexpectedly after delete. Output: {output_get}"
    print(f"✓ Get command failed as expected after delete. Exit Code: {exit_code_get}")

    # Optional: Validate the error output if it's consistent JSON
    try:
        error_data = json.loads(output_get)
        # Assuming the API client returns a standard error structure
        # Adjust the keys ('detail' or 'error') based on actual API error response
        assert (
            "not found" in error_data.get("detail", "").lower()
            or "not found" in error_data.get("error", "").lower()
        )
        print(f"✓ Get command error output validated (contains 'not found'). Output: {output_get}")
    except json.JSONDecodeError:
        # If error output isn't JSON, we might just check the raw string
        assert "not found" in output_get.lower()
        print(f"✓ Get command error output contains 'not found' (non-JSON). Output: {output_get}")
    except AssertionError as e:
        print(
            f"⚠️ Warning: Get command error output validation failed: {e}. Output was: {output_get}"
        )
        # Don't fail the test for this, as error message format might vary,
        # but log a warning. The main check is that the command failed (exit_code != 0).
