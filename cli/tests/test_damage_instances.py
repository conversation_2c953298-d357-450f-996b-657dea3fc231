#!/usr/bin/env python3
"""Tests for damage instances CLI commands."""

import json
import os
from typing import Dict, <PERSON><PERSON>

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def create_test_asset(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, claim_number: str, asset_name: str = "Test Asset") -> str:
    """Helper function to create a damaged property asset for testing damage instances."""
    command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        asset_name,
        "--asset-type",
        "STRUCTURE",
        "--description",
        "Test asset for damage instance tests",
        "--output",
        "json",
    ]
    result = runner.invoke(app, command, catch_exceptions=True)
    assert result.exit_code == 0, f"Failed to create test asset. Output: {result.stdout}"
    data = validate_json_response(result.stdout)
    return str(data["id"])


def test_01_create_damage_instance(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict):
    """Test creating a damage instance for a damaged property asset."""
    print("\nTesting Create Damage Instance...")
    claim_number = auto_claim["number"]

    # First create a parent damaged property asset
    asset_id = create_test_asset(runner, claim_number, "Parent Asset for Damage Test01")

    # Create damage instance
    damage_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "FIRE",
        "--damage-description",
        "Fire damage to south wall",
        "--damage-severity",
        "Moderate",
        "--affected-area",
        "South exterior wall",
        "--damage-cause",
        "Electrical short circuit",
        "--estimated-repair-cost",
        "5000",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, damage_command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("damaged_property_asset_id") == asset_id
    assert response.get("damage_type") == "FIRE"
    assert response.get("damage_description") == "Fire damage to south wall"
    assert response.get("damage_severity") == "Moderate"
    assert response.get("affected_area") == "South exterior wall"
    assert response.get("damage_cause") == "Electrical short circuit"
    assert response.get("estimated_repair_cost") == "5000.0"

    # Return the created damage instance ID for potential use in other tests
    return asset_id, response.get("id")


def test_02_list_damage_instances(runner: CliRunner, auto_claim: dict):
    """Test listing damage instances for a damaged property asset."""
    print("\nTesting List Damage Instances...")
    claim_number = auto_claim["number"]

    # Create a parent damaged property asset for this test
    asset_id = create_test_asset(runner, claim_number, "Asset with Multiple Damages Test02")

    # Create first damage instance
    damage1_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "WATER",
        "--damage-description",
        "Water damage to ceiling",
        "--damage-severity",
        "Minor",
        "--affected-area",
        "Kitchen ceiling",
        "--output",
        "json",
    ]
    damage1_result = runner.invoke(app, damage1_command, catch_exceptions=True)
    print("\nSetup Step (adding first damage instance for test_02):")
    print(f"  Exit code: {damage1_result.exit_code}")
    print(f"  Exception: {damage1_result.exception}")
    assert (
        damage1_result.exit_code == 0
    ), f"Failed to create first damage instance for test_02. Output: {damage1_result.stdout}"
    damage1_data = validate_json_response(damage1_result.stdout)
    damage1_id = str(damage1_data["id"])

    # Create a second damage instance
    damage2_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "WIND",
        "--damage-description",
        "Wind damage to windows",
        "--damage-severity",
        "Moderate",
        "--affected-area",
        "North-facing windows",
        "--output",
        "json",
    ]
    damage2_result = runner.invoke(app, damage2_command, catch_exceptions=True)
    print("\nSetup Step (adding second damage instance for test_02):")
    print(f"  Exit code: {damage2_result.exit_code}")
    print(f"  Exception: {damage2_result.exception}")
    assert (
        damage2_result.exit_code == 0
    ), f"Failed to create second damage instance for test_02. Output: {damage2_result.stdout}"
    damage2_data = validate_json_response(damage2_result.stdout)
    damage2_id = str(damage2_data["id"])

    # List all damage instances
    list_command = ["claims", "list-damage-instances", claim_number, asset_id, "--output", "json"]
    result = runner.invoke(app, list_command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert isinstance(response, list)
    assert len(response) >= 2  # Should have at least our two test damage instances

    # Find our test damage instances in the response
    found_damage1 = False
    found_damage2 = False
    for damage in response:
        if damage.get("id") == damage1_id:
            found_damage1 = True
            assert damage.get("damage_type") == "WATER"
            assert damage.get("damage_description") == "Water damage to ceiling"
        elif damage.get("id") == damage2_id:
            found_damage2 = True
            assert damage.get("damage_type") == "WIND"
            assert damage.get("damage_description") == "Wind damage to windows"

    assert found_damage1, "First test damage instance not found in list response"
    assert found_damage2, "Second test damage instance not found in list response"


def test_03_get_damage_instance(runner: CliRunner, auto_claim: dict):
    """Test getting a specific damage instance."""
    print("\nTesting Get Damage Instance...")
    claim_number = auto_claim["number"]

    # Create a parent damaged property asset for this test
    asset_id = create_test_asset(runner, claim_number, "Asset for Get Damage Test03")

    # Create a damage instance specifically for this test
    damage_description_to_get = "Damage to retrieve in test"
    create_command_args = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "HAIL",
        "--damage-description",
        damage_description_to_get,
        "--damage-severity",
        "Severe",
        "--estimated-repair-cost",
        "8000",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating damage instance for get_damage_instance test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create damage instance for get_damage_instance test. Output: {create_result.stdout}"
    created_damage_data = validate_json_response(create_result.stdout)
    damage_to_get_id = str(created_damage_data["id"])

    # Get the created damage instance
    get_command_args = [
        "claims",
        "get-damage-instance",
        claim_number,
        asset_id,
        damage_to_get_id,
        "--json",
    ]
    result = runner.invoke(app, get_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == damage_to_get_id
    assert response.get("damaged_property_asset_id") == asset_id
    assert response.get("damage_type") == "HAIL"
    assert response.get("damage_description") == damage_description_to_get
    assert response.get("damage_severity") == "Severe"
    assert response.get("estimated_repair_cost") == "8000.0"


def test_04_update_damage_instance(runner: CliRunner, auto_claim: dict):
    """Test updating a damage instance."""
    print("\nTesting Update Damage Instance...")
    claim_number = auto_claim["number"]

    # Create a parent damaged property asset for this test
    asset_id = create_test_asset(runner, claim_number, "Asset for Update Damage Test04")

    # Create a damage instance to update
    create_command_args = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "VANDALISM",
        "--damage-description",
        "Original damage description",
        "--damage-severity",
        "Minor",
        "--estimated-repair-cost",
        "2000",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating damage instance for update test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create damage instance for update test. Output: {create_result.stdout}"
    created_damage_data = validate_json_response(create_result.stdout)
    damage_id = str(created_damage_data["id"])

    # Update the damage instance
    update_command_args = [
        "claims",
        "update-damage-instance",
        claim_number,
        asset_id,
        damage_id,
        "--damage-description",
        "Updated damage description",
        "--damage-severity",
        "Moderate",
        "--estimated-repair-cost",
        "3500",
        "--repair-status",
        "IN_PROGRESS",
        "--repair-vendor",
        "ABC Repairs, Inc.",
        "--output",
        "json",
    ]
    result = runner.invoke(app, update_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == damage_id
    assert response.get("damage_description") == "Updated damage description"
    assert response.get("damage_severity") == "Moderate"
    assert response.get("estimated_repair_cost") == "3500.0"
    assert response.get("repair_status") == "IN_PROGRESS"
    assert response.get("repair_vendor") == "ABC Repairs, Inc."
    assert response.get("damage_type") == "VANDALISM"  # Should remain unchanged


def test_05_delete_damage_instance(runner: CliRunner, auto_claim: dict):
    """Test deleting a damage instance."""
    print("\nTesting Delete Damage Instance...")
    claim_number = auto_claim["number"]

    # Create a parent damaged property asset for this test
    asset_id = create_test_asset(runner, claim_number, "Asset for Delete Damage Test05")

    # Create a damage instance to delete
    create_command_args = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "VANDALISM",
        "--damage-description",
        "This damage instance will be deleted",
        "--damage-severity",
        "Minor",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating damage instance for delete test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create damage instance for delete test. Output: {create_result.stdout}"
    created_damage_data = validate_json_response(create_result.stdout)
    damage_id = str(created_damage_data["id"])

    # Delete the damage instance
    delete_command_args = [
        "claims",
        "delete-damage-instance",
        claim_number,
        asset_id,
        damage_id,
        "--yes",  # Bypass confirmation
    ]
    result = runner.invoke(app, delete_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0
    assert "deleted successfully" in result.stdout.lower()

    # Verify it's gone by trying to get it
    get_command_args = [
        "claims",
        "get-damage-instance",
        claim_number,
        asset_id,
        damage_id,
        "--json",
    ]
    get_result = runner.invoke(app, get_command_args, catch_exceptions=True)

    # Should fail or return not found
    assert get_result.exit_code != 0 or "not found" in get_result.stdout.lower()


def test_06_multiple_damage_instances(runner: CliRunner, auto_claim: dict):
    """Test creating multiple damage instances for a single property asset."""
    print("\nTesting Multiple Damage Instances on Single Asset...")

    claim_number = auto_claim["number"]

    # Create a parent damaged property asset
    asset_id = create_test_asset(runner, claim_number, "Asset with Multiple Damages Test06")
    damage_ids = []

    # Create first damage instance
    damage1_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "FIRE",
        "--damage-description",
        "First test damage - Fire damage to kitchen",
        "--damage-severity",
        "Severe",
        "--output",
        "json",
    ]
    damage1_result = runner.invoke(app, damage1_command, catch_exceptions=True)
    assert (
        damage1_result.exit_code == 0
    ), f"Failed to create first damage. Output: {damage1_result.stdout}"
    damage1_data = validate_json_response(damage1_result.stdout)
    damage1_id = str(damage1_data["id"])
    damage_ids.append(damage1_id)

    # Create second damage instance
    damage2_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "WATER",
        "--damage-description",
        "Second test damage - Water damage from firefighting",
        "--damage-severity",
        "Moderate",
        "--output",
        "json",
    ]
    damage2_result = runner.invoke(app, damage2_command, catch_exceptions=True)
    assert (
        damage2_result.exit_code == 0
    ), f"Failed to create second damage. Output: {damage2_result.stdout}"
    damage2_data = validate_json_response(damage2_result.stdout)
    damage2_id = str(damage2_data["id"])
    damage_ids.append(damage2_id)

    # Create third damage instance
    damage3_command = [
        "claims",
        "create-damage-instance",
        claim_number,
        asset_id,
        "--damage-type",
        "OTHER",
        "--damage-description",
        "Third test damage - Smoke damage throughout",
        "--damage-severity",
        "Minor",
        "--output",
        "json",
    ]
    damage3_result = runner.invoke(app, damage3_command, catch_exceptions=True)
    assert (
        damage3_result.exit_code == 0
    ), f"Failed to create third damage. Output: {damage3_result.stdout}"
    damage3_data = validate_json_response(damage3_result.stdout)
    damage3_id = str(damage3_data["id"])
    damage_ids.append(damage3_id)

    # List all damage instances for the asset
    list_command = ["claims", "list-damage-instances", claim_number, asset_id, "--output", "json"]
    list_result = runner.invoke(app, list_command, catch_exceptions=True)
    assert list_result.exit_code == 0
    damages = validate_json_response(list_result.stdout)

    # Check we have at least our three damages
    assert len(damages) >= 3

    # Find our test damages
    test_damages = [d for d in damages if d.get("id") in damage_ids]
    assert len(test_damages) == 3, f"Expected 3 test damages, found {len(test_damages)}"

    # Check individual damages
    fire_damage = next((d for d in damages if d.get("damage_type") == "FIRE"), None)
    assert fire_damage is not None
    assert "kitchen" in fire_damage.get("damage_description").lower()

    water_damage = next((d for d in damages if d.get("damage_type") == "WATER"), None)
    assert water_damage is not None
    assert "firefighting" in water_damage.get("damage_description").lower()

    smoke_damage = next((d for d in damages if d.get("damage_type") == "OTHER"), None)
    assert smoke_damage is not None
    assert "smoke" in smoke_damage.get("damage_description").lower()

    print("\nSuccessfully verified multiple damage instances on a single property asset")


def test_07_asset_with_damage_instances(runner: CliRunner, gl_claim: dict):
    """Test creating a property asset with multiple damage instances and retrieving it."""
    print("\nTesting Property Asset with Multiple Damage Instances...")

    claim_number = gl_claim["number"]

    # Create a parent damaged property asset
    asset_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Comprehensive Damage Test Asset",
        "--asset-type",
        "BUILDING",
        "--description",
        "Building with multiple types of damage",
        "--estimated-value",
        "500000",
        "--output",
        "json",
    ]
    asset_result = runner.invoke(app, asset_command, catch_exceptions=True)
    assert (
        asset_result.exit_code == 0
    ), f"Failed to create test asset. Output: {asset_result.stdout}"
    asset_data = validate_json_response(asset_result.stdout)
    asset_id = str(asset_data["id"])

    # Add multiple damage instances to the asset
    damage_types = ["FIRE", "WATER", "STRUCTURAL", "OTHER"]
    damage_descriptions = [
        "Fire damage to north wing",
        "Water damage from sprinkler system",
        "Structural damage to support beams",
        "Electrical system damage from fire",
    ]

    for i, (damage_type, description) in enumerate(zip(damage_types, damage_descriptions)):
        command = [
            "claims",
            "create-damage-instance",
            claim_number,
            asset_id,
            "--damage-type",
            damage_type,
            "--damage-description",
            description,
            "--damage-severity",
            "Severe" if i == 0 else "Moderate",
            "--estimated-repair-cost",
            str(25000 + (i * 10000)),
            "--output",
            "json",
        ]
        result = runner.invoke(app, command, catch_exceptions=True)
        assert result.exit_code == 0, f"Failed to create damage {i+1}. Output: {result.stdout}"

    # Now retrieve the asset and check it includes the damage instances
    get_command = [
        "claims",
        "get-damaged-property-asset",
        claim_number,
        asset_id,
        "--json",
    ]
    get_result = runner.invoke(app, get_command, catch_exceptions=True)
    assert get_result.exit_code == 0
    asset_with_damages = validate_json_response(get_result.stdout)

    # Verify asset data
    assert asset_with_damages.get("id") == asset_id
    assert asset_with_damages.get("name") == "Comprehensive Damage Test Asset"

    # Verify damage instances
    assert "damage_instances" in asset_with_damages
    damages = asset_with_damages.get("damage_instances", [])
    assert len(damages) == 4

    # Check each damage type is present
    damage_types_found = [d.get("damage_type") for d in damages]
    for damage_type in damage_types:
        assert damage_type in damage_types_found

    # Calculate total estimated repair cost
    total_estimated_cost = sum(float(d.get("estimated_repair_cost", 0)) for d in damages)
    assert total_estimated_cost > 0

    print(
        f"\nSuccessfully verified asset with multiple damage instances (total estimated cost: ${total_estimated_cost:.2f})"
    )
