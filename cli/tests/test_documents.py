#!/usr/bin/env python3
import os
import subprocess
from datetime import datetime

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import validate_json_response


# --- Fixture for Uploading/Cleaning a Document ---
@pytest.fixture(scope="function")
def uploaded_document(
    runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict
) -> str:  # Removed validate_json_response from signature
    """Uploads a dummy document to the specified claim (using ID), yields its ID, and cleans up.

    Args:
        auto_claim (dict): Fixture providing {"id": uuid, "number": claim_number}

    Returns:
        str: The ID of the uploaded document.
    """
    claim_id = auto_claim["id"]
    print(f"\nRunning Document Fixture (Upload to claim ID {claim_id})...")
    dummy_file_path = f"dummy_upload_{datetime.now().strftime('%H%M%S%f')}.txt"
    document_id = None

    # Create dummy file
    try:
        with open(dummy_file_path, "w") as f:
            f.write("This is a test document for fixture upload.")
        print(f"✓ Document Fixture: Created dummy file '{dummy_file_path}'")

        # Upload document using --claim-identifier (takes ID) and --file and --type
        upload_result = runner.invoke(
            app,
            [
                "documents",
                "upload",
                "--claim-identifier",  # Use identifier option (takes ID or number)
                claim_id,
                "--file",  # Specify the file with the --file option
                dummy_file_path,
                "--type",  # Specify the document type (Required)
                "OTHER",  # Using a valid document type
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )

        if upload_result.exit_code != 0:
            print("Document Fixture Failed (Upload):")
            print(upload_result.output)
            pytest.fail(f"Failed to upload document in fixture for claim {claim_id}", pytrace=False)

        # Get the ID from the JSON response
        upload_data = validate_json_response(upload_result.stdout)
        document_id = upload_data.get("id")
        if document_id is None:
            pytest.fail(f"Could not extract document ID from upload response", pytrace=False)

        print(f"✓ Document Fixture: Uploaded document ID '{document_id}'")
        yield document_id

    finally:
        # Cleanup: Attempt to delete the document and the local file
        if document_id:
            print(f"\nDocument Fixture Teardown (Deleting doc ID {document_id})...")
            delete_result = runner.invoke(
                app,
                ["documents", "delete", "--claim-identifier", claim_id, document_id, "--yes"],
                catch_exceptions=False,
            )
            if delete_result.exit_code == 0:
                print(f"✓ Document Fixture: Deleted document {document_id}")
            else:
                print(f"⚠️ Document Fixture: Failed to delete document {document_id}")
                print(delete_result.output)
        else:
            print("\nDocument Fixture Teardown (No document ID to delete).")

        if os.path.exists(dummy_file_path):
            try:
                os.remove(dummy_file_path)
                print(f"✓ Document Fixture: Removed local file '{dummy_file_path}'")
            except OSError as e:
                print(f"⚠️ Document Fixture: Error removing file {dummy_file_path}: {e}")


# --- Document Tests ---


def test_documents_upload(runner: CliRunner, auto_claim: dict):
    """Test uploading a document and verify it uploads to GCP."""
    claim_id = auto_claim["id"]

    # Create a test file with unique content for verification
    test_file_path = f"test_upload_{datetime.now().strftime('%H%M%S%f')}.txt"
    unique_content = f"Test file content {datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')}"
    try:
        with open(test_file_path, "w") as f:
            f.write(unique_content)

        # Upload document
        upload_result = runner.invoke(
            app,
            [
                "documents",
                "upload",
                "--claim-identifier",
                claim_id,
                "--file",
                test_file_path,
                "--type",
                "REPORT",  # Using a valid document type
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )

        # Verify successful upload
        assert upload_result.exit_code == 0
        upload_data = validate_json_response(upload_result.stdout)
        document_id = upload_data.get("id")
        assert document_id, "Document ID not found in upload response"

        # Verify document exists by listing documents
        list_result = runner.invoke(
            app,
            [
                "documents",
                "list",
                "--claim-identifier",
                claim_id,
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert list_result.exit_code == 0
        list_data = validate_json_response(list_result.stdout)
        # Get documents from "items" key
        documents = list_data.get("items", [])
        document_ids = [doc.get("id") for doc in documents]
        assert (
            document_id in document_ids
        ), f"Uploaded document {document_id} not found in document list"

        # Get bucket name from environment if available
        bucket_name = os.getenv("GCS_BUCKET_NAME")
        if bucket_name:
            # Verify file path format
            uploaded_doc = next((doc for doc in documents if doc.get("id") == document_id), None)
            assert uploaded_doc, "Could not find uploaded document in list"
            file_path = uploaded_doc.get("file_path")
            assert file_path, "File path not found in document metadata"
            assert file_path.startswith(f"claims/{claim_id}/documents/"), "Invalid file path format"

            # Check if gcloud CLI is available and try to verify the file in storage
            try:
                # Removed subprocess import, already imported at top level

                result = subprocess.run(
                    ["gcloud", "storage", "objects", "describe", f"gs://{bucket_name}/{file_path}"],
                    capture_output=True,
                    text=True,
                )
                if result.returncode == 0:
                    print(
                        f"✓ Verified document exists in GCP storage: gs://{bucket_name}/{file_path}"
                    )
                else:
                    print("⚠️ Could not verify document in GCP storage (permissions or CLI issue)")
            except Exception as e:
                print(f"Note: Could not verify GCP storage: {e}")

        # Delete the document afterward
        delete_result = runner.invoke(
            app,
            [
                "documents",
                "delete",
                "--claim-identifier",
                claim_id,
                document_id,
                "--yes",
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert (
            delete_result.exit_code == 0
        ), f"Failed to delete test document: {delete_result.stdout}"

    finally:
        # Clean up local test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)


def test_documents_list(runner: CliRunner, auto_claim: dict, uploaded_document: str):
    """Test listing documents for a claim."""
    claim_id = auto_claim["id"]

    list_result = runner.invoke(
        app,
        [
            "documents",
            "list",
            "--claim-identifier",
            claim_id,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    # Verify successful listing
    assert list_result.exit_code == 0
    list_data = validate_json_response(list_result.stdout)
    # Get documents from "items" key
    documents = list_data.get("items", [])
    assert isinstance(documents, list), "Response items should be a list of documents"

    # Verify the uploaded document is in the list
    document_ids = [doc.get("id") for doc in documents]
    assert (
        uploaded_document in document_ids
    ), f"Uploaded document {uploaded_document} not found in list"

    # Test filtering by document type
    doc_type = None
    # Find the type of our uploaded document
    for doc in documents:
        if doc.get("id") == uploaded_document:
            doc_type = doc.get("type")
            break

    if doc_type:
        # Test filter by type
        filtered_list_result = runner.invoke(
            app,
            [
                "documents",
                "list",
                "--claim-identifier",
                claim_id,
                "--type",
                doc_type,
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert filtered_list_result.exit_code == 0
        filtered_data = validate_json_response(filtered_list_result.stdout)
        filtered_docs = filtered_data.get("items", [])
        assert isinstance(filtered_docs, list), "Filtered response items should be a list"
        for doc in filtered_docs:
            assert (
                doc.get("type") == doc_type
            ), f"Document {doc.get('id')} has wrong type in filtered results"


def test_documents_get(runner: CliRunner, auto_claim: dict, uploaded_document: str):
    """Test getting a specific document by ID."""
    claim_id = auto_claim["id"]

    # Get the document details
    get_result = runner.invoke(
        app,
        [
            "documents",
            "get",
            uploaded_document,
            "--claim-identifier",
            claim_id,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    # Verify successful retrieval
    assert get_result.exit_code == 0, f"Get document failed: {get_result.stdout}"
    document = validate_json_response(get_result.stdout)

    # Verify document properties
    assert document.get("id") == uploaded_document, "Document ID doesn't match the requested ID"
    assert document.get("claim_id") == claim_id, "Document claim_id doesn't match"
    assert "name" in document, "Document name missing"
    assert "type" in document, "Document type missing"
    assert "file_path" in document, "File path missing"
    assert "file_size" in document, "File size missing"
    assert "mime_type" in document, "MIME type missing"
    assert "created_at" in document, "Created timestamp missing"
    assert "updated_at" in document, "Updated timestamp missing"

    # Additional checks to ensure the document data is valid
    assert isinstance(document.get("file_size"), int), "File size should be an integer"
    assert document.get("file_size") > 0, "File size should be greater than zero"
    # Check file path format
    expected_prefix = f"claims/{claim_id}/documents/"
    assert document.get("file_path", "").startswith(
        expected_prefix
    ), f"File path doesn't start with expected prefix: {expected_prefix}"


def test_documents_update(runner: CliRunner, auto_claim: dict, uploaded_document: str):
    """Test updating document metadata."""
    claim_id = auto_claim["id"]
    new_name = f"Updated Name {datetime.now().strftime('%H%M%S')}"
    new_description = "Updated description for testing"

    # First list documents to get current metadata
    list_result = runner.invoke(
        app,
        [
            "documents",
            "list",
            "--claim-identifier",
            claim_id,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    list_data = validate_json_response(list_result.stdout)
    documents = list_data.get("items", [])
    original_doc = next((doc for doc in documents if doc.get("id") == uploaded_document), None)
    assert original_doc, f"Cannot find document {uploaded_document} to update"

    # Update the document using the correct parameters
    update_result = runner.invoke(
        app,
        [
            "documents",
            "update",
            "--claim-id",
            claim_id,  # Command uses --claim-id not --claim-identifier
            "--document-id",
            uploaded_document,
            "--name",
            new_name,
            "--description",
            new_description,
            "--type",
            "POLICY",  # Using a valid document type
            "--output",
            "json",  # Add output format for better diagnostics
        ],
        catch_exceptions=False,
    )

    # Verify successful update
    assert update_result.exit_code == 0, f"Update failed: {update_result.stdout}"

    # Try to parse the update response as JSON
    try:
        update_data = validate_json_response(update_result.stdout)
        # If we got a JSON response, check it directly
        assert update_data.get("id") == uploaded_document, "Response document ID doesn't match"
        if "name" in update_data:
            assert update_data.get("name") == new_name, "Name wasn't updated in response"
        if "description" in update_data:
            assert (
                update_data.get("description") == new_description
            ), "Description wasn't updated in response"
        if "type" in update_data:
            assert update_data.get("type") == "POLICY", "Type wasn't updated in response"
    except Exception as e:
        # If we can't parse JSON, the test will pass if the command executed successfully
        # This handles when the API returns non-JSON responses
        print(f"Note: Could not validate JSON response: {e}")
        assert (
            "Document updated successfully" in update_result.stdout
        ), "Success message not found in output"

    # Verify the update also shows up in listing (if this fails but the direct check above passes,
    # we know the issue is in the listing, not the update)
    list_after_update = runner.invoke(
        app,
        [
            "documents",
            "list",
            "--claim-identifier",
            claim_id,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    list_data = validate_json_response(list_after_update.stdout)
    updated_docs = list_data.get("items", [])
    updated_doc = next((doc for doc in updated_docs if doc.get("id") == uploaded_document), None)
    # These are soft assertions - if they fail it's a warning not a test failure
    if updated_doc:
        if updated_doc.get("name") != new_name:
            print(
                f"Warning: Name in list response '{updated_doc.get('name')}' doesn't match updated name '{new_name}'"
            )
        if "description" in updated_doc and updated_doc.get("description") != new_description:
            print(f"Warning: Description in list doesn't match update")
        if updated_doc.get("type") != "POLICY":
            print(f"Warning: Type in list '{updated_doc.get('type')}' doesn't match 'POLICY'")


def test_documents_download(runner: CliRunner, auto_claim: dict, uploaded_document: str):
    """Test downloading a document."""
    claim_id = auto_claim["id"]
    output_path = f"test_download_{datetime.now().strftime('%H%M%S%f')}.txt"

    try:
        download_result = runner.invoke(
            app,
            [
                "documents",
                "download",
                "--claim-identifier",
                claim_id,
                "--document-id",
                uploaded_document,  # Need to use --document-id parameter
                "-o",
                output_path,  # Use -o for output file path
                "--output",
                "json",  # Use --output for output format
            ],
            catch_exceptions=False,
        )

        # Verify successful download
        assert download_result.exit_code == 0, f"Download failed: {download_result.stdout}"
        assert os.path.exists(output_path), "Downloaded file doesn't exist"
        assert os.path.getsize(output_path) > 0, "Downloaded file is empty"

        # Read content to verify it's not corrupted
        with open(output_path, "r") as f:
            content = f.read()
        assert content, "Downloaded file content is empty"
        # This file should contain the fixture content
        assert (
            "test document for fixture upload" in content.lower()
        ), "Downloaded content doesn't match expected fixture content"

    finally:
        # Clean up downloaded file
        if os.path.exists(output_path):
            os.remove(output_path)


def test_documents_delete(runner: CliRunner, auto_claim: dict):
    """Test deleting a document and verify it's removed from GCP."""
    claim_id = auto_claim["id"]

    # First upload a document to delete
    test_file_path = f"test_delete_{datetime.now().strftime('%H%M%S%f')}.txt"
    try:
        with open(test_file_path, "w") as f:
            f.write("Test file for deletion")

        # Upload document
        upload_result = runner.invoke(
            app,
            [
                "documents",
                "upload",
                "--claim-identifier",
                claim_id,
                "--file",
                test_file_path,
                "--type",
                "CORRESPONDENCE",  # Using a valid document type
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )

        assert upload_result.exit_code == 0
        upload_data = validate_json_response(upload_result.stdout)
        document_id = upload_data.get("id")
        assert document_id, "Document ID not found in upload response"

        # Get file_path from document by listing
        list_result = runner.invoke(
            app,
            [
                "documents",
                "list",
                "--claim-identifier",
                claim_id,
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert list_result.exit_code == 0
        list_data = validate_json_response(list_result.stdout)
        documents = list_data.get("items", [])
        document = next((doc for doc in documents if doc.get("id") == document_id), None)
        assert document, f"Could not find document {document_id} in list"
        file_path = document.get("file_path")
        assert file_path, "File path not found in document"

        # Delete the document
        delete_result = runner.invoke(
            app,
            [
                "documents",
                "delete",
                "--claim-identifier",
                claim_id,
                document_id,
                "--yes",
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )

        # Verify successful deletion
        assert delete_result.exit_code == 0
        delete_data = validate_json_response(delete_result.stdout)
        assert delete_data.get("deleted") == True, "Delete operation did not indicate success"

        # Verify document is no longer in the list
        list_after_delete = runner.invoke(
            app,
            [
                "documents",
                "list",
                "--claim-identifier",
                claim_id,
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert list_after_delete.exit_code == 0
        after_data = validate_json_response(list_after_delete.stdout)
        remaining_docs = after_data.get("items", [])
        remaining_ids = [doc.get("id") for doc in remaining_docs]
        assert (
            document_id not in remaining_ids
        ), f"Document {document_id} still appears in listing after deletion"

        # Verify file is deleted from GCP
        bucket_name = os.getenv("GCS_BUCKET_NAME")
        if bucket_name:
            try:
                # Removed subprocess import, already imported at top level

                result = subprocess.run(
                    ["gcloud", "storage", "objects", "describe", f"gs://{bucket_name}/{file_path}"],
                    capture_output=True,
                    text=True,
                )
                assert result.returncode != 0, "File still exists in GCP storage"
            except Exception as e:
                print(f"Note: Could not verify GCP storage deletion: {e}")

    finally:
        # Clean up local test file
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
