#!/usr/bin/env python3
"""Tests for bodily injury CLI commands."""

import json
import os
from typing import Dict

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_01_update_auto_bodily_injury(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict):
    """Test updating bodily injury details for an auto claim."""
    print("\nTesting Auto Claim Bodily Injury Update...")

    claim_id = auto_claim.get("id")
    if not claim_id:
        pytest.fail("No auto claim ID available")

    # Create bodily injury details
    command = [
        "claims",
        "update-bodily-injury",
        auto_claim["number"],
        "--injury-description",
        "Test auto injury",
        "--incident-location",
        "Highway 101",
        "--injured-person-type",
        "PATRON",
        "--equipment-involved",
        "--equipment-details",
        "Vehicle airbag deployment",
        "--equipment-owned-by-insured",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "John Test",
        "--medical-treatment-requirements",
        "HOSPITALIZATION",
        "--treatment-nature",
        "Emergency surgery",
        "--estimated-cost",
        "5000.50",
        "--insurance-billing-status",
        "PENDING",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("auto_details_id") is not None
    assert response.get("gl_details_id") is None  # Should be None for auto claim
    assert response.get("injury_description") == "Test auto injury"
    assert response.get("incident_location") == "Highway 101"
    assert response.get("injured_person_type") == "PATRON"
    assert response.get("equipment_involved") is True

    # Store the bodily injury ID for later tests
    os.environ["AUTO_BODILY_INJURY_ID"] = str(response["id"])
    print(f"AUTO_BODILY_INJURY_ID: {os.environ['AUTO_BODILY_INJURY_ID']}")


def test_02_update_gl_bodily_injury(runner: CliRunner, gl_claim: dict):
    """Test updating bodily injury details for a GL claim."""
    print("\nTesting GL Claim Bodily Injury Update...")

    claim_id = gl_claim.get("id")
    if not claim_id:
        pytest.fail("No GL claim ID available")

    # Create bodily injury details
    command = [
        "claims",
        "update-bodily-injury",
        gl_claim["number"],
        "--injury-description",
        "Test GL injury",
        "--incident-location",
        "Store front",
        "--injured-person-type",
        "GUEST",
        "--safety-measures-involved",
        "--safety-measures-description",
        "Warning signs were present",
        "--incident-report-status",
        "FILED",
        "--report-filer-name",
        "Jane Test",
        "--medical-treatment-requirements",
        "OUTPATIENT",
        "--medical-provider-name",
        "City Hospital",
        "--estimated-cost",
        "2500.75",
        "--insurance-billing-status",
        "PENDING",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("gl_details_id") is not None
    assert response.get("auto_details_id") is None  # Should be None for GL claim
    assert response.get("injury_description") == "Test GL injury"
    assert response.get("incident_location") == "Store front"
    assert response.get("injured_person_type") == "GUEST"
    assert response.get("safety_measures_involved") is True

    # Store the bodily injury ID for later tests
    os.environ["GL_BODILY_INJURY_ID"] = str(response["id"])
    print(f"GL_BODILY_INJURY_ID: {os.environ['GL_BODILY_INJURY_ID']}")


def test_03_get_auto_bodily_injury(runner: CliRunner, auto_claim: dict):
    """Test fetching bodily injury details for an auto claim."""
    print("\nTesting Auto Claim Bodily Injury Get...")

    # === Setup: Create BI details first ===
    setup_command = [
        "claims",
        "update-bodily-injury",
        auto_claim["number"],
        "--injury-description",
        "Test auto injury for get",
        "--incident-location",
        "Highway 101 for get",
        "--injured-person-type",
        "GUEST",
        "--output",
        "json",
    ]
    setup_result = runner.invoke(app, setup_command, catch_exceptions=True)
    print("\nSetup Step:")
    print(f"  Exit code: {setup_result.exit_code}")
    print(f"  Exception: {setup_result.exception}")
    print(f"  Output: {setup_result.stdout}")
    assert setup_result.exit_code == 0, "Setup step: Failed to create BI details for get test"
    # === End Setup ===

    # === Test: Get bodily injury details ===
    get_command = ["claims", "get-bodily-injury", auto_claim["number"], "--output", "json"]

    # Run get command
    result = runner.invoke(app, get_command, catch_exceptions=True)

    # Print debug info
    print("\nActual Get Step:")
    print(f"  Exit code: {result.exit_code}")
    print(f"  Exception: {result.exception}")
    print(f"  Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("auto_details_id") is not None
    assert response.get("gl_details_id") is None
    # Check fields set during setup
    assert response.get("injury_description") == "Test auto injury for get"
    assert response.get("incident_location") == "Highway 101 for get"
    assert response.get("injured_person_type") == "GUEST"


def test_04_get_gl_bodily_injury(runner: CliRunner, gl_claim: dict):
    """Test fetching bodily injury details for a GL claim."""
    print("\nTesting GL Claim Bodily Injury Get...")

    # === Setup: Create BI details first ===
    setup_command = [
        "claims",
        "update-bodily-injury",
        gl_claim["number"],
        "--injury-description",
        "Test GL injury for get",
        "--incident-location",
        "Store front for get",
        "--injured-person-type",
        "EMPLOYEE",
        "--output",
        "json",
    ]
    setup_result = runner.invoke(app, setup_command, catch_exceptions=True)
    print("\nSetup Step:")
    print(f"  Exit code: {setup_result.exit_code}")
    print(f"  Exception: {setup_result.exception}")
    print(f"  Output: {setup_result.stdout}")
    assert setup_result.exit_code == 0, "Setup step: Failed to create BI details for GL get test"
    # === End Setup ===

    # === Test: Get bodily injury details ===
    get_command = ["claims", "get-bodily-injury", gl_claim["number"], "--output", "json"]

    # Run get command
    result = runner.invoke(app, get_command, catch_exceptions=True)

    # Print debug info
    print("\nActual Get Step:")
    print(f"  Exit code: {result.exit_code}")
    print(f"  Exception: {result.exception}")
    print(f"  Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("gl_details_id") is not None
    assert response.get("auto_details_id") is None
    # Check fields set during setup
    assert response.get("injury_description") == "Test GL injury for get"
    assert response.get("incident_location") == "Store front for get"
    assert response.get("injured_person_type") == "EMPLOYEE"


def test_05_update_existing_bodily_injury(runner: CliRunner, auto_claim: dict):
    """Test updating existing bodily injury details."""
    print("\nTesting Existing Bodily Injury Update...")

    # === Setup: Create initial BI details ===
    initial_command = [
        "claims",
        "update-bodily-injury",
        auto_claim["number"],
        "--injury-description",
        "Initial auto injury",
        "--incident-location",
        "Highway 101",  # Set baseline value
        "--injured-person-type",
        "PATRON",
        "--output",
        "json",
    ]
    initial_result = runner.invoke(app, initial_command, catch_exceptions=True)
    print("\nInitial Update Step:")
    print(f"  Exit code: {initial_result.exit_code}")
    print(f"  Exception: {initial_result.exception}")
    print(f"  Output: {initial_result.stdout}")
    assert initial_result.exit_code == 0, "Setup step: Failed to create initial BI details"
    initial_response = validate_json_response(initial_result.stdout)
    assert initial_response.get("incident_location") == "Highway 101"
    # === End Setup ===

    # === Test: Update existing bodily injury details ===
    update_command = [
        "claims",
        "update-bodily-injury",
        auto_claim["number"],
        "--injury-description",
        "Updated auto injury description",  # Update this field
        "--medical-provider-name",
        "Updated Hospital",  # Add this field
        "--output",
        "json",
    ]

    # Run update command
    result = runner.invoke(app, update_command, catch_exceptions=True)

    # Print debug info for the update step
    print("\nActual Update Step:")
    print(f"  Exit code: {result.exit_code}")
    print(f"  Exception: {result.exception}")
    print(f"  Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    # Check updated fields
    assert response.get("injury_description") == "Updated auto injury description"
    assert response.get("medical_provider_name") == "Updated Hospital"

    # Verify other fields weren't changed (or remain from initial setup)
    assert response.get("incident_location") == "Highway 101"  # Should persist from setup
    assert response.get("injured_person_type") == "PATRON"  # Should persist from setup


def test_06_delete_bodily_injury(runner: CliRunner, auto_claim: dict):
    """Test deleting bodily injury details."""
    print("\nTesting Bodily Injury Delete...")

    # === Setup: Create BI details first ===
    setup_command = [
        "claims",
        "update-bodily-injury",
        auto_claim["number"],
        "--injury-description",
        "Test auto injury for delete",
        "--incident-location",
        "Highway 101 for delete",
        "--output",
        "json",
    ]
    setup_result = runner.invoke(app, setup_command, catch_exceptions=True)
    print("\nSetup Step:")
    print(f"  Exit code: {setup_result.exit_code}")
    print(f"  Exception: {setup_result.exception}")
    print(f"  Output: {setup_result.stdout}")
    assert setup_result.exit_code == 0, "Setup step: Failed to create BI details for delete test"
    initial_response = validate_json_response(setup_result.stdout)
    assert initial_response.get("id") is not None  # Ensure we got an ID
    # === End Setup ===

    # === Test: Delete bodily injury details ===
    delete_command = ["claims", "delete-bodily-injury", auto_claim["number"], "--yes"]

    # Run delete command
    result = runner.invoke(app, delete_command, catch_exceptions=True)

    # Print debug info for the delete step
    print("\nActual Delete Step:")
    print(f"  Exit code: {result.exit_code}")
    print(f"  Exception: {result.exception}")
    print(f"  Output: {result.stdout}")

    # Check that the delete command itself succeeded
    assert result.exit_code == 0
    assert "details deleted for claim" in result.stdout.lower()

    # === Verification: Verify the details were deleted by trying to get them ===
    get_command = ["claims", "get-bodily-injury", auto_claim["number"], "--output", "json"]

    get_result = runner.invoke(app, get_command, catch_exceptions=True)

    print("\nVerification Get Step:")
    print(f"  Exit code: {get_result.exit_code}")
    print(f"  Exception: {get_result.exception}")
    print(f"  Output: {get_result.stdout}")

    # The API should return 200 OK, but the body should be null
    assert get_result.exit_code == 0
    response_text = get_result.stdout.strip()
    assert response_text == "null", f"Expected 'null' after delete, but got: {response_text}"


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main(["-xvs", __file__])
