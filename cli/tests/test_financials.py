#!/usr/bin/env python3
from datetime import datetime
from typing import Dict

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


@pytest.fixture(scope="function")
def financials_fixture(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict) -> str:
    """Ensures financials exist for the auto_claim, yields claim_id."""
    claim_id = auto_claim["id"]
    claim_number = auto_claim["number"]
    print(f"\nRunning Financials Fixture (Create for claim {claim_number} - {claim_id})...")

    # --- Ensure required reserve configs exist for AUTO --- #
    # This is a workaround for potential seeding issues or state conflicts.
    # Ideally, the test DB should be seeded correctly before tests run.
    print(f"  Financials Fixture: Ensuring required reserve configs exist for AUTO...")
    run_command(
        runner,
        [
            "config",
            "reserves",
            "create",
            "--claim-type",
            "AUTO",
            "--reserve-type",
            "BODILY_INJURY",
            "--required",
            "--min-amount",
            "5000",
            "--description",
            "Required BI (fixture ensure)",
        ],
        allow_error=True,
    )  # Allow error if already exists
    run_command(
        runner,
        [
            "config",
            "reserves",
            "create",
            "--claim-type",
            "AUTO",
            "--reserve-type",
            "PROPERTY_DAMAGE",
            "--required",
            "--min-amount",
            "2500",
            "--description",
            "Required PD (fixture ensure)",
        ],
        allow_error=True,
    )  # Allow error if already exists
    # ---------------------------------------------------- #

    # Check if financials already exist (optional, for robustness)
    print(f"  Financials Fixture: Checking existing financials for {claim_id}...")

    exit_code_show, stdout_show = run_command(
        runner, ["claims", "financials", "show", claim_id], allow_error=True
    )

    if exit_code_show == 0:
        print(f"✓ Financials Fixture: Financials already exist for {claim_id}")
    elif exit_code_show == 404:
        # Financials don't exist (expected 404), create them
        print(f"  Financials Fixture: Creating financials for {claim_id}...")
        exit_code_create, stdout_create = run_command(
            runner, ["claims", "financials", "create", claim_id, "--estimated-value", "5000.00"]
        )
        # Assert creation was successful
        assert exit_code_create == 0, f"Failed to create financials in fixture: {stdout_create}"
        print(f"✓ Financials Fixture: Created financials for {claim_id}")
    else:
        # Show command failed unexpectedly
        pytest.fail(
            f"Financials Fixture: Show command failed unexpectedly (Exit: {exit_code_show}):\n{stdout_show}",
            pytrace=False,
        )

    yield claim_id
    print(f"\nFinancials Fixture Teardown for claim {claim_id}...")


@pytest.fixture(scope="function")
def payment_fixture(runner: CliRunner, financials_fixture: str) -> Dict[str, str]:
    """Ensures a payment exists for the claim from financials_fixture.

    Yields:
        dict: Containing details like 'payee' and 'amount' of the created payment.
    """
    claim_id = financials_fixture
    print(f"\nRunning Payment Fixture (Add payment to claim {claim_id})...")

    payment_details = {
        "payment_type": "INDEMNITY",
        "amount": "123.45",  # Unique amount for fixture
        "payee": f"Fixture Payee {datetime.now().strftime('%H%M%S%f')}",
        "payment_date": "2023-01-15",  # Changed to a fixed past date
    }

    exit_code, stdout = run_command(
        runner,
        [
            "claims",
            "financials",
            "add-payment",
            claim_id,
            "--payment-type",
            payment_details["payment_type"],
            "--amount",
            payment_details["amount"],
            "--payee",
            payment_details["payee"],
            "--payment-date",
            payment_details["payment_date"],
        ],
    )

    if exit_code != 0:
        pytest.fail(
            f"Payment Fixture: Failed to add payment for claim {claim_id}. Output:\n{stdout}",
            pytrace=False,
        )

    print(
        f"✓ Payment Fixture: Added payment for claim {claim_id} (Payee: {payment_details['payee']})"
    )
    yield payment_details
    # Teardown (optional - payment deletion might not be needed if claim is deleted)
    print(f"\nPayment Fixture Teardown for claim {claim_id} (Payee: {payment_details['payee']})...")


@pytest.mark.order(15)
def test_15_financials_reserve_set(runner: CliRunner, financials_fixture: str):
    """Test setting/updating a reserve after ensuring financials exist."""
    claim_id = financials_fixture  # Get claim_id from the fixture
    print("\nTesting Financials Reserve Set (update-reserve)...")

    reserve_type_to_update = "BODILY_INJURY"
    new_amount = "7500.00"  # New amount above the minimum
    notes = f"Test update via test_15 for {claim_id}"

    print(f"  Updating {reserve_type_to_update} reserve for claim {claim_id}...")
    # Expect only 2 return values now
    exit_code_update, stdout_update = run_command(
        runner,
        [
            "claims",
            "financials",
            "update-reserve",
            claim_id,
            "--reserve-type",
            reserve_type_to_update,
            "--amount",
            new_amount,
            "--notes",
            notes,
        ],
    )

    assert exit_code_update == 0, f"CLI command failed: {stdout_update}"
    assert "Updated reserve:" in stdout_update
    print(f"✓ Reserve {reserve_type_to_update} updated successfully for claim {claim_id}")


@pytest.mark.order(16)
def test_16_financials_reserve_list(runner: CliRunner, financials_fixture: str):
    """Test listing reserves using claims financials show."""
    claim_id = financials_fixture  # Get claim_id from the fixture
    print("\nTesting Financials Reserve List (show)...")

    exit_code, stdout = run_command(
        runner, ["claims", "financials", "show", claim_id, "--output", "json"]
    )
    assert exit_code == 0, f"CLI command failed: {stdout}"

    # Parse and validate JSON response
    financials = validate_json_response(stdout)
    assert isinstance(financials, dict), "Expected a financials object"

    # Check for required financial sections
    assert "reserves" in financials, "Expected reserves information in financials"
    assert isinstance(financials["reserves"], list), "Expected reserves to be a list"
    assert len(financials["reserves"]) > 0, "Expected at least one reserve"

    # Check specific reserve types exist
    reserve_types = [reserve["reserve_type"] for reserve in financials["reserves"]]
    assert "BODILY_INJURY" in reserve_types, "Expected BODILY_INJURY reserve"
    assert "PROPERTY_DAMAGE" in reserve_types, "Expected PROPERTY_DAMAGE reserve"

    # Check reserve amounts
    for reserve in financials["reserves"]:
        if reserve["reserve_type"] == "BODILY_INJURY":
            assert (
                float(reserve["amount"]) >= 5000.0
            ), "BODILY_INJURY reserve should be at least 5000.00"
        elif reserve["reserve_type"] == "PROPERTY_DAMAGE":
            assert (
                float(reserve["amount"]) >= 2500.0
            ), "PROPERTY_DAMAGE reserve should be at least 2500.00"

    print(
        "✓ Financials show command successful and includes default reserves (with JSON validation)"
    )


@pytest.mark.order(17)
def test_17_financials_payment_add(runner: CliRunner, financials_fixture: str):
    """Test adding a payment after ensuring financials exist."""
    claim_id = financials_fixture  # Get claim_id from the fixture
    print(f"\nTesting Payment Add for claim {claim_id}...")

    payment_type = "INDEMNITY"
    amount = "500.00"
    payee = "Test Payee Fixture"
    payment_date_str = "2023-01-15"  # Changed to a fixed past date

    print(f"  Adding {payment_type} payment...")
    # Expect only 2 return values now
    exit_code, stdout = run_command(
        runner,
        [
            "claims",
            "financials",
            "add-payment",
            claim_id,
            "--payment-type",
            payment_type,
            "--amount",
            amount,
            "--payee",
            payee,
            "--payment-date",
            payment_date_str,
        ],
    )

    assert exit_code == 0, f"CLI command failed: {stdout}"
    # Make assertion less brittle - check for key content
    assert "Payment ID" in stdout  # Check for the Payment ID label as confirmation
    assert payee in stdout
    assert amount in stdout
    print(f"✓ Payment added successfully for claim {claim_id}")


@pytest.mark.order(18)
def test_18_financials_payment_list(
    runner: CliRunner, payment_fixture: Dict[str, str], financials_fixture: str
):
    """Test listing payments after adding a payment."""
    claim_id = financials_fixture  # Still need claim_id for the command
    created_payment = payment_fixture  # Get details of the payment created by the fixture
    print(f"\nTesting Payment List for claim {claim_id} (using payment fixture)...")

    # List payments in JSON format
    exit_code_list, stdout_list = run_command(
        runner,
        [
            "claims",
            "financials",
            "list-payments",
            claim_id,
            "--output-format",  # Correct option name
            "json",
        ],
    )
    assert exit_code_list == 0, f"CLI command failed: {stdout_list}"

    # Parse the JSON output
    payments_data = validate_json_response(stdout_list)

    # Check that the response is a list (or dict containing a list)
    payment_list = []
    if isinstance(payments_data, list):
        payment_list = payments_data
    elif (
        isinstance(payments_data, dict)
        and "items" in payments_data
        and isinstance(payments_data["items"], list)
    ):
        payment_list = payments_data["items"]
    else:
        pytest.fail(f"Unexpected JSON structure for payments: {payments_data}")

    # Find the payment created by the fixture within the list
    found_payment = next(
        (
            p
            for p in payment_list
            if p.get("payee") == created_payment["payee"]
            and str(p.get("amount"))
            == created_payment["amount"]  # Compare as string due to potential Decimal
        ),
        None,
    )

    assert (
        found_payment is not None
    ), f"Payment created by fixture (Payee: {created_payment['payee']}) not found in list response: {payment_list}"

    # Optionally check other fields if needed
    assert found_payment["payment_type"] == created_payment["payment_type"]

    print("✓ List payments successful (JSON) and includes payment from payment_fixture")


@pytest.mark.order(19)
def test_19_financials_defense_payment(runner: CliRunner, financials_fixture: str):
    """Test adding a defense payment and verifying it is properly tracked."""
    claim_id = financials_fixture  # Get claim_id from the fixture
    print(f"\nTesting Defense Payment Add for claim {claim_id}...")

    payment_type = "DEFENSE"
    amount = "1500.00"
    payee = "Defense Attorney"
    payment_date_str = "2023-01-15"  # Changed to a fixed past date
    notes = "Test defense costs tracking"

    print(f"  Adding {payment_type} payment...")
    exit_code, stdout = run_command(
        runner,
        [
            "claims",
            "financials",
            "add-payment",
            claim_id,
            "--payment-type",
            payment_type,
            "--amount",
            amount,
            "--payee",
            payee,
            "--payment-date",
            payment_date_str,
            "--notes",
            notes,
        ],
    )

    assert exit_code == 0, f"CLI command failed: {stdout}"
    assert "Payment ID" in stdout
    assert payee in stdout
    assert amount in stdout
    print(f"✓ Defense payment added successfully for claim {claim_id}")

    # Now verify that the defense payment shows up correctly in the financials
    exit_code, stdout = run_command(
        runner, ["claims", "financials", "show", claim_id, "--output", "json"]
    )
    assert exit_code == 0, f"CLI command failed: {stdout}"

    # Parse and validate JSON response
    financials = validate_json_response(stdout)
    assert isinstance(financials, dict), "Expected a financials object"

    # Verify defense paid field exists and is properly set
    assert "defense_paid" in financials, "Expected defense_paid field in financials"
    defense_paid = float(financials["defense_paid"])
    assert defense_paid >= float(
        amount
    ), f"Expected defense_paid to be at least {amount}, got {defense_paid}"

    print(f"✓ Defense payment amount {amount} is properly tracked in financials")
