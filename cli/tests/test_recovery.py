#!/usr/bin/env python3
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_recovery_status_update(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict):
    """Test updating the recovery status of a claim."""
    print("\nTesting Recovery Status Update...")
    claim_id = auto_claim["id"]
    claim_number = auto_claim["number"]

    # Test using claim ID and JSON output
    exit_code, stdout = run_command(
        runner,
        ["recovery", "status", claim_id, "--status", "INITIATED", "--use-id", "--output", "json"],
    )
    assert exit_code == 0, f"Failed to update recovery status: {stdout}"

    # Verify the claim was updated by getting it
    exit_code, stdout = run_command(
        runner, ["claims", "get", claim_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0
    claim_data = validate_json_response(stdout)
    assert claim_data["recovery_status"] == "INITIATED", "Recovery status should be INITIATED"

    # Test using claim number and text output
    exit_code, stdout = run_command(
        runner, ["recovery", "status", claim_number, "--status", "IN_PROGRESS"]
    )
    assert exit_code == 0, f"Failed to update recovery status: {stdout}"
    assert "Recovery status updated successfully to" in stdout
    assert "IN_PROGRESS" in stdout

    # Verify the claim was updated again
    exit_code, stdout = run_command(
        runner, ["claims", "get", claim_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0
    claim_data = validate_json_response(stdout)
    assert claim_data["recovery_status"] == "IN_PROGRESS", "Recovery status should be IN_PROGRESS"

    print("✓ Recovery status update successful")


def test_recovery_carrier_update(runner: CliRunner, auto_claim: dict):
    """Test updating the recovery carrier details of a claim."""
    print("\nTesting Recovery Carrier Update...")
    claim_id = auto_claim["id"]
    claim_number = auto_claim["number"]

    # Test carrier update with both name and contact
    carrier_name = "Test Insurance Company"
    carrier_contact = "John Smith, 555-123-4567"

    exit_code, stdout = run_command(
        runner,
        [
            "recovery",
            "carrier",
            claim_id,
            "--use-id",
            "--carrier-name",
            carrier_name,
            "--carrier-contact",
            carrier_contact,
            "--output",
            "json",
        ],
    )
    assert exit_code == 0, f"Failed to update carrier details: {stdout}"

    # Verify the claim was updated
    exit_code, stdout = run_command(
        runner, ["claims", "get", claim_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0
    claim_data = validate_json_response(stdout)
    assert claim_data["carrier_name"] == carrier_name, "Carrier name should be updated"
    assert claim_data["carrier_contact"] == carrier_contact, "Carrier contact should be updated"

    # Test update with only carrier name using claim number
    new_carrier_name = "Updated Insurance Company"
    exit_code, stdout = run_command(
        runner, ["recovery", "carrier", claim_number, "--carrier-name", new_carrier_name]
    )
    assert exit_code == 0, f"Failed to update carrier name: {stdout}"
    assert "Carrier details updated for claim" in stdout

    # Verify the carrier name was updated but contact remained the same
    exit_code, stdout = run_command(
        runner, ["claims", "get", claim_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0
    claim_data = validate_json_response(stdout)
    assert claim_data["carrier_name"] == new_carrier_name, "Carrier name should be updated"
    assert claim_data["carrier_contact"] == carrier_contact, "Carrier contact should not be changed"

    print("✓ Recovery carrier update successful")


def test_recovery_get_details(runner: CliRunner, auto_claim: dict):
    """Test getting recovery details for a claim."""
    print("\nTesting Recovery Get Details...")
    claim_id = auto_claim["id"]
    claim_number = auto_claim["number"]

    # First set up some recovery data to retrieve
    carrier_name = "Test Recovery Insurance"
    carrier_contact = "Jane Doe, 555-987-6543, <EMAIL>"

    # Set recovery status
    exit_code, stdout = run_command(
        runner, ["recovery", "status", claim_number, "--status", "IN_PROGRESS"]
    )
    assert exit_code == 0, f"Failed to set recovery status: {stdout}"

    # Set carrier details
    exit_code, stdout = run_command(
        runner,
        [
            "recovery",
            "carrier",
            claim_number,
            "--carrier-name",
            carrier_name,
            "--carrier-contact",
            carrier_contact,
        ],
    )
    assert exit_code == 0, f"Failed to set carrier details: {stdout}"

    # Test getting recovery details with JSON output
    exit_code, stdout = run_command(
        runner, ["recovery", "get", claim_id, "--use-id", "--output", "json"]
    )
    assert exit_code == 0, f"Failed to get recovery details: {stdout}"

    # Validate JSON response
    recovery_data = validate_json_response(stdout)
    assert (
        recovery_data["recovery_status"] == "IN_PROGRESS"
    ), "Recovery status should be IN_PROGRESS"
    assert recovery_data["carrier_name"] == carrier_name, "Carrier name should match"
    assert recovery_data["carrier_contact"] == carrier_contact, "Carrier contact should match"
    assert recovery_data["claim_id"] == claim_id, "Claim ID should match"
    assert recovery_data["claim_number"] == claim_number, "Claim number should match"

    # Test getting recovery details with table output (default)
    exit_code, stdout = run_command(runner, ["recovery", "get", claim_number])
    assert exit_code == 0, f"Failed to get recovery details with table output: {stdout}"
    assert "Recovery Details for Claim" in stdout
    assert "IN_PROGRESS" in stdout
    assert carrier_name in stdout
    assert carrier_contact in stdout

    print("✓ Recovery get details successful")


def test_recovery_get_no_data(runner: CliRunner, auto_claim: dict):
    """Test getting recovery details for a claim with no recovery data."""
    print("\nTesting Recovery Get Details (No Data)...")

    # Create a fresh claim that won't have recovery data
    # We'll use a different claim from the fixture or create a minimal one
    claim_number = auto_claim["number"]

    # First, let's clear any existing recovery data by setting status to NOT_STARTED
    # and clearing carrier details
    exit_code, stdout = run_command(
        runner, ["recovery", "status", claim_number, "--status", "NOT_STARTED"]
    )
    assert exit_code == 0, f"Failed to reset recovery status: {stdout}"

    exit_code, stdout = run_command(
        runner,
        [
            "recovery",
            "carrier",
            claim_number,
            "--carrier-name",
            "",
            "--carrier-contact",
            "",
        ],
    )
    assert exit_code == 0, f"Failed to clear carrier details: {stdout}"

    # Test getting recovery details - should show "Not Set" values
    exit_code, stdout = run_command(runner, ["recovery", "get", claim_number])
    assert exit_code == 0, f"Failed to get recovery details: {stdout}"
    assert "Recovery Details for Claim" in stdout
    assert "NOT_STARTED" in stdout

    # Test with JSON output
    exit_code, stdout = run_command(runner, ["recovery", "get", claim_number, "--output", "json"])
    assert exit_code == 0, f"Failed to get recovery details with JSON: {stdout}"

    recovery_data = validate_json_response(stdout)
    assert recovery_data["recovery_status"] == "NOT_STARTED"
    # Carrier fields might be empty strings or null
    assert recovery_data.get("carrier_name") in [None, ""]
    assert recovery_data.get("carrier_contact") in [None, ""]

    print("✓ Recovery get details (no data) successful")
