"""Tests for basic configuration commands."""

import os
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.commands.config.basic import app
from claimentine_cli.utils.env_config import get_env_file

runner = CliRunner()


@pytest.fixture
def mock_env_file(tmp_path):
    """Create a temporary .env file for testing."""
    test_env_file = tmp_path / ".env"
    test_env_file.touch()
    with patch("claimentine_cli.utils.env_config.get_env_file", return_value=test_env_file):
        yield test_env_file


def test_set_config(mock_env_file):
    """Test setting a configuration value."""
    result = runner.invoke(app, ["set", "api-url", "http://example.com"])
    assert result.exit_code == 0
    assert "Successfully set api_url to http://example.com" in result.stdout

    # Check if the value was actually saved
    env_content = mock_env_file.read_text()
    assert "CLAIMENTINE_API_URL=http://example.com" in env_content


def test_show_config():
    """Test showing configuration values."""
    # Mock Config to return predictable values
    mock_config = MagicMock()
    mock_config.api_url = "http://test-api.com"

    with (
        patch("claimentine_cli.commands.config.basic.Config", return_value=mock_config),
        patch(
            "claimentine_cli.commands.config.basic.get_env_file",
            return_value=Path("/fake/path/.env"),
        ),
    ):
        result = runner.invoke(app, ["show"])

    assert result.exit_code == 0
    assert "Current Configuration" in result.stdout
    assert "API URL: http://test-api.com" in result.stdout
    assert "/fake/path/.env" in result.stdout


def test_reset_config_specific_key(mock_env_file):
    """Test resetting a specific configuration value."""
    # First set a value to reset
    mock_env_file.write_text("CLAIMENTINE_API_URL=http://example.com\n")

    # Reset with auto-confirmation
    result = runner.invoke(app, ["reset", "api_url", "--yes"])
    assert result.exit_code == 0
    assert "Successfully reset api_url" in result.stdout

    # Verify it was removed
    env_content = mock_env_file.read_text()
    assert "CLAIMENTINE_API_URL" not in env_content


def test_reset_config_all(mock_env_file):
    """Test resetting all configuration values."""
    # Set multiple values
    mock_env_file.write_text(
        "CLAIMENTINE_API_URL=http://example.com\n" "CLAIMENTINE_OTHER_KEY=value\n"
    )

    # Reset all with auto-confirmation
    result = runner.invoke(app, ["reset", "--yes"])
    assert result.exit_code == 0
    assert "Successfully reset configuration to defaults" in result.stdout

    # Verify API URL was reset to default dev environment URL
    env_content = mock_env_file.read_text()
    assert "CLAIMENTINE_API_URL=http://localhost:8000" in env_content
    # Other keys should be preserved (not managed by the environment system)
    assert "CLAIMENTINE_OTHER_KEY=value" in env_content


def test_init_config(mock_env_file):
    """Test initializing configuration."""
    # Remove the file to test creation
    mock_env_file.unlink()

    with patch("claimentine_cli.commands.config.basic.Config") as mock_config_class:
        mock_config = MagicMock()
        mock_config.api_url = "http://localhost:8000"
        mock_config_class.return_value = mock_config

        result = runner.invoke(app, ["init"])

    assert result.exit_code == 0
    assert "Configuration initialized successfully" in result.stdout
    assert mock_env_file.exists()

    # Verify that when the Config object is loaded, the default API URL is set
    # The mock_config is returned when Config() is called, and we verified that
    # the function used the mock correctly
