#!/usr/bin/env python3
import json
from datetime import datetime, timedelta

# Import the main app
from claimentine_cli.main import app

# Import helper functions from conftest
from .conftest import validate_json_response

# --- Audit Tests (Integration) ---


def test_audit_list(runner, auto_claim):
    """Test listing audit entries for a claim via integration."""
    claim_id = auto_claim["id"]

    # Make some changes to the claim to generate audit entries
    # Update a claim
    update_result = runner.invoke(
        app,
        [
            "claims",
            "update",
            claim_id,
            "--description",
            f"Updated description for audit test {datetime.now().isoformat()}",
        ],
        catch_exceptions=False,
    )
    assert (
        update_result.exit_code == 0
    ), f"Failed to update claim for audit test: {update_result.stdout}"

    # Test listing audit entries (JSON)
    list_result = runner.invoke(
        app,
        ["audit", "list", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert list_result.exit_code == 0, f"audit list json failed: {list_result.stdout}"
    audit_response = validate_json_response(list_result.stdout)

    # Extract the items from the paginated response
    assert "items" in audit_response, "Response missing 'items' field"
    audit_entries = audit_response["items"]
    assert isinstance(audit_entries, list), "Audit entries should be a list"
    assert len(audit_entries) > 0, "No audit entries found"

    # Check some basic properties of audit entries
    for entry in audit_entries:
        assert "id" in entry
        assert "entity_type" in entry
        assert "change_type" in entry
        assert "changed_at" in entry
        assert entry.get("claim_id") == claim_id


def test_audit_list_with_filters(runner, auto_claim):
    """Test listing audit entries with filters."""
    claim_id = auto_claim["id"]

    # Create a unique witness to generate specific audit entries
    witness_name = f"Audit Test Witness {datetime.now().isoformat()}"
    add_result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            witness_name,
            "--email",
            "<EMAIL>",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add witness for audit test: {add_result.stdout}"

    # Test listing with entity_type filter
    filter_result = runner.invoke(
        app,
        ["audit", "list", claim_id, "--entity-type", "WITNESS", "--output", "json"],
        catch_exceptions=False,
    )
    assert filter_result.exit_code == 0, f"audit list with filters failed: {filter_result.stdout}"
    filter_response = validate_json_response(filter_result.stdout)

    # Extract the items from the paginated response
    assert "items" in filter_response, "Response missing 'items' field"
    filtered_entries = filter_response["items"]
    assert isinstance(filtered_entries, list), "Filtered entries should be a list"
    assert len(filtered_entries) > 0, "No audit entries found with entity_type=WITNESS"

    # Check that all entries match the filter
    for entry in filtered_entries:
        assert (
            entry.get("entity_type") == "WITNESS"
        ), f"Entity type mismatch: {entry.get('entity_type')}"

    # Test date range filter - last 24 hours
    today = datetime.now().strftime("%Y-%m-%d")
    yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    date_filter_result = runner.invoke(
        app,
        [
            "audit",
            "list",
            claim_id,
            "--from-date",
            yesterday,
            "--to-date",
            today,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert (
        date_filter_result.exit_code == 0
    ), f"audit list with date filters failed: {date_filter_result.stdout}"
    date_filter_response = validate_json_response(date_filter_result.stdout)

    # Extract items from paginated response
    assert "items" in date_filter_response, "Response missing 'items' field"
    date_filtered_entries = date_filter_response["items"]
    assert isinstance(date_filtered_entries, list), "Date filtered entries should be a list"
    assert len(date_filtered_entries) > 0, "No audit entries found within date range"


def test_audit_get(runner, auto_claim):
    """Test getting a specific audit entry."""
    claim_id = auto_claim["id"]

    # Generate an audit entry by updating the claim
    update_result = runner.invoke(
        app,
        [
            "claims",
            "update",
            claim_id,
            "--description",
            f"Updated for audit get test {datetime.now().isoformat()}",
        ],
        catch_exceptions=False,
    )
    assert (
        update_result.exit_code == 0
    ), f"Failed to update claim for audit get test: {update_result.stdout}"

    # Get audit entries
    list_result = runner.invoke(
        app,
        ["audit", "list", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert list_result.exit_code == 0, f"audit list json failed: {list_result.stdout}"
    audit_response = validate_json_response(list_result.stdout)

    # Extract items from paginated response
    assert "items" in audit_response, "Response missing 'items' field"
    audit_entries = audit_response["items"]
    assert len(audit_entries) > 0, "No audit entries found"

    # Get the first entry
    audit_id = audit_entries[0]["id"]
    get_result = runner.invoke(
        app,
        ["audit", "get", audit_id, claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert get_result.exit_code == 0, f"audit get failed: {get_result.stdout}"
    audit_entry = validate_json_response(get_result.stdout)

    # Verify the entry details
    assert audit_entry.get("id") == audit_id
    assert audit_entry.get("claim_id") == claim_id
    assert "entity_type" in audit_entry
    assert "change_type" in audit_entry
    assert "changed_at" in audit_entry


def test_audit_summary(runner, auto_claim):
    """Test getting an audit summary for a claim."""
    claim_id = auto_claim["id"]

    # Create additional audit entries by making changes
    # Add a witness
    witness_result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            f"Summary Test Witness {datetime.now().isoformat()}",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert (
        witness_result.exit_code == 0
    ), f"Failed to add witness for audit summary test: {witness_result.stdout}"

    # Add an attorney
    attorney_result = runner.invoke(
        app,
        [
            "attorneys",
            "add",
            claim_id,
            "--name",
            f"Summary Test Attorney {datetime.now().isoformat()}",
            "--attorney-type",
            "DEFENSE",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert (
        attorney_result.exit_code == 0
    ), f"Failed to add attorney for audit summary test: {attorney_result.stdout}"

    # Get the audit summary
    summary_result = runner.invoke(
        app,
        ["audit", "summary", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert summary_result.exit_code == 0, f"audit summary failed: {summary_result.stdout}"
    summary_data = validate_json_response(summary_result.stdout)

    # Verify summary structure
    assert "total_entries" in summary_data
    assert summary_data["total_entries"] > 0
    assert "by_entity" in summary_data
    assert "by_change_type" in summary_data
    assert "recent_activity" in summary_data
    assert isinstance(summary_data["recent_activity"], list)

    # There should be entries for the entities we modified
    assert len(summary_data["by_entity"]) > 0
    assert len(summary_data["by_change_type"]) > 0

    # Check for CREATE entries since we added entities
    if "CREATE" in summary_data["by_change_type"]:
        assert summary_data["by_change_type"]["CREATE"] > 0


def test_audit_list_pagination(runner, auto_claim):
    """Test pagination for audit entries."""
    claim_id = auto_claim["id"]

    # Make some changes to generate audit entries
    for i in range(3):  # Create multiple entries
        update_result = runner.invoke(
            app,
            [
                "claims",
                "update",
                claim_id,
                "--description",
                f"Pagination test {i} - {datetime.now().isoformat()}",
            ],
            catch_exceptions=False,
        )
        assert update_result.exit_code == 0, f"Failed to update claim: {update_result.stdout}"

    # Test with limit=1 (should return 1 entry)
    limited_result = runner.invoke(
        app,
        ["audit", "list", claim_id, "--limit", "1", "--output", "json"],
        catch_exceptions=False,
    )
    assert limited_result.exit_code == 0, f"Failed to list with limit: {limited_result.stdout}"
    limited_data = validate_json_response(limited_result.stdout)

    # Verify the response structure
    assert "items" in limited_data, "Response missing 'items' key"
    assert "total" in limited_data, "Response missing 'total' key"
    assert "skip" in limited_data, "Response missing 'skip' key"
    assert "limit" in limited_data, "Response missing 'limit' key"

    # Verify pagination works
    assert len(limited_data["items"]) == 1, f"Expected 1 item, got {len(limited_data['items'])}"
    assert limited_data["limit"] == 1, f"Expected limit=1, got {limited_data['limit']}"
    assert limited_data["skip"] == 0, f"Expected skip=0, got {limited_data['skip']}"
    assert limited_data["total"] >= 3, f"Expected total>=3, got {limited_data['total']}"

    # Test with skip=1 (should skip the first entry)
    skip_result = runner.invoke(
        app,
        ["audit", "list", claim_id, "--skip", "1", "--limit", "1", "--output", "json"],
        catch_exceptions=False,
    )
    assert skip_result.exit_code == 0, f"Failed to list with skip: {skip_result.stdout}"
    skip_data = validate_json_response(skip_result.stdout)

    # Verify second page has different entry
    assert (
        skip_data["items"][0]["id"] != limited_data["items"][0]["id"]
    ), "Expected different entries when using skip"


# --- End Audit Tests ---
