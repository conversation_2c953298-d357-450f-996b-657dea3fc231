#!/usr/bin/env python3
"""Tests for the all documents list API endpoint."""

import json

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import validate_json_response

# Import uploaded_document fixture from test_documents.py
from .test_documents import uploaded_document


def test_list_all_documents(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict, uploaded_document: str):
    """Test listing all documents across all claims."""
    # Ensure we have at least one document via the fixture

    # Run the command
    result = runner.invoke(
        app,
        [
            "documents",
            "list-all",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    # Check result
    assert result.exit_code == 0
    documents_data = validate_json_response(result.stdout)

    # Verify the structure of the response
    assert "items" in documents_data
    assert "total" in documents_data
    assert isinstance(documents_data["items"], list)
    assert isinstance(documents_data["total"], int)

    # Verify the document we created (via fixture) is in the response
    document_ids = [doc.get("id") for doc in documents_data["items"]]
    assert (
        uploaded_document in document_ids
    ), f"Uploaded document {uploaded_document} not found in global document list"

    # Verify fields in the response
    for doc in documents_data["items"]:
        assert "id" in doc
        assert "name" in doc
        assert "type" in doc
        assert "claim_id" in doc
        assert "file_size" in doc
        assert "created_at" in doc


def test_list_all_documents_with_filter(
    runner: CliRunner, auto_claim: dict, uploaded_document: str
):
    """Test filtering documents by type."""
    # First get the document details to know its type
    claim_id = auto_claim["id"]
    get_doc_result = runner.invoke(
        app,
        [
            "documents",
            "get",
            "--claim-identifier",
            claim_id,
            uploaded_document,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert get_doc_result.exit_code == 0
    doc_data = validate_json_response(get_doc_result.stdout)
    doc_type = doc_data.get("type")

    # Now use that type for filtering
    result = runner.invoke(
        app,
        [
            "documents",
            "list-all",
            "--type",
            doc_type,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result.exit_code == 0
    documents_data = validate_json_response(result.stdout)

    # Verify all returned documents have the requested type
    for doc in documents_data["items"]:
        assert doc["type"] == doc_type

    # Make sure our test document is in the results
    document_ids = [doc.get("id") for doc in documents_data["items"]]
    assert uploaded_document in document_ids, f"Uploaded document not found in filtered list"


def test_list_all_documents_pagination(runner: CliRunner, auto_claim: dict, uploaded_document: str):
    """Test pagination for the list all documents command."""
    # Run command with pagination parameters - limit of 1 should return exactly 1 item
    result = runner.invoke(
        app,
        [
            "documents",
            "list-all",
            "--skip",
            "0",
            "--limit",
            "1",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result.exit_code == 0
    documents_data = validate_json_response(result.stdout)

    # Verify pagination works - should have exactly 1 item with limit=1
    assert len(documents_data["items"]) == 1

    # Verify the total count is still accurate (greater than or equal to 1)
    assert documents_data["total"] >= 1

    # Now test skipping the first item
    result_skip = runner.invoke(
        app,
        [
            "documents",
            "list-all",
            "--skip",
            "1",
            "--limit",
            "10",
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result_skip.exit_code == 0
    skip_data = validate_json_response(result_skip.stdout)

    # If there are multiple documents, verify the first item is different
    if documents_data["total"] > 1:
        assert skip_data["items"][0]["id"] != documents_data["items"][0]["id"]
