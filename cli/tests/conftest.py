import json
import random
import re
import shlex
import string
import traceback
import uuid  # Add uuid import
from datetime import datetime
from typing import Dict, Generator, List, Optional, Tuple

import httpx
import pytest
import typer  # Added import for typer.Abort
from typer.testing import Cli<PERSON>unner

# Assuming Config is not needed directly in conftest helpers/fixtures
# from claimentine_cli.config import Config
from claimentine_cli.main import app


# Fixture for CliRunner
@pytest.fixture(scope="module")
def runner():
    return Cli<PERSON>unner()


# Fixture to run login once per module
@pytest.fixture(scope="module", autouse=True)  # autouse=True ensures it runs for the module
def auth_setup(runner):
    """Logs in the admin user once before running tests in the module."""
    result = runner.invoke(
        app,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
        catch_exceptions=False,
    )

    if result.exit_code != 0:
        pytest.fail("Lo<PERSON> failed during auth_setup, cannot proceed.", pytrace=False)


# Fixture to create a customer
@pytest.fixture(scope="function")  # function scope for now, can be module if needed
def customer(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>) -> str:
    """Creates a unique customer and returns its ID."""
    random_str = "".join(random.choices(string.ascii_uppercase, k=4))
    timestamp = datetime.now().strftime("%H%M%S%f")
    prefix = random_str  # Use only uppercase letters
    customer_name = f"FixtureCust {prefix} {timestamp}"

    # Create customer
    create_result = runner.invoke(
        app,
        [
            "customers",
            "create",
            "--name",
            customer_name,
            "--prefix",
            prefix,
            "--description",
            "Test Customer via Fixture",
        ],
        catch_exceptions=False,
    )

    if create_result.exit_code != 0:
        pytest.fail(f"Failed to create customer '{customer_name}' in fixture", pytrace=False)

    # Find the created customer ID (more robust than parsing stdout)
    list_result = runner.invoke(
        app, ["customers", "list", "--output", "json"], catch_exceptions=False
    )

    if list_result.exit_code != 0:
        pytest.fail("Failed to list customers in fixture after creation", pytrace=False)

    # Call helper function directly
    customers = validate_json_response(list_result.stdout)
    customer_id = next((c["id"] for c in customers if c["name"] == customer_name), None)

    if customer_id is None:
        pytest.fail(
            f"Could not find customer ID for '{customer_name}' after creation", pytrace=False
        )

    yield customer_id
    # Add cleanup here if needed later (e.g., delete customer)


# --- New Fixture for Creating an AUTO Claim ---
@pytest.fixture(scope="function")
def auto_claim(runner: CliRunner, customer: str) -> Dict[str, str]:
    """Creates a unique AUTO claim for the given customer and returns its ID and number.

    Returns:
        dict: A dictionary containing {"id": uuid, "number": claim_number}
    """
    today = datetime.now().strftime("%Y-%m-%d")
    claim_number = None
    claim_id = None
    # Ensure description does not contain raw newlines
    description_base = f"Auto claim via fixture for customer {customer}"
    description = description_base.replace("\\n", " ")  # Explicitly replace newlines with spaces

    result = runner.invoke(
        app,
        [
            "claims",
            "create",
            "--type",
            "AUTO",
            "--client-id",
            customer,
            "--description",
            description,  # Use the explicitly cleaned description string
            "--claimant-name",
            "Fixture Claimant",
            "--incident-date",
            today,
            "--incident-location",
            "Fixture Location",
            "--vehicle-make",
            "FixtureMake",
            "--reporter-phone",
            "************",
        ],
        catch_exceptions=False,
    )

    if result.exit_code != 0:
        pytest.fail(
            f"Failed to create AUTO claim for customer {customer} in fixture", pytrace=False
        )

    # Extract claim number and ID
    list_result = runner.invoke(
        app,
        [
            "claims",
            "list",
            "--client-id",
            customer,
            "--output",
            "json",
            "--page-size",
            "10",  # Filter by customer
        ],
        catch_exceptions=False,
    )

    if list_result.exit_code != 0:
        pytest.fail(
            f"Failed to list claims in fixture after creation for customer {customer}",
            pytrace=False,
        )

    # Call helper function directly
    response_data = validate_json_response(list_result.stdout)

    # Handle paginated response format
    if isinstance(response_data, dict) and "items" in response_data:
        claims = response_data["items"]
    else:
        # Fallback for old format (shouldn't happen anymore)
        claims = response_data if isinstance(response_data, list) else []

    # Find the most recently created claim for this customer
    created_claim = next(
        (
            c
            for c in sorted(claims, key=lambda x: x["created_at"], reverse=True)
            if c["client"]["id"] == customer
        ),
        None,
    )

    if created_claim is None:
        pytest.fail(
            f"Could not find AUTO claim created in fixture for customer {customer}", pytrace=False
        )

    claim_id = created_claim.get("id")
    claim_number = created_claim.get("claim_number")

    if claim_id is None or claim_number is None:
        pytest.fail(
            f"Could not extract ID and Number for AUTO claim created in fixture", pytrace=False
        )

    yield {"id": claim_id, "number": claim_number}


# Fixture for creating a GENERAL_LIABILITY claim
@pytest.fixture(scope="function")
def gl_claim(runner: CliRunner, customer: str) -> Dict[str, str]:
    """Creates a unique GENERAL_LIABILITY claim for the given customer and returns its ID and number.

    Returns:
        dict: A dictionary containing {"id": uuid, "number": claim_number}
    """
    today = datetime.now().strftime("%Y-%m-%d")
    claim_number = None
    claim_id = None
    # Ensure description does not contain raw newlines
    description_base = f"General Liability claim via fixture for customer {customer}"
    description = description_base.replace("\\n", " ")  # Explicitly replace newlines with spaces

    result = runner.invoke(
        app,
        [
            "claims",
            "create",
            "--type",
            "GENERAL_LIABILITY",
            "--client-id",
            customer,
            "--description",
            description,
            "--claimant-name",
            "GL Fixture Claimant",
            "--incident-date",
            today,
            "--incident-location",
            "GL Fixture Location",
            "--gl-incident-type",
            "PREMISES_LIABILITY",
            "--owner-name",
            "GL Fixture Owner",
            "--owner-address",
            "GL Fixture Address",
            "--reporter-phone",
            "************",
        ],
        catch_exceptions=False,
    )

    if result.exit_code != 0:
        pytest.fail(
            f"Failed to create GENERAL_LIABILITY claim for customer {customer} in fixture",
            pytrace=False,
        )

    # Extract claim number and ID
    list_result = runner.invoke(
        app,
        [
            "claims",
            "list",
            "--client-id",
            customer,
            "--output",
            "json",
            "--page-size",
            "10",  # Filter by customer
        ],
        catch_exceptions=False,
    )

    if list_result.exit_code != 0:
        pytest.fail(
            f"Failed to list claims in fixture after creation for customer {customer}",
            pytrace=False,
        )

    # Call helper function directly
    response_data = validate_json_response(list_result.stdout)

    # Handle paginated response format
    if isinstance(response_data, dict) and "items" in response_data:
        claims = response_data["items"]
    else:
        # Fallback for old format (shouldn't happen anymore)
        claims = response_data if isinstance(response_data, list) else []

    # Find the most recently created claim for this customer
    created_claim = next(
        (
            c
            for c in sorted(claims, key=lambda x: x["created_at"], reverse=True)
            if c["client"]["id"] == customer and c["type"] == "GENERAL_LIABILITY"
        ),
        None,
    )

    if created_claim is None:
        pytest.fail(
            f"Could not find GENERAL_LIABILITY claim created in fixture for customer {customer}",
            pytrace=False,
        )

    claim_id = created_claim.get("id")
    claim_number = created_claim.get("claim_number")

    if claim_id is None or claim_number is None:
        pytest.fail(
            f"Could not extract ID and Number for GENERAL_LIABILITY claim created in fixture",
            pytrace=False,
        )

    yield {"id": claim_id, "number": claim_number}


# Helper function to safely join CLI args for printing
def join_cli_args(args: List[str]) -> str:
    """Safely joins CLI arguments for printing."""
    return " ".join(shlex.quote(str(arg)) for arg in args)


# Centralized command runner - Reverted to helper function
def run_command(
    runner: CliRunner,  # Runner now passed as argument
    cli_args: List[str],
    input_str: Optional[str] = None,
    allow_error: bool = False,
) -> Tuple[int, str]:
    """Helper function to run CLI commands and return exit code, stdout (mixed with stderr)."""
    exit_code = -1
    stdout = ""
    exception_occurred = None

    # Let the runner catch exceptions if allow_error is True
    result = runner.invoke(app, cli_args, input=input_str, catch_exceptions=allow_error)

    # Determine the final exit code and stdout based on the result
    exit_code = result.exit_code
    stdout = result.stdout  # runner.stdout captures both stdout and stderr
    exception_occurred = result.exception

    if exception_occurred:
        # If an exception was caught by the runner
        if isinstance(exception_occurred, SystemExit):
            # If it's SystemExit (includes typer.Exit), try to get the code
            # typer.Exit stores it in .code, base SystemExit stores it in .args[0] sometimes
            exit_code_from_exc = getattr(exception_occurred, "code", None)
            if exit_code_from_exc is None and exception_occurred.args:
                try:
                    # Ensure args[0] exists and is convertible to int
                    if len(exception_occurred.args) > 0:
                        exit_code_from_exc = int(exception_occurred.args[0])
                    else:
                        exit_code_from_exc = 1  # Default if args is empty
                except (ValueError, TypeError):
                    exit_code_from_exc = 1  # Default if code cannot be determined from args
            elif exit_code_from_exc is None:
                # This handles cases where .code is None and args is empty
                exit_code_from_exc = 1  # Default if code is None

            exit_code = exit_code_from_exc
            stdout = stdout.strip() + f"\n(Command exited via SystemExit({exit_code}))"

        elif isinstance(exception_occurred, httpx.HTTPStatusError):
            # This case handles HTTP errors if the CLI command *didn't* catch them and raise typer.Exit
            status_code = (
                exception_occurred.response.status_code if exception_occurred.response else 500
            )
            exit_code = status_code
            stdout = (
                stdout.strip()
                + f"\n(Caught HTTPStatusError: {status_code} - {str(exception_occurred)})"
            )

        elif not allow_error:
            # If it's another unexpected exception and errors are not allowed, raise it
            raise exception_occurred
        else:
            # If it's another unexpected exception but errors are allowed, use default exit code (likely 1 from runner)
            exit_code = (
                result.exit_code if result.exit_code != 0 else 1
            )  # Ensure non-zero exit code
            stdout = stdout.strip() + f"\n(Caught Unexpected Exception: {str(exception_occurred)})"

    # If no exception occurred, but the exit code is non-zero and errors are not allowed
    elif not allow_error and exit_code != 0:
        raise AssertionError(
            f"Command failed without exception, exit code {exit_code}.\n"
            f"Args: {cli_args}\n"
            f"Output:\n{stdout}"
        )

    # Ensure exit code has a sensible default if still -1 (shouldn't happen)
    if exit_code == -1:
        exit_code = 0

    return exit_code, stdout.strip()


# JSON validator - Reverted to helper function
def validate_json_response(stdout: str) -> dict | list:
    """Validate and parse JSON response, attempting cleanup and ANSI stripping."""
    # ANSI escape code regex
    ansi_escape_pattern = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
    stdout_no_ansi = ansi_escape_pattern.sub("", stdout)

    # Remove debug lines
    clean_lines = []
    for line in stdout_no_ansi.splitlines():
        if not line.strip().startswith("DEBUG:"):
            clean_lines.append(line)
    stdout_no_ansi = "\n".join(clean_lines)

    # Attempt to find the start and end of the JSON structure on the cleaned string
    stdout_trimmed = stdout_no_ansi.strip()
    json_str_to_parse = stdout_trimmed  # Default to trimmed string

    # Basic check for outer brackets/braces
    if stdout_trimmed.startswith("[") and stdout_trimmed.endswith("]"):
        # Assume it's a list
        pass
    elif stdout_trimmed.startswith("{") and stdout_trimmed.endswith("}"):
        # Assume it's a dictionary
        pass
    else:
        # If no clear bounds, try parsing the whole trimmed string, but warn
        pass  # Allow parsing attempt anyway

    try:
        # Attempt to parse the determined string
        parsed_json = json.loads(json_str_to_parse)
        # Add type check after parsing
        if not isinstance(parsed_json, (dict, list)):
            pytest.fail(f"Parsed JSON is unexpected type: {type(parsed_json)}")
        return parsed_json
    except json.JSONDecodeError as e:
        pytest.fail(f"Invalid JSON response: {e}\nOriginal stdout:\n{stdout}", pytrace=False)


def generate_prefix() -> str:
    """Generate a random 4-character uppercase alphanumeric prefix."""
    return "".join(random.choices(string.ascii_uppercase + string.digits, k=4))


# --- Fixture for Creating Temporary Reserve Configuration ---
@pytest.fixture(scope="function")
def config_fixture(runner: CliRunner) -> Generator[str, None, None]:
    """Creates a temporary reserve configuration for testing get/delete.

    Yields:
        str: The ID of the created reserve configuration.

    Cleans up by deleting the configuration afterwards.
    """
    unique_suffix = str(uuid.uuid4()).split("-")[0]  # Short unique ID
    config_desc = f"Temp test config {unique_suffix}"

    # Create the reserve configuration using run_command with JSON output
    # Pass runner explicitly to run_command helper
    exit_code_create, stdout_create = run_command(
        runner,
        [
            "config",
            "reserves",
            "create",
            "--claim-type",
            "AUTO",  # Corrected option
            "--reserve-type",
            "PROPERTY_DAMAGE",  # Try a standard type
            "--description",
            config_desc,
            "--min-amount",
            "100",
            "--no-required",  # Default to not required
            "--output",
            "json",  # Get JSON output to easily parse ID
        ],
    )

    if exit_code_create != 0:
        pytest.fail(f"Config Fixture Failed (Create): {stdout_create}", pytrace=False)

    # Parse the JSON response to get the ID
    try:
        created_config = validate_json_response(stdout_create)
        config_id = created_config["id"]
    except Exception as e:
        pytest.fail(
            f"Config Fixture Failed (Parsing Create Output): {e}\nOutput: {stdout_create}",
            pytrace=False,
        )

    yield config_id  # Provide the ID to the test

    # --- Teardown ---
    exit_code_delete, stdout_delete = run_command(
        runner,
        ["config", "reserves", "delete", config_id, "--force"],
        allow_error=True,  # Allow errors (e.g., 404) if test already deleted config
    )

    if exit_code_delete != 0:
        # Log a warning, but don't fail the test run itself
        pass  # Silently pass


def login_as_admin(runner: CliRunner) -> None:
    """Login as the admin user for tests that need admin privileges."""
    run_command(
        runner,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
    )


def login_as_intermediate_user(runner: CliRunner) -> str:
    """Login as an INTERMEDIATE authority user for tests. Creates the user if it doesn't exist.

    Returns:
        str: The user ID of the intermediate user
    """
    # First login as admin to ensure we can create/find the intermediate user
    login_as_admin(runner)

    # Look for an existing intermediate user
    exit_code, stdout = run_command(
        runner, ["users", "list", "--authority-role", "INTERMEDIATE", "--output", "json"]
    )

    assert exit_code == 0, f"Failed to list users: {stdout}"
    users = validate_json_response(stdout)

    intermediate_user = None
    password = "testpass123"  # Define password variable outside the conditional blocks

    if users and isinstance(users, list) and len(users) > 0:
        # Use the first INTERMEDIATE authority user found
        intermediate_user = users[0]
        if intermediate_user["status"] != "ACTIVE":
            exit_code, stdout = run_command(
                runner,
                [
                    "users",
                    "update",
                    "--uuid",
                    intermediate_user["id"],
                    "--status",
                    "ACTIVE",
                ],
            )
            assert exit_code == 0, f"Failed to activate INTERMEDIATE user: {stdout}"
    else:
        # Create a new INTERMEDIATE user
        import datetime
        import random
        import string

        # Generate unique email
        timestamp = datetime.datetime.now().strftime("%H%M%S%f")
        random_str = "".join(random.choices(string.ascii_lowercase, k=5))
        email = f"intermediate_{random_str}_{timestamp}@example.com"

        exit_code, stdout = run_command(
            runner,
            [
                "users",
                "create",
                "--email",
                email,
                "--password",
                password,
                "--first-name",
                "Test",
                "--last-name",
                "Intermediate",
                "--role",
                "MANAGER",  # Manager role with Intermediate authority
                "--authority-role",
                "INTERMEDIATE",
                "--department",
                "Testing",
                "--output",
                "json",
            ],
        )

        assert exit_code == 0, f"Failed to create INTERMEDIATE user: {stdout}"

        # Try to parse the JSON response first
        try:
            intermediate_user = validate_json_response(stdout)
        except Exception as e:
            # If JSON parsing fails, try to extract the ID from the output text
            id_match = re.search(r"with ID ([0-9a-f-]+)", stdout)
            if id_match:
                user_id = id_match.group(1)
                # Get the full user details from the list command
                exit_code, user_stdout = run_command(runner, ["users", "list", "--output", "json"])
                assert exit_code == 0, f"Failed to list users: {user_stdout}"
                users = validate_json_response(user_stdout)

                # Find the user with the matching ID or email
                intermediate_user = next(
                    (u for u in users if u.get("id") == user_id or u.get("email") == email), None
                )
                assert (
                    intermediate_user is not None
                ), f"Could not find created user with ID {user_id} or email {email}"
            else:
                # Last resort: look up by email
                exit_code, user_stdout = run_command(runner, ["users", "list", "--output", "json"])
                assert exit_code == 0, f"Failed to list users: {user_stdout}"
                users = validate_json_response(user_stdout)

                # Find the user with the matching email
                intermediate_user = next((u for u in users if u.get("email") == email), None)
                assert (
                    intermediate_user is not None
                ), f"Could not find created user with email {email}"

        # Activate the newly created user
        exit_code, stdout = run_command(
            runner,
            [
                "users",
                "update",
                "--uuid",
                intermediate_user["id"],
                "--status",
                "ACTIVE",
            ],
        )

        assert exit_code == 0, f"Failed to activate new INTERMEDIATE user: {stdout}"

    # Now login as the intermediate user
    # For existing users from populated data, try the populated data password
    # For newly created users, use the password we just set
    if "intermediate_" in intermediate_user["email"]:
        # This is a newly created test user, use the password we set
        login_password = password  # Use the password variable from user creation
    else:
        # This is an existing user from populated data, use their password
        login_password = "password123"

    exit_code, stdout = run_command(
        runner,
        [
            "auth",
            "login",
            "--email",
            intermediate_user["email"],
            "--password",
            login_password,
        ],
    )

    assert exit_code == 0, f"Failed to login as INTERMEDIATE user: {stdout}"

    return intermediate_user["id"]
