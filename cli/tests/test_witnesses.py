#!/usr/bin/env python3
import json
from datetime import datetime

# Import the main app
from claimentine_cli.main import app

# Import helper functions from conftest
from .conftest import validate_json_response

# --- Witnesses Tests (Integration) ---


def test_witnesses_add(runner, auto_claim):
    """Test adding a witness to a claim via integration."""
    claim_id = auto_claim["id"]
    witness_name = f"Test Witness {datetime.now().isoformat()}"
    witness_email = "<EMAIL>"
    witness_phone = "+1234567890"
    witness_address = "123 Witness St, Test City"
    witness_statement = "I saw the incident happen."

    result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            witness_name,
            "--email",
            witness_email,
            "--phone",
            witness_phone,
            "--address",
            witness_address,
            "--statement",
            witness_statement,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result.exit_code == 0, f"witnesses add failed: {result.stdout}"
    witness_data = validate_json_response(result.stdout)

    assert witness_data.get("claim_id") == claim_id
    assert witness_data.get("name") == witness_name
    assert witness_data.get("email") == witness_email
    assert witness_data.get("phone") == witness_phone
    assert witness_data.get("address") == witness_address
    assert witness_data.get("statement") == witness_statement
    assert "id" in witness_data
    assert "created_at" in witness_data


def test_witnesses_list(runner, auto_claim):
    """Test listing witnesses for a claim via integration."""
    claim_id = auto_claim["id"]
    witness_name_1 = f"List Test Witness 1 {datetime.now().isoformat()}"
    witness_name_2 = f"List Test Witness 2 {datetime.now().isoformat()}"

    # Add a couple of witnesses first
    for name in [witness_name_1, witness_name_2]:
        add_result = runner.invoke(
            app,
            [
                "witnesses",
                "add",
                claim_id,
                "--name",
                name,
                "--email",
                "<EMAIL>",
                "--output",
                "json",
            ],
            catch_exceptions=False,
        )
        assert (
            add_result.exit_code == 0
        ), f"Failed to add witness for list test: {add_result.stdout}"
        # Validate the response
        added_witness = validate_json_response(add_result.stdout)
        assert (
            added_witness.get("name") == name
        ), f"Name mismatch for added witness: {added_witness.get('name')}"
        assert (
            added_witness.get("claim_id") == claim_id
        ), f"Claim ID mismatch for added witness: {added_witness.get('claim_id')}"

    # Test listing (JSON)
    list_result_json = runner.invoke(
        app,
        ["witnesses", "list", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert list_result_json.exit_code == 0, f"witnesses list json failed: {list_result_json.stdout}"
    witnesses_list = validate_json_response(list_result_json.stdout)
    assert isinstance(witnesses_list, list)
    # Check if at least the witnesses we added are present
    found_1 = any(witness.get("name") == witness_name_1 for witness in witnesses_list)
    found_2 = any(witness.get("name") == witness_name_2 for witness in witnesses_list)
    assert found_1 and found_2, "Did not find added witnesses in list output"
    for witness in witnesses_list:
        assert "id" in witness
        assert witness.get("claim_id") == claim_id


def test_witnesses_get(runner, auto_claim):
    """Test getting a specific witness via integration."""
    claim_id = auto_claim["id"]
    witness_name = f"Get Test Witness {datetime.now().isoformat()}"
    witness_email = "<EMAIL>"

    # Add a witness first
    add_result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            witness_name,
            "--email",
            witness_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add witness for get test: {add_result.stdout}"
    added_witness = validate_json_response(add_result.stdout)
    witness_id = added_witness["id"]

    # Test getting (JSON)
    get_result = runner.invoke(
        app,
        ["witnesses", "get", witness_id, claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert get_result.exit_code == 0, f"witnesses get json failed: {get_result.stdout}"
    witness_data = validate_json_response(get_result.stdout)
    assert witness_data.get("id") == witness_id
    assert witness_data.get("claim_id") == claim_id
    assert witness_data.get("name") == witness_name
    assert witness_data.get("email") == witness_email


def test_witnesses_update(runner, auto_claim):
    """Test updating a witness via integration."""
    claim_id = auto_claim["id"]
    original_name = f"Update Test Original {datetime.now().isoformat()}"
    updated_name = f"Update Test Updated {datetime.now().isoformat()}"
    original_email = "<EMAIL>"
    updated_email = "<EMAIL>"

    # Add a witness first
    add_result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            original_name,
            "--email",
            original_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add witness for update test: {add_result.stdout}"
    added_witness = validate_json_response(add_result.stdout)
    witness_id = added_witness["id"]

    # Update the witness
    update_result = runner.invoke(
        app,
        [
            "witnesses",
            "update",
            witness_id,
            claim_id,
            "--name",
            updated_name,
            "--email",
            updated_email,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert update_result.exit_code == 0, f"witnesses update failed: {update_result.stdout}"
    updated_witness = validate_json_response(update_result.stdout)
    assert updated_witness.get("id") == witness_id
    assert updated_witness.get("name") == updated_name
    assert updated_witness.get("email") == updated_email
    assert updated_witness.get("claim_id") == claim_id

    # Verify the update by getting the witness again
    get_result = runner.invoke(
        app,
        ["witnesses", "get", witness_id, claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert get_result.exit_code == 0
    verify_witness = validate_json_response(get_result.stdout)
    assert verify_witness.get("name") == updated_name
    assert verify_witness.get("email") == updated_email


def test_witnesses_delete(runner, auto_claim):
    """Test deleting a witness via integration."""
    claim_id = auto_claim["id"]
    witness_name = f"Delete Test Witness {datetime.now().isoformat()}"

    # Add a witness first
    add_result = runner.invoke(
        app,
        [
            "witnesses",
            "add",
            claim_id,
            "--name",
            witness_name,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add witness for delete test: {add_result.stdout}"
    added_witness = validate_json_response(add_result.stdout)
    witness_id = added_witness["id"]

    # Delete the witness (using --force)
    delete_result = runner.invoke(
        app,
        ["witnesses", "delete", witness_id, claim_id, "--force"],
        catch_exceptions=False,
    )
    assert delete_result.exit_code == 0, f"witnesses delete failed: {delete_result.stdout}"

    # Verify deletion by trying to get the witness (should fail)
    get_result = runner.invoke(
        app,
        ["witnesses", "get", witness_id, claim_id, "--output", "json"],
        catch_exceptions=True,  # Catch expected failure
    )
    # Expecting a non-zero exit code (e.g., 404 Not Found from API -> CLI error)
    assert get_result.exit_code != 0, "Getting a deleted witness should fail"
    # Check for an error message (adjust based on actual CLI error output)
    assert (
        "not found" in get_result.stdout.lower() or "error" in get_result.stdout.lower()
    ), f"Expected error message for deleted witness, got: {get_result.stdout}"


# --- End Witnesses Tests ---
