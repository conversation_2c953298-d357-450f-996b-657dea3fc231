#!/usr/bin/env python3
import json
import re
from datetime import datetime

from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_01_users_list(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    """Test listing users with optional filtering."""
    print("\nTesting Users List...")
    exit_code, stdout = run_command(runner, ["users", "list", "--output", "json"])
    assert exit_code == 0

    # Parse and validate JSON response
    users = validate_json_response(stdout)
    assert isinstance(users, list), "Expected a list of users"
    assert len(users) > 0, "Expected at least one user"

    # Check for required fields in first user
    first_user = users[0]
    assert "id" in first_user, "Expected 'id' field in user"
    assert "email" in first_user, "Expected 'email' field in user"
    assert "first_name" in first_user, "Expected 'first_name' field in user"
    assert "last_name" in first_user, "Expected 'last_name' field in user"
    assert "role" in first_user, "Expected 'role' field in user"
    assert "status" in first_user, "Expected 'status' field in user"

    # Check if we already have a manager user
    manager_user = next((user for user in users if user["role"] == "MANAGER"), None)

    # If no manager exists, create one
    if manager_user is None:
        print("  No manager user found, creating one...")
        timestamp = datetime.now().strftime("%H%M%S%f")
        manager_email = f"manager{timestamp}@example.com"
        create_exit_code, create_stdout = run_command(
            runner,
            [
                "users",
                "create",
                "--email",
                manager_email,
                "--password",
                "testpass123",
                "--first-name",
                "Test",
                "--last-name",
                "Manager",
                "--role",
                "MANAGER",
                "--authority-role",
                "MANAGER",
                "--department",
                "Claims",
            ],
        )
        assert create_exit_code == 0, f"Failed to create manager user: {create_stdout}"
        assert f"Created user {manager_email} with ID" in create_stdout
        print(f"  ✓ Created manager user {manager_email}")

        # Re-list users to get the newly created manager
        exit_code, stdout = run_command(runner, ["users", "list", "--output", "json"])
        assert exit_code == 0
        users = validate_json_response(stdout)

    # Now verify we have at least one manager in the list
    manager_user = next((user for user in users if user["role"] == "MANAGER"), None)
    assert manager_user is not None, "Expected to find at least one user with MANAGER role"
    assert (
        manager_user["authority_role"] == "MANAGER"
    ), "Expected manager to have correct authority role"

    print("✓ Users list command successful (with JSON validation)")


def test_01a_users_get(runner: CliRunner):
    """Test getting a specific user's details by ID."""
    print("\nTesting Users Get...")

    # First, get the admin user ID by filtering the list
    exit_code, stdout = run_command(
        runner, ["users", "list", "--role", "ADMIN", "--output", "json"]
    )
    assert exit_code == 0

    # Parse and validate JSON response to get an admin user ID
    users = validate_json_response(stdout)
    assert isinstance(users, list), "Expected a list of users"
    assert len(users) > 0, "Expected at least one admin user"

    admin_user = users[0]
    admin_id = admin_user["id"]
    admin_email = admin_user["email"]

    # Now test the get command with that ID
    exit_code, stdout = run_command(runner, ["users", "get", admin_id, "--output", "json"])
    assert exit_code == 0

    # Parse and validate the user details JSON response
    user_details = validate_json_response(stdout)
    assert isinstance(user_details, dict), "Expected a user object"

    # Verify the response contains the expected user
    assert user_details["id"] == admin_id, "User ID should match the requested ID"
    assert user_details["email"] == admin_email, "Email should match the expected admin email"
    assert user_details["role"] == "ADMIN", "Role should be ADMIN"

    # Check for required fields in the user details
    required_fields = [
        "first_name",
        "last_name",
        "status",
        "authority_role",
        "created_at",
        "updated_at",
    ]
    for field in required_fields:
        assert field in user_details, f"Expected '{field}' field in user details"

    # Check for permissions array
    assert "permissions" in user_details, "Expected permissions array in user details"
    assert isinstance(user_details["permissions"], list), "Permissions should be a list"
    assert len(user_details["permissions"]) > 0, "Admin should have permissions"

    print("✓ Users get command successful (with JSON validation)")


def test_03_user_create(runner: CliRunner):
    """Test creating a new user (manager)."""
    print("\nTesting User Create...")
    timestamp = datetime.now().strftime("%H%M%S%f")
    manager_email = f"manager{timestamp}@example.com"
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "create",
            "--email",
            manager_email,
            "--password",
            "testpass123",
            "--first-name",
            "Test",
            "--last-name",
            "Manager",
            "--role",
            "MANAGER",
            "--authority-role",
            "MANAGER",
            "--department",
            "Claims",
        ],
    )
    assert exit_code == 0
    assert f"Created user {manager_email} with ID" in stdout
    print("✓ User creation successful (with output check)")


def test_04_user_update(runner: CliRunner):
    """Test updating a user's attributes."""
    print("\nTesting User Update...")

    # First, get the user we created in test_03
    # Since test functions run in order, we can create a new user just for this test
    timestamp = datetime.now().strftime("%H%M%S%f")
    test_email = f"update_test{timestamp}@example.com"
    original_first_name = "UpdateTest"
    original_last_name = "Original"

    # Create the user first
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "create",
            "--email",
            test_email,
            "--password",
            "testpass123",
            "--first-name",
            original_first_name,
            "--last-name",
            original_last_name,
            "--role",
            "ADJUSTER",
            "--authority-role",
            "BASIC",
            "--department",
            "Testing",
        ],
    )
    assert exit_code == 0
    print(f"User creation output: {stdout}")

    # Extract the user ID from the creation output - account for possible newlines
    user_id_match = re.search(
        r"Created user .+ with ID\s*([0-9a-f-]{8}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{12})",
        stdout,
    )
    assert user_id_match, "Could not find user ID in the output"
    user_id = user_id_match.group(1)
    print(f"Extracted user ID: {user_id}")

    # First check the current user details
    exit_code, stdout = run_command(runner, ["users", "get", user_id, "--output", "json"])
    assert exit_code == 0
    initial_user = validate_json_response(stdout)
    print(f"Initial user details: {json.dumps(initial_user, indent=2)}")

    # Now test updating the user
    new_first_name = "UpdatedFirst"
    new_last_name = "UpdatedLast"
    new_department = "UpdatedDept"
    new_role = "MANAGER"
    new_authority_role = "MANAGER"

    # Execute the update command
    exit_code, update_stdout = run_command(
        runner,
        [
            "users",
            "update",
            user_id,
            "--uuid",  # Use UUID flag since we're using the ID
            "--first-name",
            new_first_name,
            "--last-name",
            new_last_name,
            "--department",
            new_department,
            "--role",
            new_role,
            "--authority-role",
            new_authority_role,
        ],
    )
    assert exit_code == 0
    print(f"Update command output: {update_stdout}")
    assert f"Updated user {test_email}" in update_stdout

    # Now verify the changes by getting the user details
    exit_code, get_stdout = run_command(runner, ["users", "get", user_id, "--output", "json"])
    assert exit_code == 0
    user_details = validate_json_response(get_stdout)
    print(f"Updated user details: {json.dumps(user_details, indent=2)}")

    # Print comparison of expected vs actual values for debugging
    print(f"Expected first_name: {new_first_name}, Actual: {user_details['first_name']}")
    print(f"Expected last_name: {new_last_name}, Actual: {user_details['last_name']}")
    print(f"Expected department: {new_department}, Actual: {user_details.get('department', 'N/A')}")
    print(f"Expected role: {new_role}, Actual: {user_details['role']}")
    print(
        f"Expected authority_role: {new_authority_role}, Actual: {user_details['authority_role']}"
    )

    # Verify that the updates were applied
    assert user_details["id"] == user_id, "User ID should match"
    assert user_details["email"] == test_email, "Email should not have changed"
    assert user_details["first_name"] == new_first_name, "First name should have been updated"
    assert user_details["last_name"] == new_last_name, "Last name should have been updated"
    assert user_details["department"] == new_department, "Department should have been updated"
    assert user_details["role"] == new_role, "Role should have been updated"
    assert (
        user_details["authority_role"] == new_authority_role
    ), "Authority role should have been updated"

    print("✓ User update command successful (with verification)")


def test_05_user_delete(runner: CliRunner):
    """Test deleting a user."""
    print("\nTesting User Delete...")

    # Create a temporary user specifically for deletion
    timestamp = datetime.now().strftime("%H%M%S%f")
    test_email = f"delete_test{timestamp}@example.com"
    first_name = "DeleteTest"
    last_name = "User"

    # Create the user first
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "create",
            "--email",
            test_email,
            "--password",
            "testpass123",
            "--first-name",
            first_name,
            "--last-name",
            last_name,
            "--role",
            "AGENT",
            "--authority-role",
            "BASIC",
            "--department",
            "TestDepartment",
        ],
    )
    assert exit_code == 0
    print(f"User creation output: {stdout}")

    # Extract the user ID from the creation output
    user_id_match = re.search(
        r"Created user .+ with ID\s*([0-9a-f-]{8}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{12})",
        stdout,
    )
    assert user_id_match, "Could not find user ID in the output"
    user_id = user_id_match.group(1)
    print(f"Extracted user ID: {user_id}")

    # Verify the user exists before deletion
    exit_code, stdout = run_command(runner, ["users", "get", user_id, "--output", "json"])
    assert exit_code == 0
    user_details = validate_json_response(stdout)
    assert user_details["email"] == test_email, "Created user email should match"

    # Execute the delete command with the --yes flag to bypass confirmation
    exit_code, delete_stdout = run_command(
        runner,
        [
            "users",
            "delete",
            user_id,
            "--uuid",  # Use UUID flag since we're using the ID
            "--yes",  # Bypass confirmation prompt
        ],
    )
    assert exit_code == 0
    print(f"Delete command output: {delete_stdout}")
    assert f"Successfully deleted user {test_email}" in delete_stdout

    # A different approach to verify deletion - check the users list to ensure
    # the deleted user is not in it
    exit_code, list_stdout = run_command(runner, ["users", "list", "--output", "json"])
    assert exit_code == 0
    users_list = validate_json_response(list_stdout)

    # Check that our deleted user ID is not in the list
    deleted_user_found = any(user["id"] == user_id for user in users_list)
    assert not deleted_user_found, "Deleted user should not appear in users list"

    print("✓ User delete command successful (with verification)")


def test_06_user_me(runner: CliRunner):
    """Test viewing current user details."""
    print("\nTesting Users Me (Get current user)...")

    # Get the user's profile
    exit_code, stdout = run_command(runner, ["users", "me", "--output", "json"])
    assert exit_code == 0, f"Command failed with code {exit_code}"

    # Validate the output is valid JSON and has the expected fields
    try:
        user_data = json.loads(stdout)
        # Validate basic user fields
        assert "id" in user_data, "Expected 'id' field in user data"
        assert "email" in user_data, "Expected 'email' field in user data"
        assert "first_name" in user_data, "Expected 'first_name' field in user data"
        assert "last_name" in user_data, "Expected 'last_name' field in user data"
        assert "role" in user_data, "Expected 'role' field in user data"
        assert "permissions" in user_data, "Expected 'permissions' field in user data"
        assert isinstance(user_data["permissions"], list), "Expected 'permissions' to be a list"
    except json.JSONDecodeError:
        assert False, "Output is not valid JSON"

    print("✓ Users me command successful (with JSON validation)")


def test_06a_user_me_update(runner: CliRunner):
    """Test updating the current user's details."""
    print("\nTesting 'User Me Update'...")

    # Update the current user's profile
    timestamp = datetime.now().strftime("%H%M%S%f")
    new_first_name = f"TestFirst{timestamp}"
    new_last_name = f"TestLast{timestamp}"
    new_department = f"TestDept{timestamp}"

    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "me",
            "update",
            "--first-name",
            new_first_name,
            "--last-name",
            new_last_name,
            "--department",
            new_department,
            "--output",
            "json",
        ],
    )
    assert exit_code == 0
    user_details = validate_json_response(stdout)

    # Verify the changes were applied
    assert user_details["first_name"] == new_first_name, "First name was not updated correctly"
    assert user_details["last_name"] == new_last_name, "Last name was not updated correctly"
    assert user_details["department"] == new_department, "Department was not updated correctly"

    print("✓ User me update successful (with JSON validation)")


def test_07_user_change_password(runner: CliRunner):
    """Test changing a user's password."""
    print("\nTesting 'User Change Password'...")

    # First, create a dedicated test user for this test
    timestamp = datetime.now().strftime("%H%M%S%f")
    test_email = f"password_test_{timestamp}@example.com"
    test_password = "TestPass123!"

    # Create the test user
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "create",
            "--email",
            test_email,
            "--password",
            test_password,
            "--first-name",
            "Password",
            "--last-name",
            "Test",
            "--role",
            "ADJUSTER",
            "--authority-role",
            "BASIC",
            "--output",
            "json",
        ],
    )
    assert exit_code == 0, f"Failed to create test user: {stdout}"
    user_data = validate_json_response(stdout)
    user_id = user_data["id"]
    print(f"  Created test user with ID {user_id}")

    # Activate the user (needed before login)
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "update",
            user_id,
            "--uuid",
            "--status",
            "ACTIVE",
        ],
    )
    assert exit_code == 0, f"Failed to activate test user: {stdout}"
    print(f"  Activated test user")

    # Need to login as this user to change their password
    exit_code, stdout = run_command(
        runner,
        [
            "auth",
            "login",
            "--email",
            test_email,
            "--password",
            test_password,
        ],
    )
    assert exit_code == 0, "Failed to login as test user"
    print(f"  Logged in as test user")

    # Test changing password with JSON output
    new_password = "NewTestPass456!"
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "change-password",
            "--current-password",
            test_password,
            "--new-password",
            new_password,
            "--output",
            "json",
        ],
    )

    # Verify the call was successful
    assert exit_code == 0, f"Password change command failed with: {stdout}"

    # Validate JSON response structure
    response_data = validate_json_response(stdout)
    assert "id" in response_data, "Response should include user ID"
    assert "email" in response_data, "Response should include user email"
    assert "permissions" in response_data, "Response should include permissions"

    # Verify response doesn't contain any password data
    assert "password" not in response_data, "Response should not include password field"
    assert "password_hash" not in response_data, "Response should not include password_hash field"

    # Test that we can login with the new password
    exit_code, stdout = run_command(
        runner,
        [
            "auth",
            "login",
            "--email",
            test_email,
            "--password",
            new_password,
        ],
    )
    assert exit_code == 0, "Failed to login with new password"

    # Test the standard text output (not JSON)
    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "change-password",
            "--current-password",
            new_password,
            "--new-password",
            test_password,
        ],
    )

    assert exit_code == 0, f"Password change command failed with: {stdout}"
    assert "Password changed successfully" in stdout, "Success message not found in output"

    # Clean up - login as admin again and delete the test user
    exit_code, stdout = run_command(
        runner,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
    )
    assert exit_code == 0, "Failed to login as admin for cleanup"

    exit_code, stdout = run_command(
        runner,
        [
            "users",
            "delete",
            user_id,
            "--uuid",
            "--yes",
        ],
    )
    assert exit_code == 0, f"Failed to delete test user: {stdout}"
    print(f"  Cleaned up test user")

    print("✓ Password change successful (with validation)")
