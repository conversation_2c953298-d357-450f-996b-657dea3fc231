#!/usr/bin/env python3
import json  # Needed for json.loads/dumps if used directly
from datetime import datetime

# Import the main app
from claimentine_cli.main import app

# Import helper functions if needed directly, or rely on fixtures that use them
from .conftest import validate_json_response

# Import necessary fixtures from conftest.py (they are automatically available)
# runner, auth_setup, customer, auto_claim


# Helper function to validate JSON (no longer needed, imported from conftest)
# import re
# def validate_json_response(stdout: str) -> dict | list:
#    ...

# --- Notes Tests (Integration) ---


def test_notes_add(runner, auto_claim):
    """Test adding a note to a claim via integration."""
    claim_id = auto_claim["id"]
    note_content = f"Integration test note added at {datetime.now().isoformat()}"

    result = runner.invoke(
        app,
        [
            "notes",
            "add",
            "--claim-identifier",
            claim_id,
            "--content",
            note_content,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )

    assert result.exit_code == 0, f"notes add failed: {result.stdout}"
    note_data = validate_json_response(result.stdout)

    assert note_data.get("claim_id") == claim_id
    assert note_data.get("content") == note_content
    assert "id" in note_data
    assert "created_at" in note_data
    assert "author_id" in note_data  # Assuming author comes from logged-in user context


def test_notes_list(runner, auto_claim):
    """Test listing notes for a claim via integration."""
    claim_id = auto_claim["id"]
    note_content_1 = f"List test note 1 {datetime.now().isoformat()}"
    note_content_2 = f"List test note 2 {datetime.now().isoformat()}"

    # Add a couple of notes first
    for content in [note_content_1, note_content_2]:
        add_result = runner.invoke(
            app,
            [
                "notes",
                "add",
                "--claim-identifier",
                claim_id,
                "--content",
                content,
                "--output",  # Add output flag
                "json",
            ],
            catch_exceptions=False,
        )
        assert add_result.exit_code == 0, f"Failed to add note for list test: {add_result.stdout}"
        # Validate the response
        added_note = validate_json_response(add_result.stdout)
        assert (
            added_note.get("content") == content
        ), f"Content mismatch for added note: {added_note.get('content')}"
        assert (
            added_note.get("claim_id") == claim_id
        ), f"Claim ID mismatch for added note: {added_note.get('claim_id')}"

    # Test listing (JSON)
    list_result_json = runner.invoke(
        app,
        ["notes", "list", "--claim-id", claim_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert list_result_json.exit_code == 0, f"notes list json failed: {list_result_json.stdout}"
    notes_list_data = validate_json_response(list_result_json.stdout)
    assert isinstance(notes_list_data, list)
    # Check if at least the notes we added are present
    found_1 = any(note.get("content") == note_content_1 for note in notes_list_data)
    found_2 = any(note.get("content") == note_content_2 for note in notes_list_data)
    assert found_1 and found_2, "Did not find added notes in list output"
    for note in notes_list_data:
        assert "id" in note
        assert note.get("claim_id") == claim_id


def test_notes_view(runner, auto_claim):
    """Test viewing a specific note via integration."""
    claim_id = auto_claim["id"]
    note_content = f"View test note {datetime.now().isoformat()}"

    # Add a note first
    add_result = runner.invoke(
        app,
        [
            "notes",
            "add",
            "--claim-identifier",
            claim_id,
            "--content",
            note_content,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add note for view test: {add_result.stdout}"
    added_note_data = validate_json_response(add_result.stdout)
    note_id = added_note_data["id"]

    # Test viewing (JSON)
    view_result_json = runner.invoke(
        app,
        ["notes", "view", note_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert view_result_json.exit_code == 0, f"notes view json failed: {view_result_json.stdout}"
    viewed_note_data = validate_json_response(view_result_json.stdout)
    assert viewed_note_data.get("id") == note_id
    assert viewed_note_data.get("claim_id") == claim_id
    assert viewed_note_data.get("content") == note_content


def test_notes_edit(runner, auto_claim):
    """Test editing a note via integration."""
    claim_id = auto_claim["id"]
    original_content = f"Edit test original {datetime.now().isoformat()}"
    updated_content = f"Edit test updated {datetime.now().isoformat()}"

    # Add a note first
    add_result = runner.invoke(
        app,
        [
            "notes",
            "add",
            "--claim-identifier",
            claim_id,
            "--content",
            original_content,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add note for edit test: {add_result.stdout}"
    added_note_data = validate_json_response(add_result.stdout)
    note_id = added_note_data["id"]

    # Edit the note
    edit_result = runner.invoke(
        app,
        [
            "notes",
            "edit",
            note_id,
            "--content",
            updated_content,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert edit_result.exit_code == 0, f"notes edit failed: {edit_result.stdout}"
    edited_note_data = validate_json_response(edit_result.stdout)
    assert edited_note_data.get("id") == note_id
    assert edited_note_data.get("content") == updated_content
    assert edited_note_data.get("claim_id") == claim_id  # Ensure claim_id remains the same

    # Verify the edit by viewing the note again
    view_result = runner.invoke(
        app,
        ["notes", "view", note_id, "--output", "json"],
        catch_exceptions=False,
    )
    assert view_result.exit_code == 0
    verify_note_data = validate_json_response(view_result.stdout)
    assert verify_note_data.get("content") == updated_content


def test_notes_delete(runner, auto_claim):
    """Test deleting a note via integration."""
    claim_id = auto_claim["id"]
    note_content = f"Delete test note {datetime.now().isoformat()}"

    # Add a note first
    add_result = runner.invoke(
        app,
        [
            "notes",
            "add",
            "--claim-identifier",
            claim_id,
            "--content",
            note_content,
            "--output",
            "json",
        ],
        catch_exceptions=False,
    )
    assert add_result.exit_code == 0, f"Failed to add note for delete test: {add_result.stdout}"
    added_note_data = validate_json_response(add_result.stdout)
    note_id = added_note_data["id"]

    # Delete the note (using --force)
    delete_result = runner.invoke(
        app,
        ["notes", "delete", note_id, "--force", "--output", "json"],
        catch_exceptions=False,
    )
    assert delete_result.exit_code == 0, f"notes delete failed: {delete_result.stdout}"

    # Check JSON output of delete command
    delete_data = validate_json_response(delete_result.stdout)
    assert delete_data.get("success") is True
    assert delete_data.get("id") == note_id

    # Verify deletion by trying to view the note (should fail)
    view_result = runner.invoke(
        app,
        ["notes", "view", note_id, "--output", "json"],
        catch_exceptions=False,  # Catch expected failure
    )
    # Expecting a non-zero exit code (e.g., 404 Not Found from API -> CLI error)
    assert view_result.exit_code != 0, "Viewing a deleted note should fail"
    # Check for an error message (adjust based on actual CLI error output)
    assert (
        "not found" in view_result.stdout.lower()
    ), f"Expected 'not found' in error message, got: {view_result.stdout}"


# --- End Notes Tests ---
