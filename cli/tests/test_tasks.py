"""Tests for the CLI task commands."""

import re  # Need re for parsing output
import uuid

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Assuming necessary fixtures and helpers are in conftest.py
from .conftest import run_command, validate_json_response

# --- Test Data --- #
TEST_TASK_TITLE = "Test Task via CLI"
TEST_TASK_DESC = "This is a description added during CLI testing."


# --- Helper to get an adjuster ID (adjust implementation as needed) ---
# This assumes 'users list' command works and returns at least one adjuster
def _get_adjuster_user_id(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>) -> str:
    """Gets an Adjuster user ID. Creates one if none are found."""
    print("  Attempting to find existing Adjuster user...")
    list_args = ["users", "list", "--role", "ADJUSTER", "--output", "json"]
    exit_code, stdout = run_command(runner, list_args)

    if exit_code == 0:
        try:
            users = validate_json_response(stdout)
            if users and isinstance(users, list) and users[0].get("id"):
                user_id = users[0]["id"]
                print(f"  Found existing Adjuster user: {user_id}")
                return user_id
            else:
                print("  No suitable Adjuster found in list output.")
        except Exception as e:
            print(f"  Error parsing user list output: {e}. Proceeding to create.")
            # Fall through to create if parsing fails
    else:
        print(f"  Listing Adjuster users failed (exit code {exit_code}). Proceeding to create.")
        # Fall through to create if listing fails

    # If no suitable adjuster found, create one
    print("  Creating a new Adjuster user for testing...")
    timestamp = uuid.uuid4()  # Use UUID for uniqueness
    adjuster_email = f"adjuster-{timestamp}@example.com"
    create_args = [
        "users",
        "create",
        "--email",
        adjuster_email,
        "--password",
        "testpass123",  # Standard test password
        "--first-name",
        "Test",
        "--last-name",
        "Adjuster",
        "--role",
        "ADJUSTER",
        "--authority-role",
        "BASIC",  # Default authority for adjuster
        "--department",
        "Testing",
    ]
    create_exit_code, create_stdout = run_command(runner, create_args)

    if create_exit_code != 0:
        pytest.fail(f"Failed to create Adjuster user: {create_stdout}")

    # Extract ID from output (similar to test_users.py)
    user_id_match = re.search(
        r"Created user .+ with ID\s*([0-9a-f-]{8}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{4}-[0-9a-f-]{12})",
        create_stdout,
    )
    if not user_id_match:
        # Fallback: Try getting ID via list again
        print("  Could not parse ID from create output, trying list again...")
        exit_code_retry, stdout_retry = run_command(runner, list_args)
        if exit_code_retry == 0:
            users_retry = validate_json_response(stdout_retry)
            if users_retry and isinstance(users_retry, list):
                found = next((u for u in users_retry if u["email"] == adjuster_email), None)
                if found and found.get("id"):
                    user_id = found["id"]
                    print(f"  Found ID via re-list: {user_id}")
                    return user_id
        pytest.fail(f"Could not determine ID for newly created Adjuster: {adjuster_email}")

    user_id = user_id_match.group(1)
    print(f"  ✓ Created Adjuster {adjuster_email} with ID: {user_id}")
    return user_id


# --- Test Functions --- #


def test_task_create(runner: CliRunner, auto_claim: dict):
    """Test creating a task using 'clm tasks create'"""
    print("\nTesting Task Creation...")
    claim_id = auto_claim["id"]
    # Extract prefix from claim number as fallback
    try:
        customer_prefix = auto_claim["number"].split("-")[0]
    except:  # noqa
        customer_prefix = "CUST"  # Fallback if number format is unexpected

    # 1. Basic create command (Text Output)
    print("  Testing basic create (text output)...")
    args_text = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        TEST_TASK_TITLE + " Text",
        "--description",
        TEST_TASK_DESC,
        "--priority",
        "HIGH",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_text)
    assert exit_code == 0, f"Failed to create task (text): {stdout}"
    task_data = validate_json_response(stdout)
    assert task_data["title"] == TEST_TASK_TITLE + " Text"
    assert task_data["description"] == TEST_TASK_DESC
    assert task_data["priority"] == "HIGH"
    assert task_data["hr_id"].startswith(f"T-{customer_prefix}-")
    print("  ✓ Basic create (json output) successful")

    # 2. Create with more options and JSON Output
    print("  Testing create with JSON output...")
    title_json = TEST_TASK_TITLE + " JSON"
    args_json = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        title_json,
        "--priority",
        "LOW",
        "--status",
        "IN_PROGRESS",
        "--output",
        "json",
    ]
    exit_code_json, stdout_json = run_command(runner, args_json)
    assert exit_code_json == 0, f"Failed to create task (json): {stdout_json}"

    task_data = validate_json_response(stdout_json)
    assert isinstance(task_data, dict), "Expected JSON object for created task"
    assert task_data["title"] == title_json
    assert task_data["priority"] == "LOW"
    assert task_data["status"] == "IN_PROGRESS"
    assert task_data["claim_id"] == str(claim_id)
    assert "id" in task_data
    assert "hr_id" in task_data
    assert task_data["hr_id"].startswith(f"T-{customer_prefix}-")
    # Save details for potential use in other tests (if needed, though aiming for self-contained)
    # created_task_id = task_data["id"]
    # created_task_hr_id = task_data["hr_id"]
    print("  ✓ Create with JSON output successful")

    # 3. Test create with assignee (requires another user)
    print("  Testing create with assignee...")
    try:
        assignee_id = _get_adjuster_user_id(runner)
        print(f"  Found adjuster user ID for assignment test: {assignee_id}")
        title_assign = TEST_TASK_TITLE + " Assigned"
        args_assign = [
            "tasks",
            "create",
            "--claim-identifier",
            str(claim_id),
            "--title",
            title_assign,
            "--assignee",
            str(assignee_id),
            "--output",
            "json",
        ]
        exit_code_assign, stdout_assign = run_command(runner, args_assign)
        assert exit_code_assign == 0, f"Failed to create assigned task: {stdout_assign}"

        assigned_task_data = validate_json_response(stdout_assign)
        assert assigned_task_data["title"] == title_assign
        assert assigned_task_data["assignee"] is not None
        assert assigned_task_data["assignee"]["id"] == str(assignee_id)
        print("  ✓ Create with assignee successful")
    except Exception as e:
        print(f"  [SKIP] Skipping assignee test: Could not get assignee ID. Reason: {e}")
        pytest.skip("Skipping task creation with assignee test - could not get assignee ID.")


# --- Placeholder for other task tests --- #


def test_task_list(runner: CliRunner, auto_claim: dict):
    """Test listing tasks using 'clm tasks list'"""
    print("\nTesting Task List...")
    claim_id = auto_claim["id"]
    title_prefix = f"List Test Task {uuid.uuid4()}"

    # 1. Create a couple of tasks to list
    created_task_ids = []
    task_titles = {f"{title_prefix} {i}" for i in range(3)}
    for i, title in enumerate(task_titles):
        print(f"  Creating task {i+1}/3 for list test...")
        args_create = [
            "tasks",
            "create",
            "--claim-identifier",
            str(claim_id),
            "--title",
            title,
            "--priority",
            "MEDIUM" if i % 2 == 0 else "LOW",
            "--status",
            "PENDING" if i < 2 else "IN_PROGRESS",
            "--output",
            "json",
        ]
        exit_code, stdout = run_command(runner, args_create)
        assert exit_code == 0, f"Failed to create task for list test: {stdout}"
        task_data = validate_json_response(stdout)
        created_task_ids.append(task_data["id"])
    print(f"  ✓ Created {len(created_task_ids)} tasks for listing.")

    # 2. List tasks for the specific claim (JSON)
    print("  Testing list by claim ID...")
    args_list_claim = ["tasks", "list", "--claim-id", str(claim_id), "--output", "json"]
    exit_code, stdout = run_command(runner, args_list_claim)
    assert exit_code == 0, f"Failed to list tasks by claim: {stdout}"
    response_data = validate_json_response(stdout)

    # Handle paginated response format
    if isinstance(response_data, dict) and "items" in response_data:
        tasks_data = response_data["items"]
        total = response_data.get("total", 0)
        assert isinstance(tasks_data, list), "Expected 'items' to be a list in paginated response"
        assert total >= len(
            created_task_ids
        ), f"Expected total >= {len(created_task_ids)}, got {total}"
    else:
        # Fallback for old format (shouldn't happen anymore)
        tasks_data = response_data if isinstance(response_data, list) else []

    # Verify our created tasks are in the list (match by title prefix)
    found_titles = {t["title"] for t in tasks_data if t["title"].startswith(title_prefix)}
    assert (
        found_titles == task_titles
    ), f"Expected {task_titles}, but found {found_titles} in list output"
    print("  ✓ List by claim ID successful.")

    # 3. List tasks filtering by status (PENDING)
    print("  Testing list filter by status=PENDING...")
    args_list_status = [
        "tasks",
        "list",
        "--claim-id",
        str(claim_id),  # Keep claim filter for relevance
        "--status",
        "PENDING",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_list_status)
    assert exit_code == 0, f"Failed to list tasks by status: {stdout}"
    response_data_pending = validate_json_response(stdout)

    # Handle paginated response format
    if isinstance(response_data_pending, dict) and "items" in response_data_pending:
        tasks_data_pending = response_data_pending["items"]
    else:
        # Fallback for old format (shouldn't happen anymore)
        tasks_data_pending = (
            response_data_pending if isinstance(response_data_pending, list) else []
        )

    assert isinstance(tasks_data_pending, list)
    assert len(tasks_data_pending) >= 2, "Expected at least 2 PENDING tasks"
    for task in tasks_data_pending:
        if task["title"].startswith(title_prefix):
            assert task["status"] == "PENDING"
    print("  ✓ List filter by status=PENDING successful.")

    # 4. Test listing with text output (basic check)
    print("  Testing list with text output...")
    args_list_text = ["tasks", "list", "--claim-id", str(claim_id), "--output", "json"]
    exit_code, stdout = run_command(runner, args_list_text)
    assert exit_code == 0, f"Failed to list tasks: {stdout}"
    # Validate JSON response instead of checking text output format
    response_data_text = validate_json_response(stdout)

    # Handle paginated response format
    if isinstance(response_data_text, dict) and "items" in response_data_text:
        list_data = response_data_text["items"]
    else:
        # Fallback for old format (shouldn't happen anymore)
        list_data = response_data_text if isinstance(response_data_text, list) else []

    assert isinstance(list_data, list), "Expected list in JSON response"
    assert len(list_data) >= 3, f"Expected at least 3 tasks, got {len(list_data)}"
    # Check if all HR_IDs follow the correct pattern
    customer_prefix = auto_claim["number"].split("-")[0]
    for task in list_data:
        assert task["hr_id"].startswith(
            f"T-{customer_prefix}-"
        ), f"Invalid HR_ID format: {task['hr_id']}"
    print("  ✓ List verification with JSON output successful.")

    # 5. Test filtering by title
    print("  Testing list filter by title...")
    # Use a unique part of the title_prefix to filter
    search_term = title_prefix.split()[-1]  # Get the UUID part of the title
    args_list_title = [
        "tasks",
        "list",
        "--claim-id",
        str(claim_id),
        "--title",
        search_term,
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_list_title)
    assert exit_code == 0, f"Failed to list tasks by title: {stdout}"
    response_data_title = validate_json_response(stdout)

    # Handle paginated response format
    if isinstance(response_data_title, dict) and "items" in response_data_title:
        tasks_by_title = response_data_title["items"]
    else:
        # Fallback for old format (shouldn't happen anymore)
        tasks_by_title = response_data_title if isinstance(response_data_title, list) else []

    assert isinstance(tasks_by_title, list)
    assert (
        len(tasks_by_title) == 3
    ), f"Expected exactly 3 tasks matching title filter, got {len(tasks_by_title)}"
    for task in tasks_by_title:
        assert (
            search_term in task["title"]
        ), f"Task title '{task['title']}' doesn't contain search term '{search_term}'"
    print("  ✓ List filter by title successful.")


def test_task_view(runner: CliRunner, auto_claim: dict):
    """Test viewing a task using 'clm tasks view' by ID and hr_id"""
    print("\nTesting Task View...")
    claim_id = auto_claim["id"]
    title = f"View Test Task {uuid.uuid4()}"

    # 1. Create a task to view
    print("  Creating task for view test...")
    args_create = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        title,
        "--description",
        "View Test Description",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_create)
    assert exit_code == 0, f"Failed to create task for view test: {stdout}"
    task_data = validate_json_response(stdout)
    task_id = task_data["id"]
    task_hr_id = task_data["hr_id"]
    print(f"  ✓ Created task {task_hr_id} ({task_id}) for viewing.")

    # 2. View task by UUID (JSON)
    print(f"  Testing view by UUID ({task_id})...")
    args_view_uuid = ["tasks", "view", task_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view_uuid)
    assert exit_code == 0, f"Failed to view task by UUID: {stdout}"
    view_data_uuid = validate_json_response(stdout)
    assert view_data_uuid["id"] == task_id
    assert view_data_uuid["hr_id"] == task_hr_id
    assert view_data_uuid["title"] == title
    assert view_data_uuid["description"] == "View Test Description"
    print("  ✓ View by UUID successful.")

    # 3. View task by HR_ID (JSON)
    print(f"  Testing view by HR_ID ({task_hr_id})...")
    args_view_hrid = ["tasks", "view", task_hr_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view_hrid)
    assert exit_code == 0, f"Failed to view task by HR_ID: {stdout}"
    view_data_hrid = validate_json_response(stdout)
    assert view_data_hrid["id"] == task_id
    assert view_data_hrid["hr_id"] == task_hr_id
    assert view_data_hrid["title"] == title
    print("  ✓ View by HR_ID successful.")

    # 4. View task with text output (basic check)
    print("  Testing view with text output...")
    args_view_text = ["tasks", "view", task_hr_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view_text)
    assert exit_code == 0, f"Failed to view task (text): {stdout}"
    view_data = validate_json_response(stdout)
    assert view_data["hr_id"] == task_hr_id
    assert view_data["title"] == title
    assert view_data["description"] == "View Test Description"
    print("  ✓ View with json output successful.")

    # 5. Test viewing non-existent task
    print("  Testing view for non-existent task...")
    non_existent_id = str(uuid.uuid4())
    args_view_bad = ["tasks", "view", non_existent_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view_bad, allow_error=True)
    assert exit_code != 0, "Expected view non-existent task to fail"
    # Still expect an error message, which might not be valid JSON
    assert "not found" in stdout.lower()
    print("  ✓ View non-existent task failed as expected.")


def test_task_update(runner: CliRunner, auto_claim: dict):
    """Test updating a task using 'clm tasks update'"""
    print("\nTesting Task Update...")
    claim_id = auto_claim["id"]
    original_title = f"Update Original Title {uuid.uuid4()}"
    updated_title = f"Update New Title {uuid.uuid4()}"
    updated_desc = "Updated description."

    # 1. Create a task to update
    print("  Creating task for update test...")
    args_create = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        original_title,
        "--priority",
        "MEDIUM",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_create)
    assert exit_code == 0, f"Failed to create task for update test: {stdout}"
    task_data = validate_json_response(stdout)
    task_id = task_data["id"]
    task_hr_id = task_data["hr_id"]
    print(f"  ✓ Created task {task_hr_id} ({task_id}) for updating.")

    # 2. Update the task using HR_ID
    print(f"  Updating task {task_hr_id}...")
    args_update = [
        "tasks",
        "update",
        task_hr_id,
        "--title",
        updated_title,
        "--description",
        updated_desc,
        "--priority",
        "URGENT",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_update)
    assert exit_code == 0, f"Failed to update task: {stdout}"
    update_data = validate_json_response(stdout)
    assert update_data["id"] == task_id
    assert update_data["hr_id"] == task_hr_id
    assert update_data["title"] == updated_title
    assert update_data["description"] == updated_desc
    assert update_data["priority"] == "URGENT"
    print("  ✓ Task update successful.")

    # 3. Verify update by viewing using UUID
    print(f"  Verifying update for task {task_id}...")
    args_view = ["tasks", "view", task_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view)
    assert exit_code == 0, f"Failed to view task after update: {stdout}"
    verify_data = validate_json_response(stdout)
    assert verify_data["title"] == updated_title
    assert verify_data["description"] == updated_desc
    assert verify_data["priority"] == "URGENT"
    print("  ✓ Update verification successful.")


def test_task_assign(runner: CliRunner, auto_claim: dict):
    """Test assigning and unassigning a task using 'clm tasks assign'"""
    print("\nTesting Task Assign...")
    claim_id = auto_claim["id"]
    title = f"Assign Test Task {uuid.uuid4()}"

    # 1. Get an assignee ID (skip test if none found)
    print("  Getting assignee (Adjuster) ID...")
    try:
        assignee_id = _get_adjuster_user_id(runner)
        print(f"  Using Adjuster ID for assignment: {assignee_id}")
    except Exception as e:
        print(f"  [SKIP] Skipping assign test: Could not get assignee ID. Reason: {e}")
        pytest.skip("Skipping task assign test - could not get assignee ID.")
        return  # Ensure function exits if skipped

    # 2. Create a task to assign
    print("  Creating task for assign test...")
    args_create = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        title,
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_create)
    assert exit_code == 0, f"Failed to create task for assign test: {stdout}"
    task_data = validate_json_response(stdout)
    task_id = task_data["id"]
    task_hr_id = task_data["hr_id"]
    assert task_data.get("assignee") is None, "Task should initially be unassigned"
    print(f"  ✓ Created task {task_hr_id} ({task_id}) for assigning.")

    # 3. Assign the task using HR_ID
    print(f"  Assigning task {task_hr_id} to {assignee_id}...")
    args_assign = [
        "tasks",
        "assign",
        task_hr_id,
        "--assignee",
        str(assignee_id),
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_assign)
    assert exit_code == 0, f"Failed to assign task: {stdout}"
    assign_data = validate_json_response(stdout)
    assert assign_data["assignee"] is not None
    assert assign_data["assignee"]["id"] == str(assignee_id)
    print("  ✓ Assign task successful.")

    # 4. Verify assignment by viewing using UUID
    print(f"  Verifying assignment for task {task_id}...")
    args_view = ["tasks", "view", task_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view)
    assert exit_code == 0, f"Failed to view task after assignment: {stdout}"
    verify_data = validate_json_response(stdout)
    assert verify_data["assignee"] is not None
    assert verify_data["assignee"]["id"] == str(assignee_id)
    print("  ✓ Assignment verification successful.")

    # 5. Unassign the task using UUID and 'none'
    print(f"  Unassigning task {task_id}...")
    args_unassign = [
        "tasks",
        "assign",
        task_id,
        "--assignee",
        "none",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_unassign)
    assert exit_code == 0, f"Failed to unassign task: {stdout}"
    unassign_data = validate_json_response(stdout)
    assert unassign_data["assignee"] is None
    print("  ✓ Unassign task successful.")

    # 6. Verify unassignment by viewing using HR_ID
    print(f"  Verifying unassignment for task {task_hr_id}...")
    args_view_unassigned = ["tasks", "view", task_hr_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view_unassigned)
    assert exit_code == 0, f"Failed to view task after unassignment: {stdout}"
    verify_unassigned_data = validate_json_response(stdout)
    assert verify_unassigned_data["assignee"] is None
    print("  ✓ Unassignment verification successful.")


def test_task_status(runner: CliRunner, auto_claim: dict):
    """Test changing task status using 'clm tasks status'"""
    print("\nTesting Task Status Change...")
    claim_id = auto_claim["id"]
    title = f"Status Test Task {uuid.uuid4()}"

    # 1. Create a task (defaults to PENDING)
    print("  Creating task for status test...")
    args_create = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        title,
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_create)
    assert exit_code == 0, f"Failed to create task for status test: {stdout}"
    task_data = validate_json_response(stdout)
    task_id = task_data["id"]
    task_hr_id = task_data["hr_id"]
    assert task_data["status"] == "PENDING", "Task should initially be PENDING"
    assert task_data.get("completed_at") is None
    print(f"  ✓ Created task {task_hr_id} ({task_id}) in PENDING state.")

    # 2. Change status to IN_PROGRESS using HR_ID
    print(f"  Changing status of {task_hr_id} to IN_PROGRESS...")
    args_status_progress = [
        "tasks",
        "status",
        task_hr_id,
        "--status",
        "IN_PROGRESS",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_status_progress)
    assert exit_code == 0, f"Failed to change status to IN_PROGRESS: {stdout}"
    status_data_progress = validate_json_response(stdout)
    assert status_data_progress["status"] == "IN_PROGRESS"
    assert status_data_progress.get("completed_at") is None
    print("  ✓ Status change to IN_PROGRESS successful.")

    # 3. Change status to COMPLETED using UUID
    print(f"  Changing status of {task_id} to COMPLETED...")
    args_status_completed = [
        "tasks",
        "status",
        task_id,
        "--status",
        "COMPLETED",
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_status_completed)
    assert exit_code == 0, f"Failed to change status to COMPLETED: {stdout}"
    status_data_completed = validate_json_response(stdout)
    assert status_data_completed["status"] == "COMPLETED"
    assert status_data_completed.get("completed_at") is not None, "completed_at should be set"
    print("  ✓ Status change to COMPLETED successful (completed_at set).")

    # 4. Verify status change by viewing
    print(f"  Verifying final status for {task_hr_id}...")
    args_view = ["tasks", "view", task_hr_id, "--output", "json"]
    exit_code, stdout = run_command(runner, args_view)
    assert exit_code == 0, f"Failed to view task after status change: {stdout}"
    verify_data = validate_json_response(stdout)
    assert verify_data["status"] == "COMPLETED"
    assert verify_data.get("completed_at") is not None
    print("  ✓ Status verification successful.")


def test_task_delete(runner: CliRunner, auto_claim: dict):
    """Test deleting a task using 'clm tasks delete'"""
    print("\nTesting Task Delete...")
    claim_id = auto_claim["id"]
    title = f"Delete Test Task {uuid.uuid4()}"

    # 1. Create a task to delete
    print("  Creating task for delete test...")
    args_create = [
        "tasks",
        "create",
        "--claim-identifier",
        str(claim_id),
        "--title",
        title,
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_create)
    assert exit_code == 0, f"Failed to create task for delete test: {stdout}"
    task_data = validate_json_response(stdout)
    task_id = task_data["id"]
    task_hr_id = task_data["hr_id"]
    print(f"  ✓ Created task {task_hr_id} ({task_id}) for deleting.")

    # 2. Delete the task using HR_ID with force
    print(f"  Deleting task {task_hr_id}...")
    args_delete = [
        "tasks",
        "delete",
        task_hr_id,
        "--force",  # Avoid interactive prompt
        "--output",
        "json",
    ]
    exit_code, stdout = run_command(runner, args_delete)
    assert exit_code == 0, f"Failed to delete task: {stdout}"
    delete_data = validate_json_response(stdout)
    assert delete_data.get("success") is True
    assert delete_data.get("deleted_task", {}).get("id") == task_id
    assert delete_data.get("deleted_task", {}).get("hr_id") == task_hr_id
    print("  ✓ Task delete successful.")

    # 3. Verify deletion by trying to view using UUID
    print(f"  Verifying deletion by attempting to view {task_id}...")
    args_view_deleted = ["tasks", "view", task_id]
    exit_code, stdout = run_command(runner, args_view_deleted, allow_error=True)
    assert exit_code != 0, "Expected view deleted task to fail"
    assert "not found" in stdout.lower()
    print("  ✓ View deleted task failed as expected.")
