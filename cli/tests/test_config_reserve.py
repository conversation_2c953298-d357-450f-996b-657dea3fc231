#!/usr/bin/env python3
import re
import uuid
from datetime import datetime
from typing import Generator

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


# --- Fixture for Creating Temporary Reserve Configuration ---
@pytest.fixture(scope="function")
def config_fixture(
    runner: <PERSON><PERSON><PERSON><PERSON><PERSON>,
) -> Generator[str, None, None]:  # Removed dependencies from signature
    """Creates a temporary reserve configuration for testing get/delete.

    Yields:
        str: The ID of the created reserve configuration.

    Cleans up by deleting the configuration afterwards.
    """
    print("\nRunning Config Fixture (Create Reserve)...")
    unique_suffix = str(uuid.uuid4()).split("-")[0]  # Short unique ID
    config_desc = f"Temp test config {unique_suffix}"

    # Create the reserve configuration using run_command with JSON output
    exit_code_create, stdout_create = run_command(
        runner,
        [
            "config",
            "reserves",
            "create",
            "--claim-type",
            "AUTO",  # Corrected option
            "--reserve-type",
            "PROPERTY_DAMAGE",  # Try a standard type
            "--description",
            config_desc,
            "--min-amount",
            "100",
            "--no-required",  # Default to not required
            "--output",
            "json",  # Get JSON output to easily parse ID
        ],
    )

    if exit_code_create != 0:
        pytest.fail(f"Config Fixture Failed (Create): {stdout_create}", pytrace=False)

    # Parse the JSON response to get the ID
    try:
        created_config = validate_json_response(stdout_create)
        config_id = created_config["id"]
        print(
            f"✓ Config Fixture: Created/Updated reserve config ID '{config_id}' (Type: PROPERTY_DAMAGE)"
        )
    except Exception as e:
        pytest.fail(
            f"Config Fixture Failed (Parsing Create Output): {e}\nOutput: {stdout_create}",
            pytrace=False,
        )

    yield config_id  # Provide the ID to the test

    # --- Teardown ---
    print(f"\nConfig Fixture Teardown: Deleting config ID '{config_id}'...")
    exit_code_delete, stdout_delete = run_command(
        runner,
        ["config", "reserves", "delete", config_id, "--force"],
        allow_error=True,  # Allow errors (e.g., 404) if test already deleted config
    )

    if exit_code_delete != 0:
        # Log a warning, but don't fail the test run itself
        print(
            f"⚠️ Config Fixture Warning: Failed to delete config ID '{config_id}'. Exit={exit_code_delete}, Output: {stdout_delete}"
        )
    else:
        print(f"✓ Config Fixture: Deleted config ID '{config_id}'")


def test_02_config_list(runner: CliRunner):
    """Test listing reserve configurations."""
    print("\nTesting Config List...")
    exit_code, stdout = run_command(runner, ["config", "reserves", "list", "--output", "json"])
    assert exit_code == 0

    # Parse and validate JSON response
    configs = validate_json_response(stdout)
    assert isinstance(configs, list), "Expected a list of configurations"
    assert len(configs) > 0, "Expected at least one configuration"

    # Check for required fields in first config
    first_config = configs[0]
    assert "claim_type" in first_config, "Expected 'claim_type' field in configuration"
    assert "reserve_type" in first_config, "Expected 'reserve_type' field in configuration"
    assert "is_required" in first_config, "Expected 'is_required' field in configuration"
    assert "minimum_amount" in first_config, "Expected 'minimum_amount' field in configuration"

    print("✓ Config list command successful (with JSON validation)")


def test_config_create_reserve(runner: CliRunner):
    """Test creating a new reserve configuration."""
    print("\nTesting Config Create Reserve...")
    # Use valid reserve types from the enum
    claim_type = "PROPERTY"
    reserve_type = "ALLOCATED_EXPENSE"  # Valid enum value
    min_amount = 1234.56
    description = f"Test reserve configuration (CREATE TEST)"
    config_id = None

    try:
        # Execute the create command
        exit_code, stdout = run_command(
            runner,
            [
                "config",
                "reserves",
                "create",
                "--claim-type",
                claim_type,
                "--reserve-type",
                reserve_type,
                "--required",
                "--min-amount",
                str(min_amount),
                "--description",
                description,
            ],
        )

        # Assert command execution - could create new or update existing
        assert exit_code == 0
        assert (
            "Successfully created reserve configuration" in stdout
            or "Successfully updated reserve configuration"
            in stdout  # Assuming update uses similar wording
            # Old checks:
            # "Created reserve configuration" in stdout
            # or "Updated existing reserve configuration" in stdout
        ), f"Expected success but got: {stdout}"

        # Extract the config ID from the output
        config_id_match = re.search(
            r"Successfully (?:created|updated) reserve configuration\s*([0-9a-f-]{36})",
            stdout,
            # Old regex: r"(?:Created|Updated existing) reserve configuration\s*([0-9a-f-]{36})", stdout
        )
        assert config_id_match, "Could not find configuration ID in the output"
        config_id = config_id_match.group(1)
        print(f"Configuration ID: {config_id}")

        # Verify the configuration by retrieving it
        exit_code, get_stdout = run_command(
            runner, ["config", "reserves", "show", config_id, "--output", "json"]
        )
        assert exit_code == 0
        config_details = validate_json_response(get_stdout)

        # Verify the details
        assert config_details["id"] == config_id, "Config ID should match"
        assert config_details["claim_type"] == claim_type, "Claim type should match"
        assert config_details["reserve_type"] == reserve_type, "Reserve type should match"
        assert config_details["is_required"] is True, "Config should be required"
        assert float(config_details["minimum_amount"]) == min_amount, "Minimum amount should match"
        assert config_details["description"] == description, "Description should match"

        print("✓ Config create reserve command successful (with verification)")

    finally:
        # Store the original state if it was updated instead of created
        original_config = None
        if "Updated existing" in stdout and config_id:
            try:
                # Get original config details before our test
                original_configs = run_command(
                    runner,
                    [
                        "config",
                        "reserves",
                        "list",
                        "--claim-type",
                        claim_type,
                        "--reserve-type",
                        reserve_type,
                        "--output",
                        "json",
                    ],
                    allow_error=True,
                )[1]
                original_configs = validate_json_response(original_configs)
                if original_configs and len(original_configs) > 0:
                    original_config = original_configs[0]
            except:
                print("  Note: Unable to get original configuration details")

        # If we created a new config, delete it
        if config_id and "Created reserve configuration" in stdout:
            print(f"  Cleaning up test configuration {config_id}...")
            run_command(
                runner, ["config", "reserves", "delete", config_id, "--force"], allow_error=True
            )
        # If we updated an existing config, restore it
        elif config_id and original_config:
            print(f"  Restoring original configuration {config_id}...")
            run_command(
                runner,
                [
                    "config",
                    "reserves",
                    "update",
                    config_id,
                    "--required" if original_config.get("is_required", False) else "--no-required",
                    "--min-amount",
                    str(original_config.get("minimum_amount", 0)),
                    "--description",
                    original_config.get("description", ""),
                ],
                allow_error=True,
            )


def test_config_get_reserve(
    runner: CliRunner, config_fixture: str
):  # Removed run_command, validate_json_response
    """Test retrieving a specific reserve configuration using a fixture."""
    config_id = config_fixture  # Get ID from fixture
    print(f"\nTesting Config Get Reserve (using fixture ID {config_id})...")

    # Test the get command with JSON output
    exit_code, get_stdout = run_command(
        runner, ["config", "reserves", "show", config_id, "--output", "json"]
    )
    assert exit_code == 0, f"Get command failed (JSON): {get_stdout}"
    config_details = validate_json_response(get_stdout)

    # Verify the details - check only ID existence as other details are fixture-internal
    assert config_details["id"] == config_id, "Config ID should match"
    print("✓ Config get reserve command successful (JSON verified)")

    # Test with table output as well
    exit_code, table_stdout = run_command(runner, ["config", "reserves", "show", config_id])
    assert exit_code == 0, f"Get command failed (table): {table_stdout}"
    assert "Reserve Configuration Details" in table_stdout
    assert config_id in table_stdout
    print("✓ Config get reserve command successful (table verified)")


def test_config_update_reserve(runner: CliRunner):  # Removed helpers from signature
    """Test updating an existing reserve configuration."""
    print("\nTesting Config Update Reserve...")

    # Get an existing config to update
    exit_code, list_stdout = run_command(runner, ["config", "reserves", "list", "--output", "json"])
    assert exit_code == 0
    all_configs = validate_json_response(list_stdout)
    assert len(all_configs) > 0, "Need at least one config to test update"

    # Pick a config that is less likely to break other tests
    config_to_update = None
    for config in all_configs:
        # Avoid touching the standard AUTO/PROPERTY required ones if possible
        if not config["is_required"] or config["reserve_type"] not in [
            "BODILY_INJURY",
            "PROPERTY_DAMAGE",
        ]:
            config_to_update = config
            break

    # If no suitable config found, use the first one (less ideal)
    if not config_to_update:
        print("  Warning: No ideal config found for update test, using the first available.")
        config_to_update = all_configs[0]

    config_id = config_to_update["id"]
    original_required = config_to_update["is_required"]
    original_amount = (
        float(config_to_update["minimum_amount"]) if config_to_update["minimum_amount"] else 0
    )
    original_description = config_to_update["description"]

    print(f"Using config ID {config_id} for update test")

    try:
        # Generate new values for update
        new_required = not original_required  # Toggle required status
        new_amount = original_amount + 1000  # Increase amount
        new_description = f"Updated description for test ({datetime.now().strftime('%H:%M:%S')})"

        # Update the config
        exit_code, update_stdout = run_command(
            runner,
            [
                "config",
                "reserves",
                "update",
                config_id,
                "--required" if new_required else "--no-required",
                "--min-amount",
                str(new_amount),
                "--description",
                new_description,
            ],
        )

        assert exit_code == 0, f"Update command failed: {update_stdout}"
        assert f"Updated reserve configuration {config_id}" in update_stdout

        # Verify the updates were applied
        exit_code, get_stdout = run_command(
            runner, ["config", "reserves", "show", config_id, "--output", "json"]
        )
        assert exit_code == 0, f"Get command failed after update: {get_stdout}"
        updated_config = validate_json_response(get_stdout)

        assert updated_config["id"] == config_id
        assert updated_config["is_required"] == new_required
        # Compare floats with tolerance or convert back to Decimal for exactness if needed
        assert float(updated_config["minimum_amount"]) == new_amount
        assert updated_config["description"] == new_description

        print("✓ Config update reserve command successful (verified)")

    finally:
        # Restore original values
        print(f"  Restoring original configuration {config_id}...")
        restore_exit_code, restore_stdout = run_command(
            runner,
            [
                "config",
                "reserves",
                "update",
                config_id,
                "--required" if original_required else "--no-required",
                "--min-amount",
                str(original_amount),
                "--description",
                original_description,
            ],
            allow_error=True,  # Allow errors during cleanup
        )
        if restore_exit_code != 0:
            print(f"⚠️ Failed to restore config {config_id}. Output: {restore_stdout}")
        else:
            print(f"✓ Config {config_id} restored.")


def test_config_delete_reserve(
    runner: CliRunner, config_fixture: str
):  # Removed helper from signature
    """Test deleting a reserve configuration using a fixture."""
    config_id = config_fixture  # Get ID from fixture
    print(f"\nTesting Config Delete Reserve (using fixture ID {config_id})...")

    # 1. Verify the config exists before deleting
    exit_code_show, stdout_show = run_command(
        runner, ["config", "reserves", "show", config_id], allow_error=True
    )
    assert (
        exit_code_show == 0
    ), f"Config {config_id} should exist before delete, but show failed: {stdout_show}"
    print("  ✓ Config exists before delete.")

    # 2. Delete the configuration (using --force to avoid prompt)
    exit_code_delete, stdout_delete = run_command(
        runner, ["config", "reserves", "delete", config_id, "--force"]
    )
    assert exit_code_delete == 0, f"Delete command failed: {stdout_delete}"
    # Check for generic success message
    assert (
        "deleted reserve configuration" in stdout_delete.lower()
    ), f"Delete success message not found in: {stdout_delete}"
    print("  ✓ Delete command successful.")

    # 3. Verify the config is gone
    exit_code_show_after, stdout_show_after = run_command(
        runner, ["config", "reserves", "show", config_id], allow_error=True
    )
    # Expecting the show command to fail (e.g., exit code 1 or non-zero) and indicate not found
    assert (
        exit_code_show_after != 0
    ), f"Config {config_id} should NOT exist after delete, but show succeeded: {stdout_show_after}"
    assert (
        "not found" in stdout_show_after.lower() or exit_code_show_after == 404
    ), f"Expected 'not found' or 404 after delete, got Exit={exit_code_show_after}, Output: {stdout_show_after}"

    print("✓ Config delete reserve command successful (verified config is gone)")
    # No cleanup needed here, as the fixture handles creation/deletion
