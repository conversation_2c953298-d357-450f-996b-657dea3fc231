#!/usr/bin/env python3
import re

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app


def test_auth_session_list(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    """Test listing active sessions."""
    print("\nRunning test_auth_session_list...")

    # Assuming auth_setup has run

    result = runner.invoke(app, ["auth", "session", "list"], catch_exceptions=False)
    print(f"Session List Output:\n{result.stdout}")

    assert result.exit_code == 0
    # Expect at least one session from the initial login
    assert "Session" in result.stdout
    assert "Agent=" in result.stdout
    assert "IP=" in result.stdout
    assert "Last Active=" in result.stdout
    assert "Expires=" in result.stdout
    # We could potentially parse the output further or check against API response if needed

    print("✓ test_auth_session_list passed")


def test_auth_session_revoke(runner: CliRunner):
    """Tests the auth session revoke command."""
    print("\nRunning test_auth_session_revoke...")

    # First, list the sessions to get a session ID
    list_result = runner.invoke(app, ["auth", "session", "list"], catch_exceptions=False)
    print(f"Session List Output:\n{list_result.stdout}")
    assert list_result.exit_code == 0
    assert "Session" in list_result.stdout  # Ensure at least one session exists

    # Extract a session ID from the output
    # Format: Session <uuid>: Agent="...", IP=..., Last Active=..., Expires=...
    # import re (already imported)

    session_id_match = re.search(r"Session ([0-9a-f-]+):", list_result.stdout)
    assert session_id_match, "Could not find session ID in the output"
    session_id = session_id_match.group(1)
    print(f"Found session ID: {session_id}")

    # Revoke the session
    revoke_result = runner.invoke(
        app, ["auth", "session", "revoke", session_id], catch_exceptions=False
    )
    print(f"Revoke Output:\n{revoke_result.stdout}")
    assert revoke_result.exit_code == 0
    assert f"Session {session_id} successfully revoked" in revoke_result.stdout

    # Verify the session is no longer listed
    verify_result = runner.invoke(app, ["auth", "session", "list"], catch_exceptions=False)
    print(f"Verification List Output:\n{verify_result.stdout}")
    assert verify_result.exit_code == 0
    assert session_id not in verify_result.stdout

    # Log back in to ensure we have an active session for other tests
    print("Logging admin back in to restore session...")
    login_result = runner.invoke(
        app,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
        catch_exceptions=False,
    )
    if login_result.exit_code != 0:
        print("Failed to log back in after session revoke test:")
        print(login_result.stdout)
        pytest.fail("Failed to restore login state after test_auth_session_revoke", pytrace=False)
    print("✓ Re-login successful")

    print("✓ test_auth_session_revoke passed")


def test_auth_logout(runner: CliRunner):
    """Tests the auth logout command."""
    print("\nRunning test_auth_logout...")

    # First, ensure we're logged in
    login_result = runner.invoke(
        app,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
        catch_exceptions=False,
    )
    assert login_result.exit_code == 0
    assert "Successfully logged in" in login_result.stdout

    # Execute the logout command
    logout_result = runner.invoke(app, ["auth", "logout"], catch_exceptions=False)
    print(f"Logout Output:\n{logout_result.stdout}")

    # Check that the command executed successfully
    assert logout_result.exit_code == 0
    assert "Successfully logged out" in logout_result.stdout
    assert "Tokens cleared" in logout_result.stdout

    # Attempt to list sessions after logout (should fail due to missing token)
    list_result = runner.invoke(app, ["auth", "session", "list"])
    print(f"Session List After Logout Output:\n{list_result.stdout}")
    assert list_result.exit_code != 0  # Should fail due to missing authentication

    # Log back in to ensure we have an active session for other tests
    print("Logging admin back in to restore session...")
    restore_result = runner.invoke(
        app,
        [
            "auth",
            "login",
            "--email",
            "<EMAIL>",
            "--password",
            "admin",
        ],
        catch_exceptions=False,
    )
    if restore_result.exit_code != 0:
        print("Failed to log back in after logout test:")
        print(restore_result.stdout)
        pytest.fail("Failed to restore login state after test_auth_logout", pytrace=False)
    print("✓ Re-login successful")

    print("✓ test_auth_logout passed")
