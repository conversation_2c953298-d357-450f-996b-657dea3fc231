#!/usr/bin/env python3
"""Tests for damaged property assets CLI commands."""

import json
import os
from typing import Dict

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app

# Import specific helpers from conftest
from .conftest import run_command, validate_json_response


def test_01_create_damaged_property_asset_auto(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>, auto_claim: dict):
    """Test creating a damaged property asset for an auto claim."""
    print("\nTesting Auto Claim Damaged Property Asset Creation...")

    claim_id = auto_claim.get("id")
    if not claim_id:
        pytest.fail("No auto claim ID available")

    # Create damaged property asset
    command = [
        "claims",
        "create-damaged-property-asset",
        auto_claim["number"],
        "--name",
        "Damaged Vehicle",
        "--asset-type",
        "VEHICLE",
        "--description",
        "Third party damaged vehicle",
        "--location",
        "Intersection of Main St and 1st Ave",
        "--address",
        "123 Main St",
        "--city",
        "Anytown",
        "--state",
        "CA",
        "--zip-code",
        "90210",
        "--owner-name",
        "<PERSON>",
        "--owner-type",
        "Third Party",
        "--owned-by-insured",
        "--estimated-value",
        "15000",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("auto_details_id") is not None
    assert response.get("gl_details_id") is None  # Should be None for auto claim
    assert response.get("name") == "Damaged Vehicle"
    assert response.get("asset_type") == "VEHICLE"
    assert response.get("description") == "Third party damaged vehicle"
    assert response.get("location") == "Intersection of Main St and 1st Ave"
    assert response.get("address") == "123 Main St"
    assert response.get("city") == "Anytown"
    assert response.get("state") == "CA"
    assert response.get("zip_code") == "90210"
    assert response.get("owner_name") == "Jane Smith"
    assert response.get("owner_type") == "Third Party"
    assert response.get("owned_by_insured") is True
    assert response.get("estimated_value") == "15000.0"

    # Return the created asset ID for potential use in other tests
    return response.get("id")


def test_02_create_damaged_property_asset_gl(runner: CliRunner, gl_claim: dict):
    """Test creating a damaged property asset for a general liability claim."""
    print("\nTesting GL Claim Damaged Property Asset Creation...")

    claim_id = gl_claim.get("id")
    if not claim_id:
        pytest.fail("No GL claim ID available")

    # Create damaged property asset
    command = [
        "claims",
        "create-damaged-property-asset",
        gl_claim["number"],
        "--name",
        "Damaged Building",
        "--asset-type",
        "BUILDING",
        "--description",
        "Commercial building with water damage",
        "--location",
        "Downtown Business District",
        "--address",
        "456 Commerce Ave",
        "--city",
        "Businesstown",
        "--state",
        "NY",
        "--zip-code",
        "10001",
        "--owner-name",
        "Business Corp LLC",
        "--owner-type",
        "Third Party",
        "--owned-by-insured",
        "--estimated-value",
        "250000",
        "--output",
        "json",
    ]

    # Run command
    result = runner.invoke(app, command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert "id" in response
    assert response.get("gl_details_id") is not None
    assert response.get("auto_details_id") is None  # Should be None for GL claim
    assert response.get("name") == "Damaged Building"
    assert response.get("asset_type") == "BUILDING"

    # Return the created asset ID for potential use in other tests
    return response.get("id")


def test_03_list_damaged_property_assets(runner: CliRunner, auto_claim: dict):
    """Test listing damaged property assets for a claim."""
    print("\nTesting List Damaged Property Assets...")
    claim_number = auto_claim["number"]

    # Create first damaged property asset for this test's claim
    asset1_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "First Test Asset",
        "--asset-type",
        "STRUCTURE",
        "--description",
        "First test damaged structure",
        "--location",
        "First test location",
        "--owner-name",
        "First Test Owner",
        "--output",
        "json",
    ]
    asset1_result = runner.invoke(app, asset1_command, catch_exceptions=True)
    print("\nSetup Step (adding first asset for test_03):")
    print(f"  Exit code: {asset1_result.exit_code}")
    print(f"  Exception: {asset1_result.exception}")
    assert (
        asset1_result.exit_code == 0
    ), f"Failed to create first damaged property asset for test_03. Output: {asset1_result.stdout}"
    asset1_data = validate_json_response(asset1_result.stdout)
    asset1_id = str(asset1_data["id"])

    # Create a second damaged property asset for this test's claim
    asset2_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Second Test Asset",
        "--asset-type",
        "THIRD_PARTY_PROPERTY",
        "--description",
        "Second test damaged property",
        "--location",
        "Second test location",
        "--owner-name",
        "Second Test Owner",
        "--output",
        "json",
    ]
    asset2_result = runner.invoke(app, asset2_command, catch_exceptions=True)
    print("\nSetup Step (adding second asset for test_03):")
    print(f"  Exit code: {asset2_result.exit_code}")
    print(f"  Exception: {asset2_result.exception}")
    assert (
        asset2_result.exit_code == 0
    ), f"Failed to create second damaged property asset for test_03. Output: {asset2_result.stdout}"
    asset2_data = validate_json_response(asset2_result.stdout)
    asset2_id = str(asset2_data["id"])

    # List all damaged property assets
    list_command = ["claims", "list-damaged-property-assets", claim_number, "--output", "json"]
    result = runner.invoke(app, list_command, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert isinstance(response, list)
    assert len(response) >= 2  # Should have at least our two test assets

    # Find our test assets in the response
    found_asset1 = False
    found_asset2 = False
    for asset in response:
        if asset.get("id") == asset1_id:
            found_asset1 = True
            assert asset.get("name") == "First Test Asset"
            assert asset.get("asset_type") == "STRUCTURE"
        elif asset.get("id") == asset2_id:
            found_asset2 = True
            assert asset.get("name") == "Second Test Asset"
            assert asset.get("asset_type") == "THIRD_PARTY_PROPERTY"

    assert found_asset1, "First test asset not found in list response"
    assert found_asset2, "Second test asset not found in list response"


def test_04_get_damaged_property_asset(runner: CliRunner, auto_claim: dict):
    """Test getting a specific damaged property asset."""
    print("\nTesting Get Damaged Property Asset...")
    claim_number = auto_claim["number"]

    # Create a damaged property asset specifically for this test
    asset_name_to_get = "Get Me Asset Test04"
    create_command_args = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        asset_name_to_get,
        "--asset-type",
        "VEHICLE",
        "--description",
        "Asset to retrieve in test",
        "--owner-name",
        "Test Owner 04",
        "--estimated-value",
        "12500",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating asset for get_damaged_property_asset test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create asset for get_damaged_property_asset test. Output: {create_result.stdout}"
    created_asset_data = validate_json_response(create_result.stdout)
    asset_to_get_id = str(created_asset_data["id"])

    # Get the created damaged property asset
    get_command_args = [
        "claims",
        "get-damaged-property-asset",
        claim_number,
        asset_to_get_id,
        "--json",
    ]
    result = runner.invoke(app, get_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == asset_to_get_id
    assert response.get("name") == asset_name_to_get
    assert response.get("asset_type") == "VEHICLE"
    assert response.get("description") == "Asset to retrieve in test"
    assert response.get("owner_name") == "Test Owner 04"
    assert response.get("estimated_value") == "12500.0"
    assert "damage_instances" in response  # Should have damage_instances array, even if empty


def test_05_update_damaged_property_asset(runner: CliRunner, auto_claim: dict):
    """Test updating a damaged property asset."""
    print("\nTesting Update Damaged Property Asset...")
    claim_number = auto_claim["number"]

    # Create a damaged property asset to update
    create_command_args = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Asset To Update",
        "--asset-type",
        "STRUCTURE",
        "--description",
        "Original description",
        "--estimated-value",
        "10000",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating asset for update test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create asset for update test. Output: {create_result.stdout}"
    created_asset_data = validate_json_response(create_result.stdout)
    asset_id = str(created_asset_data["id"])

    # Update the asset
    update_command_args = [
        "claims",
        "update-damaged-property-asset",
        claim_number,
        asset_id,
        "--name",
        "Updated Asset Name",
        "--description",
        "Updated description",
        "--estimated-value",
        "15000",
        "--owner-name",
        "Updated Owner",
        "--output",
        "json",
    ]
    result = runner.invoke(app, update_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate response
    response = validate_json_response(result.stdout)
    assert response.get("id") == asset_id
    assert response.get("name") == "Updated Asset Name"
    assert response.get("description") == "Updated description"
    assert response.get("estimated_value") == "15000.0"
    assert response.get("owner_name") == "Updated Owner"
    assert response.get("asset_type") == "STRUCTURE"  # Should remain unchanged


def test_06_delete_damaged_property_asset(runner: CliRunner, auto_claim: dict):
    """Test deleting a damaged property asset."""
    print("\nTesting Delete Damaged Property Asset...")
    claim_number = auto_claim["number"]

    # Create a damaged property asset to delete
    create_command_args = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Asset To Delete",
        "--asset-type",
        "VEHICLE",
        "--description",
        "This asset will be deleted",
        "--output",
        "json",
    ]
    create_result = runner.invoke(app, create_command_args, catch_exceptions=True)
    print("\nSetup Step (creating asset for delete test):")
    print(f"  Exit code: {create_result.exit_code}")
    print(f"  Exception: {create_result.exception}")
    assert (
        create_result.exit_code == 0
    ), f"Failed to create asset for delete test. Output: {create_result.stdout}"
    created_asset_data = validate_json_response(create_result.stdout)
    asset_id = str(created_asset_data["id"])

    # Delete the asset
    delete_command_args = [
        "claims",
        "delete-damaged-property-asset",
        claim_number,
        asset_id,
        "--yes",  # Bypass confirmation
    ]
    result = runner.invoke(app, delete_command_args, catch_exceptions=True)

    # Print debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Exception: {result.exception}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0
    assert "deleted successfully" in result.stdout.lower()

    # Verify it's gone by trying to get it
    get_command_args = [
        "claims",
        "get-damaged-property-asset",
        claim_number,
        asset_id,
        "--json",
    ]
    get_result = runner.invoke(app, get_command_args, catch_exceptions=True)

    # Should fail or return not found
    assert get_result.exit_code != 0 or "not found" in get_result.stdout.lower()


def test_07_multiple_property_assets(runner: CliRunner, gl_claim: dict):
    """Test creating multiple damaged property assets on a single claim."""
    print("\nTesting Multiple Damaged Property Assets...")

    claim_number = gl_claim["number"]
    asset_ids = []

    # Create first asset
    asset1_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "First Multiple Test Asset",
        "--asset-type",
        "BUILDING",
        "--description",
        "First of multiple assets",
        "--output",
        "json",
    ]
    asset1_result = runner.invoke(app, asset1_command, catch_exceptions=True)
    assert (
        asset1_result.exit_code == 0
    ), f"Failed to create first asset. Output: {asset1_result.stdout}"
    asset1_data = validate_json_response(asset1_result.stdout)
    asset1_id = str(asset1_data["id"])
    asset_ids.append(asset1_id)

    # Create second asset
    asset2_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Second Multiple Test Asset",
        "--asset-type",
        "STRUCTURE",
        "--description",
        "Second of multiple assets",
        "--output",
        "json",
    ]
    asset2_result = runner.invoke(app, asset2_command, catch_exceptions=True)
    assert (
        asset2_result.exit_code == 0
    ), f"Failed to create second asset. Output: {asset2_result.stdout}"
    asset2_data = validate_json_response(asset2_result.stdout)
    asset2_id = str(asset2_data["id"])
    asset_ids.append(asset2_id)

    # Create third asset
    asset3_command = [
        "claims",
        "create-damaged-property-asset",
        claim_number,
        "--name",
        "Third Multiple Test Asset",
        "--asset-type",
        "THIRD_PARTY_PROPERTY",
        "--description",
        "Third of multiple assets",
        "--output",
        "json",
    ]
    asset3_result = runner.invoke(app, asset3_command, catch_exceptions=True)
    assert (
        asset3_result.exit_code == 0
    ), f"Failed to create third asset. Output: {asset3_result.stdout}"
    asset3_data = validate_json_response(asset3_result.stdout)
    asset3_id = str(asset3_data["id"])
    asset_ids.append(asset3_id)

    # List all assets for the claim
    list_assets_command = [
        "claims",
        "list-damaged-property-assets",
        claim_number,
        "--output",
        "json",
    ]
    list_assets_result = runner.invoke(app, list_assets_command, catch_exceptions=True)
    assert list_assets_result.exit_code == 0
    assets = validate_json_response(list_assets_result.stdout)

    # Find our test assets
    test_assets = [
        a
        for a in assets
        if a.get("name").startswith("First Multiple")
        or a.get("name").startswith("Second Multiple")
        or a.get("name").startswith("Third Multiple")
    ]
    assert len(test_assets) == 3, f"Expected 3 test assets, found {len(test_assets)}"

    print("\nSuccessfully verified multiple property assets on a single claim")
