#!/usr/bin/env python3
import json
import re
from typing import Dict, Optional

import pytest
from typer.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from claimentine_cli.main import app


def validate_json_response(stdout: str) -> Dict:
    """Helper function to validate and parse JSON response."""
    # Remove any ANSI color codes
    ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
    stdout = ansi_escape.sub("", stdout)

    # Remove any debug lines
    if "DEBUG:" in stdout:
        cleaned_lines = []
        for line in stdout.splitlines():
            if not line.strip().startswith("DEBUG:"):
                cleaned_lines.append(line)
        stdout = "\n".join(cleaned_lines)

    try:
        return json.loads(stdout)
    except json.JSONDecodeError:
        raise AssertionError(f"Invalid JSON response: {stdout}")


def test_metrics_dashboard(runner: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    """Test the metrics dashboard command."""
    print("\nRunning test_metrics_dashboard...")

    # Basic metrics dashboard command
    result = runner.invoke(app, ["metrics", "dashboard"], catch_exceptions=False)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected table headings
    assert "Claims Metrics" in result.stdout
    assert "Financial Metrics" in result.stdout
    assert "Task Metrics" in result.stdout

    print("✓ test_metrics_dashboard passed")


def test_metrics_dashboard_with_period(runner: CliRunner):
    """Test the metrics dashboard command with period filter."""
    print("\nRunning test_metrics_dashboard_with_period...")

    # Metrics dashboard with period filter
    result = runner.invoke(
        app, ["metrics", "dashboard", "--period", "last_6_months"], catch_exceptions=False
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected table headings
    assert "Claims Metrics" in result.stdout
    assert "Financial Metrics" in result.stdout
    assert "Task Metrics" in result.stdout

    print("✓ test_metrics_dashboard_with_period passed")


def test_metrics_dashboard_with_customer(runner: CliRunner, customer: str):
    """Test the metrics dashboard command with client filter."""
    print("\nRunning test_metrics_dashboard_with_customer...")

    # Metrics dashboard with client filter
    result = runner.invoke(
        app, ["metrics", "dashboard", "--client-id", customer], catch_exceptions=False
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected table headings
    assert "Claims Metrics" in result.stdout
    assert "Financial Metrics" in result.stdout
    assert "Task Metrics" in result.stdout

    print("✓ test_metrics_dashboard_with_customer passed")


def test_metrics_dashboard_json_output(runner: CliRunner):
    """Test the metrics dashboard command with JSON output."""
    print("\nRunning test_metrics_dashboard_json_output...")

    # Metrics dashboard with JSON output
    result = runner.invoke(app, ["metrics", "dashboard", "--output", "json"], catch_exceptions=True)

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Validate JSON response
    try:
        json_data = validate_json_response(result.stdout)
        assert "total_claims" in json_data
        assert "open_claims" in json_data
        assert "tasks_pending" in json_data
        assert "total_outstanding_reserves" in json_data
    except Exception as e:
        pytest.fail(f"Failed to validate JSON response: {e}")

    print("✓ test_metrics_dashboard_json_output passed")


def test_metrics_dashboard_no_compare(runner: CliRunner):
    """Test the metrics dashboard command without period comparison."""
    print("\nRunning test_metrics_dashboard_no_compare...")

    # Metrics dashboard without period comparison
    result = runner.invoke(
        app, ["metrics", "dashboard", "--no-compare-period"], catch_exceptions=False
    )

    # Print detailed debug info
    print(f"Exit code: {result.exit_code}")
    print(f"Output: {result.stdout}")

    assert result.exit_code == 0

    # Check for expected table headings
    assert "Claims Metrics" in result.stdout
    assert "Financial Metrics" in result.stdout
    assert "Task Metrics" in result.stdout

    print("✓ test_metrics_dashboard_no_compare passed")
