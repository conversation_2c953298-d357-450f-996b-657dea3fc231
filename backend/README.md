# Cadence Backend

Backend API for the Elysian Claims Management System.

## Requirements

- Python 3.13+
- Poetry for dependency management
- PostgreSQL database

## Development Setup

1. Install dependencies:
```bash
poetry install
```

2. Install pre-commit hooks:
```bash
poetry run pre-commit install
```

3. Create a `.env` file:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Run the development server:
```bash
poetry run uvicorn cadence.main:app --reload
```

## Project Structure

```
src/cadence/
├── api/         # API routes and endpoints
├── core/        # Core functionality and config
├── db/          # Database models and config
├── schemas/     # Pydantic models
└── services/    # Business logic
```

## Development

- Format code: `poetry run black .`
- Sort imports: `poetry run isort .`
- Run linter: `poetry run ruff check .`
- Run type checker: `poetry run mypy .`
- Run tests: `poetry run pytest` 