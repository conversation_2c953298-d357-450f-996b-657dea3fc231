[tool.poetry]
name = "claimentine"
version = "0.1.0"
description = "Elysian Claims Management System Backend"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "claimentine", from = "src"}]

[tool.poetry.dependencies]
python = "^3.13"
fastapi = "^0.109.0"
uvicorn = "^0.27.0"
sqlalchemy = "^2.0.25"
pydantic = "^2.5.3"
pydantic-settings = "^2.1.0"
psycopg = {extras = ["binary"], version = "^3.1.18"}
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
python-json-logger = "^2.0.7"
psycopg2-binary = "^2.9.10"
email-validator = "^2.2.0"
alembic = "^1.14.0"
google-cloud-storage = "^2.14.0"
faker = "^37.3.0"

[tool.poetry.group.dev.dependencies]
black = "^24.10.0"
isort = "^5.13.2"
autoflake = "^2.2.1"
pytest = "^7.4.4"
pytest-cov = "^4.1.0"
pytest-asyncio = "^0.23.5"
httpx = "^0.26.0"
mypy = "^1.14.0"
ruff = "^0.8.4"
pre-commit = "^3.6.0"
flake8 = "^7.2.0"
pylint = "^3.3.6"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 120
target-version = ['py313']

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 120

[tool.ruff]
line-length = 120
target-version = "py313"

[tool.mypy]
python_version = "3.13"
strict = true
ignore_missing_imports = true
disallow_untyped_defs = true
check_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
asyncio_mode = "auto" 