# Virtualenv settings
[virtualenvs]
in-project = true  # Keep virtualenv in project directory
prefer-active-python = false  # Strictly use Python version from pyproject.toml

# Virtualenv creation options
[virtualenvs.options]
no-pip = false  # Required for editable installs and dev dependencies
no-setuptools = false  # Required for building packages
system-site-packages = false  # Keep virtualenv isolated from system packages

# Installation settings
[installer]
parallel = true  # Enable parallel package installation
max-workers = 8  # Maximum number of parallel workers
modern-installation = true  # Use PEP 517 build system and wheel caching
