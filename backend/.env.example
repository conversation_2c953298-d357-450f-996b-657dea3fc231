# API Settings
API_ENV=development
API_DEBUG=true
API_HOST=0.0.0.0
API_PORT=8000

# Database Settings
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/cadence

# Security Settings
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
CORS_ORIGINS=http://localhost:3000

# File Storage Settings
STORAGE_PROVIDER=gcs
GCS_BUCKET_NAME=your-bucket-name
GCS_PROJECT_ID=your-project-id 