"""Database setup script called by <PERSON><PERSON><PERSON>'s setup-db target."""

import logging
import sys

from sqlalchemy import create_engine

# Import setup functions from other scripts
from claimentine.core.config import settings
from claimentine.scripts.create_superuser import create_superuser
from claimentine.scripts.insert_initial_data import insert_initial_data

# Import only the schema creation part from setup_db
from claimentine.scripts.setup_db import create_schema_objects

# Setup basic logging for setup script
logging.basicConfig(level=logging.INFO, format="%(message)s")
logger = logging.getLogger(__name__)


def setup_schema_and_data() -> None:
    """Run schema creation and initial data insertion."""
    logger.info("\n🚀 Starting database schema and data setup...")
    logger.info(f"Using database URL: {settings.POSTGRES_URL}")

    # Create engine for setup tasks
    engine = create_engine(str(settings.POSTGRES_URL), echo=False)

    try:
        # Step 1: Create schema objects (extensions, tables)
        logger.info("\n--- Step 1: Creating schema objects (extensions, tables) ---")
        create_schema_objects(engine)

        # Step 2: Insert initial data (permissions, roles, etc.)
        logger.info("\n--- Step 2: Inserting initial data ---")
        insert_initial_data()

        # Step 3: Create initial superuser (optional, can be run separately)
        logger.info("\n--- Step 3: Creating initial superuser ---")
        create_superuser()

        logger.info("\n✨ Schema and data setup completed successfully!")

    except Exception as e:
        logger.error(f"\n❌ Setup failed: {e}", exc_info=True)
        sys.exit(1)
    finally:
        # Dispose the engine
        if engine:
            logger.info("Disposing setup engine...")
            engine.dispose()


if __name__ == "__main__":
    setup_schema_and_data()
