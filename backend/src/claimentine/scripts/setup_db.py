"""Database setup script."""

import argparse
import logging
import sys

from sqlalchemy import Engine, create_engine, text

from claimentine.core.config import settings
from claimentine.db.base import Base  # Import all models through base.py

# Setup logging
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
handler = logging.StreamHandler()
handler.setFormatter(logging.Formatter("%(message)s"))
logger.addHandler(handler)


def drop_database_if_exists() -> None:
    """Drop the database if it exists."""
    logger.info("Checking if database exists...")

    # Create engine without database name
    db_url = str(settings.POSTGRES_URL)
    db_url_without_name = db_url.rsplit("/", 1)[0]
    engine = create_engine(
        f"{db_url_without_name}/postgres",
        isolation_level="AUTOCOMMIT",  # Required for dropping databases
    )

    try:
        # Connect to postgres database to check if our database exists
        with engine.connect() as conn:
            # Try to terminate all connections to the database, but continue if it fails
            try:
                logger.info(f"Attempting to terminate connections to {settings.POSTGRES_DB}...")
                conn.execute(
                    text(
                        f"""
                        SELECT pg_terminate_backend(pg_stat_activity.pid)
                        FROM pg_stat_activity
                        WHERE pg_stat_activity.datname = '{settings.POSTGRES_DB}'
                        AND pid <> pg_backend_pid();
                        """
                    )
                )
                logger.info("✅ Connections terminated successfully")
            except Exception as e:
                logger.warning(f"⚠️ Could not terminate all connections: {e}")
                logger.warning("Continuing with database operations...")

            # Check if database exists
            result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}'"))
            exists = result.scalar() is not None

            # Drop database if it exists
            if exists:
                logger.info(f"Dropping database {settings.POSTGRES_DB}...")
                try:
                    conn.execute(text(f"DROP DATABASE {settings.POSTGRES_DB}"))
                    logger.info("✅ Database dropped successfully!")
                except Exception as e:
                    logger.error(f"❌ Could not drop database: {e}")
                    logger.warning("This may be due to active connections. You may need to manually stop the backend.")
                    sys.exit(1)
            else:
                logger.info(f"Database {settings.POSTGRES_DB} does not exist")
    except Exception as e:
        logger.error(f"❌ Error in database operations: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def create_database_if_not_exists() -> None:
    """Create the database if it doesn't exist."""
    logger.info("Checking if database exists...")

    # Create engine without database name
    db_url = str(settings.POSTGRES_URL)
    db_url_without_name = db_url.rsplit("/", 1)[0]
    engine = create_engine(
        f"{db_url_without_name}/postgres",
        isolation_level="AUTOCOMMIT",  # Required for creating databases
    )

    try:
        # Connect to postgres database to check if our database exists
        with engine.connect() as conn:
            # Check if database exists
            result = conn.execute(text(f"SELECT 1 FROM pg_database WHERE datname = '{settings.POSTGRES_DB}'"))
            exists = result.scalar() is not None

            # Create database if it doesn't exist
            if not exists:
                logger.info(f"Creating database {settings.POSTGRES_DB}...")
                conn.execute(text(f"CREATE DATABASE {settings.POSTGRES_DB}"))
                logger.info("✅ Database created successfully!")
            else:
                logger.info(f"Database {settings.POSTGRES_DB} already exists")
    except Exception as e:
        logger.error(f"❌ Error creating database: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


def create_schema_objects(engine: Engine) -> None:
    """Create extensions and tables using the provided engine."""
    try:
        logger.info("Creating database engine...")
        # Create all tables
        with engine.begin() as conn:
            logger.info("Creating extensions...")
            # Create extensions if they don't exist
            conn.execute(text('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"'))

            # Add pgcrypto for PostgreSQL versions before 13
            # In PostgreSQL 13+, gen_random_uuid() is available natively
            conn.execute(text('CREATE EXTENSION IF NOT EXISTS "pgcrypto"'))

            logger.info("Creating database tables...")
            # Create all tables
            Base.metadata.create_all(conn)

        logger.info("✅ Database tables created successfully!")

    except Exception as e:
        logger.error(f"❌ Error creating database tables: {e}", exc_info=True)
        sys.exit(1)


def setup_database(drop_db: bool = False) -> None:
    """Drop, create, and set up the database (extensions, tables).

    Args:
        drop_db: Whether to drop the database before creating it. Default is False.
    """
    logger.info("Starting full database setup...")
    logger.info(f"Using database URL: {settings.POSTGRES_URL}")

    # First drop and recreate database
    if drop_db:
        logger.info("Drop database option is enabled")
        drop_database_if_exists()
    else:
        logger.info("Drop database option is disabled, skipping database drop")

    create_database_if_not_exists()

    # Create engine with the target database
    engine = create_engine(
        str(settings.POSTGRES_URL),
        echo=False,  # Set to True to see SQL queries
        pool_pre_ping=True,
    )

    try:
        # Create schema objects (extensions, tables)
        create_schema_objects(engine)
    finally:
        logger.info("Disposing database engine...")
        engine.dispose()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Set up the database schema and tables")
    parser.add_argument("--drop", action="store_true", help="Drop the database before creating it (default: False)")
    args = parser.parse_args()

    # Pass the drop argument directly
    setup_database(drop_db=args.drop)
