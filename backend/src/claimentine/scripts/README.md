# Database Setup Scripts

This directory contains scripts for setting up and managing the database.

## Prerequisites

1. PostgreSQL server running locally or accessible via network
2. Database user with permissions to create databases and extensions
3. Python environment with all dependencies installed

## Scripts

### `setup.py`

Main setup script that runs all steps in sequence:
1. Creates database tables
2. Inserts initial data (permissions)
3. Creates a superuser

Usage:
```bash
poetry run python scripts/setup.py
```

### Individual Scripts

You can also run individual scripts if needed:

#### `setup_db.py`
Creates all database tables:
```bash
poetry run python scripts/setup_db.py
```

#### `insert_initial_data.py`
Inserts initial permissions:
```bash
poetry run python scripts/insert_initial_data.py
```

#### `create_superuser.py`
Creates a superuser with all permissions:
```bash
poetry run python scripts/create_superuser.py
```

## Environment Variables

The scripts use the following environment variables (can be set in `.env`):

```env
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=claimentine
```

## Error Handling

- All scripts will exit with status code 1 if an error occurs
- Detailed error messages are printed to stderr
- Each step is idempotent and can be safely re-run 