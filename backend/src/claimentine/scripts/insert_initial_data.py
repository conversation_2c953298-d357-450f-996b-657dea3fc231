"""<PERSON><PERSON><PERSON> to insert initial data into the database."""

import sys
from decimal import Decimal
from typing import Set

from sqlalchemy import create_engine, select
from sqlalchemy.orm import Session

from claimentine.core.config import settings
from claimentine.models.auth import ROLE_PERMISSIONS, Permission
from claimentine.models.authority import AuthorityLevel, AuthorityRole
from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType
from claimentine.models.config.reserve import ReserveConfiguration
from claimentine.models.client import Client
from claimentine.models.user import UserRole


# Get all permissions from role definitions
def get_all_permissions() -> Set[str]:
    """Get all defined permissions from role definitions."""
    permissions = set()
    for role_perms in ROLE_PERMISSIONS.values():
        permissions.update(role_perms)
    return permissions


# Initial permissions with descriptions
PERMISSION_DESCRIPTIONS = {
    # Claims - Basic
    "VIEW_OWN_CLAIMS": "Ability to view own claims",
    "CREATE_OWN_CLAIMS": "Ability to create own claims",
    "CREATE_CLAIMS": "Ability to create claims on behalf of others",
    "EDIT_ASSIGNED_CLAIMS": "Ability to edit assigned claims",
    "EDIT_CLAIMS": "Ability to edit any claim",
    "DELETE_CLAIMS": "Ability to delete claims",
    "ASSIGN_CLAIMS": "Ability to assign claims to others",
    "UNASSIGN_CLAIMS": "Ability to unassign claims from users",
    "CLOSE_CLAIMS": "Ability to close claims",
    "CHANGE_CLAIM_STATUS": "Ability to change claim status",
    "VIEW_ASSIGNED_CLAIMS": "Ability to view assigned claims",
    "VIEW_ALL_CLAIMS": "Ability to view all claims",
    # Documents
    "VIEW_OWN_DOCUMENTS": "Ability to view own documents",
    "VIEW_ASSIGNED_DOCUMENTS": "Ability to view documents of assigned claims",
    "VIEW_ALL_DOCUMENTS": "Ability to view all documents",
    "UPLOAD_OWN_DOCUMENTS": "Ability to upload documents to own claims",
    "UPLOAD_DOCUMENTS": "Ability to upload documents to any claim",
    "DELETE_DOCUMENTS": "Ability to delete documents",
    "MANAGE_DOCUMENT_CATEGORIES": "Ability to manage document categories",
    "VIEW_CLAIM_DOCUMENTS": "Ability to view documents of specific claims",
    "UPLOAD_CLAIM_DOCUMENTS": "Ability to upload documents to specific claims",
    "EDIT_CLAIM_DOCUMENTS": "Ability to edit documents of specific claims",
    "DELETE_CLAIM_DOCUMENTS": "Ability to delete documents of specific claims",
    # Profile & Users
    "VIEW_OWN_PROFILE": "Ability to view own profile",
    "EDIT_OWN_PROFILE": "Ability to edit own profile",
    "VIEW_USERS": "Ability to view user list",
    "CREATE_USERS": "Ability to create new users",
    "EDIT_USERS": "Ability to edit users",
    "DELETE_USERS": "Ability to delete users",
    "MANAGE_ROLES": "Ability to manage user roles",
    # Clients
    "VIEW_CLIENTS": "Ability to view client information",
    "CREATE_CLIENTS": "Ability to create new clients",
    "EDIT_CLIENTS": "Ability to edit client information",
    "DELETE_CLIENTS": "Ability to delete clients",
    # Communications & Tasks
    "SEND_COMMUNICATIONS": "Ability to send communications",
    "MANAGE_COMMUNICATIONS": "Ability to manage all communications",
    "MANAGE_TASKS": "Ability to manage tasks",
    # Tasks - Granular
    "CREATE_TASK": "Ability to create tasks",
    "VIEW_ASSIGNED_TASKS": "Ability to view tasks assigned to self or created by self",
    "VIEW_ALL_TASKS": "Ability to view all tasks",
    "EDIT_ASSIGNED_TASKS": "Ability to edit tasks assigned to self or created by self",
    "EDIT_ALL_TASKS": "Ability to edit all tasks",
    "ASSIGN_TASK": "Ability to assign/reassign tasks",
    "DELETE_TASK": "Ability to delete tasks",
    "VIEW_TASKS": "Ability to view tasks",
    "LIST_TASKS": "Ability to list tasks",
    "UPDATE_TASKS": "Ability to update task details",
    "CHANGE_TASK_STATUS": "Ability to change task status",
    # Financial Operations
    "VIEW_CLAIM_FINANCIALS": "Ability to view claim financials",
    "SET_INITIAL_RESERVE": "Ability to set initial claim reserves",
    "UPDATE_RESERVES": "Ability to update claim reserves",
    "PROCESS_PAYMENTS": "Ability to process claim payments",
    "OVERRIDE_FINANCIAL_LIMITS": "Ability to override financial limits",
    # Metrics
    "VIEW_METRICS": "Ability to view system metrics and KPIs",
    # Reports & Analytics
    "VIEW_REPORTS": "Ability to view standard reports",
    "VIEW_BASIC_ANALYTICS": "Ability to view basic analytics",
    "VIEW_ADVANCED_ANALYTICS": "Ability to view advanced analytics",
    "CONFIGURE_REPORTS": "Ability to configure and create reports",
    "MANAGE_WORKFLOWS": "Ability to manage claim workflows",
    # System & Security
    "SYSTEM_CONFIGURATION": "Ability to modify system configuration",
    "MANAGE_PERMISSIONS": "Ability to manage system permissions",
    "VIEW_AUDIT_LOGS": "Ability to view audit logs",
    "MANAGE_API_KEYS": "Ability to manage API keys",
    "MANAGE_SECURITY_SETTINGS": "Ability to manage security settings",
    "VIEW_SECURITY_LOGS": "Ability to view security logs",
}


# Initial authority levels with limits
INITIAL_AUTHORITY_LEVELS = {
    AuthorityRole.UNLIMITED: {
        "reserve_limit": Decimal("1000000"),  # $1M
        "payment_limit": Decimal("1000000"),  # $1M
        "description": "Unlimited authority for all financial operations",
    },
    AuthorityRole.MANAGER: {
        "reserve_limit": Decimal("750000"),  # $750K
        "payment_limit": Decimal("750000"),  # $750K
        "description": "High authority for most financial operations",
    },
    AuthorityRole.SUPERVISOR: {
        "reserve_limit": Decimal("500000"),  # $500K
        "payment_limit": Decimal("500000"),  # $500K
        "description": "Supervisory authority for significant financial operations",
    },
    AuthorityRole.SENIOR: {
        "reserve_limit": Decimal("250000"),  # $250K
        "payment_limit": Decimal("250000"),  # $250K
        "description": "Senior level authority for substantial financial operations",
    },
    AuthorityRole.INTERMEDIATE: {
        "reserve_limit": Decimal("100000"),  # $100K
        "payment_limit": Decimal("100000"),  # $100K
        "description": "Intermediate authority for moderate financial operations",
    },
    AuthorityRole.BASIC: {
        "reserve_limit": Decimal("25000"),  # $25K
        "payment_limit": Decimal("25000"),  # $25K
        "description": "Basic authority for routine financial operations",
    },
    AuthorityRole.NO_AUTHORITY: {
        "reserve_limit": Decimal("0"),  # $0
        "payment_limit": Decimal("0"),  # $0
        "description": "No financial authority",
    },
}


# Initial clients
INITIAL_CLIENTS = [
    {
        "name": "Selective Insurance",
        "prefix": "SLCT",
        "description": "Selective Insurance Company of America",
    },
    {
        "name": "Progressive",
        "prefix": "PROG",
        "description": "Progressive Casualty Insurance Company",
    },
    {
        "name": "Travelers",
        "prefix": "TRAV",
        "description": "The Travelers Companies, Inc.",
    },
]

# Initial reserve configurations
INITIAL_RESERVE_CONFIGS = [
    # Auto Claim Configurations
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.BODILY_INJURY,
        "is_required": True,
        "minimum_amount": Decimal("5000"),
        "description": "Required bodily injury reserve for auto claims",
    },
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.PROPERTY_DAMAGE,
        "is_required": True,
        "minimum_amount": Decimal("2500"),
        "description": "Required property damage reserve for auto claims",
    },
    {
        "claim_type": ClaimType.AUTO,
        "reserve_type": ReserveType.MEDICAL_PAYMENTS,
        "is_required": False,
        "minimum_amount": Decimal("1000"),
        "description": "Optional medical payments reserve for auto claims",
    },
    # Property Claim Configurations
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.PROPERTY_DAMAGE,
        "is_required": True,
        "minimum_amount": Decimal("5000"),
        "description": "Required property damage reserve",
    },
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.LOSS_OF_USE,
        "is_required": False,
        "minimum_amount": Decimal("1000"),
        "description": "Optional loss of use reserve for property claims",
    },
    {
        "claim_type": ClaimType.PROPERTY,
        "reserve_type": ReserveType.BUSINESS_INTERRUPTION,
        "is_required": False,
        "minimum_amount": None,
        "description": "Optional business interruption reserve for commercial property",
    },
]


def insert_initial_data() -> None:
    """Insert initial permissions into the database."""
    # Create engine without pooling for setup
    engine = create_engine(
        str(settings.POSTGRES_URL),
        echo=True,  # Log all SQL
        pool_pre_ping=True,
    )

    try:
        with Session(engine) as session:
            # Insert permissions
            print("\nInserting permissions...")
            existing_permissions = {p.name: p for p in session.scalars(select(Permission)).all()}

            # Get all permissions from roles
            all_permissions = get_all_permissions()

            # Validate all role permissions are defined
            undefined = all_permissions - PERMISSION_DESCRIPTIONS.keys()
            if undefined:
                # Temporary: Allow MANAGE_TASKS to be undefined if granular exist
                granular_tasks = {
                    "CREATE_TASK",
                    "VIEW_ASSIGNED_TASKS",
                    "VIEW_ALL_TASKS",
                    "EDIT_ASSIGNED_TASKS",
                    "EDIT_ALL_TASKS",
                    "ASSIGN_TASK",
                    "DELETE_TASK",
                }
                truly_undefined = undefined - {"MANAGE_TASKS"}
                if truly_undefined:
                    print(f"❌ Error: Found undefined permissions: {truly_undefined}")
                    sys.exit(1)
                else:
                    print("ℹ️ Note: Ignoring undefined 'MANAGE_TASKS' as granular task permissions are defined.")
            else:
                print("✅ All permissions defined in descriptions.")

            # Create new permissions
            for name, description in PERMISSION_DESCRIPTIONS.items():
                if name not in existing_permissions:
                    permission = Permission(name=name, description=description)
                    session.add(permission)
                    print(f"✅ Created permission: {name}")
                else:
                    print(f"ℹ️ Permission already exists: {name}")

            # Insert authority levels
            print("\nInserting authority levels...")
            existing_authorities = {a.role: a for a in session.scalars(select(AuthorityLevel)).all()}

            # Create new authority levels
            for role, data in INITIAL_AUTHORITY_LEVELS.items():
                if role not in existing_authorities:
                    authority = AuthorityLevel(
                        role=role,
                        reserve_limit=data["reserve_limit"],
                        payment_limit=data["payment_limit"],
                        description=data["description"],
                    )
                    session.add(authority)
                    print(f"✅ Created authority level: {role}")
                else:
                    print(f"ℹ️ Authority level already exists: {role}")

            # Insert clients
            print("\nInserting clients...")
            existing_clients = {c.prefix: c for c in session.scalars(select(Client)).all()}

            # Create new clients
            for client_data in INITIAL_CLIENTS:
                prefix = client_data["prefix"]
                if prefix not in existing_clients:
                    client = Client(**client_data)
                    session.add(client)
                    print(f"✅ Created client: {client.name} ({client.prefix})")
                else:
                    print(f"ℹ️ Client already exists: {prefix}")

            # Insert reserve configurations
            print("\nInserting reserve configurations...")
            existing_configs = {
                (c.claim_type, c.reserve_type): c for c in session.scalars(select(ReserveConfiguration)).all()
            }

            # Create new configurations
            for config_data in INITIAL_RESERVE_CONFIGS:
                key = (config_data["claim_type"], config_data["reserve_type"])
                if key not in existing_configs:
                    config = ReserveConfiguration(**config_data)
                    session.add(config)
                    print(f"✅ Created reserve config: {config.claim_type}:{config.reserve_type}")
                else:
                    print(f"ℹ️ Reserve config already exists: {key[0]}:{key[1]}")

            session.commit()
            print("\n✅ Initial data inserted successfully!")

            # Print role permissions summary
            print("\nRole Permissions Summary:")
            for role in UserRole:
                perms = ROLE_PERMISSIONS.get(role, [])
                print(f"\n{role}:")
                for perm in sorted(perms):
                    print(f"  - {perm}")

            # Print authority levels summary
            print("\nAuthority Levels Summary:")
            for role, data in INITIAL_AUTHORITY_LEVELS.items():
                print(f"\n{role}:")
                print(f"  - Reserve Limit: ${data['reserve_limit']:,.2f}")
                print(f"  - Payment Limit: ${data['payment_limit']:,.2f}")
                print(f"  - Description: {data['description']}")

            # Print clients summary
            print("\nClients Summary:")
            for client in session.scalars(select(Client)):
                print(f"\n{client.name}:")
                print(f"  - Prefix: {client.prefix}")
                print(f"  - Description: {client.description}")

            # Print reserve configurations summary
            print("\nReserve Configurations Summary:")
            for config in session.scalars(select(ReserveConfiguration)):
                print(f"\n{config.claim_type}:{config.reserve_type}:")
                print(f"  - Required: {config.is_required}")
                print(f"  - Minimum: ${config.minimum_amount:,.2f}" if config.minimum_amount else "  - Minimum: None")
                print(f"  - Description: {config.description}")

    except Exception as e:
        print(f"❌ Error inserting initial data: {e}")
        sys.exit(1)
    finally:
        engine.dispose()


if __name__ == "__main__":
    insert_initial_data()
