"""<PERSON><PERSON><PERSON> to create a superuser account."""

import os
import sys

import bcrypt
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from claimentine.core.config import settings
from claimentine.models.associations import UserPermission
from claimentine.models.auth import Permission
from claimentine.models.authority import AuthorityRole
from claimentine.models.user import User, UserR<PERSON>, User<PERSON>tatus


def create_superuser():
    """Create a superuser with all permissions."""
    email = os.getenv("SUPERUSER_EMAIL", "<EMAIL>")
    password = os.getenv("SUPERUSER_PASSWORD", "admin")
    first_name = os.getenv("SUPERUSER_FIRST_NAME", "Admin")
    last_name = os.getenv("SUPERUSER_LAST_NAME", "User")

    print("\nCreating superuser...")
    print(f"Email: {email}")

    # Create database engine and session
    engine = create_engine(str(settings.POSTGRES_URL))
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Check if superuser already exists
        existing_user = session.query(User).filter(User.email == email).first()
        if existing_user:
            print(f"⚠️ Superuser with email {email} already exists!")
            return

        # Hash password
        try:
            password_hash = bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()
        except Exception as e:
            print(f"❌ Failed to hash password: {str(e)}")
            return

        # Create superuser
        superuser = User(
            email=email,
            first_name=first_name,
            last_name=last_name,
            role=UserRole.ADMIN,
            authority_role=AuthorityRole.UNLIMITED,  # Give admin unlimited authority
            status=UserStatus.ACTIVE,
            password_hash=password_hash,
            email_verified=True,
        )

        # Add user first to get an ID
        session.add(superuser)
        session.flush()

        # Get all permissions
        all_permissions = session.query(Permission).all()
        if not all_permissions:
            print("❌ No permissions found in database. Run setup_db.py first.")
            return

        # Create UserPermission associations
        for permission in all_permissions:
            user_permission = UserPermission(
                user_id=superuser.id,
                permission_id=permission.id,
                granted_by=superuser.id,  # Self-granted for superuser
            )
            session.add(user_permission)

        # Commit all changes
        session.commit()

        print("✅ Superuser created successfully!")
        print(f"Email: {email}")
        print(f"Password: {password}")

    except Exception as e:
        print(f"❌ Failed to create superuser: {str(e)}")
        session.rollback()
        sys.exit(1)  # Exit with error code
    finally:
        session.close()


if __name__ == "__main__":
    create_superuser()
