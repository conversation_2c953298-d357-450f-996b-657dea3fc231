"""Database session management."""

import logging
from typing import Generator

from sqlalchemy import create_engine, text
from sqlalchemy.orm import Session, sessionmaker

from claimentine.core.config import settings

# Get logger for this module
logger = logging.getLogger(__name__)

# Log database connection attempt
logger.info(f"Initializing database engine for host: {settings.POSTGRES_HOST}")

# Create database engine
engine = create_engine(
    str(settings.POSTGRES_URL),
    pool_pre_ping=True,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    echo=settings.DB_ECHO_LOG,
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """Get a database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_sync_db() -> Generator[Session, None, None]:
    """Get a synchronous database session.

    This is used by async functions that need to call synchronous code.
    """
    return get_db()
