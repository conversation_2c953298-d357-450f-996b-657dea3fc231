"""Note model for storing claim-related notes."""

from datetime import datetime
from uuid import UUID

from sqlalchemy import ForeignKey, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class Note(Base):
    """Note model for storing claim-related notes."""

    __tablename__ = "notes"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)
    content: Mapped[str] = mapped_column(Text, nullable=False)
    created_by: Mapped[UUID] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="notes")
    author = relationship("User", back_populates="notes")

    def __repr__(self) -> str:
        """String representation of Note."""
        return f"<Note {self.id} for Claim {self.claim_id}>"
