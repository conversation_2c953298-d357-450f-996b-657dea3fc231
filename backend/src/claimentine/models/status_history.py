"""Status history model for tracking claim status changes."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy import Foreign<PERSON>ey, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class ClaimStatusHistory(Base):
    """Model for tracking claim status changes."""

    __tablename__ = "claim_status_history"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)
    from_status: Mapped[Optional[str]] = mapped_column(String(50))  # Null for initial status
    to_status: Mapped[str] = mapped_column(String(50), nullable=False)
    reason: Mapped[Optional[str]] = mapped_column(Text)
    changed_by: Mapped[UUID] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))

    # Relationships
    claim = relationship("BaseClaim", back_populates="status_history")
    user = relationship("User", back_populates="status_changes")

    def __repr__(self) -> str:
        """String representation of ClaimStatusHistory."""
        return f"<ClaimStatusHistory {self.from_status} -> {self.to_status}>"
