"""Claim models package."""

from claimentine.models.claim.base import BaseClaim, ClaimStatus, ClaimType
from claimentine.models.claim.damage import DamagedPropertyAsset, DamageInstance, PropertyAssetType, RepairStatus
from claimentine.models.claim.details import (
    AutoDetails,
    AutoIncidentType,
    AutoPropertyDamage,
    BodilyInjuryDetails,
    CollisionType,
    CompletedOperationsDetails,
    DamageType,
    GeneralLiabilityDetails,
    GeneralLiabilityIncidentType,
    InjuredPerson,
    InjuredPersonType,
    Injury,
    PersonalAdvertisingInjuryDetails,
    PointOfImpact,
    PremisesLiabilityDetails,
    ProductsLiabilityDetails,
    PropertyDetails,
    PropertyType,
    USState,
)
from claimentine.models.claim.financial import (
    ClaimFinancials,
    ClaimReserve,
    Payment,
    PaymentType,
    ReserveHistory,
    ReserveType,
)
from claimentine.models.claim.types import AutoClaim, GeneralLiabilityClaim, PropertyClaim

__all__ = [
    # Base claim models
    "BaseClaim",
    "ClaimType",
    "ClaimStatus",
    "AutoClaim",
    "PropertyClaim",
    "GeneralLiabilityClaim",
    # Detail models
    "AutoDetails",
    "AutoPropertyDamage",
    "PropertyDetails",
    "GeneralLiabilityDetails",
    "PremisesLiabilityDetails",
    "ProductsLiabilityDetails",
    "CompletedOperationsDetails",
    "PersonalAdvertisingInjuryDetails",
    "BodilyInjuryDetails",
    "InjuredPerson",
    "Injury",
    # Damage models
    "DamagedPropertyAsset",
    "DamageInstance",
    # Financial models
    "ClaimFinancials",
    "ClaimReserve",
    "Payment",
    "ReserveHistory",
    # Enums
    "PropertyType",
    "DamageType",
    "USState",
    "AutoIncidentType",
    "CollisionType",
    "PointOfImpact",
    "GeneralLiabilityIncidentType",
    "InjuredPersonType",
    "PaymentType",
    "ReserveType",
    "PropertyAssetType",
    "RepairStatus",
]
