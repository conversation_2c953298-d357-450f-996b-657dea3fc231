"""Type-specific claim models."""

from claimentine.core.exceptions import ValidationError
from claimentine.models.claim.base import BaseClaim, ClaimType


class AutoClaim(BaseClaim):
    """Auto claim model."""

    __mapper_args__ = {
        "polymorphic_identity": ClaimType.AUTO,
    }

    def __init__(self, **kwargs):
        """Initialize auto claim."""
        # Ensure type is always AUTO
        kwargs["type"] = ClaimType.AUTO
        super().__init__(**kwargs)

    def validate_auto_details(self) -> None:
        """Validate auto-specific details."""
        if not self.auto_details:
            return

        # Example validation: VIN format if provided
        if self.auto_details.vehicle_vin:
            if len(self.auto_details.vehicle_vin) != 17:
                raise ValidationError("VIN must be 17 characters")


class PropertyClaim(BaseClaim):
    """Property claim model."""

    __mapper_args__ = {
        "polymorphic_identity": ClaimType.PROPERTY,
    }

    def __init__(self, **kwargs):
        """Initialize property claim."""
        # Ensure type is always PROPERTY
        kwargs["type"] = ClaimType.PROPERTY
        super().__init__(**kwargs)

    def validate_property_details(self) -> None:
        """Validate property-specific details."""
        if not self.property_details:
            return

        # Example validation: ZIP code format if provided
        if self.property_details.property_zip:
            if not (len(self.property_details.property_zip) == 5 or len(self.property_details.property_zip) == 10):
                raise ValidationError("ZIP code must be 5 or 10 characters")


class GeneralLiabilityClaim(BaseClaim):
    """General Liability claim model."""

    __mapper_args__ = {
        "polymorphic_identity": ClaimType.GENERAL_LIABILITY,
    }

    def __init__(self, **kwargs):
        """Initialize General Liability claim."""
        # Ensure type is always GENERAL_LIABILITY
        kwargs["type"] = ClaimType.GENERAL_LIABILITY
        super().__init__(**kwargs)

    def validate_gl_details(self) -> None:
        """Validate GL-specific details."""
        if not self.gl_details:
            return

        # Basic validation can be added here
        # For example, checking that incident type is set
        pass
