"""Claim financial model."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import ForeignKey, String, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.core.exceptions import ValidationError
from claimentine.db.base_class import Base


class ReserveType(str, Enum):
    """Types of reserves."""

    # Indemnity Types
    BODILY_INJURY = "BODILY_INJURY"
    PROPERTY_DAMAGE = "PROPERTY_DAMAGE"
    MEDICAL_PAYMENTS = "MEDICAL_PAYMENTS"
    LOSS_OF_USE = "LOSS_OF_USE"
    BUSINESS_INTERRUPTION = "BUSINESS_INTERRUPTION"
    # Expense Types
    DEFENSE_COST = "DEFENSE_COST"
    ALLOCATED_EXPENSE = "ALLOCATED_EXPENSE"
    UNALLOCATED_EXPENSE = "UNALLOCATED_EXPENSE"


class PaymentType(str, Enum):
    """Types of payments."""

    INDEMNITY = "INDEMNITY"
    EXPENSE = "EXPENSE"
    DEFENSE = "DEFENSE"  # New payment type for defense costs


class ClaimReserve(Base):
    """Individual reserve entry for a claim."""

    __tablename__ = "claim_reserves"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    financials_id: Mapped[UUID] = mapped_column(ForeignKey("claim_financials.id", ondelete="CASCADE"), nullable=False)
    reserve_type: Mapped[ReserveType] = mapped_column(nullable=False)
    amount: Mapped[Decimal] = mapped_column(nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    financials = relationship("ClaimFinancials", back_populates="reserves")

    def __repr__(self) -> str:
        """String representation."""
        return f"<ClaimReserve {self.reserve_type}: {self.amount}>"


class ClaimFinancials(Base):
    """Financial details for a claim."""

    __tablename__ = "claim_financials"

    # Required fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)
    estimated_value: Mapped[Decimal] = mapped_column(nullable=False)

    # Payment fields
    indemnity_paid: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    expense_paid: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    defense_paid: Mapped[Optional[Decimal]] = mapped_column(nullable=True)  # New field for defense costs
    recovery_expected: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    recovery_received: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    deductible_amount: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    coverage_limit: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    currency: Mapped[str] = mapped_column(String(3), nullable=False, default="USD")

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )
    last_reserve_change: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    last_payment_date: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Relationships
    claim = relationship("BaseClaim", back_populates="financials")
    reserves: Mapped[List["ClaimReserve"]] = relationship(
        "ClaimReserve", back_populates="financials", cascade="all, delete-orphan"
    )
    payments: Mapped[List["Payment"]] = relationship(
        "Payment", back_populates="financials", cascade="all, delete-orphan", order_by="Payment.payment_date"
    )

    def validate_financials(self) -> None:
        """Validate financial data beyond basic field constraints.

        Focus on business rules and cross-field validations.
        """
        # Financial consistency rules
        if (
            self.recovery_received is not None
            and self.recovery_expected is not None
            and self.recovery_received > self.recovery_expected
        ):
            raise ValidationError("Recovery received cannot exceed recovery expected")

        # Ensure all numeric values are non-negative when provided
        for field_name in [
            "indemnity_paid",
            "expense_paid",
            "defense_paid",  # Add new field to validation list
            "recovery_expected",
            "recovery_received",
            "deductible_amount",
            "coverage_limit",
        ]:
            value = getattr(self, field_name)
            if value is not None and value < 0:
                raise ValidationError(f"{field_name} cannot be negative")

        # Ensure estimated value is always non-negative
        if self.estimated_value < 0:
            raise ValidationError("Estimated value cannot be negative")

    # Properties
    @property
    def claim_number(self) -> str:
        """Get the claim number."""
        return self.claim.claim_number if self.claim else None

    @property
    def total_reserves(self) -> Decimal:
        """Get total reserves across all types."""
        return sum((reserve.amount for reserve in self.reserves), Decimal(0))

    @property
    def total_paid(self) -> Decimal:
        """Get total payments across all types."""
        return sum(
            x
            for x in [
                self.indemnity_paid or Decimal(0),
                self.expense_paid or Decimal(0),
                self.defense_paid or Decimal(0),
            ]
        )

    def get_reserve_by_type(self, reserve_type: ReserveType) -> Optional[Decimal]:
        """Get reserve amount for a specific type."""
        reserve = next((r for r in self.reserves if r.reserve_type == reserve_type), None)
        return reserve.amount if reserve else None

    def __repr__(self) -> str:
        """String representation of ClaimFinancials."""
        return f"<ClaimFinancials for claim {self.claim_id}>"


class ReserveHistory(Base):
    """History of reserve changes."""

    __tablename__ = "reserve_history"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(nullable=False)
    financials_id: Mapped[UUID] = mapped_column(nullable=False)
    reserve_type: Mapped[ReserveType] = mapped_column(nullable=False)
    previous_amount: Mapped[Decimal] = mapped_column(nullable=False)
    new_amount: Mapped[Decimal] = mapped_column(nullable=False)
    changed_by_id: Mapped[UUID] = mapped_column(nullable=False)
    changed_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    notes: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)


class Payment(Base):
    """Individual payment entry for a claim."""

    __tablename__ = "payments"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    financials_id: Mapped[UUID] = mapped_column(ForeignKey("claim_financials.id", ondelete="CASCADE"), nullable=False)
    payment_type: Mapped[PaymentType] = mapped_column(nullable=False)
    amount: Mapped[Decimal] = mapped_column(nullable=False)
    payee: Mapped[str] = mapped_column(String(255), nullable=False)
    payment_date: Mapped[datetime] = mapped_column(nullable=False)
    notes: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    created_by_id: Mapped[UUID] = mapped_column(ForeignKey("users.id"), nullable=False)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    financials = relationship("ClaimFinancials", back_populates="payments")
    created_by = relationship("User")

    def __repr__(self) -> str:
        """String representation."""
        return f"<Payment {self.payment_type}: {self.amount} to {self.payee} on {self.payment_date}>"
