"""Type-specific detail models for claims."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, CheckConstraint, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class PointOfImpact(str, Enum):
    """Vehicle impact location."""

    FRONT = "FRONT"
    REAR = "REAR"
    DRIVER_SIDE = "DRIVER_SIDE"
    PASSENGER_SIDE = "PASSENGER_SIDE"
    ROOF = "ROOF"
    UNDERCARRIAGE = "UNDERCARRIAGE"
    MULTIPLE = "MULTIPLE"


# New Enum for Auto Incident Type
class AutoIncidentType(str, Enum):
    """Type of auto incident."""

    # Collision-related
    COLLISION = "COLLISION"

    # Comprehensive coverage incidents
    COMPREHENSIVE = "COMPREHENSIVE"
    THEFT = "THEFT"
    VANDALISM = "VANDALISM"
    FIRE = "FIRE"
    FLOOD = "FLOOD"
    HAIL = "HAIL"
    FALLEN_OBJECT = "FALLEN_OBJECT"
    ANIMAL_COLLISION = "ANIMAL_COLLISION"

    # Commercial-specific
    CARGO_LOSS = "CARGO_LOSS"
    CARGO_THEFT = "CARGO_THEFT"
    CARGO_DAMAGE = "CARGO_DAMAGE"
    TRAILER_DAMAGE = "TRAILER_DAMAGE"
    EQUIPMENT_DAMAGE = "EQUIPMENT_DAMAGE"

    # Liability-related
    THIRD_PARTY_INJURY = "THIRD_PARTY_INJURY"
    THIRD_PARTY_PROPERTY_DAMAGE = "THIRD_PARTY_PROPERTY_DAMAGE"

    # Other
    MECHANICAL_BREAKDOWN = "MECHANICAL_BREAKDOWN"
    OTHER = "OTHER"


# New Enum for Collision Type
class CollisionType(str, Enum):
    """Type of collision."""

    REAR_END = "REAR_END"
    TURNING = "TURNING"
    HEAD_ON = "HEAD_ON"
    BACKING = "BACKING"
    CONTROLLED_INTERSECTION = "CONTROLLED_INTERSECTION"
    NON_CONTROLLED_INTERSECTION = "NON_CONTROLLED_INTERSECTION"
    SIDESWIPE = "SIDESWIPE"
    LANE_CHANGE = "LANE_CHANGE"
    OTHER = "OTHER"


class PropertyType(str, Enum):
    """Type of property."""

    # Residential
    RESIDENTIAL = "RESIDENTIAL"
    SINGLE_FAMILY = "SINGLE_FAMILY"
    MULTI_FAMILY = "MULTI_FAMILY"
    CONDOMINIUM = "CONDOMINIUM"
    APARTMENT_BUILDING = "APARTMENT_BUILDING"

    # Commercial
    COMMERCIAL = "COMMERCIAL"
    OFFICE_BUILDING = "OFFICE_BUILDING"
    RETAIL_STORE = "RETAIL_STORE"
    SHOPPING_CENTER = "SHOPPING_CENTER"
    RESTAURANT = "RESTAURANT"
    HOTEL_MOTEL = "HOTEL_MOTEL"
    WAREHOUSE = "WAREHOUSE"
    MANUFACTURING_FACILITY = "MANUFACTURING_FACILITY"
    MEDICAL_FACILITY = "MEDICAL_FACILITY"
    EDUCATIONAL_FACILITY = "EDUCATIONAL_FACILITY"

    # Industrial
    INDUSTRIAL = "INDUSTRIAL"
    FACTORY = "FACTORY"
    DISTRIBUTION_CENTER = "DISTRIBUTION_CENTER"
    PROCESSING_FACILITY = "PROCESSING_FACILITY"

    # Other
    VACANT_LAND = "VACANT_LAND"
    AGRICULTURAL = "AGRICULTURAL"
    MIXED_USE = "MIXED_USE"
    OTHER = "OTHER"


class USState(str, Enum):
    """US States."""

    AL = "AL"
    AK = "AK"
    AZ = "AZ"
    AR = "AR"
    CA = "CA"
    CO = "CO"
    CT = "CT"
    DE = "DE"
    FL = "FL"
    GA = "GA"
    HI = "HI"
    ID = "ID"
    IL = "IL"
    IN = "IN"
    IA = "IA"
    KS = "KS"
    KY = "KY"
    LA = "LA"
    ME = "ME"
    MD = "MD"
    MA = "MA"
    MI = "MI"
    MN = "MN"
    MS = "MS"
    MO = "MO"
    MT = "MT"
    NE = "NE"
    NV = "NV"
    NH = "NH"
    NJ = "NJ"
    NM = "NM"
    NY = "NY"
    NC = "NC"
    ND = "ND"
    OH = "OH"
    OK = "OK"
    OR = "OR"
    PA = "PA"
    RI = "RI"
    SC = "SC"
    SD = "SD"
    TN = "TN"
    TX = "TX"
    UT = "UT"
    VT = "VT"
    VA = "VA"
    WA = "WA"
    WV = "WV"
    WI = "WI"
    WY = "WY"
    UNKNOWN = "UNKNOWN"


class DamageType(str, Enum):
    """Types of property damage."""

    WATER = "WATER"
    FIRE = "FIRE"
    WIND = "WIND"
    HAIL = "HAIL"
    THEFT = "THEFT"
    VANDALISM = "VANDALISM"
    STRUCTURAL = "STRUCTURAL"
    ELECTRICAL = "ELECTRICAL"
    SMOKE = "SMOKE"
    COLLISION = "COLLISION"
    EXPLOSION = "EXPLOSION"
    MACHINERY_BREAKDOWN = "MACHINERY_BREAKDOWN"
    FREEZING = "FREEZING"
    MOLD = "MOLD"
    LANDSLIDE = "LANDSLIDE"
    FLOOD = "FLOOD"
    EARTHQUAKE = "EARTHQUAKE"
    LIGHTNING = "LIGHTNING"
    WEIGHT_OF_ICE_SNOW = "WEIGHT_OF_ICE_SNOW"
    CIVIL_DISTURBANCE = "CIVIL_DISTURBANCE"
    OTHER = "OTHER"


class GeneralLiabilityIncidentType(str, Enum):
    """General Liability incident types."""

    # Premises Liability
    PREMISES_LIABILITY = "PREMISES_LIABILITY"
    SLIP_AND_FALL = "SLIP_AND_FALL"
    TRIP_AND_FALL = "TRIP_AND_FALL"
    FALLING_OBJECT = "FALLING_OBJECT"
    INADEQUATE_SECURITY = "INADEQUATE_SECURITY"
    NEGLIGENT_MAINTENANCE = "NEGLIGENT_MAINTENANCE"

    # Products Liability
    PRODUCTS_LIABILITY = "PRODUCTS_LIABILITY"
    DEFECTIVE_DESIGN = "DEFECTIVE_DESIGN"
    MANUFACTURING_DEFECT = "MANUFACTURING_DEFECT"
    FAILURE_TO_WARN = "FAILURE_TO_WARN"
    PRODUCT_RECALL = "PRODUCT_RECALL"

    # Operations Liability
    COMPLETED_OPERATIONS = "COMPLETED_OPERATIONS"
    ONGOING_OPERATIONS = "ONGOING_OPERATIONS"
    CONTRACTOR_ERROR = "CONTRACTOR_ERROR"
    PROFESSIONAL_NEGLIGENCE = "PROFESSIONAL_NEGLIGENCE"

    # Damage Types
    THIRD_PARTY_PROPERTY_DAMAGE = "THIRD_PARTY_PROPERTY_DAMAGE"
    THIRD_PARTY_BODILY_INJURY = "THIRD_PARTY_BODILY_INJURY"

    # Other Types
    PERSONAL_ADVERTISING_INJURY = "PERSONAL_ADVERTISING_INJURY"
    LIBEL_SLANDER = "LIBEL_SLANDER"
    COPYRIGHT_INFRINGEMENT = "COPYRIGHT_INFRINGEMENT"
    MEDICAL_PAYMENTS = "MEDICAL_PAYMENTS"
    ENVIRONMENTAL_DAMAGE = "ENVIRONMENTAL_DAMAGE"
    CYBER_LIABILITY = "CYBER_LIABILITY"
    FOOD_CONTAMINATION = "FOOD_CONTAMINATION"
    OTHER = "OTHER"


class InjuredPersonType(str, Enum):
    """Types of injured persons."""

    # General Liability specific
    GUEST = "GUEST"
    PATRON = "PATRON"
    EMPLOYEE = "EMPLOYEE"
    TENANT = "TENANT"
    CONTRACTOR = "CONTRACTOR"
    VENDOR = "VENDOR"
    TRESPASSER = "TRESPASSER"
    VISITOR = "VISITOR"
    CUSTOMER = "CUSTOMER"
    SUBCONTRACTOR = "SUBCONTRACTOR"
    DELIVERY_PERSON = "DELIVERY_PERSON"
    SERVICE_PROVIDER = "SERVICE_PROVIDER"
    PROPERTY_OWNER = "PROPERTY_OWNER"
    STUDENT = "STUDENT"
    PATIENT = "PATIENT"

    # Auto specific
    DRIVER = "DRIVER"
    INSURED_DRIVER = "INSURED_DRIVER"
    THIRD_PARTY_DRIVER = "THIRD_PARTY_DRIVER"
    PASSENGER = "PASSENGER"
    PEDESTRIAN = "PEDESTRIAN"
    CYCLIST = "CYCLIST"
    OTHER_DRIVER = "OTHER_DRIVER"
    OTHER_PASSENGER = "OTHER_PASSENGER"
    COMMERCIAL_DRIVER = "COMMERCIAL_DRIVER"
    TRUCKER = "TRUCKER"

    # General categories
    BYSTANDER = "BYSTANDER"
    CHILD = "CHILD"
    ELDERLY = "ELDERLY"
    UNKNOWN = "UNKNOWN"
    OTHER = "OTHER"


class IncidentReportStatus(str, Enum):
    """Status of incident reports."""

    FILED = "FILED"
    PENDING = "PENDING"
    NOT_REQUIRED = "NOT_REQUIRED"


class MedicalTreatmentRequirements(str, Enum):
    """Medical treatment requirements."""

    NONE = "NONE"
    FIRST_AID = "FIRST_AID"
    OUTPATIENT = "OUTPATIENT"
    HOSPITALIZATION = "HOSPITALIZATION"
    SURGERY = "SURGERY"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    ONGOING_CARE = "ONGOING_CARE"


class InsuranceBillingStatus(str, Enum):
    """Insurance billing status."""

    NOT_SUBMITTED = "NOT_SUBMITTED"
    PENDING = "PENDING"
    PARTIAL_PAYMENT = "PARTIAL_PAYMENT"
    PAID = "PAID"
    DENIED = "DENIED"


class AutoDetails(Base):
    """Details specific to auto claims."""

    __tablename__ = "auto_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)

    # Vehicle Information
    vehicle_year: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    vehicle_make: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    vehicle_model: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    vehicle_vin: Mapped[Optional[str]] = mapped_column(String(17), nullable=True)
    vehicle_license_plate: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    vehicle_state: Mapped[Optional[USState]] = mapped_column(SQLAlchemyEnum(USState), nullable=True)

    # Driver Information
    driver_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    driver_license_number: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    driver_state: Mapped[Optional[USState]] = mapped_column(SQLAlchemyEnum(USState), nullable=True)
    driver_relationship: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)

    # Accident Details
    point_of_impact: Mapped[Optional[PointOfImpact]] = mapped_column(SQLAlchemyEnum(PointOfImpact), nullable=True)
    airbags_deployed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    vehicle_driveable: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    towed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    tow_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # New fields for auto claim enhancements
    # Incident type classification
    incident_type: Mapped[Optional[AutoIncidentType]] = mapped_column(SQLAlchemyEnum(AutoIncidentType), nullable=True)

    # Collision type categorization (only applicable if incident_type is COLLISION)
    collision_type: Mapped[Optional[CollisionType]] = mapped_column(SQLAlchemyEnum(CollisionType), nullable=True)

    # Passenger details tracking
    passenger_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    passenger_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Cargo theft tracking
    cargo_theft: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    cargo_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Property damage indicators (detailed property damage will be in a separate related model)
    has_property_damage: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=text("CURRENT_TIMESTAMP"),
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="auto_details")
    bodily_injury_details: Mapped[Optional["BodilyInjuryDetails"]] = relationship(
        "BodilyInjuryDetails",
        back_populates="auto_details",
        uselist=False,
        foreign_keys="BodilyInjuryDetails.auto_details_id",
    )
    # New relationship for injured persons
    injured_persons: Mapped[List["InjuredPerson"]] = relationship(
        "InjuredPerson",
        back_populates="auto_details",
        cascade="all, delete-orphan",
        foreign_keys="InjuredPerson.auto_details_id",
    )
    # Existing relationship for property damage
    property_damage: Mapped[Optional["AutoPropertyDamage"]] = relationship(
        "AutoPropertyDamage", back_populates="auto_details", uselist=False, cascade="all, delete-orphan"
    )
    # New relationship for damaged property assets
    damaged_property_assets: Mapped[List["DamagedPropertyAsset"]] = relationship(
        "DamagedPropertyAsset",
        back_populates="auto_details",
        cascade="all, delete-orphan",
        foreign_keys="DamagedPropertyAsset.auto_details_id",
    )

    def __repr__(self) -> str:
        """String representation of AutoDetails."""
        return f"<AutoDetails for Claim {self.claim_id}>"


# New model for auto property damage
class AutoPropertyDamage(Base):
    """Property damage details for auto claims."""

    __tablename__ = "auto_property_damage"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    auto_details_id: Mapped[UUID] = mapped_column(ForeignKey("auto_details.id", ondelete="CASCADE"), nullable=False)

    # Property damage details
    damage_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    property_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    property_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    property_owner: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    estimated_damage_value: Mapped[Optional[Decimal]] = mapped_column(nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=text("CURRENT_TIMESTAMP"),
    )

    # Relationships
    auto_details = relationship("AutoDetails", back_populates="property_damage")

    def __repr__(self) -> str:
        """String representation of AutoPropertyDamage."""
        return f"<AutoPropertyDamage for AutoDetails {self.auto_details_id}>"


class PropertyDetails(Base):
    """Details specific to property claims."""

    __tablename__ = "property_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)

    # Property Information
    property_type: Mapped[Optional[PropertyType]] = mapped_column(SQLAlchemyEnum(PropertyType), nullable=True)
    property_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    property_city: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    property_state: Mapped[Optional[USState]] = mapped_column(SQLAlchemyEnum(USState), nullable=True)
    property_zip: Mapped[Optional[str]] = mapped_column(String(10), nullable=True)

    # Damage Information
    damage_type: Mapped[Optional[DamageType]] = mapped_column(SQLAlchemyEnum(DamageType), nullable=True)
    damage_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    affected_areas: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    inhabitable: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    temporary_repairs_needed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Additional Details
    police_report_filed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    police_report_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    emergency_services_called: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=text("CURRENT_TIMESTAMP"),
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="property_details")
    # New relationship for damaged property assets
    damaged_property_assets: Mapped[List["DamagedPropertyAsset"]] = relationship(
        "DamagedPropertyAsset",
        back_populates="property_details",
        cascade="all, delete-orphan",
        foreign_keys="DamagedPropertyAsset.property_details_id",
    )
    # New relationships for bodily injury support
    bodily_injury_details: Mapped[Optional["BodilyInjuryDetails"]] = relationship(
        "BodilyInjuryDetails",
        back_populates="property_details",
        uselist=False,
        foreign_keys="BodilyInjuryDetails.property_details_id",
    )
    injured_persons: Mapped[List["InjuredPerson"]] = relationship(
        "InjuredPerson",
        back_populates="property_details",
        cascade="all, delete-orphan",
        foreign_keys="InjuredPerson.property_details_id",
    )

    def __repr__(self) -> str:
        """String representation of PropertyDetails."""
        return f"<PropertyDetails for Claim {self.claim_id}>"


class PremisesLiabilityDetails(Base):
    """Details specific to Premises Liability incidents."""

    __tablename__ = "premises_liability_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    gl_details_id: Mapped[UUID] = mapped_column(ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=False)

    # Premises Information
    owner_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    owner_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    premises_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    incident_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    is_inside: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    weather_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    third_party_property_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    hazard_signs_present: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    claimant_activity_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    security_cameras_present: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    insured_relationship: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="premises_details")

    def __repr__(self) -> str:
        """String representation of PremisesLiabilityDetails."""
        return f"<PremisesLiabilityDetails for GL Details {self.gl_details_id}>"


class ProductsLiabilityDetails(Base):
    """Details specific to Products Liability incidents."""

    __tablename__ = "products_liability_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    gl_details_id: Mapped[UUID] = mapped_column(ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=False)

    # Product Information
    manufacturer_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    manufacturer_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    product_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    purchase_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    installation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    usage_complies_with_intent: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    product_modified: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    serial_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    warnings_present: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    third_party_materials_used: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    similar_incidents_history: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="products_details")

    def __repr__(self) -> str:
        """String representation of ProductsLiabilityDetails."""
        return f"<ProductsLiabilityDetails for GL Details {self.gl_details_id}>"


class CompletedOperationsDetails(Base):
    """Details specific to Completed Operations incidents."""

    __tablename__ = "completed_operations_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    gl_details_id: Mapped[UUID] = mapped_column(ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=False)

    # Operation Information
    work_type: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    completion_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    customer_acceptance_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    contract_status: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    contract_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    defect_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    problem_detection_timeline: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    repair_attempts: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    repair_descriptions: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    applicable_warranties: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    subcontractor_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    subcontractor_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="operations_details")

    def __repr__(self) -> str:
        """String representation of CompletedOperationsDetails."""
        return f"<CompletedOperationsDetails for GL Details {self.gl_details_id}>"


class PersonalAdvertisingInjuryDetails(Base):
    """Details specific to Personal and Advertising Injury incidents."""

    __tablename__ = "personal_advertising_injury_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    gl_details_id: Mapped[UUID] = mapped_column(ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=False)

    # Injury Information
    injury_nature: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    offense_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    offensive_material_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    publication_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    material_creator: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    material_removed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    removal_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="advertising_details")

    def __repr__(self) -> str:
        """String representation of PersonalAdvertisingInjuryDetails."""
        return f"<PersonalAdvertisingInjuryDetails for GL Details {self.gl_details_id}>"


class GeneralLiabilityDetails(Base):
    """Base details for General Liability claims."""

    __tablename__ = "gl_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)

    # GL incident type
    incident_type: Mapped[GeneralLiabilityIncidentType] = mapped_column(
        SQLAlchemyEnum(GeneralLiabilityIncidentType), nullable=False
    )

    # Common GL fields for all incident types
    # (Additional fields could be added here that are common to all GL claims)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=text("CURRENT_TIMESTAMP"),
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="gl_details")
    premises_details: Mapped[Optional["PremisesLiabilityDetails"]] = relationship(
        "PremisesLiabilityDetails", back_populates="gl_details", uselist=False
    )
    products_details: Mapped[Optional["ProductsLiabilityDetails"]] = relationship(
        "ProductsLiabilityDetails", back_populates="gl_details", uselist=False
    )
    operations_details: Mapped[Optional["CompletedOperationsDetails"]] = relationship(
        "CompletedOperationsDetails", back_populates="gl_details", uselist=False
    )
    advertising_details: Mapped[Optional["PersonalAdvertisingInjuryDetails"]] = relationship(
        "PersonalAdvertisingInjuryDetails", back_populates="gl_details", uselist=False
    )
    bodily_injury_details: Mapped[Optional["BodilyInjuryDetails"]] = relationship(
        "BodilyInjuryDetails",
        back_populates="gl_details",
        uselist=False,
        foreign_keys="BodilyInjuryDetails.gl_details_id",
    )
    # New relationship for injured persons
    injured_persons: Mapped[List["InjuredPerson"]] = relationship(
        "InjuredPerson",
        back_populates="gl_details",
        cascade="all, delete-orphan",
        foreign_keys="InjuredPerson.gl_details_id",
    )
    # New relationship for damaged property assets
    damaged_property_assets: Mapped[List["DamagedPropertyAsset"]] = relationship(
        "DamagedPropertyAsset",
        back_populates="gl_details",
        cascade="all, delete-orphan",
        foreign_keys="DamagedPropertyAsset.gl_details_id",
    )

    def __repr__(self) -> str:
        """String representation of GeneralLiabilityDetails."""
        return f"<GeneralLiabilityDetails for Claim {self.claim_id}>"


class BodilyInjuryDetails(Base):
    """Details specific to Bodily Injury incidents."""

    __tablename__ = "bodily_injury_details"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))

    # Triple parent approach - link to GL, Auto, or Property
    gl_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=True
    )
    auto_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("auto_details.id", ondelete="CASCADE"), nullable=True
    )
    property_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("property_details.id", ondelete="CASCADE"), nullable=True
    )

    # Validation to ensure one and only one parent type
    __table_args__ = (
        CheckConstraint(
            "(gl_details_id IS NULL AND auto_details_id IS NULL AND property_details_id IS NOT NULL) OR "
            "(gl_details_id IS NULL AND auto_details_id IS NOT NULL AND property_details_id IS NULL) OR "
            "(gl_details_id IS NOT NULL AND auto_details_id IS NULL AND property_details_id IS NULL)",
            name="check_one_parent_type",
        ),
    )

    # Required fields from TODO list
    injury_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    incident_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    injured_person_type: Mapped[Optional[InjuredPersonType]] = mapped_column(
        SQLAlchemyEnum(InjuredPersonType), nullable=True
    )

    # Equipment details
    equipment_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    equipment_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    equipment_owned_by_insured: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Safety information
    safety_measures_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    safety_measures_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Reporting
    incident_report_status: Mapped[Optional[IncidentReportStatus]] = mapped_column(
        SQLAlchemyEnum(IncidentReportStatus), nullable=True
    )
    report_filer_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    report_filer_contact: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    # Medical information
    medical_treatment_requirements: Mapped[Optional[MedicalTreatmentRequirements]] = mapped_column(
        SQLAlchemyEnum(MedicalTreatmentRequirements), nullable=True
    )
    treatment_nature: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    medical_provider_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    medical_provider_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Financial
    estimated_cost: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    insurance_billing_status: Mapped[Optional[InsuranceBillingStatus]] = mapped_column(
        SQLAlchemyEnum(InsuranceBillingStatus), nullable=True
    )

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"),
        onupdate=text("CURRENT_TIMESTAMP"),
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="bodily_injury_details")
    auto_details = relationship("AutoDetails", back_populates="bodily_injury_details")
    property_details = relationship("PropertyDetails", back_populates="bodily_injury_details")

    def __repr__(self) -> str:
        """String representation of BodilyInjuryDetails."""
        if self.gl_details_id:
            return f"<BodilyInjuryDetails for GL Details {self.gl_details_id}>"
        elif self.auto_details_id:
            return f"<BodilyInjuryDetails for Auto Details {self.auto_details_id}>"
        else:
            return f"<BodilyInjuryDetails for Property Details {self.property_details_id}>"


# New model for injured persons
class InjuredPerson(Base):
    """Details about an injured person involved in a claim."""

    __tablename__ = "injured_persons"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))

    # Triple parent approach - link to GL, Auto, or Property
    gl_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=True
    )
    auto_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("auto_details.id", ondelete="CASCADE"), nullable=True
    )
    property_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("property_details.id", ondelete="CASCADE"), nullable=True
    )

    # Validation to ensure one and only one parent type
    __table_args__ = (
        CheckConstraint(
            "(gl_details_id IS NULL AND auto_details_id IS NULL AND property_details_id IS NOT NULL) OR "
            "(gl_details_id IS NULL AND auto_details_id IS NOT NULL AND property_details_id IS NULL) OR "
            "(gl_details_id IS NOT NULL AND auto_details_id IS NULL AND property_details_id IS NULL)",
            name="check_one_parent_type_injured_person",
        ),
    )

    # Person details
    name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    person_type: Mapped[Optional[InjuredPersonType]] = mapped_column(SQLAlchemyEnum(InjuredPersonType), nullable=True)
    contact_info: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    age: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Incident information
    incident_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    incident_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Report information
    incident_report_status: Mapped[Optional[IncidentReportStatus]] = mapped_column(
        SQLAlchemyEnum(IncidentReportStatus), nullable=True
    )
    report_filer_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    report_filer_contact: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    # Soft delete field
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    gl_details = relationship("GeneralLiabilityDetails", back_populates="injured_persons")
    auto_details = relationship("AutoDetails", back_populates="injured_persons")
    property_details = relationship("PropertyDetails", back_populates="injured_persons")
    injuries: Mapped[List["Injury"]] = relationship(
        "Injury", back_populates="injured_person", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """String representation of InjuredPerson."""
        if self.gl_details_id:
            return f"<InjuredPerson {self.name} for GL Details {self.gl_details_id}>"
        elif self.auto_details_id:
            return f"<InjuredPerson {self.name} for Auto Details {self.auto_details_id}>"
        else:
            return f"<InjuredPerson {self.name} for Property Details {self.property_details_id}>"


class Injury(Base):
    """Details about a specific injury."""

    __tablename__ = "injuries"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    injured_person_id: Mapped[UUID] = mapped_column(
        ForeignKey("injured_persons.id", ondelete="CASCADE"), nullable=False
    )

    # Injury details
    injury_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    injury_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    injury_severity: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Equipment details
    equipment_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    equipment_details: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    equipment_owned_by_insured: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Safety information
    safety_measures_involved: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    safety_measures_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Medical information
    medical_treatment_requirements: Mapped[Optional[MedicalTreatmentRequirements]] = mapped_column(
        SQLAlchemyEnum(MedicalTreatmentRequirements), nullable=True
    )
    treatment_nature: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    medical_provider_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    medical_provider_address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Financial
    estimated_cost: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    insurance_billing_status: Mapped[Optional[InsuranceBillingStatus]] = mapped_column(
        SQLAlchemyEnum(InsuranceBillingStatus), nullable=True
    )

    # Soft delete field
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    injured_person = relationship("InjuredPerson", back_populates="injuries")

    def __repr__(self) -> str:
        """String representation of Injury."""
        return f"<Injury {self.id} for InjuredPerson {self.injured_person_id}>"
