"""Models for property damage and related entities in claims."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, CheckConstraint, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import Foreign<PERSON><PERSON>, String, Text
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import functions as sqlalchemy_functions
from sqlalchemy.sql.expression import cast, text

from claimentine.db.base_class import Base
from claimentine.models.claim.details import DamageType, USState


class PropertyAssetType(str, Enum):
    """Types of property assets that can be damaged."""

    # Building related
    BUILDING = "BUILDING"
    STRUCTURE = "STRUCTURE"
    ROOF = "ROOF"
    WALL = "WALL"
    FLOOR = "FLOOR"
    FOUNDATION = "FOUNDATION"
    HVAC = "HVAC"
    PLUMBING = "PLUMBING"
    ELECTRICAL_SYSTEM = "ELECTRICAL_SYSTEM"

    # Contents related
    FURNITURE = "FURNITURE"
    APPLIANCE = "APPLIANCE"
    ELECTRONICS = "ELECTRONICS"
    MACHINERY = "MACHINERY"
    EQUIPMENT = "EQUIPMENT"
    INVENTORY = "INVENTORY"
    TOOLS = "TOOLS"
    RAW_MATERIALS = "RAW_MATERIALS"
    FINISHED_GOODS = "FINISHED_GOODS"
    OFFICE_EQUIPMENT = "OFFICE_EQUIPMENT"

    # Auto related
    VEHICLE = "VEHICLE"
    CARGO = "CARGO"
    TRAILER = "TRAILER"
    FLEET_VEHICLE = "FLEET_VEHICLE"
    SPECIALIZED_VEHICLE = "SPECIALIZED_VEHICLE"

    # Third-party related
    THIRD_PARTY_STRUCTURE = "THIRD_PARTY_STRUCTURE"
    THIRD_PARTY_VEHICLE = "THIRD_PARTY_VEHICLE"
    THIRD_PARTY_PROPERTY = "THIRD_PARTY_PROPERTY"
    CUSTOMER_PROPERTY = "CUSTOMER_PROPERTY"

    # Outdoor/property related
    LANDSCAPING = "LANDSCAPING"
    FIXTURE = "FIXTURE"
    SIGNAGE = "SIGNAGE"
    PARKING_LOT = "PARKING_LOT"
    FENCING = "FENCING"

    # Special commercial property
    MANUFACTURING_EQUIPMENT = "MANUFACTURING_EQUIPMENT"
    RESTAURANT_EQUIPMENT = "RESTAURANT_EQUIPMENT"
    MEDICAL_EQUIPMENT = "MEDICAL_EQUIPMENT"
    IT_INFRASTRUCTURE = "IT_INFRASTRUCTURE"
    DATA = "DATA"

    # Other
    OTHER = "OTHER"


class RepairStatus(str, Enum):
    """Status of repairs for damaged property."""

    NOT_STARTED = "NOT_STARTED"
    ASSESSMENT_PENDING = "ASSESSMENT_PENDING"
    ESTIMATED = "ESTIMATED"
    WAITING_FOR_PARTS = "WAITING_FOR_PARTS"
    WAITING_FOR_CONTRACTOR = "WAITING_FOR_CONTRACTOR"
    PERMITS_PENDING = "PERMITS_PENDING"
    SCHEDULED = "SCHEDULED"
    IN_PROGRESS = "IN_PROGRESS"
    PARTIALLY_COMPLETED = "PARTIALLY_COMPLETED"
    INSPECTION_PENDING = "INSPECTION_PENDING"
    COMPLETED = "COMPLETED"
    REPLACED = "REPLACED"
    NOT_REPAIRABLE = "NOT_REPAIRABLE"
    REPAIRS_DECLINED = "REPAIRS_DECLINED"
    ON_HOLD = "ON_HOLD"


class DamagedPropertyAsset(Base):
    """Details about a damaged property asset involved in a claim."""

    __tablename__ = "damaged_property_assets"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))

    # Dual parent approach - link to either Property or Auto details
    property_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("property_details.id", ondelete="CASCADE"), nullable=True
    )
    auto_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("auto_details.id", ondelete="CASCADE"), nullable=True
    )
    gl_details_id: Mapped[Optional[UUID]] = mapped_column(
        ForeignKey("gl_details.id", ondelete="CASCADE"), nullable=True
    )

    # Validation to ensure one and only one parent type
    __table_args__ = (
        CheckConstraint(
            "(property_details_id IS NULL AND auto_details_id IS NULL AND gl_details_id IS NOT NULL) OR "
            "(property_details_id IS NULL AND auto_details_id IS NOT NULL AND gl_details_id IS NULL) OR "
            "(property_details_id IS NOT NULL AND auto_details_id IS NULL AND gl_details_id IS NULL)",
            name="check_one_parent_type_damaged_property_asset",
        ),
    )

    # Asset details
    name: Mapped[str] = mapped_column(String(200), nullable=False)
    asset_type: Mapped[PropertyAssetType] = mapped_column(SQLAlchemyEnum(PropertyAssetType), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Location information
    location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    city: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    state: Mapped[Optional[USState]] = mapped_column(SQLAlchemyEnum(USState), nullable=True)
    zip_code: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)

    # Ownership details
    owner_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    owner_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Insured, Third Party, etc.
    owned_by_insured: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)

    # Financial information
    estimated_value: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    purchase_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Documentation
    police_report_filed: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    police_report_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Soft delete field
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    property_details = relationship("PropertyDetails", back_populates="damaged_property_assets")
    auto_details = relationship("AutoDetails", back_populates="damaged_property_assets")
    gl_details = relationship("GeneralLiabilityDetails", back_populates="damaged_property_assets")
    damage_instances: Mapped[List["DamageInstance"]] = relationship(
        "DamageInstance", back_populates="damaged_property_asset", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        """String representation of DamagedPropertyAsset."""
        parent_type = None
        parent_id = None
        if self.property_details_id:
            parent_type = "PropertyDetails"
            parent_id = self.property_details_id
        elif self.auto_details_id:
            parent_type = "AutoDetails"
            parent_id = self.auto_details_id
        else:
            parent_type = "GLDetails"
            parent_id = self.gl_details_id

        return f"<DamagedPropertyAsset {self.name} ({self.asset_type}) for {parent_type} {parent_id}>"


class DamageInstance(Base):
    """Details about a specific instance of damage to a property asset."""

    __tablename__ = "damage_instances"

    # Primary fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    damaged_property_asset_id: Mapped[UUID] = mapped_column(
        ForeignKey("damaged_property_assets.id", ondelete="CASCADE"), nullable=False
    )

    # Damage details
    damage_type: Mapped[DamageType] = mapped_column(SQLAlchemyEnum(DamageType), nullable=False)
    damage_description: Mapped[str] = mapped_column(Text, nullable=False)
    damage_severity: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Minor, Moderate, Severe, etc.
    affected_area: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    # Cause information
    damage_cause: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    date_of_damage: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Repair information
    repair_status: Mapped[Optional[RepairStatus]] = mapped_column(SQLAlchemyEnum(RepairStatus), nullable=True)
    repair_description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    repair_vendor: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)
    estimated_repair_cost: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    actual_repair_cost: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    repair_start_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    repair_completion_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)

    # Financial
    estimated_replacement_cost: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    deductible_applied: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    depreciation_amount: Mapped[Optional[Decimal]] = mapped_column(nullable=True)

    # Soft delete field
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    damaged_property_asset = relationship("DamagedPropertyAsset", back_populates="damage_instances")

    def __repr__(self) -> str:
        """String representation of DamageInstance."""
        return f"<DamageInstance {self.id} ({self.damage_type}) for PropertyAsset {self.damaged_property_asset_id}>"
