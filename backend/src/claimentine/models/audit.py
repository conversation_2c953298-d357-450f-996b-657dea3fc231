"""Audit model for tracking changes to claim details."""

from datetime import datetime
from enum import Enum
from typing import Dict, Optional
from uuid import UUID

from sqlalchemy import J<PERSON><PERSON>, DateTime, ForeignKey, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class ChangeType(str, Enum):
    """Types of changes tracked in the audit system."""

    CREATE = "CREATE"  # Initial creation
    UPDATE = "UPDATE"  # Update to existing field
    DELETE = "DELETE"  # Deletion (not often used with soft deletes)


class EntityType(str, Enum):
    """Types of entities that can be audited."""

    CLAIM = "CLAIM"  # Base claim data
    CLAIM_DETAILS = "CLAIM_DETAILS"  # Generic claim details changes
    AUTO_DETAILS = "AUTO_DETAILS"  # Auto claim specific details
    PROPERTY_DETAILS = "PROPERTY_DETAILS"  # Property claim specific details
    GL_DETAILS = "GL_DETAILS"  # General liability claim specific details
    WITNESS = "WITNESS"  # Witness data
    ATTORNEY = "ATTORNEY"  # Attorney data
    FINANCIAL = "FINANCIAL"  # Financial data
    RESERVE = "RESERVE"  # Reserve change (special case since it has its own history)
    PAYMENT = "PAYMENT"  # Payment data
    DOCUMENT = "DOCUMENT"  # Document metadata
    NOTE = "NOTE"  # Notes for claims
    TASK = "TASK"  # Tasks related to claims


class AuditTrail(Base):
    """Model for tracking all changes to claim details."""

    __tablename__ = "audit_trail"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)
    entity_type: Mapped[EntityType] = mapped_column(nullable=False)
    entity_id: Mapped[Optional[UUID]] = mapped_column(nullable=True)  # ID of the specific entity if applicable
    change_type: Mapped[ChangeType] = mapped_column(nullable=False)

    # Fields that changed
    field_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # Null for whole entity changes
    previous_value: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    new_value: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)

    # Change metadata
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    changed_by_id: Mapped[Optional[UUID]] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    changed_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))

    # Relationships
    claim = relationship("BaseClaim", back_populates="audit_trail")
    user = relationship("User", back_populates="audit_changes")

    def __repr__(self) -> str:
        """String representation of AuditTrail."""
        return f"<AuditTrail {self.change_type} {self.entity_type}:{self.field_name} at {self.changed_at}>"
