"""Witness model for tracking witnesses associated with claims."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime, ForeignKey, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class Witness(Base):
    """Model for tracking claim witnesses."""

    __tablename__ = "witnesses"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)

    # Witness information
    name: Mapped[str] = mapped_column(String(200), nullable=False)

    # Optional contact details
    email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Witness statement
    statement: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(<PERSON>olean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="witnesses")

    def __repr__(self) -> str:
        """String representation of Witness."""
        return f"<Witness {self.name} for claim {self.claim_id}>"
