"""First Notice of Loss model."""

from datetime import date, datetime, time
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, CheckConstraint, Date, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import ForeignKey, Index, String, Text, Time, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.claim.details import USState
from claimentine.models.client import Client


# Updated enum for reporter relationship
class ReporterRelationship(str, Enum):
    """Relationship of the reporter to the claim/incident."""

    INSURED = "INSURED"
    CLAIMANT = "CLAIMANT"
    ATTORNEY = "ATTORNEY"
    AGENT = "AGENT"
    OTHER = "OTHER"


class CommunicationPreference(str, Enum):
    """Preferred method of communication for responses."""

    EMAIL = "EMAIL"
    PHONE = "PHONE"
    TEXT = "TEXT"
    MAIL = "MAIL"
    PORTAL = "PORTAL"
    NO_PREFERENCE = "NO_PREFERENCE"


class FNOL(Base):
    """First Notice of Loss model."""

    __tablename__ = "fnols"

    # Table constraints and indexes
    __table_args__ = (
        CheckConstraint(
            "(reporter_phone IS NOT NULL) OR (reporter_email IS NOT NULL)",
            name="check-reporter-phone-or-email",
        ),
        # Search optimization indexes
        Index("idx_fnol_reporter_phone", "reporter_phone"),
        Index("idx_fnol_reporter_email", "reporter_email"),
        Index("idx_fnol_reported_by", "reported_by"),
        Index("idx_fnol_incident_date", "incident_date"),
        Index("idx_fnol_incident_location", "incident_location"),
        Index("idx_fnol_policy_number", "policy_number"),
        Index("idx_fnol_incident_state", "incident_state"),
        Index("idx_fnol_client_incident_date", "client_id", "incident_date"),
        Index("idx_fnol_reported_at", "reported_at"),
    )

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    client_id: Mapped[UUID] = mapped_column(ForeignKey("clients.id", ondelete="RESTRICT"), nullable=False)
    fnol_number: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    reported_by: Mapped[str] = mapped_column(String(200), nullable=False)
    reported_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    incident_date: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    incident_location: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    policy_number: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Updated contact fields - separate phone and email
    reporter_phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    reporter_email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)

    # New incident state field
    incident_state: Mapped[str] = mapped_column(SQLAlchemyEnum(USState), nullable=False)

    # Updated fields for FNOL enhancements
    incident_time: Mapped[Optional[time]] = mapped_column(Time, nullable=True)
    reporter_relationship: Mapped[Optional[ReporterRelationship]] = mapped_column(
        SQLAlchemyEnum(ReporterRelationship), nullable=True
    )
    communication_preference: Mapped[Optional[CommunicationPreference]] = mapped_column(
        SQLAlchemyEnum(CommunicationPreference), nullable=True
    )

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    client: Mapped["Client"] = relationship("Client")
    claims: Mapped[List["BaseClaim"]] = relationship("BaseClaim", back_populates="fnol")

    def __repr__(self) -> str:
        """String representation of FNOL."""
        return f"<FNOL {self.fnol_number}>"
