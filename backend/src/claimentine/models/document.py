"""Document model for storing claim-related documents."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import ForeignKey, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.core.exceptions import ValidationError
from claimentine.db.base_class import Base


class DocumentType(str, Enum):
    """Types of documents."""

    PHOTO = "PHOTO"
    REPORT = "REPORT"
    INVOICE = "INVOICE"
    STATEMENT = "STATEMENT"
    CONTRACT = "CONTRACT"
    POLICY = "POLICY"
    CORRESPONDENCE = "CORRESPONDENCE"
    OTHER = "OTHER"


class Document(Base):
    """Document model for storing claim-related files."""

    __tablename__ = "documents"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)
    type: Mapped[DocumentType] = mapped_column(SQLAlchemyEnum(DocumentType), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text)
    file_path: Mapped[str] = mapped_column(String(1024), nullable=False)
    file_size: Mapped[int] = mapped_column(nullable=False)  # Size in bytes
    mime_type: Mapped[str] = mapped_column(String(100), nullable=False)
    uploaded_by: Mapped[UUID] = mapped_column(ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="documents")
    uploader = relationship("User", back_populates="uploaded_documents")

    def validate_document(self) -> None:
        """
        Validate document data beyond basic field constraints.
        Focus on security rules and business policies.
        """
        # Security validation - high risk file types
        high_risk_types = [
            "application/x-msdownload",
            "application/x-dosexec",
            "application/java",
            "application/x-javascript",
            "application/x-shockwave-flash",
        ]
        if any(risk_type in self.mime_type.lower() for risk_type in high_risk_types):
            raise ValidationError(f"Security policy prohibits uploading {self.mime_type} files")

        # Validate file size - must be positive and reasonable
        if self.file_size <= 0:
            raise ValidationError("File size must be positive")

        # Example of reasonable max file size (100MB)
        max_size = 100 * 1024 * 1024  # 100MB in bytes
        if self.file_size > max_size:
            raise ValidationError(f"File size exceeds maximum allowed size of {max_size} bytes")

        # File path validation - must contain claim ID for organization
        if self.claim_id and str(self.claim_id) not in self.file_path:
            raise ValidationError("File path must include claim ID for proper organization")

    def __repr__(self) -> str:
        """String representation of Document."""
        return f"<Document {self.name} ({self.type})>"
