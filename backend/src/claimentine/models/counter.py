"""Model for managing per-client counters."""

from uuid import <PERSON><PERSON><PERSON>

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.client import Client  # Import Client


class ClientTaskCounter(Base):
    """Stores the last used task number for a specific client."""

    __tablename__ = "client_task_counters"

    # The client_id is the primary key and a foreign key to the clients table.
    client_id: Mapped[UUID] = mapped_column(
        UUID(as_uuid=True), ForeignKey("clients.id"), primary_key=True
    )
    last_task_number: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Update relationship to point to Client
    # Note: Check Client model for back_populates if needed
    client: Mapped["Client"] = relationship("Client")

    def __repr__(self) -> str:
        """String representation of the counter."""
        return f"<ClientTaskCounter client_id={self.client_id} last_task_number={self.last_task_number}>"


# Add relationships if needed in the future, e.g., backref from User
# user = relationship("User", back_populates="task_counter")
