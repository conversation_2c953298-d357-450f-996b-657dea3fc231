"""System configuration models."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, DateTime, String, text
from sqlalchemy.orm import Mapped, mapped_column

from claimentine.db.base_class import Base


class SystemConfiguration(Base):
    """System-wide configuration settings."""

    __tablename__ = "system_configurations"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    key: Mapped[str] = mapped_column(String(100), nullable=False, unique=True, index=True)
    value: Mapped[str] = mapped_column(String(500), nullable=False)
    description: Mapped[str] = mapped_column(String(500), nullable=False)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    def __repr__(self) -> str:
        """String representation."""
        return f"<SystemConfiguration {self.key}={self.value}>"
