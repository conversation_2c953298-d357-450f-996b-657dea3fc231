"""Customer-specific authority threshold configurations."""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, UniqueConstraint, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.authority import AuthorityRole


class ClientAuthorityThreshold(Base):
    """Client-specific authority thresholds for financial operations."""

    __tablename__ = "client_authority_thresholds"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    client_id: Mapped[UUID] = mapped_column(ForeignKey("clients.id", ondelete="CASCADE"), nullable=False)
    authority_role: Mapped[AuthorityRole] = mapped_column(nullable=False)
    reserve_limit: Mapped[Decimal] = mapped_column(nullable=False)
    payment_limit: Mapped[Decimal] = mapped_column(nullable=False)
    description: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    client = relationship("Client", back_populates="authority_thresholds")

    __table_args__ = (
        # Ensure there's only one threshold per client+role combination
        UniqueConstraint("client_id", "authority_role", name="uq_client_authority_role"),
    )

    def __repr__(self) -> str:
        """String representation of ClientAuthorityThreshold."""
        return f"<ClientAuthorityThreshold {self.authority_role} for client {self.client_id} (reserve: {self.reserve_limit})>"
