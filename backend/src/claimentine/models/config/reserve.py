"""Reserve configuration models."""

from datetime import datetime
from decimal import Dec<PERSON><PERSON>
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON>ole<PERSON>, DateTime, String, UniqueConstraint, text
from sqlalchemy.orm import Mapped, mapped_column

from claimentine.db.base_class import Base
from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType


class ReserveConfiguration(Base):
    """Configuration for claim type reserve requirements."""

    __tablename__ = "reserve_configurations"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_type: Mapped[ClaimType] = mapped_column(nullable=False)
    reserve_type: Mapped[ReserveType] = mapped_column(nullable=False)
    is_required: Mapped[bool] = mapped_column(default=False)
    minimum_amount: Mapped[Optional[Decimal]] = mapped_column(nullable=True)
    description: Mapped[str] = mapped_column(String(500), nullable=False)

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    __table_args__ = (UniqueConstraint("claim_type", "reserve_type", name="uq_claim_reserve_type"),)

    def __repr__(self) -> str:
        """String representation."""
        return f"<ReserveConfiguration {self.claim_type}:{self.reserve_type}>"
