"""Attorney model for tracking attorneys associated with claims."""

from datetime import datetime
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON>ole<PERSON>, DateTime, ForeignKey, String, Text, text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base


class AttorneyType(str, Enum):
    """Types of attorneys associated with claims."""

    PLAINTIFF = "PLAINTIFF"
    DEFENSE = "DEFENSE"
    COVERAGE = "COVERAGE"
    MONITORING = "MONITORING"
    OTHER = "OTHER"


class Attorney(Base):
    """Model for tracking attorneys associated with claims."""

    __tablename__ = "attorneys"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    claim_id: Mapped[UUID] = mapped_column(ForeignKey("base_claims.id", ondelete="CASCADE"), nullable=False)

    # Attorney information
    name: Mapped[str] = mapped_column(String(200), nullable=False)
    attorney_type: Mapped[AttorneyType] = mapped_column(nullable=False)
    firm_name: Mapped[Optional[str]] = mapped_column(String(200), nullable=True)

    # Contact details
    email: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    phone: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    address: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # Additional notes
    notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    claim = relationship("BaseClaim", back_populates="attorneys")

    def __repr__(self) -> str:
        """String representation of Attorney."""
        return f"<Attorney {self.name} ({self.firm_name}, {self.attorney_type}) for claim {self.claim_id}>"
