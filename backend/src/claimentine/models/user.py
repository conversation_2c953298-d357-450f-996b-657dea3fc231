"""User model and related database models."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import String, text
from sqlalchemy.dialects.postgresql import J<PERSON>N<PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.associations import UserPermission
from claimentine.models.authority import AuthorityRole


class UserRole(str, Enum):
    """User role values."""

    ADMIN = "ADMIN"
    MANAGER = "MANAGER"
    ADJUSTER = "ADJUSTER"
    AGENT = "AGENT"
    CUSTOMER = "CUSTOMER"


class UserStatus(str, Enum):
    """User status values."""

    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PENDING = "PENDING"
    SUSPENDED = "SUSPENDED"


class User(Base):
    """User model."""

    __tablename__ = "users"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    password_hash: Mapped[str] = mapped_column(String(255), nullable=False)
    first_name: Mapped[str] = mapped_column(String(100), nullable=False)
    last_name: Mapped[str] = mapped_column(String(100), nullable=False)
    prefix: Mapped[str] = mapped_column(String(10), unique=True, index=True, nullable=True)
    role: Mapped[UserRole] = mapped_column(SQLAlchemyEnum(UserRole), nullable=False)
    authority_role: Mapped[AuthorityRole] = mapped_column(
        SQLAlchemyEnum(AuthorityRole), nullable=False, default=AuthorityRole.NO_AUTHORITY
    )
    status: Mapped[UserStatus] = mapped_column(SQLAlchemyEnum(UserStatus), nullable=False, default=UserStatus.PENDING)
    email_verified: Mapped[bool] = mapped_column(default=False)

    # Profile fields
    department: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    job_title: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    phone_number: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    timezone: Mapped[str] = mapped_column(String(50), nullable=False, default="UTC")
    preferences: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSONB, nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Soft delete fields
    is_deleted: Mapped[bool] = mapped_column(Boolean, default=False, index=True)
    deleted_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)

    # Security fields
    failed_login_attempts: Mapped[int] = mapped_column(default=0)
    last_login_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    locked_until: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    last_password_change_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    force_password_change: Mapped[bool] = mapped_column(default=False)

    # Relationships
    permissions: Mapped[List["Permission"]] = relationship(
        "Permission",
        secondary="user_permissions",
        back_populates="users",
        primaryjoin="User.id == UserPermission.user_id",
        secondaryjoin="UserPermission.permission_id == Permission.id",
        viewonly=True,
    )
    user_permissions: Mapped[List[UserPermission]] = relationship(
        UserPermission,
        back_populates="user",
        foreign_keys=[UserPermission.user_id],
        cascade="all, delete-orphan",
    )
    sessions: Mapped[List["UserSession"]] = relationship("UserSession", back_populates="user")

    # Document relationships
    uploaded_documents: Mapped[List["Document"]] = relationship(
        "Document", back_populates="uploader", foreign_keys="Document.uploaded_by"
    )

    # Note relationships
    notes: Mapped[List["Note"]] = relationship("Note", back_populates="author", foreign_keys="Note.created_by")

    # Task relationships
    assigned_tasks: Mapped[List["Task"]] = relationship(
        "Task", back_populates="assignee", foreign_keys="Task.assigned_to"
    )
    created_tasks: Mapped[List["Task"]] = relationship("Task", back_populates="creator", foreign_keys="Task.created_by")

    # Status history relationship
    status_changes: Mapped[List["ClaimStatusHistory"]] = relationship(
        "ClaimStatusHistory", back_populates="user", foreign_keys="ClaimStatusHistory.changed_by"
    )

    # Audit trail relationship
    audit_changes: Mapped[List["AuditTrail"]] = relationship(
        "AuditTrail", back_populates="user", foreign_keys="AuditTrail.changed_by_id"
    )

    # Claim relationships
    assigned_claims: Mapped[List["BaseClaim"]] = relationship(
        "BaseClaim", back_populates="assigned_to", foreign_keys="BaseClaim.assigned_to_id"
    )
    supervised_claims: Mapped[List["BaseClaim"]] = relationship(
        "BaseClaim", back_populates="supervisor", foreign_keys="BaseClaim.supervisor_id"
    )
    created_claims: Mapped[List["BaseClaim"]] = relationship(
        "BaseClaim", back_populates="created_by", foreign_keys="BaseClaim.created_by_id"
    )

    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"

    def __repr__(self) -> str:
        """String representation of User."""
        return f"<User {self.email} ({self.role})>"
