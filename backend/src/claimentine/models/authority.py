"""Authority level model."""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from uuid import UUID

from sqlalchemy import String, text
from sqlalchemy.orm import Mapped, mapped_column

from claimentine.db.base_class import Base


class AuthorityRole(str, Enum):
    """Authority roles for financial operations."""

    NO_AUTHORITY = "NO_AUTHORITY"  # No financial authority
    BASIC = "BASIC"  # Basic authority for small claims
    INTERMEDIATE = "INTERMEDIATE"  # Mid-level authority
    SENIOR = "SENIOR"  # Senior level authority
    SUPERVISOR = "SUPERVISOR"  # Supervisory authority
    MANAGER = "MANAGER"  # Management level authority
    UNLIMITED = "UNLIMITED"  # Unlimited authority

    def get_level(self) -> int:
        """Get numeric level of authority role."""
        return list(self.__class__).index(self)

    def has_authority_over(self, other: "AuthorityRole") -> bool:
        """Check if this role has authority over another role."""
        return self.get_level() > other.get_level()


class AuthorityLevel(Base):
    """Authority levels for financial operations."""

    __tablename__ = "authority_levels"

    # Required fields
    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    role: Mapped[AuthorityRole] = mapped_column(nullable=False)
    reserve_limit: Mapped[Decimal] = mapped_column(nullable=False)
    payment_limit: Mapped[Decimal] = mapped_column(nullable=False)

    # Optional fields
    description: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)

    # System timestamps
    created_at: Mapped[datetime] = mapped_column(server_default=text("CURRENT_TIMESTAMP"))
    updated_at: Mapped[datetime] = mapped_column(
        server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    def __repr__(self) -> str:
        """String representation of AuthorityLevel."""
        return f"<AuthorityLevel {self.role} (reserve: {self.reserve_limit}, payment: {self.payment_limit})>"
