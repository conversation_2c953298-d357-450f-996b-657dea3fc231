"""Authentication and authorization models."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import DateTime, ForeignKey, String, text
from sqlalchemy.dialects.postgresql import INET
from sqlalchemy.orm import Mapped, mapped_column, relationship

from claimentine.db.base_class import Base
from claimentine.models.associations import UserPermission
from claimentine.models.user import User, UserRole

# Role hierarchy and default permissions
ROLE_HIERARCHY = {
    UserRole.ADMIN: [],  # Has all permissions by default
    UserRole.MANAGER: [UserRole.ADJUSTER],
    UserRole.ADJUSTER: [UserRole.AGENT],
    UserRole.AGENT: [UserRole.CUSTOMER],
    UserRole.CUSTOMER: [],
}

# Default permissions for each role
ROLE_PERMISSIONS = {
    UserRole.CUSTOMER: [
        # Claims - Basic
        "VIEW_OWN_CLAIMS",
        "CREATE_OWN_CLAIMS",
        # Documents
        "VIEW_OWN_DOCUMENTS",
        "UPLOAD_OWN_DOCUMENTS",
        # Profile
        "VIEW_OWN_PROFILE",
        "EDIT_OWN_PROFILE",
    ],
    UserRole.AGENT: [
        # Claims - Extended
        "VIEW_OWN_CLAIMS",
        "VIEW_ASSIGNED_CLAIMS",
        "CREATE_CLAIMS",
        "EDIT_ASSIGNED_CLAIMS",
        # Documents
        "VIEW_OWN_DOCUMENTS",
        "VIEW_ASSIGNED_DOCUMENTS",
        "UPLOAD_DOCUMENTS",
        "VIEW_CLAIM_DOCUMENTS",
        "UPLOAD_CLAIM_DOCUMENTS",
        # Communications
        "SEND_COMMUNICATIONS",
        # Profile
        "VIEW_OWN_PROFILE",
        "EDIT_OWN_PROFILE",
        # Financial - Basic
        "VIEW_CLAIM_FINANCIALS",
        # Tasks - Basic Agent Permissions
        "CREATE_TASK",  # Allow agents to create tasks related to claims they handle
        "VIEW_ASSIGNED_TASKS",  # Allow agents to see tasks assigned specifically to them
    ],
    UserRole.ADJUSTER: [
        # Claims - Full
        "VIEW_ALL_CLAIMS",
        "CREATE_CLAIMS",
        "EDIT_CLAIMS",
        "ASSIGN_CLAIMS",
        "CHANGE_CLAIM_STATUS",
        "UNASSIGN_CLAIMS",
        # Documents
        "VIEW_ALL_DOCUMENTS",
        "UPLOAD_DOCUMENTS",
        "VIEW_CLAIM_DOCUMENTS",
        "UPLOAD_CLAIM_DOCUMENTS",
        "EDIT_CLAIM_DOCUMENTS",
        # Tasks
        "CREATE_TASK",
        "VIEW_ASSIGNED_TASKS",
        "EDIT_ASSIGNED_TASKS",
        "ASSIGN_TASK",
        "VIEW_TASKS",
        "LIST_TASKS",
        "UPDATE_TASKS",
        "CHANGE_TASK_STATUS",
        # Communications
        "MANAGE_COMMUNICATIONS",
        # Users - Basic
        "VIEW_USERS",
        # Clients
        "VIEW_CLIENTS",
        "CREATE_CLIENTS",
        "EDIT_CLIENTS",
        # Profile
        "VIEW_OWN_PROFILE",
        "EDIT_OWN_PROFILE",
        # Financial - Extended
        "VIEW_CLAIM_FINANCIALS",
        "SET_INITIAL_RESERVE",
        "UPDATE_RESERVES",
        "PROCESS_PAYMENTS",
        # Metrics
        "VIEW_METRICS",
        # Reports
        "VIEW_REPORTS",
    ],
    UserRole.MANAGER: [
        # Claims - Full+
        "VIEW_ALL_CLAIMS",
        "CREATE_CLAIMS",
        "EDIT_CLAIMS",
        "DELETE_CLAIMS",
        "ASSIGN_CLAIMS",
        "CLOSE_CLAIMS",
        "CHANGE_CLAIM_STATUS",
        "UNASSIGN_CLAIMS",
        # Documents - Full
        "VIEW_ALL_DOCUMENTS",
        "UPLOAD_DOCUMENTS",
        "DELETE_DOCUMENTS",
        "MANAGE_DOCUMENT_CATEGORIES",
        # Tasks
        "CREATE_TASK",
        "VIEW_ALL_TASKS",
        "EDIT_ALL_TASKS",
        "ASSIGN_TASK",
        "DELETE_TASK",
        "VIEW_TASKS",
        "LIST_TASKS",
        "UPDATE_TASKS",
        "CHANGE_TASK_STATUS",
        # Communications
        "MANAGE_COMMUNICATIONS",
        # Users - Extended
        "VIEW_USERS",
        "CREATE_USERS",
        "EDIT_USERS",
        "DELETE_USERS",
        # Clients - Full
        "VIEW_CLIENTS",
        "CREATE_CLIENTS",
        "EDIT_CLIENTS",
        "DELETE_CLIENTS",
        # Profile
        "VIEW_OWN_PROFILE",
        "EDIT_OWN_PROFILE",
        # Financial - Full
        "VIEW_CLAIM_FINANCIALS",
        "SET_INITIAL_RESERVE",
        "UPDATE_RESERVES",
        "PROCESS_PAYMENTS",
        "OVERRIDE_FINANCIAL_LIMITS",
        # Reporting & Workflow
        "VIEW_REPORTS",
        "MANAGE_WORKFLOWS",
        "VIEW_BASIC_ANALYTICS",
        # Metrics
        "VIEW_METRICS",
    ],
    UserRole.ADMIN: [
        # System Level
        "SYSTEM_CONFIGURATION",
        "MANAGE_ROLES",
        "MANAGE_PERMISSIONS",
        "VIEW_AUDIT_LOGS",
        "MANAGE_API_KEYS",
        # Advanced Analytics
        "VIEW_ADVANCED_ANALYTICS",
        "CONFIGURE_REPORTS",
        # Security
        "MANAGE_SECURITY_SETTINGS",
        "VIEW_SECURITY_LOGS",
    ],
}


class Permission(Base):
    """Permission model for granular access control."""

    __tablename__ = "permissions"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    description: Mapped[Optional[str]] = mapped_column(String)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=text("CURRENT_TIMESTAMP"))

    # Relationships
    users: Mapped[List[User]] = relationship(
        User,
        secondary="user_permissions",
        back_populates="permissions",
        primaryjoin="Permission.id == UserPermission.permission_id",
        secondaryjoin="UserPermission.user_id == User.id",
        viewonly=True,
    )
    user_permissions: Mapped[List[UserPermission]] = relationship(
        UserPermission,
        back_populates="permission",
        foreign_keys=[UserPermission.permission_id],
        cascade="all, delete-orphan",
    )

    def __repr__(self) -> str:
        """String representation of Permission."""
        return f"<Permission {self.name}>"


class UserSession(Base):
    """User session model for managing active sessions."""

    __tablename__ = "user_sessions"

    id: Mapped[UUID] = mapped_column(primary_key=True, server_default=text("gen_random_uuid()"))
    user_id: Mapped[UUID] = mapped_column(ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    token: Mapped[str] = mapped_column(String(1024), unique=True, nullable=False)
    ip_address: Mapped[Optional[str]] = mapped_column(INET)
    user_agent: Mapped[Optional[str]] = mapped_column(String)
    expires_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=False)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=text("CURRENT_TIMESTAMP"))
    last_active_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=text("CURRENT_TIMESTAMP"), onupdate=text("CURRENT_TIMESTAMP")
    )

    # Relationships
    user = relationship("User", back_populates="sessions")

    def __repr__(self) -> str:
        """String representation of UserSession."""
        return f"<UserSession {self.user_id}>"
