"""Main application module."""

import logging

from fastapi import API<PERSON><PERSON><PERSON>, FastAP<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, Response
from sqlalchemy import text

from claimentine.api.health import api_router as health_router
from claimentine.api.v1.all_documents import api_router as all_documents_router
from claimentine.api.v1.auth import api_router as auth_router
from claimentine.api.v1.claims import api_router as claims_router
from claimentine.api.v1.config import config_router
from claimentine.api.v1.clients import api_router as clients_router
from claimentine.api.v1.fnols import api_router as fnols_router
from claimentine.api.v1.metrics import router as metrics_router
from claimentine.api.v1.notes import api_router as notes_router
from claimentine.api.v1.reports import router as reports_router
from claimentine.api.v1.tasks import task_create_router as tasks_create_router
from claimentine.api.v1.tasks import task_manage_router as tasks_manage_router
from claimentine.api.v1.users import api_router as users_router
from claimentine.core.config import settings
from claimentine.core.exceptions import AppException
from claimentine.core.json import CustomJSONEncoder
from claimentine.core.middleware import CustomCORSMiddleware
from claimentine.db.session import engine

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    default_response_class=JSONResponse,
)

# Set up CORS
origins = settings.CORS_ORIGINS or settings.BACKEND_CORS_ORIGINS
if origins:
    app.add_middleware(
        CustomCORSMiddleware,
        allow_origins=[str(origin) for origin in origins],
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_METHODS,
        allow_headers=settings.CORS_HEADERS,
        max_age=settings.CORS_MAX_AGE,
    )
    logging.info(f"CORS enabled for origins: {origins}")

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)  # Optional: Get a logger for main.py itself if needed


# Add startup event handler
@app.on_event("startup")
async def startup_event():
    """Log when the application starts and verify database connection."""
    logger.info(f"Starting {settings.PROJECT_NAME} v{settings.VERSION}")
    logger.info(f"Environment: {'Development' if settings.POSTGRES_HOST == 'localhost' else 'Production'}")

    # Explicitly test and log database connection
    logger.info(f"Testing database connection to {settings.POSTGRES_HOST}...")
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1")).scalar()
            if result == 1:
                logger.info(f"Successfully connected to database at {settings.POSTGRES_HOST}")
            else:
                logger.warning(f"Database connection test returned unexpected result: {result}")
    except Exception as e:
        logger.error(f"Failed to connect to database at {settings.POSTGRES_HOST}: {str(e)}")
        # Don't raise the exception since we want the app to continue starting up


# Global exception handler for all custom exceptions
@app.exception_handler(AppException)
async def app_exception_handler(request: Request, exc: AppException):
    """Global handler for all application exceptions."""
    # Log the specific AppException details
    logging.error(
        f"AppException caught for request {request.method} {request.url}: "
        f"{type(exc).__name__} - Status={exc.status_code}, Code={exc.code}, Detail={exc.detail}"
    )
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.code,
            "message": exc.detail,
            "details": exc.details.dict() if exc.details else None,
        },
    )


# New RequestValidationError handler
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Logs validation errors before returning the standard 422 response."""
    # Log the detailed validation errors using the root logger
    logging.error(f"Caught validation error for request {request.method} {request.url}")
    logging.error(f"Validation Error Details: {exc.errors()}")

    # Return the default FastAPI 422 response structure
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors()},
    )


# Generic Exception handler (catches unexpected errors)
@app.exception_handler(Exception)
async def generic_exception_handler(request: Request, exc: Exception):
    """Handles any unexpected exceptions not caught by specific handlers."""
    # Log the unexpected error with traceback
    logging.error(
        f"Unhandled exception for request {request.method} {request.url}", exc_info=True
    )  # exc_info=True includes traceback
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "code": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected internal server error occurred.",
            "details": str(exc),  # Provide basic exception info
        },
    )


router = APIRouter()
app.include_router(health_router)
claims_router.include_router(tasks_create_router, prefix="/{claim_id}/tasks", tags=["tasks"])
router.include_router(auth_router, prefix="/auth", tags=["auth"])
router.include_router(users_router, prefix="/users", tags=["users"])
router.include_router(claims_router, prefix="/claims", tags=["claims"])
router.include_router(clients_router, prefix="/clients", tags=["clients"])
router.include_router(fnols_router, prefix="/fnols", tags=["fnols"])
router.include_router(config_router, prefix="/config", tags=["config"])
router.include_router(metrics_router, tags=["metrics"])
router.include_router(reports_router, tags=["reports"])
router.include_router(notes_router, tags=["notes"])
router.include_router(tasks_manage_router, prefix="/tasks", tags=["tasks"])
router.include_router(all_documents_router, prefix="/documents", tags=["documents"])


# Add global OPTIONS handler
@router.options("/{path:path}")
async def options_handler(path: str):
    """
    Global OPTIONS request handler for any path.

    This is a fallback to handle OPTIONS requests for any path
    that doesn't have a specific OPTIONS handler.
    CORS middleware will add appropriate headers.
    """
    return Response(status_code=200)


app.include_router(router, prefix=settings.API_V1_STR)
app.json_encoder = CustomJSONEncoder
