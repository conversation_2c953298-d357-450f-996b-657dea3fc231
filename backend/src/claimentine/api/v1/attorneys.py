"""Attorney API endpoints."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Path, status

from claimentine.api.v1.deps import get_attorney_service, get_claim_by_identifier
from claimentine.core.exceptions import NotFoundError
from claimentine.models.claim.base import BaseClaim
from claimentine.schemas.attorney import <PERSON><PERSON><PERSON>, Attorney<PERSON><PERSON>ponse, AttorneyUpdate
from claimentine.services.attorney import AttorneyService

# Create API router for attorneys at /claims/{claim_id}/attorneys
api_router = APIRouter()


@api_router.get("", response_model=List[AttorneyResponse], tags=["attorneys"])
def list_attorneys(
    claim: BaseClaim = Depends(get_claim_by_identifier),
    attorney_service: AttorneyService = Depends(get_attorney_service),
) -> List[AttorneyResponse]:
    """Get all attorneys for a claim."""
    return attorney_service.get_attorneys(claim.id)


@api_router.post("", response_model=AttorneyResponse, status_code=status.HTTP_201_CREATED, tags=["attorneys"])
def create_attorney(
    attorney_data: Attorney<PERSON><PERSON>,
    claim: BaseClaim = Depends(get_claim_by_identifier),
    attorney_service: AttorneyService = Depends(get_attorney_service),
) -> AttorneyResponse:
    """Create a new attorney for a claim."""
    return attorney_service.create_attorney(claim.id, attorney_data)


@api_router.get("/{attorney_id}", response_model=AttorneyResponse, tags=["attorneys"])
def get_attorney(
    attorney_id: UUID = Path(..., description="Attorney ID"),
    attorney_service: AttorneyService = Depends(get_attorney_service),
) -> AttorneyResponse:
    """Get a specific attorney by ID."""
    attorney = attorney_service.get_attorney(attorney_id)
    if not attorney:
        raise NotFoundError(f"Attorney {attorney_id} not found")
    return attorney


@api_router.patch("/{attorney_id}", response_model=AttorneyResponse, tags=["attorneys"])
def update_attorney(
    attorney_data: AttorneyUpdate,
    attorney_id: UUID = Path(..., description="Attorney ID"),
    attorney_service: AttorneyService = Depends(get_attorney_service),
) -> AttorneyResponse:
    """Update an existing attorney."""
    return attorney_service.update_attorney(attorney_id, attorney_data)


@api_router.delete("/{attorney_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["attorneys"])
def delete_attorney(
    attorney_id: UUID = Path(..., description="Attorney ID"),
    attorney_service: AttorneyService = Depends(get_attorney_service),
) -> None:
    """Delete an attorney."""
    attorney_service.delete_attorney(attorney_id)
    return None
