"""API endpoints for document management."""

import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Body, Depends, File, Form, Path, Request, UploadFile, status

from claimentine.api.v1.deps import get_document_service
from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import (
    AuthorizationError,
    BadRequestError,
    NotFoundError,
    StorageError,
    ValidationError,
    create_error_detail,
)
from claimentine.models.document import DocumentType
from claimentine.models.user import User
from claimentine.schemas.document import (
    DocumentCreate,
    DocumentDownloadUrlResponse,
    DocumentList,
    DocumentResponse,
    DocumentUpdate,
    DocumentUploadUrlRequest,
    DocumentUploadUrlResponse,
)
from claimentine.services.document import DocumentService

api_router = APIRouter(tags=["documents"])

logger = logging.getLogger(__name__)


@api_router.get("/{document_id}", response_model=DocumentResponse)
def get_document_by_id(
    claim_id: str = Path(..., description="Claim ID or Claim Number"),
    document_id: UUID = Path(..., description="Document ID"),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentResponse:
    """
    Get document by ID for a specific claim.

    Args:
        claim_id: Claim ID or Claim Number
        document_id: Document ID
        document_service: Document service

    Returns:
        Document data
    """
    document = document_service.get_document_by_claim(claim_id, document_id)
    return document


@api_router.get("", response_model=DocumentList)
def list_claim_documents(
    claim_id: str = Path(..., description="Claim ID or Claim Number"),
    skip: int = 0,
    limit: int = 100,
    document_type: Optional[DocumentType] = None,
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentList:
    """
    List documents for a specific claim.

    Args:
        claim_id: Claim ID or Claim Number
        skip: Number of records to skip
        limit: Maximum number of records to return
        document_type: Filter by document type
        document_service: Document service

    Returns:
        List of documents
    """
    documents, total = document_service.list_documents(
        claim_id=claim_id, skip=skip, limit=limit, document_type=document_type
    )

    # Convert SQLAlchemy models to Pydantic models
    document_responses = [DocumentResponse.model_validate(doc) for doc in documents]

    return DocumentList(items=document_responses, total=total)


@api_router.patch("/{document_id}", response_model=DocumentResponse)
def update_document(
    claim_id: str = Path(..., description="Claim ID or Claim Number (used for context, validation done by service)"),
    document_id: UUID = Path(..., description="Document ID"),
    document_data: DocumentUpdate = Body(..., description="Document data to update"),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentResponse:
    """
    Update document metadata.
    Validation ensures document exists and user has permission.

    Args:
        document_id: Document ID
        document_data: Document data to update
        document_service: Document service

    Returns:
        Updated document data
    """
    updated_document = document_service.update_document(document_id, document_data)
    return updated_document


@api_router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_claim_document(
    claim_id: str = Path(..., description="Claim ID or Claim Number (used for context, validation done by service)"),
    document_id: UUID = Path(..., description="Document ID"),
    document_service: DocumentService = Depends(get_document_service),
) -> None:
    """
    Delete document by ID.
    Validation ensures document exists and user has permission.

    Args:
        document_id: Document ID
        document_service: Document service
    """
    document_service.delete_document(document_id)


@api_router.get("/{document_id}/download-url", response_model=DocumentDownloadUrlResponse)
def get_document_download_url(
    claim_id: str = Path(..., description="Claim ID or Claim Number (used for context, validation done by service)"),
    document_id: UUID = Path(..., description="Document ID"),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentDownloadUrlResponse:
    """
    Get a signed URL for downloading a document.
    Validation ensures document exists and user has permission.

    Args:
        document_id: Document ID
        document_service: Document service

    Returns:
        Download URL and expiration time
    """
    download_url, expires_at = document_service.generate_download_url(document_id)

    return DocumentDownloadUrlResponse(
        download_url=download_url,
        expires_at=expires_at,
    )


@api_router.post("/upload-url", response_model=DocumentUploadUrlResponse, status_code=status.HTTP_201_CREATED)
def get_claim_document_upload_url(
    upload_request: DocumentUploadUrlRequest,
    claim_id: str = Path(..., description="Claim ID or Claim Number"),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentUploadUrlResponse:
    """
    Get a signed URL for uploading a document to a claim.

    Args:
        upload_request: Upload request data
        claim_id: Claim ID or Claim Number
        document_service: Document service

    Returns:
        Upload URL, document ID, and expiration time
    """
    upload_url, document_id, storage_path, expires_at = document_service.generate_upload_url(
        claim_id=claim_id,
        file_name=upload_request.file_name,
        content_type=upload_request.content_type,
        document_type=upload_request.document_type,
    )

    return DocumentUploadUrlResponse(
        upload_url=upload_url,
        document_id=document_id,
        expires_at=expires_at,
    )


@api_router.post("", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
def create_claim_document(
    claim_id: str = Path(..., description="Claim ID or Claim Number"),
    document_data: DocumentCreate = Body(..., description="Document data"),
    current_user: User = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentResponse:
    """
    Create a new document record for a claim.

    This endpoint expects the document to have already been uploaded to the storage.
    Use the upload-url endpoint first to get a URL for uploading the file.

    Args:
        claim_id: Claim ID or Claim Number
        document_data: Document data
        current_user: Current authenticated user
        document_service: Document service

    Returns:
        Created document
    """
    # Note: The document service will check permissions to create a document for the claim
    document = document_service.create_document(
        claim_id=claim_id,
        document_data=document_data,
        uploaded_by=current_user.id,
    )

    return document


@api_router.post("/direct-upload", response_model=DocumentResponse, status_code=status.HTTP_201_CREATED)
async def direct_upload_document(
    request: Request,
    claim_id: str = Path(..., description="Claim ID or Claim Number"),
    file: UploadFile = File(..., description="File to upload"),
    document_type: DocumentType = Form(..., description="Document type"),
    document_name: Optional[str] = Form(None, description="Document display name"),
    document_description: Optional[str] = Form(None, description="Document description"),
    current_user: User = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentResponse:
    """
    Upload a file directly and create a document record.

    This endpoint handles both file upload and record creation in one request.
    Maximum file size: 100MB.

    Args:
        request: Request object
        claim_id: Claim ID or Claim Number
        file: File to upload
        document_type: Document type
        document_name: Document display name (optional)
        document_description: Document description (optional)
        current_user: Current authenticated user
        document_service: Document service

    Returns:
        Created document
    """
    # Check file size
    # Note: This is just a sanity check. The actual size will be checked when the file is read.
    # Content-Length header may not be accurate or present, so we'll check as we read.
    content_length = request.headers.get("content-length")
    max_size = 100 * 1024 * 1024  # 100MB
    if content_length and int(content_length) > max_size:
        raise BadRequestError(
            message=f"File size exceeds maximum limit of 100MB",
            details=create_error_detail(
                resource="file",
                reason="file_too_large",
                context={
                    "max_size_bytes": max_size,
                    "provided_size_bytes": content_length,
                },
            ),
        )

    # Read file (with size check)
    file_contents = await file.read()
    if len(file_contents) > max_size:
        raise BadRequestError(
            message=f"File size exceeds maximum limit of 100MB",
            details=create_error_detail(
                resource="file",
                reason="file_too_large",
                context={
                    "max_size_bytes": max_size,
                    "provided_size_bytes": len(file_contents),
                },
            ),
        )

    # Use service to handle upload
    try:
        document = document_service.direct_upload(
            claim_id=claim_id,
            file_name=file.filename,
            content_type=file.content_type,
            file_content=file_contents,
            document_type=document_type,
            document_name=document_name,
            document_description=document_description,
        )
        return document
    except Exception as e:
        logger.error(f"Error in direct upload: {str(e)}", exc_info=True)
        if isinstance(e, (BadRequestError, StorageError, ValidationError, NotFoundError, AuthorizationError)):
            raise
        raise StorageError(f"Error uploading file: {str(e)}")


@api_router.get("/all", response_model=DocumentList)
def list_all_documents(
    skip: int = 0,
    limit: int = 100,
    document_type: Optional[DocumentType] = None,
    keyword: Optional[str] = None,
    document_service: DocumentService = Depends(get_document_service),
) -> DocumentList:
    """
    List all documents across all claims with optional filtering.
    Requires MANAGE_DOCUMENTS permission.

    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        document_type: Filter by document type
        keyword: Search in document name, description, or filename
        document_service: Document service

    Returns:
        List of documents
    """
    documents, total = document_service.list_all_documents(
        skip=skip,
        limit=limit,
        document_type=document_type,
        keyword=keyword,
    )

    # Convert SQLAlchemy models to Pydantic models
    document_responses = [DocumentResponse.model_validate(doc) for doc in documents]

    return DocumentList(items=document_responses, total=total)
