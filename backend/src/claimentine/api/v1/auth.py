"""Authentication endpoints."""

import logging
from datetime import datetime, timedelta
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Body, <PERSON>ie, Depends, Form, Request, Response, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.auth import (
    ACCESS_TOKEN_EXPIRE_MINUTES,
    REFRESH_TOKEN_EXPIRE_DAYS,
    cleanup_expired_sessions,
    create_access_token,
    create_refresh_token,
    create_user_session,
    end_user_session,
    get_current_user,
    verify_token,
)
from claimentine.core.exceptions import AuthenticationError, NotFoundError
from claimentine.core.permissions import require_permissions
from claimentine.db.base import get_db
from claimentine.models.auth import UserSession
from claimentine.models.user import User, UserStatus
from claimentine.schemas.auth import TokenResponse, UserSessionResponse
from claimentine.services.user import UserService

# Setup logger
logger = logging.getLogger(__name__)

api_router = APIRouter()


@api_router.post("/token", response_model=TokenResponse, tags=["auth"])
def login(
    request: Request,
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db),
    remember_me: bool = Form(False),
) -> TokenResponse:
    """Login endpoint to get access and refresh tokens."""
    # Authenticate user
    user = UserService(db).authenticate_user(
        email=form_data.username,
        password=form_data.password,
    )

    # Check user status
    if user.status != UserStatus.ACTIVE:
        raise AuthenticationError("User account is not active")

    # Adjust token expiration based on remember_me flag
    access_token_expire = ACCESS_TOKEN_EXPIRE_MINUTES
    refresh_token_expire = REFRESH_TOKEN_EXPIRE_DAYS

    if remember_me:
        # Extend token lifetimes for "remember me" option
        access_token_expire = ACCESS_TOKEN_EXPIRE_MINUTES * 4  # 1 hour instead of 15 minutes
        refresh_token_expire = REFRESH_TOKEN_EXPIRE_DAYS * 4  # 28 days instead of 7 days

    # Create tokens
    access_token = create_access_token({"sub": str(user.id)}, expire_minutes=access_token_expire)
    refresh_token = create_refresh_token({"sub": str(user.id)}, expire_days=refresh_token_expire)

    # Create session ONLY for refresh token
    create_user_session(
        db=db,
        user=user,
        token=refresh_token,
        ip_address=request.client.host if request.client else "unknown",
        user_agent=request.headers.get("user-agent"),
    )

    # Set cookies for tokens
    # Access token - shorter lived
    access_expires = datetime.utcnow() + timedelta(minutes=access_token_expire)
    response.set_cookie(
        key="access_token",
        value=access_token,
        httponly=True,
        secure=True,  # Only send over HTTPS
        samesite="lax",  # Protects against CSRF while allowing normal navigation
        expires=access_expires.timestamp(),
        path="/",  # Available across the entire site
    )

    # Refresh token - longer lived
    refresh_expires = datetime.utcnow() + timedelta(days=refresh_token_expire)
    response.set_cookie(
        key="refresh_token",
        value=refresh_token,
        httponly=True,
        secure=True,
        samesite="lax",
        expires=refresh_expires.timestamp(),
        path="/",
    )

    # Still return the tokens in the response for backward compatibility
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
    )


@api_router.post("/refresh", response_model=TokenResponse, tags=["auth"])
def refresh_token(
    request: Request,
    response: Response,
    refresh_token: Optional[str] = None,
    refresh_token_cookie: Optional[str] = Cookie(None, alias="refresh_token"),
    db: Session = Depends(get_db),
) -> TokenResponse:
    """Get a new access token using a refresh token."""
    # Use token from cookie if not provided in request body
    token_to_use = refresh_token or refresh_token_cookie

    if not token_to_use:
        raise AuthenticationError("No refresh token provided")

    # Verify refresh token
    payload = verify_token(token_to_use)
    if payload.get("type") != "refresh":
        raise AuthenticationError("Invalid refresh token")

    # Get user
    user = UserService(db).get_user_by_id(payload.get("sub"))
    if not user or user.status != UserStatus.ACTIVE:
        raise AuthenticationError("User not found or inactive")

    # Create new tokens - preserve the same expiration times as the original tokens
    access_token_expire = ACCESS_TOKEN_EXPIRE_MINUTES
    refresh_token_expire = REFRESH_TOKEN_EXPIRE_DAYS

    # Check if this was a "remember me" token (longer expiration)
    try:
        # Calculate the original expiration from the payload
        token_exp = payload.get("exp")
        token_iat = payload.get("iat")
        if token_exp and token_iat:
            token_lifetime_seconds = token_exp - token_iat
            # If token lifetime is longer than standard, this was a "remember me" token
            if token_lifetime_seconds > (REFRESH_TOKEN_EXPIRE_DAYS * 24 * 60 * 60):
                access_token_expire = ACCESS_TOKEN_EXPIRE_MINUTES * 4
                refresh_token_expire = REFRESH_TOKEN_EXPIRE_DAYS * 4
    except Exception as e:
        logger.warning(f"Failed to determine token expiration settings: {e}")

    new_access_token = create_access_token({"sub": str(user.id)}, expire_minutes=access_token_expire)
    new_refresh_token = create_refresh_token({"sub": str(user.id)}, expire_days=refresh_token_expire)

    # End old session and create new one
    end_user_session(db, token_to_use)
    create_user_session(
        db=db,
        user=user,
        token=new_refresh_token,
        ip_address=request.client.host if request.client else "unknown",
        user_agent=request.headers.get("user-agent"),
    )

    # Set cookies for new tokens
    access_expires = datetime.utcnow() + timedelta(minutes=access_token_expire)
    response.set_cookie(
        key="access_token",
        value=new_access_token,
        httponly=True,
        secure=True,
        samesite="lax",
        expires=access_expires.timestamp(),
        path="/",
    )

    refresh_expires = datetime.utcnow() + timedelta(days=refresh_token_expire)
    response.set_cookie(
        key="refresh_token",
        value=new_refresh_token,
        httponly=True,
        secure=True,
        samesite="lax",
        expires=refresh_expires.timestamp(),
        path="/",
    )

    return TokenResponse(
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        token_type="bearer",
    )


@api_router.post("/logout", status_code=status.HTTP_200_OK, tags=["auth"])
def logout(
    request: Request,
    response: Response,
    refresh_token: Optional[str] = Body(None),
    refresh_token_cookie: Optional[str] = Cookie(None, alias="refresh_token"),
    db: Session = Depends(get_db),
) -> dict:
    """Logout and invalidate the refresh token."""
    token_to_use = refresh_token or refresh_token_cookie

    if token_to_use:
        try:
            # Verify and get payload from token for user_id
            payload = verify_token(token_to_use)
            if payload and payload.get("type") == "refresh":
                # End the session to invalidate the token
                end_user_session(db, token_to_use)
        except Exception as e:
            logger.warning(f"Error during logout token verification: {e}")

    # Clear cookies regardless of token validation
    response.delete_cookie(key="access_token", path="/")
    response.delete_cookie(key="refresh_token", path="/")

    return {"detail": "Successfully logged out"}


@api_router.get("/sessions", response_model=List[UserSessionResponse], tags=["auth"])
def list_sessions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> List[UserSessionResponse]:
    """List active sessions for the current user."""
    # First get all sessions
    stmt = select(UserSession).where(UserSession.user_id == current_user.id)
    sessions = list(db.scalars(stmt))

    return sessions


@api_router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["auth"])
def revoke_session(
    session_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> None:
    """Revoke a specific session."""
    stmt = select(UserSession).where(
        UserSession.id == session_id,
        UserSession.user_id == current_user.id,
    )
    session = db.scalar(stmt)
    if not session:
        raise NotFoundError(f"Session {session_id} not found")

    db.delete(session)
    db.commit()


@api_router.post("/sessions/cleanup", tags=["auth"])
def cleanup_sessions(
    db: Session = Depends(get_db),
    _: None = Depends(require_permissions("MANAGE_SECURITY_SETTINGS")),
) -> dict:
    """Clean up expired sessions.

    Requires MANAGE_SECURITY_SETTINGS permission.
    Only removes sessions that have expired based on their expires_at timestamp.
    """
    removed = cleanup_expired_sessions(db)
    return {"message": f"Cleaned up {removed} expired sessions"}
