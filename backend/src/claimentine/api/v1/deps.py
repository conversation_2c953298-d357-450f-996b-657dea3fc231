"""API Dependencies for services."""

from typing import Optional, Set, Union
from uuid import UUID

from fastapi import Depends, Path, Query
from sqlalchemy.orm import Session

from claimentine.core.auth import get_current_user, get_current_user_optional
from claimentine.core.exceptions import NotFoundError
from claimentine.db.base import get_db
from claimentine.models.claim.base import BaseClaim
from claimentine.models.user import User
from claimentine.services.attorney import AttorneyService
from claimentine.services.audit import AuditService
from claimentine.services.claim.base import BaseClaimService
from claimentine.services.claim.damage import DamageService
from claimentine.services.claim.financial import ClaimFinancialsService
from claimentine.services.claim.types import AutoClaimService, GeneralLiabilityClaimService, PropertyClaimService
from claimentine.services.config.authority import ClientAuthorityThresholdService
from claimentine.services.config.reserve import ReserveConfigurationService
from claimentine.services.config.system import SystemConfigurationService
from claimentine.services.client import ClientService
from claimentine.services.document import DocumentService
from claimentine.services.fnol import FNOLService
from claimentine.services.metrics import MetricsService
from claimentine.services.note import NoteService
from claimentine.services.report import ReportService
from claimentine.services.user import UserService
from claimentine.services.witness import WitnessService


def get_client_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> ClientService:
    """Dependency provider for ClientService."""
    return ClientService(db=db, current_user=current_user)


def get_base_claim_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    client_service: ClientService = Depends(get_client_service),
) -> BaseClaimService:
    """Dependency provider for BaseClaimService."""
    return BaseClaimService(db=db, current_user=current_user, client_service=client_service)


def get_auto_claim_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> AutoClaimService:
    """Dependency provider for AutoClaimService."""
    return AutoClaimService(db=db, current_user=current_user)


def get_property_claim_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> PropertyClaimService:
    """Dependency provider for PropertyClaimService."""
    return PropertyClaimService(db=db, current_user=current_user)


def get_fnol_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    client_service: ClientService = Depends(get_client_service),
) -> FNOLService:
    """Dependency provider for FNOLService."""
    return FNOLService(db=db, current_user=current_user, client_service=client_service)


def get_document_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> DocumentService:
    """Dependency provider for DocumentService."""
    return DocumentService(db=db, current_user=current_user)


def get_note_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> NoteService:
    """Dependency provider for NoteService."""
    return NoteService(db=db, current_user=current_user)


def get_user_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> UserService:
    """Dependency provider for UserService."""
    return UserService(db=db, current_user=current_user)


def get_claim_financials_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
) -> ClaimFinancialsService:
    """Dependency provider for ClaimFinancialsService."""
    return ClaimFinancialsService(db=db, current_user=current_user, claim_service=claim_service)


def get_claim_by_identifier(
    claim_identifier: str,
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    includes: Optional[Set[str]] = Query(
        {"customer", "gl_details", "premises_details", "bodily_injury_details"},
        description="Optional includes for claim data (customer, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)",
    ),
) -> BaseClaim:
    """Dependency to fetch a claim by either its UUID or claim number."""
    claim: Optional[BaseClaim] = None

    # Ensure we have a set for includes
    includes = includes or {"customer", "gl_details", "premises_details", "bodily_injury_details"}

    # Always include customer and type-specific details
    includes.add("customer")
    includes.add("gl_details")  # For general liability claims
    includes.add("premises_details")  # For GL premises details
    includes.add("bodily_injury_details")  # For bodily injury details

    try:
        # Attempt to parse as UUID
        claim_uuid = UUID(claim_identifier)
        claim = claim_service.get_claim_by_id(claim_uuid, includes=includes)
    except ValueError:
        # If not a valid UUID, treat as claim number
        claim = claim_service.get_claim_by_number(claim_identifier, includes=includes)

    if not claim:
        raise NotFoundError(f"Claim with identifier '{claim_identifier}' not found")

    return claim


def get_general_liability_claim_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> GeneralLiabilityClaimService:
    """Dependency provider for GeneralLiabilityClaimService."""
    return GeneralLiabilityClaimService(db=db, current_user=current_user)


def get_witness_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> WitnessService:
    """Dependency provider for WitnessService."""
    return WitnessService(db=db, current_user=current_user)


def get_attorney_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> AttorneyService:
    """Dependency provider for AttorneyService."""
    return AttorneyService(db=db, current_user=current_user)


def get_audit_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> AuditService:
    """Dependency provider for AuditService."""
    return AuditService(db=db, current_user=current_user)


def get_authority_threshold_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> ClientAuthorityThresholdService:
    """Dependency provider for ClientAuthorityThresholdService."""
    return ClientAuthorityThresholdService(db=db, current_user=current_user)


def get_reserve_config_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> ReserveConfigurationService:
    """Dependency provider for ReserveConfigurationService."""
    return ReserveConfigurationService(db=db, current_user=current_user)


def get_system_config_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> SystemConfigurationService:
    """Dependency provider for SystemConfigurationService."""
    return SystemConfigurationService(db=db, current_user=current_user)


def get_metrics_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> MetricsService:
    """Dependency provider for MetricsService."""
    return MetricsService(db=db, current_user=current_user)


def get_report_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> ReportService:
    """Dependency provider for ReportService."""
    return ReportService(db=db, current_user=current_user)


def get_damage_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
) -> DamageService:
    """Dependency provider for DamageService."""
    return DamageService(db=db, claim_service=claim_service)
