"""Audit trail API endpoints."""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Path, Query, status

from claimentine.api.v1.deps import get_audit_service, get_claim_by_identifier
from claimentine.core.exceptions import NotFoundError
from claimentine.core.permissions import require_permissions
from claimentine.models.audit import ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim
from claimentine.schemas.audit import (
    AuditSummaryResponse,
    AuditTrailCreate,
    AuditTrailFilter,
    AuditTrailResponse,
    PaginatedAuditTrailResponse,
)
from claimentine.services.audit import AuditService

# Create API router for audit trail at /claims/{claim_id}/audit
api_router = APIRouter()


@api_router.get("", response_model=PaginatedAuditTrailResponse, tags=["audit"])
def list_audit_entries(
    claim: BaseClaim = Depends(get_claim_by_identifier),
    entity_type: Optional[EntityType] = Query(None, description="Filter by entity type"),
    change_type: Optional[ChangeType] = Query(None, description="Filter by change type"),
    from_date: Optional[datetime] = Query(None, description="Filter by date range start"),
    to_date: Optional[datetime] = Query(None, description="Filter by date range end"),
    changed_by_id: Optional[UUID] = Query(None, description="Filter by user who made the change"),
    skip: int = Query(0, description="Number of items to skip (pagination)"),
    limit: int = Query(50, description="Maximum number of items to return (pagination)"),
    audit_service: AuditService = Depends(get_audit_service),
    _: None = Depends(require_permissions("VIEW_CLAIM_AUDIT")),
) -> PaginatedAuditTrailResponse:
    """Get audit trail entries for a claim with optional filtering and pagination."""
    filters = AuditTrailFilter(
        entity_type=entity_type,
        change_type=change_type,
        from_date=from_date,
        to_date=to_date,
        changed_by_id=changed_by_id,
        skip=skip,
        limit=limit,
    )
    return audit_service.get_audit_entries(claim.id, filters)


@api_router.get("/summary", response_model=AuditSummaryResponse, tags=["audit"])
def get_audit_summary(
    claim: BaseClaim = Depends(get_claim_by_identifier),
    entries_limit: int = Query(5, description="Number of recent entries to include in summary"),
    audit_service: AuditService = Depends(get_audit_service),
    _: None = Depends(require_permissions("VIEW_CLAIM_AUDIT")),
) -> AuditSummaryResponse:
    """Get a summary of audit activity for a claim."""
    return audit_service.get_audit_summary(claim.id, entries_limit=entries_limit)


@api_router.post("", response_model=AuditTrailResponse, status_code=status.HTTP_201_CREATED, tags=["audit"])
def create_audit_entry(
    audit_data: AuditTrailCreate,
    claim: BaseClaim = Depends(get_claim_by_identifier),
    audit_service: AuditService = Depends(get_audit_service),
    _: None = Depends(require_permissions("CREATE_AUDIT_ENTRIES")),
) -> AuditTrailResponse:
    """Create a manual audit entry.

    This is typically used for administrative purposes or to document
    actions that occurred outside the system.
    """
    return audit_service.create_manual_audit_entry(claim.id, audit_data)


@api_router.get("/{audit_id}", response_model=AuditTrailResponse, tags=["audit"])
def get_audit_entry(
    audit_id: UUID = Path(..., description="Audit entry ID"),
    audit_service: AuditService = Depends(get_audit_service),
    _: None = Depends(require_permissions("VIEW_CLAIM_AUDIT")),
) -> AuditTrailResponse:
    """Get a specific audit entry by ID."""
    audit_entry = audit_service.get_audit_entry(audit_id)
    if not audit_entry:
        raise NotFoundError(f"Audit entry {audit_id} not found")
    return audit_entry
