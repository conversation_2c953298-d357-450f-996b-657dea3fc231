"""API endpoints for reports."""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Path, Query

from claimentine.api.v1.deps import get_report_service
from claimentine.core.auth import get_current_user
from claimentine.models.claim.types import ClaimType
from claimentine.models.user import User
from claimentine.schemas.reports.adjuster_performance import ReportResponseAdjusterPerformance
from claimentine.schemas.reports.base import ReportRequest
from claimentine.schemas.reports.claims_by_status import ReportResponseClaimsByStatus
from claimentine.schemas.reports.claims_by_type import ReportResponseClaimsByType
from claimentine.schemas.reports.claims_kpi import ReportResponseClaimsKpis
from claimentine.schemas.reports.claims_over_time import ReportResponseClaimsOverTime
from claimentine.schemas.reports.financial_kpis import ReportResponseFinancialKpis
from claimentine.schemas.reports.payments_vs_reserves import ReportResponsePaymentsVsReserves
from claimentine.services.report import ReportService

router = APIRouter(prefix="/reports", tags=["reports"])


def _parse_date(date_str: Optional[str]) -> Optional[datetime]:
    """Parse date string to datetime."""
    if not date_str:
        return None
    try:
        return datetime.fromisoformat(date_str.replace("Z", "+00:00"))
    except ValueError:
        return None


@router.get("/claims-kpis", response_model=ReportResponseClaimsKpis)
async def get_claims_kpi_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    compare_period: bool = Query(False, description="Compare with previous period"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseClaimsKpis:
    """
    Get Claims KPI Report.

    Retrieves key performance indicators for claims, including:
    - Total claims
    - Open claims percentage
    - Closed claims percentage
    - Average resolution time

    The report can be filtered by customer_id, claim_type, or user_id.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        compare_period=compare_period,
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report("claims_kpis", request)


@router.get("/claims-by-type", response_model=ReportResponseClaimsByType)
async def get_claims_by_type_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseClaimsByType:
    """
    Get Claims by Type Report.

    Breaks down claims by their type (AUTO, PROPERTY, GENERAL_LIABILITY, etc.)
    and provides count and percentage for each type.

    The report can be filtered by customer_id or user_id.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        customer_id=customer_id,
        user_id=user_id,
    )

    return report_service.get_report("claims_by_type", request)


@router.get("/claims-by-status", response_model=ReportResponseClaimsByStatus)
async def get_claims_by_status_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseClaimsByStatus:
    """
    Get Claims by Status Report.

    Breaks down claims by their status (Open, Closed) and provides count
    and percentage for each status.

    The report can be filtered by customer_id, claim_type, or user_id.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report("claims_by_status", request)


@router.get("/claims-over-time", response_model=ReportResponseClaimsOverTime)
async def get_claims_over_time_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseClaimsOverTime:
    """
    Get Claims Over Time Report.

    Tracks new and closed claims over time periods (weekly or monthly).

    The report can be filtered by customer_id, claim_type, or user_id.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report("claims_over_time", request)


@router.get("/financial-kpis", response_model=ReportResponseFinancialKpis)
async def get_financial_kpi_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    compare_period: bool = Query(False, description="Compare with previous period"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseFinancialKpis:
    """
    Get Financial KPI Report.

    Retrieves key financial performance indicators, including:
    - Total reserves
    - Total payments
    - Average reserve per claim
    - Average payment per claim

    The report can be filtered by customer_id, claim_type, or user_id,
    and compared with the previous period.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        compare_period=compare_period,
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report("financial_kpis", request)


@router.get("/payments-vs-reserves", response_model=ReportResponsePaymentsVsReserves)
async def get_payments_vs_reserves_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponsePaymentsVsReserves:
    """
    Get Payments vs Reserves Report.

    Compares payments and reserves over time periods (weekly or monthly).

    The report can be filtered by customer_id, claim_type, or user_id.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report("payments_vs_reserves", request)


@router.get("/adjuster-performance", response_model=ReportResponseAdjusterPerformance)
async def get_adjuster_performance_report(
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> ReportResponseAdjusterPerformance:
    """
    Get Adjuster Performance Report.

    Provides performance metrics for adjusters, including:
    - Claims handled
    - Average resolution time
    - Total payments authorized
    - Pending tasks
    - Overdue tasks

    The report can be filtered by customer_id or claim_type.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        customer_id=customer_id,
        claim_type=claim_type,
    )

    return report_service.get_report("adjuster_performance", request)


@router.get("/{report_name}", response_model=Dict[str, Any])
async def get_report_by_name(
    report_name: str = Path(..., description="Name of the report"),
    period: Optional[str] = Query(None, description="Time period for report"),
    start_date: Optional[str] = Query(None, description="Start date (ISO format)"),
    end_date: Optional[str] = Query(None, description="End date (ISO format)"),
    compare_period: bool = Query(False, description="Compare with previous period"),
    customer_id: Optional[str] = Query(None, description="Filter by customer ID"),
    claim_type: Optional[ClaimType] = Query(None, description="Filter by claim type"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    report_service: ReportService = Depends(get_report_service),
    current_user: User = Depends(get_current_user),
) -> Dict[str, Any]:
    """
    Get Report by Name.

    Generic endpoint to retrieve any report by its name.

    For most use cases, you should use the specific report endpoints
    instead of this generic one.
    """
    request = ReportRequest(
        period=period,
        start_date=_parse_date(start_date),
        end_date=_parse_date(end_date),
        compare_period=compare_period,
        customer_id=customer_id,
        claim_type=claim_type,
        user_id=user_id,
    )

    return report_service.get_report(report_name, request)
