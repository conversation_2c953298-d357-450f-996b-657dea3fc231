"""Witness API endpoints."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Path, status

from claimentine.api.v1.deps import get_claim_by_identifier, get_witness_service
from claimentine.core.exceptions import NotFoundError
from claimentine.models.claim.base import BaseClaim
from claimentine.schemas.witness import Witness<PERSON><PERSON>, WitnessResponse, WitnessUpdate
from claimentine.services.witness import WitnessService

# Create API router for witnesses at /claims/{claim_id}/witnesses
api_router = APIRouter()


@api_router.get("", response_model=List[WitnessResponse], tags=["witnesses"])
def list_witnesses(
    claim: BaseClaim = Depends(get_claim_by_identifier),
    witness_service: WitnessService = Depends(get_witness_service),
) -> List[WitnessResponse]:
    """Get all witnesses for a claim."""
    return witness_service.get_witnesses(claim.id)


@api_router.post("", response_model=WitnessResponse, status_code=status.HTTP_201_CREATED, tags=["witnesses"])
def create_witness(
    witness_data: WitnessCreate,
    claim: BaseClaim = Depends(get_claim_by_identifier),
    witness_service: WitnessService = Depends(get_witness_service),
) -> WitnessResponse:
    """Create a new witness for a claim."""
    return witness_service.create_witness(claim.id, witness_data)


@api_router.get("/{witness_id}", response_model=WitnessResponse, tags=["witnesses"])
def get_witness(
    witness_id: UUID = Path(..., description="Witness ID"),
    witness_service: WitnessService = Depends(get_witness_service),
) -> WitnessResponse:
    """Get a specific witness by ID."""
    witness = witness_service.get_witness(witness_id)
    if not witness:
        raise NotFoundError(f"Witness {witness_id} not found")
    return witness


@api_router.patch("/{witness_id}", response_model=WitnessResponse, tags=["witnesses"])
def update_witness(
    witness_data: WitnessUpdate,
    witness_id: UUID = Path(..., description="Witness ID"),
    witness_service: WitnessService = Depends(get_witness_service),
) -> WitnessResponse:
    """Update an existing witness."""
    return witness_service.update_witness(witness_id, witness_data)


@api_router.delete("/{witness_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["witnesses"])
def delete_witness(
    witness_id: UUID = Path(..., description="Witness ID"),
    witness_service: WitnessService = Depends(get_witness_service),
) -> None:
    """Delete a witness."""
    witness_service.delete_witness(witness_id)
    return None
