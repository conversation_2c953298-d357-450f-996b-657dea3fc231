"""API endpoints for Task management."""

import logging
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Path, Query, status
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from claimentine.api.v1.deps import get_claim_by_identifier
from claimentine.core.auth import get_current_user, get_current_user_optional
from claimentine.core.exceptions import (
    BadRequestError,
    DatabaseError,
    NotFoundError,
    ServiceUnavailableError,
    ValidationError,
    create_error_detail,
)
from claimentine.db.session import get_db
from claimentine.models.claim.base import BaseClaim
from claimentine.models.task import Task, TaskPriority, TaskStatus
from claimentine.models.user import User
from claimentine.schemas.task import PaginatedTaskResponse, TaskCreate, TaskCreateRequestBody, TaskRead, TaskUpdate
from claimentine.services.task import TaskService

logger = logging.getLogger(__name__)

# Router for task creation (will be nested under claims)
task_create_router = APIRouter()

# Router for managing existing tasks (will be under /tasks)
task_manage_router = APIRouter()

# Standard router export - for compatibility with api.py
api_router = APIRouter()

# We need to include sub-routers with proper prefixes for api.py compatibility
# Note: in main.py, we include these directly, so this is for api.py compatibility
api_router.include_router(task_manage_router, prefix="/tasks")
# We can't directly include the task_create_router here because it needs claim_id parameter
# which can only be provided when mounted under claims router


# --- Dependency for Task Service --- #
def get_task_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> TaskService:
    """Dependency to get the TaskService instance."""
    return TaskService(db=db, current_user=current_user)


# --- Dependency to get Task by Identifier --- #
def get_task_from_identifier(
    task_identifier: str = Path(..., description="UUID or HR_ID of the task"),
    service: TaskService = Depends(get_task_service),
) -> Task:
    """Dependency to fetch a Task by UUID or hr_id, raising 404 if not found."""
    task = service.resolve_task(task_identifier=task_identifier)
    if not task:
        logger.warning(f"Task with identifier '{task_identifier}' not found.")
        raise NotFoundError(
            message=f"Task with identifier '{task_identifier}' not found",
            details=create_error_detail(resource="task", identifier=str(task_identifier)),
        )
    return task


# --- Input Schemas for Specific Actions --- #


class TaskAssignSchema(BaseModel):
    assignee_id: Optional[UUID] = Field(..., description="UUID of the user to assign the task to, or null to unassign")


class TaskStatusSchema(BaseModel):
    status: TaskStatus = Field(..., description="The new status for the task")


# --- API Endpoints --- #


@task_create_router.post(
    "/",
    response_model=TaskRead,
    status_code=status.HTTP_201_CREATED,
    summary="Create Task for Claim",
    description="Create a new task associated with a specific claim. Requires 'CREATE_TASK' permission.",
)
def create_task(
    task_in: TaskCreateRequestBody,
    claim_identifier: str = Path(..., description="The ID or Number of the claim to associate the task with."),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
):
    """Creates a new task associated with the given claim identifier (UUID or Number). Permissions checked in service layer."""
    logger.info(
        f"User {current_user.email} attempting to create task for claim {claim_identifier} (resolved ID: {claim.id})"
    )
    task_data_from_request = task_in.model_dump()

    service_task_create_data = {**task_data_from_request, "claim_id": claim.id}

    try:
        task_to_create = TaskCreate(**service_task_create_data)
        new_task = service.create(obj_in=task_to_create, creator=current_user)
        logger.info(
            f"Task '{new_task.hr_id or new_task.id}' created successfully for claim {claim.id} by user {current_user.email}"
        )
        return new_task
    except ValidationError as e:
        logger.warning(f"Validation error creating task for claim {claim.id}: {e}")
        # ValidationError is already a proper AppException, so we can just re-raise it
        raise
    except NotFoundError as e:
        logger.warning(f"Not found error creating task for claim {claim.id}: {e}")
        # NotFoundError is already a proper AppException, so we can just re-raise it
        raise
    except DatabaseError as e:
        logger.exception(f"Database error creating task for claim {claim.id}: {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error creating task for claim {claim.id}: {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while creating the task.",
            details=create_error_detail(resource="task", reason=str(e)),
        )


@task_manage_router.get(
    "/",
    response_model=PaginatedTaskResponse,
    summary="List Tasks",
    description="Get a list of tasks, optionally filtered. Requires 'LIST_TASKS' permission.",
)
def list_tasks(
    claim_id: Optional[UUID] = Query(None, description="Filter tasks by claim ID"),
    assigned_to: Optional[UUID] = Query(None, description="Filter tasks assigned to a specific user ID"),
    status: Optional[TaskStatus] = Query(None, description="Filter tasks by status"),
    priority: Optional[TaskPriority] = Query(None, description="Filter tasks by priority"),
    title: Optional[str] = Query(None, description="Filter tasks by partial title match (case-insensitive)"),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
) -> PaginatedTaskResponse:
    """Lists tasks based on provided filters. Permissions checked in service layer."""
    logger.info(
        f"User {current_user.email} listing tasks with filters: claim_id={claim_id}, "
        f"assigned_to={assigned_to}, status={status}, priority={priority}, title={title} (skip={skip}, limit={limit})"
    )

    try:
        tasks, total_count = service.get_multi(
            skip=skip,
            limit=limit,
            claim_id=claim_id,
            assigned_to=assigned_to,
            status=status,
            priority=priority,
            title=title,
        )
        return PaginatedTaskResponse(items=tasks, total=total_count, skip=skip, limit=limit)
    except DatabaseError as e:
        logger.exception(f"Database error listing tasks: {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error listing tasks: {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while listing tasks.",
            details=create_error_detail(resource="task", reason=str(e)),
        )


@task_manage_router.get(
    "/{task_identifier}",
    response_model=TaskRead,
    status_code=status.HTTP_200_OK,
    summary="Get Task",
    description="Get details of a specific task by its UUID or HR ID. Requires 'VIEW_TASKS' permission.",
)
def get_task(
    task: Task = Depends(get_task_from_identifier),
    current_user: User = Depends(get_current_user),
):
    """Gets a specific task by its identifier. Permissions checked in service layer."""
    logger.info(f"User {current_user.email} viewing task '{task.hr_id or task.id}'")
    return task


@task_manage_router.put(
    "/{task_identifier}",
    response_model=TaskRead,
    summary="Update Task",
    description="Update details of an existing task. Requires 'UPDATE_TASKS' permission.",
    status_code=status.HTTP_200_OK,
)
def update_task(
    task_in: TaskUpdate,
    task: Task = Depends(get_task_from_identifier),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
):
    """Updates an existing task. Permissions checked in service layer."""
    logger.info(f"User {current_user.email} attempting to update task '{task.hr_id or task.id}'")
    try:
        updated_task = service.update(db_obj=task, obj_in=task_in)
        logger.info(f"Task '{updated_task.hr_id or updated_task.id}' updated successfully by {current_user.email}")
        return updated_task
    except DatabaseError as e:
        logger.exception(f"Database error updating task '{task.hr_id or task.id}': {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error updating task '{task.hr_id or task.id}': {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while updating the task.",
            details=create_error_detail(resource="task", identifier=str(task.hr_id or task.id), reason=str(e)),
        )


@task_manage_router.patch(
    "/{task_identifier}/assign",
    response_model=TaskRead,
    summary="Assign Task",
    description="Assign or unassign a task to a specific user. Requires 'ASSIGN_TASK' permission.",
    status_code=status.HTTP_200_OK,
)
def assign_task(
    assign_data: TaskAssignSchema,
    task: Task = Depends(get_task_from_identifier),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
):
    """Assigns a task to a user. If assignee_id is null, unassigns the task. Permissions checked in service layer."""
    logger.info(f"User {current_user.email} attempting to assign task '{task.hr_id or task.id}'")
    try:
        updated_task = service.assign(db_obj=task, assignee_id=assign_data.assignee_id)
        if assign_data.assignee_id:
            logger.info(
                f"Task '{updated_task.hr_id or updated_task.id}' assigned to user {assign_data.assignee_id} by {current_user.email}"
            )
        else:
            logger.info(f"Task '{updated_task.hr_id or updated_task.id}' unassigned by {current_user.email}")
        return updated_task
    except DatabaseError as e:
        logger.exception(f"Database error assigning task '{task.hr_id or task.id}': {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except NotFoundError as e:
        # User might not exist
        logger.warning(f"Not found error assigning task '{task.hr_id or task.id}': {e}")
        # NotFoundError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error assigning task '{task.hr_id or task.id}': {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while assigning the task.",
            details=create_error_detail(resource="task", identifier=str(task.hr_id or task.id), reason=str(e)),
        )


@task_manage_router.patch(
    "/{task_identifier}/status",
    response_model=TaskRead,
    summary="Change Task Status",
    description="Change the status of a task. Requires 'CHANGE_TASK_STATUS' permission.",
    status_code=status.HTTP_200_OK,
)
def change_task_status(
    status_data: TaskStatusSchema,
    task: Task = Depends(get_task_from_identifier),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
):
    """Changes a task's status. Permissions checked in service layer."""
    logger.info(
        f"User {current_user.email} attempting to change task '{task.hr_id or task.id}' "
        f"status from {task.status} to {status_data.status}"
    )

    # Validate status transition if needed
    if task.status == status_data.status:
        logger.info(f"Task '{task.hr_id or task.id}' already has status {status_data.status}. No change needed.")
        return task

    try:
        updated_task = service.change_status(db_obj=task, status=status_data.status)
        logger.info(
            f"Task '{updated_task.hr_id or updated_task.id}' status changed from {task.status} to {updated_task.status} by {current_user.email}"
        )
        return updated_task
    except ValidationError as e:
        # This might happen if the status transition is invalid
        logger.warning(f"Invalid status transition for task '{task.hr_id or task.id}': {e}")
        # ValidationError is already a proper AppException, so we can just re-raise it
        raise
    except DatabaseError as e:
        logger.exception(f"Database error changing task status '{task.hr_id or task.id}': {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error changing task status '{task.hr_id or task.id}': {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while changing the task status.",
            details=create_error_detail(resource="task", identifier=str(task.hr_id or task.id), reason=str(e)),
        )


@task_manage_router.delete(
    "/{task_identifier}",
    response_model=TaskRead,
    status_code=status.HTTP_200_OK,
    summary="Delete Task",
    description="Delete a task by its UUID or HR ID. Requires 'DELETE_TASK' permission.",
)
def delete_task(
    task: Task = Depends(get_task_from_identifier),
    service: TaskService = Depends(get_task_service),
    current_user: User = Depends(get_current_user),
):
    """Deletes a task. Permissions checked in service layer."""
    logger.info(f"User {current_user.email} attempting to delete task '{task.hr_id or task.id}'")
    try:
        deleted_task_data = TaskRead.model_validate(task)
        service.remove(db_obj=task)
        logger.info(
            f"Task '{deleted_task_data.hr_id or deleted_task_data.id}' deleted successfully by {current_user.email}"
        )
        return deleted_task_data
    except DatabaseError as e:
        logger.exception(f"Database error deleting task '{task.hr_id or task.id}': {e}", exc_info=True)
        # DatabaseError is already a proper AppException, so we can just re-raise it
        raise
    except Exception as e:
        logger.exception(f"Unexpected error deleting task '{task.hr_id or task.id}': {e}", exc_info=True)
        raise ServiceUnavailableError(
            message="An unexpected error occurred while deleting the task.",
            details=create_error_detail(resource="task", identifier=str(task.hr_id or task.id), reason=str(e)),
        )


# --- Placeholder for other API Endpoints --- #
