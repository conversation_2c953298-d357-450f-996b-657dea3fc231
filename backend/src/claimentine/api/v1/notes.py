"""API endpoints for managing notes."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, Query, status

from claimentine.api.v1.deps import get_claim_by_identifier, get_note_service
from claimentine.core.auth import get_current_user  # Required for checking logged-in status implicitly
from claimentine.core.exceptions import NotFoundError  # Removed ValueError
from claimentine.models.claim.base import BaseClaim  # Added BaseClaim
from claimentine.models.user import User  # Needed for get_current_user dependency
from claimentine.schemas.note import (  # Added NoteCreateRequestBody
    NoteCreate,
    NoteCreateRequestBody,
    NoteResponse,
    NoteUpdate,
)
from claimentine.services.note import NoteService

api_router = APIRouter()


@api_router.post(
    "/claims/{claim_identifier}/notes",
    response_model=NoteResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["notes"],
)
def create_note_for_claim(
    claim_identifier: str,
    note_in: NoteCreateRequestBody,
    claim: BaseClaim = Depends(get_claim_by_identifier),
    note_service: NoteService = Depends(get_note_service),
    current_user: User = Depends(get_current_user),  # Ensures user is logged in
) -> NoteResponse:
    """Create a new note associated with a specific claim (identified by UUID or Number)."""

    # Construct the full NoteCreate object that the service expects
    full_note_data = NoteCreate(content=note_in.content, claim_id=claim.id)

    note = note_service.create_note(note_data=full_note_data)

    # Manually map author info for the response
    response_data = note.__dict__.copy()
    if note.author:
        response_data["author_id"] = note.author.id
        response_data["author_email"] = note.author.email
        response_data["author"] = note.author.full_name or note.author.email.split("@")[0]
    else:
        response_data["author_id"] = None
        response_data["author_email"] = None
        response_data["author"] = None

    return NoteResponse.model_validate(response_data)


@api_router.get("/claims/{claim_id}/notes", response_model=List[NoteResponse], tags=["notes"])
def list_notes_for_claim(
    claim_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    note_service: NoteService = Depends(get_note_service),
    current_user: User = Depends(get_current_user),  # Ensures user is logged in
) -> List[NoteResponse]:
    """List all notes associated with a specific claim."""
    notes = note_service.list_notes_for_claim(claim_id=claim_id, skip=skip, limit=limit)

    # Manually map author info for the response list
    response_list = []
    for note in notes:
        response_data = note.__dict__.copy()
        if note.author:
            response_data["author_id"] = note.author.id
            response_data["author_email"] = note.author.email
            response_data["author"] = note.author.full_name or note.author.email.split("@")[0]
        else:
            response_data["author_id"] = None
            response_data["author_email"] = None
            response_data["author"] = None
        response_list.append(NoteResponse.model_validate(response_data))

    return response_list


@api_router.get("/notes/{note_id}", response_model=NoteResponse, tags=["notes"])
def get_note(
    note_id: UUID,
    note_service: NoteService = Depends(get_note_service),
    current_user: User = Depends(get_current_user),  # Ensures user is logged in
) -> NoteResponse:
    """Retrieve a specific note by its ID."""
    note = note_service.get_note_by_id(note_id=note_id)
    if not note:
        raise NotFoundError(f"Note {note_id} not found")

    # Manually map author info for the response
    response_data = note.__dict__.copy()
    if note.author:
        response_data["author_id"] = note.author.id
        response_data["author_email"] = note.author.email
        response_data["author"] = note.author.full_name or note.author.email.split("@")[0]
    else:
        response_data["author_id"] = None
        response_data["author_email"] = None
        response_data["author"] = None

    return NoteResponse.model_validate(response_data)


@api_router.patch("/notes/{note_id}", response_model=NoteResponse, tags=["notes"])
def update_note(
    note_id: UUID,
    note_in: NoteUpdate,
    note_service: NoteService = Depends(get_note_service),
    current_user: User = Depends(get_current_user),  # Ensures user is logged in
) -> NoteResponse:
    """Update the content of a specific note."""
    note = note_service.update_note(note_id=note_id, note_data=note_in)
    # Note: update_note already re-fetches and raises NotFoundError if needed

    # Manually map author info for the response
    # Reload author if not already loaded (update_note doesn't load it)
    refreshed_note = note_service.get_note_by_id(note_id=note.id, load_author=True)
    if not refreshed_note:
        # Should not happen if update succeeded, but safety check
        raise NotFoundError(f"Note {note_id} not found after update attempt.")

    response_data = refreshed_note.__dict__.copy()
    if refreshed_note.author:
        response_data["author_id"] = refreshed_note.author.id
        response_data["author_email"] = refreshed_note.author.email
        response_data["author"] = refreshed_note.author.full_name or refreshed_note.author.email.split("@")[0]
    else:
        response_data["author_id"] = None
        response_data["author_email"] = None
        response_data["author"] = None

    return NoteResponse.model_validate(response_data)


@api_router.delete("/notes/{note_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["notes"])
def delete_note(
    note_id: UUID,
    note_service: NoteService = Depends(get_note_service),
    current_user: User = Depends(get_current_user),  # Ensures user is logged in
) -> None:
    """Delete a specific note."""
    note_service.delete_note(note_id=note_id)
    # Note: delete_note handles NotFoundError
    return None
