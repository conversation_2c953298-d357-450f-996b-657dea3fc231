"""User management API endpoints."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session

from claimentine.api.v1.deps import get_user_service
from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import AuthorizationError, NotFoundError, ValidationError
from claimentine.db.base import get_db
from claimentine.models.authority import AuthorityRole
from claimentine.models.user import User, UserRole, UserStatus
from claimentine.schemas.user import PasswordChange, UserCreate, UserResponse, UserUpdate
from claimentine.services.user import UserService

api_router = APIRouter()


@api_router.get("/me", response_model=UserResponse, tags=["users"])
def get_current_user_details(
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Get current user details."""
    # Get user permissions using the service
    permissions = user_service.get_user_permissions(current_user)

    # Create response with permissions
    response_dict = {**current_user.__dict__, "permissions": list(permissions)}
    return UserResponse.model_validate(response_dict)


@api_router.get("", response_model=List[UserResponse], tags=["users"])
def list_users(
    *,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None, description="Search in names, email, and department"),
    role: Optional[UserRole] = None,
    authority_role: Optional[AuthorityRole] = None,
    status: Optional[UserStatus] = None,
    department: Optional[str] = None,
    email: Optional[str] = None,
    user_service: UserService = Depends(get_user_service),
) -> List[UserResponse]:
    """List users with optional filtering and search. Permissions are checked at the service layer."""
    users = user_service.list_users(
        skip=skip,
        limit=limit,
        search=search,
        role=role,
        authority_role=authority_role,
        status=status,
        department=department,
        email=email,
    )

    # Convert users to response objects with permissions
    responses = []
    for user in users:
        permissions = user_service.get_user_permissions(user)
        response_dict = {**user.__dict__, "permissions": list(permissions)}
        responses.append(UserResponse.model_validate(response_dict))

    return responses


@api_router.post("", response_model=UserResponse, status_code=status.HTTP_201_CREATED, tags=["users"])
def create_user(
    *,
    user_data: UserCreate,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Create a new user. Permissions are checked at the service layer."""
    # Only ADMIN can create other ADMIN users
    if user_data.role == UserRole.ADMIN and current_user.role != UserRole.ADMIN:
        raise AuthorizationError("Only ADMIN can create other ADMIN users")

    # Only users with higher authority can grant authority roles
    if user_data.authority_role != AuthorityRole.NO_AUTHORITY:
        # Allow UNLIMITED users to assign any authority level including UNLIMITED
        # For other authority levels, require higher authority
        if current_user.authority_role != AuthorityRole.UNLIMITED:
            if not current_user.authority_role.has_authority_over(user_data.authority_role):
                raise AuthorizationError(
                    f"Cannot assign authority role {user_data.authority_role}. "
                    "You can only assign authority roles lower than your own."
                )

    user = user_service.create_user(user_data)
    return user


@api_router.patch("/me", response_model=UserResponse, tags=["users"])
def update_current_user(
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Update current user's own profile. Permissions are checked at the service layer."""
    # Create a filtered update data with only allowed fields
    allowed_fields = ["first_name", "last_name", "phone_number", "timezone", "preferences", "department", "job_title"]

    filtered_data = UserUpdate.model_validate(
        {k: v for k, v in user_data.model_dump(exclude_unset=True).items() if k in allowed_fields}
    )

    # Don't allow changing sensitive fields like role, status, authority_role
    if not filtered_data.model_dump(exclude_unset=True):
        # No valid fields to update
        raise ValidationError("No valid fields to update")

    updated_user = user_service.update_user(current_user.id, filtered_data)

    # Get user permissions using the service
    permissions = user_service.get_user_permissions(updated_user)

    # Create response with permissions
    response_dict = {**updated_user.__dict__, "permissions": list(permissions)}
    return UserResponse.model_validate(response_dict)


@api_router.get("/{user_id}", response_model=UserResponse, tags=["users"])
def get_user(
    user_id: UUID,
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Get user by ID. Permissions are checked at the service layer."""
    user = user_service.get_user_by_id(user_id)
    if not user:
        raise NotFoundError(f"User {user_id} not found")

    # Get user permissions using the service
    permissions = user_service.get_user_permissions(user)

    # Create response with permissions
    response_dict = {**user.__dict__, "permissions": list(permissions)}
    return UserResponse.model_validate(response_dict)


@api_router.patch("/{user_id}", response_model=UserResponse, tags=["users"])
def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Update user by ID. Permissions are checked at the service layer."""
    # Get target user
    target_user = user_service.get_user_by_id(user_id)
    if not target_user:
        raise NotFoundError(f"User {user_id} not found")

    # Only ADMIN can modify ADMIN users
    if current_user.role != UserRole.ADMIN:
        if target_user.role == UserRole.ADMIN:
            raise AuthorizationError("Cannot modify ADMIN users")

    # Validate authority role changes
    if user_data.authority_role is not None:
        # Allow UNLIMITED users to assign any authority level including UNLIMITED
        # For other authority levels, require higher authority
        if current_user.authority_role != AuthorityRole.UNLIMITED:
            # Check if user has authority to change to this role
            if not current_user.authority_role.has_authority_over(user_data.authority_role):
                raise AuthorizationError(
                    f"Cannot assign authority role {user_data.authority_role}. "
                    "You can only assign authority roles lower than your own."
                )

            # Check if user has authority to modify current role
            if not current_user.authority_role.has_authority_over(target_user.authority_role):
                raise AuthorizationError(
                    f"Cannot modify user with authority role {target_user.authority_role}. "
                    "You can only modify users with lower authority roles."
                )

    user = user_service.update_user(user_id, user_data)
    return user


@api_router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["users"])
def delete_user(
    user_id: UUID,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> None:
    """Delete user by ID. Permissions are checked at the service layer."""
    # Cannot delete yourself
    if user_id == current_user.id:
        raise AuthorizationError("Cannot delete your own account")

    # Only ADMIN can delete other ADMIN users
    if current_user.role != UserRole.ADMIN:
        target_user = user_service.get_user_by_id(user_id)
        if target_user and target_user.role == UserRole.ADMIN:
            raise AuthorizationError("Cannot delete ADMIN users")

    user_service.delete_user(user_id)


@api_router.post("/password-change", response_model=UserResponse, tags=["users"])
def change_password(
    password_data: PasswordChange,
    current_user: User = Depends(get_current_user),
    user_service: UserService = Depends(get_user_service),
) -> UserResponse:
    """Change the current user's password."""
    updated_user = user_service.change_user_password(
        user_id=current_user.id,
        current_password=password_data.current_password,
        new_password=password_data.new_password,
    )

    # Get user permissions using the service
    permissions = user_service.get_user_permissions(updated_user)

    # Create response with permissions
    response_dict = {**updated_user.__dict__, "permissions": list(permissions)}
    return UserResponse.model_validate(response_dict)
