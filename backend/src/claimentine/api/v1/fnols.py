"""FNOL API endpoints."""

from typing import List, Optional, Set, Union
from uuid import UUID

from fastapi import APIRouter, Depends, Query, status

from claimentine.api.v1.deps import (
    get_auto_claim_service,
    get_fnol_service,
    get_general_liability_claim_service,
    get_property_claim_service,
)
from claimentine.core.exceptions import NotFoundError, ValidationError
from claimentine.core.permissions import require_permissions
from claimentine.models.claim.base import ClaimType
from claimentine.schemas.claim.details import AutoDetailsCreate, GeneralLiabilityDetailsCreate, PropertyDetailsCreate
from claimentine.schemas.claim.types import AutoClaimCreate, GeneralLiabilityClaimCreate, PropertyClaimCreate
from claimentine.schemas.fnol import FNOLConversionResponse, FNOLCreate, FNOLResponse, FNOLUpdate
from claimentine.services.claim.types import AutoClaimService, GeneralLiabilityClaimService, PropertyClaimService
from claimentine.services.fnol import FNOLService

api_router = APIRouter()


def parse_claim_type(
    claim_type: str = Query(..., description="Type of claim to create (case-insensitive)")
) -> ClaimType:
    """Parse claim type parameter, handling case-insensitive input."""
    try:
        # Convert to uppercase and validate against enum
        normalized_type = claim_type.upper()
        return ClaimType(normalized_type)
    except ValueError:
        valid_types = [ct.value for ct in ClaimType]
        raise ValidationError(f"Invalid claim type '{claim_type}'. Must be one of: {', '.join(valid_types)}")


@api_router.get("", response_model=List[FNOLResponse], tags=["fnols"])
def list_fnols(
    *,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    customer_id: Optional[UUID] = Query(None, description="Filter by customer ID"),
    include: Optional[Set[str]] = Query(
        {"customer"},  # Always include customer by default
        description="Include related items (customer, claims)",
    ),
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> List[FNOLResponse]:
    """List FNOLs with filtering based on user's permissions."""
    # Add customer to includes if not present
    include = include or set()
    include.add("customer")

    fnols = service.list_fnols(
        skip=skip,
        limit=limit,
        customer_id=customer_id,
        includes=include,
    )
    return fnols


@api_router.get("/number/{fnol_number}", response_model=FNOLResponse, tags=["fnols"])
def get_fnol_by_number(
    fnol_number: str,
    include: Optional[Set[str]] = Query(
        {"customer", "claims"},
        description="Include related items (customer, claims)",
    ),
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> FNOLResponse:
    """Get FNOL by number."""
    fnol = service.get_fnol_by_number(fnol_number, includes=include)
    if not fnol:
        raise NotFoundError(f"FNOL {fnol_number} not found")
    return fnol


@api_router.get("/{fnol_id}", response_model=FNOLResponse, tags=["fnols"])
def get_fnol(
    fnol_id: UUID,
    include: Optional[Set[str]] = Query(
        {"customer", "claims"},
        description="Include related items (customer, claims)",
    ),
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> FNOLResponse:
    """Get FNOL by ID."""
    fnol = service.get_fnol_by_id(fnol_id, includes=include)
    if not fnol:
        raise NotFoundError(f"FNOL {fnol_id} not found")
    return fnol


@api_router.post("", response_model=FNOLResponse, status_code=status.HTTP_201_CREATED, tags=["fnols"])
def create_fnol(
    *,
    fnol_data: FNOLCreate,
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("CREATE_CLAIMS")),
) -> FNOLResponse:
    """Create a new FNOL."""
    return service.create_fnol(fnol_data)


@api_router.patch("/{fnol_id}", response_model=FNOLResponse, tags=["fnols"])
def update_fnol(
    fnol_id: UUID,
    fnol_data: FNOLUpdate,
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("EDIT_CLAIMS")),
) -> FNOLResponse:
    """Update FNOL by ID."""
    return service.update_fnol(fnol_id, fnol_data)


@api_router.delete("/{fnol_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["fnols"])
def delete_fnol(
    fnol_id: UUID,
    service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("DELETE_CLAIMS")),
) -> None:
    """Delete FNOL by ID."""
    service.delete_fnol(fnol_id)


@api_router.post(
    "/{fnol_id}/convert",
    response_model=FNOLConversionResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["fnols", "claims"],
)
def convert_fnol_to_claim(
    fnol_id: UUID,
    claim_type: ClaimType = Depends(parse_claim_type),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    fnol_service: FNOLService = Depends(get_fnol_service),
    _: None = Depends(require_permissions("CREATE_CLAIMS")),
) -> FNOLConversionResponse:
    """Convert an FNOL to a claim.

    Takes an FNOL ID and creates a new claim with the FNOL linked to it.
    Returns a simplified response to avoid circular references.
    """
    # Get the FNOL
    fnol = fnol_service.get_fnol_by_id(fnol_id)
    if not fnol:
        raise NotFoundError(f"FNOL {fnol_id} not found")

    # Create base claim data from FNOL
    base_claim_data = {
        "customer_id": fnol.customer_id,
        "claimant_name": fnol.reported_by,  # Use reporter as initial claimant
        "description": fnol.description,
        "incident_date": fnol.incident_date,
        "incident_location": fnol.incident_location,
        "fnol_id": fnol_id,  # Link to FNOL
        # Enhanced FNOL-to-Claims field mapping (ELY-1008)
        "policy_number": fnol.policy_number,  # Map policy_number if available
        "jurisdiction": fnol.incident_state,  # Map incident_state to jurisdiction
        "reporter_phone": fnol.reporter_phone,  # Map reporter contact fields
        "reporter_email": fnol.reporter_email,
    }

    # Create claim with appropriate type and detail objects
    if claim_type == ClaimType.AUTO:
        claim_data = AutoClaimCreate(**base_claim_data, auto_details=AutoDetailsCreate())  # Create empty auto details
        claim = auto_claim_service.create_claim(claim_data)
        return FNOLConversionResponse.from_auto_claim(claim)
    elif claim_type == ClaimType.PROPERTY:
        claim_data = PropertyClaimCreate(
            **base_claim_data, property_details=PropertyDetailsCreate()  # Create empty property details
        )
        claim = property_claim_service.create_claim(claim_data)
        return FNOLConversionResponse.from_property_claim(claim)
    elif claim_type == ClaimType.GENERAL_LIABILITY:
        claim_data = GeneralLiabilityClaimCreate(
            **base_claim_data,
            gl_details=GeneralLiabilityDetailsCreate(
                incident_type="OTHER"  # Required field - use generic "OTHER" for FNOL conversion
            ),
        )
        claim = general_liability_claim_service.create_claim(claim_data)
        return FNOLConversionResponse.from_gl_claim(claim)
    else:
        raise NotFoundError(f"Unsupported claim type: {claim_type}")
