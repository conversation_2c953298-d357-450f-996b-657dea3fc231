"""API endpoints for global document management (not claim-specific)."""

import logging
from typing import Optional
from uuid import UUID

from fastapi import APIRouter, Depends, Query

from claimentine.api.v1.deps import get_document_service
from claimentine.core.permissions import require_permissions
from claimentine.models.document import DocumentType
from claimentine.schemas.document import DocumentList, DocumentResponse
from claimentine.services.document import DocumentService

# Create a separate router for global document endpoints
api_router = APIRouter(tags=["documents"])

logger = logging.getLogger(__name__)


@api_router.get("", response_model=DocumentList)
def list_all_documents(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    document_type: Optional[DocumentType] = Query(None, description="Filter by document type"),
    document_service: DocumentService = Depends(get_document_service),
    _: None = Depends(require_permissions("VIEW_ALL_DOCUMENTS")),
) -> DocumentList:
    """
    List all documents irrespective of their associated claims.

    This endpoint returns the latest N documents across all claims in the system.

    Args:
        skip: Number of records to skip (for pagination)
        limit: Maximum number of records to return (for pagination)
        document_type: Optional filter by document type
        document_service: Document service

    Returns:
        A list of documents with metadata including their associated claim IDs
    """
    documents, total = document_service.list_all_documents(skip=skip, limit=limit, document_type=document_type)

    # Convert SQLAlchemy models to Pydantic models
    document_responses = [DocumentResponse.model_validate(doc) for doc in documents]

    return DocumentList(items=document_responses, total=total)
