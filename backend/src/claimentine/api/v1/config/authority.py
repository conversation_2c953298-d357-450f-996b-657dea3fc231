"""API endpoints for client-specific authority thresholds."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from claimentine.api.v1.deps import get_authority_threshold_service
from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import Authorization<PERSON>rror, BadRequestError, DuplicateError, NotFoundError
from claimentine.db.base import get_db
from claimentine.models.authority import AuthorityRole
from claimentine.models.user import User
from claimentine.schemas.config.authority import (
    ClientAuthorityThresholdCreate,
    ClientAuthorityThresholdInDB,
    ClientAuthorityThresholdUpdate,
)
from claimentine.services.config.authority import ClientAuthorityThresholdService

api_router = APIRouter()


@api_router.get("/clients/{client_id}/authority", response_model=List[ClientAuthorityThresholdInDB])
def list_client_authority_thresholds(
    client_id: UUID,
    service: ClientAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> List[ClientAuthorityThresholdInDB]:
    """List authority thresholds for a specific client. Permissions are checked at the service layer."""
    return service.list_thresholds(client_id=client_id)


@api_router.post(
    "/clients/{client_id}/authority",
    response_model=ClientAuthorityThresholdInDB,
    status_code=status.HTTP_201_CREATED,
)
def create_client_authority_threshold(
    client_id: UUID,
    data: ClientAuthorityThresholdCreate,
    service: ClientAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> ClientAuthorityThresholdInDB:
    """Create a new authority threshold for a client. Permissions are checked at the service layer."""
    # Ensure client ID in URL matches the one in the request body
    if client_id != data.client_id:
        raise BadRequestError("Client ID in URL must match the one in the request body")

    return service.create_threshold(
        client_id=data.client_id,
        authority_role=data.authority_role,
        reserve_limit=float(data.reserve_limit),
        payment_limit=float(data.payment_limit),
        description=data.description,
    )


@api_router.get("/authority/{threshold_id}", response_model=ClientAuthorityThresholdInDB)
def get_client_authority_threshold(
    threshold_id: UUID,
    service: ClientAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> ClientAuthorityThresholdInDB:
    """Get a specific client authority threshold. Permissions are checked at the service layer."""
    threshold = service.get_threshold(threshold_id)
    if not threshold:
        raise NotFoundError(f"Authority threshold {threshold_id} not found")
    return threshold


@api_router.patch("/authority/{threshold_id}", response_model=ClientAuthorityThresholdInDB)
def update_client_authority_threshold(
    threshold_id: UUID,
    data: ClientAuthorityThresholdUpdate,
    service: ClientAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> ClientAuthorityThresholdInDB:
    """Update a client authority threshold. Permissions are checked at the service layer."""
    try:
        return service.update_threshold(
            threshold_id=threshold_id,
            reserve_limit=data.reserve_limit,
            payment_limit=data.payment_limit,
            description=data.description,
        )
    except NotFoundError as e:
        raise e


@api_router.delete("/authority/{threshold_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_client_authority_threshold(
    threshold_id: UUID,
    service: ClientAuthorityThresholdService = Depends(get_authority_threshold_service),
) -> None:
    """Delete a client authority threshold. Permissions are checked at the service layer."""
    service.delete_threshold(threshold_id)
