"""API endpoints for reserve configurations."""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import NotFoundError, ValidationError
from claimentine.db.base import get_db
from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType
from claimentine.models.user import User
from claimentine.schemas.config.reserve import (
    ReserveConfigurationCreate,
    ReserveConfigurationInDB,
    ReserveConfigurationUpdate,
)
from claimentine.services.config.reserve import ReserveConfigurationService

api_router = APIRouter()


@api_router.get("/reserves", response_model=List[ReserveConfigurationInDB])
def list_reserve_configurations(
    claim_type: Optional[ClaimType] = None,
    reserve_type: Optional[ReserveType] = None,
    is_required: Optional[bool] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> List[ReserveConfigurationInDB]:
    """List reserve configurations with optional filtering. Permissions are checked at the service layer."""
    service = ReserveConfigurationService(db, current_user)
    return service.list_configurations(
        claim_type=claim_type,
        reserve_type=reserve_type,
        is_required=is_required,
    )


@api_router.get("/reserves/{config_id}", response_model=ReserveConfigurationInDB)
def get_reserve_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> ReserveConfigurationInDB:
    """Get a specific reserve configuration. Permissions are checked at the service layer."""
    service = ReserveConfigurationService(db, current_user)
    config = service.get_configuration(config_id)
    if not config:
        raise NotFoundError(f"Reserve configuration {config_id} not found")
    return config


@api_router.post("/reserves", response_model=ReserveConfigurationInDB, status_code=status.HTTP_201_CREATED)
def create_reserve_configuration(
    data: ReserveConfigurationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> ReserveConfigurationInDB:
    """Create a new reserve configuration. Permissions are checked at the service layer."""
    service = ReserveConfigurationService(db, current_user)
    try:
        return service.create_configuration(
            claim_type=data.claim_type,
            reserve_type=data.reserve_type,
            is_required=data.is_required,
            minimum_amount=data.minimum_amount,
            description=data.description,
        )
    except ValidationError as e:
        # Re-raise validation error from service layer
        raise e


@api_router.patch("/reserves/{config_id}", response_model=ReserveConfigurationInDB)
def update_reserve_configuration(
    config_id: UUID,
    data: ReserveConfigurationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> ReserveConfigurationInDB:
    """Update a reserve configuration. Permissions are checked at the service layer."""
    service = ReserveConfigurationService(db, current_user)
    try:
        return service.update_configuration(
            config_id=config_id,
            is_required=data.is_required,
            minimum_amount=data.minimum_amount,
            description=data.description,
        )
    except ValidationError as e:
        # Re-raise validation error from service layer
        raise e


@api_router.delete("/reserves/{config_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_reserve_configuration(
    config_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> None:
    """Delete a reserve configuration. Permissions are checked at the service layer."""
    service = ReserveConfigurationService(db, current_user)
    service.delete_configuration(config_id)
