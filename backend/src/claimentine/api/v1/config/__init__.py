"""Configuration API endpoints package."""

from fastapi import APIRouter

from claimentine.api.v1.config.authority import api_router as authority_router
from claimentine.api.v1.config.reserve import api_router as reserve_router
from claimentine.api.v1.config.system import api_router as system_router

config_router = APIRouter()

config_router.include_router(reserve_router, tags=["config"])
config_router.include_router(authority_router, tags=["config"])
config_router.include_router(system_router, tags=["config"])
