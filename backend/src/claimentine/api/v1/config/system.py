"""System configuration API endpoints."""

from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, Query, status
from sqlalchemy.orm import Session

from claimentine.core.auth import get_current_user, get_current_user_optional
from claimentine.core.exceptions import NotFoundError, ServiceUnavailableError
from claimentine.db.base import get_db
from claimentine.models.user import User
from claimentine.schemas.config.system import (
    SystemConfigurationCreate,
    SystemConfigurationInDB,
    SystemConfigurationUpdate,
)
from claimentine.services.config.system import SystemConfigurationService
from claimentine.services.system import SystemService

api_router = APIRouter()


@api_router.post("/initialize", response_model=Dict[str, bool])
def initialize_database(
    force: bool = Query(False, description="Force reinitialization even if already initialized"),
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> Dict[str, bool]:
    """Initialize the database if not already initialized.

    This endpoint creates all necessary tables and inserts initial data.
    It will do nothing if the database is already initialized, unless
    the force parameter is set to true.

    This endpoint does not require authentication to allow initial setup.
    """
    service = SystemService(db, current_user)
    try:
        was_initialized = service.initialize_database(force=force)
        return {"initialized": was_initialized, "already_initialized": not was_initialized and not force}
    except Exception as e:
        # Keep a try-except here as database initialization is a critical operation
        # that might encounter various database-specific errors
        raise ServiceUnavailableError(f"Database initialization failed: {str(e)}")


@api_router.get("/is-initialized", response_model=Dict[str, bool])
def is_database_initialized(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
) -> Dict[str, bool]:
    """Check if the database has been initialized.

    Returns a simple boolean response indicating whether the database has been
    initialized with the required initial data.

    This endpoint does not require authentication to allow initial status check.
    """
    service = SystemService(db, current_user)
    try:
        initialized = service.is_database_initialized()
        return {"initialized": initialized}
    except Exception as e:
        # Keep try-except for this critical database check
        raise ServiceUnavailableError(f"Error checking database initialization: {str(e)}")


@api_router.get("/system-configs", response_model=List[SystemConfigurationInDB])
def list_system_configurations(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> List[SystemConfigurationInDB]:
    """List all system configurations. Permissions are checked at the service layer."""
    service = SystemConfigurationService(db, current_user)
    return service.list_configurations()


@api_router.post("/system-configs", response_model=SystemConfigurationInDB, status_code=status.HTTP_201_CREATED)
def create_system_configuration(
    data: SystemConfigurationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> SystemConfigurationInDB:
    """Create a new system configuration. Permissions are checked at the service layer."""
    service = SystemConfigurationService(db, current_user)
    return service.create_configuration(
        key=data.key,
        value=data.value,
        description=data.description,
    )


@api_router.patch("/system-configs/{config_id}", response_model=SystemConfigurationInDB)
def update_system_configuration(
    config_id: str,
    data: SystemConfigurationUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> SystemConfigurationInDB:
    """Update a system configuration. Permissions are checked at the service layer."""
    service = SystemConfigurationService(db, current_user)
    return service.update_configuration(
        config_id=config_id,
        value=data.value,
        description=data.description,
    )


@api_router.get("/system-configs/{key}", response_model=SystemConfigurationInDB)
def get_system_configuration_by_key(
    key: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> SystemConfigurationInDB:
    """Get a system configuration by key. Permissions are checked at the service layer."""
    service = SystemConfigurationService(db, current_user)
    config = service.get_by_key(key)
    if not config:
        raise NotFoundError(f"System configuration with key '{key}' not found")
    return config


@api_router.delete("/system-configs/{config_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_system_configuration(
    config_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
) -> None:
    """Delete a system configuration."""
    service = SystemConfigurationService(db, current_user)
    service.delete_configuration(config_id)
