"""Client API endpoints."""

from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, status
from sqlalchemy.orm import Session

from claimentine.core.exceptions import NotFoundError
from claimentine.core.permissions import require_permissions
from claimentine.db.base import get_db
from claimentine.models.user import User
from claimentine.schemas.client import ClientCreate, ClientResponse, ClientUpdate
from claimentine.services.client import ClientService

api_router = APIRouter()


@api_router.get("", response_model=List[ClientResponse], tags=["clients"])
def list_clients(
    active_only: bool = False,
    db: Session = Depends(get_db),
    _: User = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> List[ClientResponse]:
    """List all clients."""
    return ClientService(db).list_clients(active_only=active_only)


@api_router.get("/{client_id}", response_model=ClientResponse, tags=["clients"])
def get_client(
    client_id: UUID,
    db: Session = Depends(get_db),
    _: User = Depends(require_permissions("VIEW_ALL_CLAIMS")),
) -> ClientResponse:
    """Get a client by ID."""
    client = ClientService(db).get_client_by_id(client_id)
    if not client:
        raise NotFoundError(f"Client {client_id} not found")
    return client


@api_router.post("", response_model=ClientResponse, status_code=status.HTTP_201_CREATED, tags=["clients"])
def create_client(
    data: ClientCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> ClientResponse:
    """Create a new client."""
    return ClientService(db, current_user).create_client(data)


@api_router.patch("/{client_id}", response_model=ClientResponse, tags=["clients"])
def update_client(
    client_id: UUID,
    data: ClientUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> ClientResponse:
    """Update a client."""
    return ClientService(db, current_user).update_client(client_id, data)


@api_router.delete("/{client_id}", status_code=status.HTTP_204_NO_CONTENT, tags=["clients"])
def delete_client(
    client_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_permissions("SYSTEM_CONFIGURATION")),
) -> None:
    """Delete a client."""
    ClientService(db, current_user).delete_client(client_id)
