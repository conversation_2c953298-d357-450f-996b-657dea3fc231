"""Health check endpoint."""

from typing import Dict

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from sqlalchemy import text
from sqlalchemy.orm import Session

from claimentine.core.config import settings
from claimentine.db.base import get_db

# Create a router for the health check endpoint
api_router = APIRouter()


def check_db_connection(db: Session) -> bool:
    """Check if database connection is working using the provided session."""
    try:
        # Execute a simple query using the existing session
        db.execute(text("SELECT 1"))
        return True
    except Exception:
        # Log the error, maybe?
        # logger.error(f"Database health check failed: {e}")
        return False
    # No need for engine creation/disposal


# Define the endpoint using the router
@api_router.get("/health", tags=["health"])
def get_health_status(db: Session = Depends(get_db)) -> Dict[str, str | bool | dict]:
    """Get system health status."""
    db_connected = check_db_connection(db)
    db_status = "connected" if db_connected else "disconnected"

    status_code = 200 if db_connected else 503

    response_body = {
        "status": "healthy" if db_connected else "unhealthy",
        "version": settings.VERSION,
        "database": {"status": db_status, "host": settings.POSTGRES_HOST, "database": settings.POSTGRES_DB},
    }
    return JSONResponse(content=response_body, status_code=status_code)
