# Dependency Injection Standards in Claimentine

This document outlines the standardized patterns for dependency injection in the Claimentine backend.

## Dependency Functions

All service dependencies should be injected via dedicated functions in `backend/src/claimentine/api/v1/deps.py`. This ensures consistency and makes it easier to modify how services are initialized throughout the codebase.

### Standard Pattern

Service dependency functions should follow this pattern:

```python
def get_<service_name>_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    # Add other service dependencies if needed
) -> ServiceClass:
    """Dependency provider for ServiceClass."""
    return ServiceClass(db=db, current_user=current_user, ...)
```

## User Authentication Dependencies

There are two main user authentication dependencies:

1. `get_current_user` - Requires authentication, raises error if user is not authenticated
2. `get_current_user_optional` - Makes authentication optional, returns None if user is not authenticated

### When to use each:

- **get_current_user_optional**: Use in service dependencies to support both authenticated and unauthenticated access. This allows services to adjust their behavior based on whether a user is authenticated.

- **get_current_user**: Use in endpoint functions that require authentication.

## Service Dependencies

Services should depend on other services through the dependency injection system, not by creating instances directly.

### Example:

```python
def get_claim_financials_service(
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
) -> ClaimFinancialsService:
    """Dependency provider for ClaimFinancialsService."""
    return ClaimFinancialsService(db=db, current_user=current_user, claim_service=claim_service)
```

## Endpoint Dependencies

Endpoints should:

1. Use service dependencies from `deps.py`
2. Use `get_current_user` to require authentication
3. Use `require_permissions()` to check permissions
4. Not directly instantiate services or access the database

### Example:

```python
@api_router.get("/clients/{client_id}", response_model=ClientResponse)
def get_client(
    client_id: UUID,
    client_service: ClientService = Depends(get_client_service),
    current_user: User = Depends(get_current_user),
    _: None = Depends(require_permissions("VIEW_CLIENTS")),
) -> ClientResponse:
    """Get client by ID."""
    return client_service.get_client_by_id(client_id)
```

## Best Practices

1. **Delegate DB operations to services**: Endpoints should never directly fetch data from the database.
2. **Consistent naming**: Use `get_<service_name>_service` for service dependency functions.
3. **Current user handling**: Services should accept and handle the `current_user` parameter consistently.
4. **Documentation**: All dependency functions should have docstrings explaining their purpose.
5. **Parameter passing**: Always pass all service dependencies to the constructor, don't ignore dependencies. 