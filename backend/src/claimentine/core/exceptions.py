"""Custom exceptions and error handling for the application."""

from enum import Enum
from typing import Any, Dict, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status
from pydantic import BaseModel


class ErrorCode(str, Enum):
    """Standardized error codes for the application."""

    # 4xx Client Errors
    BAD_REQUEST = "BAD_REQUEST"
    UNAUTHORIZED = "UNAUTHORIZED"
    FORBIDDEN = "FORBIDDEN"
    NOT_FOUND = "NOT_FOUND"
    CONFLICT = "CONFLICT"
    DUPLICATE_ERROR = "DUPLICATE_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    RATE_LIMIT = "RATE_LIMIT"
    STORAGE_ERROR = "STORAGE_ERROR"

    # 5xx Server Errors
    DATABASE_ERROR = "DATABASE_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    INTERNAL_ERROR = "INTERNAL_ERROR"

    def __str__(self) -> str:
        return self.value


class ErrorDetail(BaseModel):
    """Standardized structure for error details."""

    resource: str
    """The type of resource that caused the error (e.g., 'claim', 'document')"""

    identifier: Optional[str] = None
    """ID or other identifier of the resource"""

    field: Optional[str] = None
    """Field name for validation errors"""

    reason: Optional[str] = None
    """Specific reason for the error"""

    context: Optional[Dict[str, Any]] = None
    """Additional context about the error"""


class ErrorResponse(BaseModel):
    """Standardized error response model."""

    code: ErrorCode
    """Error code identifying the type of error"""

    message: str
    """Human-readable error message"""

    details: Optional[ErrorDetail] = None
    """Structured error details"""

    request_id: Optional[str] = None
    """Request ID for tracing"""


class AppException(HTTPException):
    """Base exception for application-specific errors."""

    def __init__(
        self,
        code: ErrorCode,
        message: str,
        status_code: int,
        details: Optional[ErrorDetail] = None,
    ) -> None:
        """
        Initialize application exception.

        Args:
            code: Error code identifying the type of error
            message: Human-readable error message
            status_code: HTTP status code
            details: Structured error details
        """
        self.code = code
        self.details = details
        super().__init__(status_code=status_code, detail=message)


class DatabaseError(AppException):
    """Raised when database operations fail."""

    def __init__(
        self,
        message: str = "Database operation failed",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="DATABASE_ERROR",
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details,
        )


class AuthenticationError(AppException):
    """Raised when authentication fails."""

    def __init__(
        self,
        message: str = "Authentication failed",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="AUTHENTICATION_ERROR",
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            details=details,
        )


class AuthorizationError(AppException):
    """Raised when user doesn't have required permissions."""

    def __init__(
        self,
        message: str = "Insufficient permissions",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="AUTHORIZATION_ERROR",
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            details=details,
        )


class ValidationError(AppException):
    """Raised when request validation fails."""

    def __init__(
        self,
        message: str = "Validation failed",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="VALIDATION_ERROR",
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


class NotFoundError(AppException):
    """Raised when a requested resource is not found."""

    def __init__(
        self,
        message: str = "Resource not found",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="NOT_FOUND",
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details=details,
        )


class ConflictError(AppException):
    """Raised when there's a conflict with existing data."""

    def __init__(
        self,
        message: str = "Resource conflict",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="CONFLICT",
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details,
        )


class DuplicateError(AppException):
    """Raised when attempting to create a resource that already exists."""

    def __init__(
        self,
        message: str = "Resource already exists",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="DUPLICATE_ERROR",
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details,
        )


class StateTransitionError(AppException):
    """Raised when an invalid state transition is attempted."""

    def __init__(
        self,
        message: str = "Invalid state transition",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code="INVALID_STATE_TRANSITION",
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
        )


def create_error_detail(
    resource: str,
    identifier: Optional[str] = None,
    reason: Optional[str] = None,
    field: Optional[str] = None,
    context: Optional[Dict[str, Any]] = None,
) -> ErrorDetail:
    """
    Create a standardized error detail object.

    Args:
        resource: The type of resource that caused the error (e.g., 'claim', 'document')
        identifier: ID or other identifier of the resource
        reason: Specific reason for the error
        field: Field name for validation errors
        context: Additional context about the error

    Returns:
        ErrorDetail object
    """
    return ErrorDetail(
        resource=resource,
        identifier=identifier,
        reason=reason,
        field=field,
        context=context,
    )


class BadRequestError(AppException):
    """Raised when the request is malformed or invalid."""

    def __init__(
        self,
        message: str = "Bad request",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code=ErrorCode.BAD_REQUEST,
            message=message,
            status_code=status.HTTP_400_BAD_REQUEST,
            details=details,
        )


class ServiceUnavailableError(AppException):
    """Raised when an external service is unavailable."""

    def __init__(
        self,
        message: str = "Service unavailable",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code=ErrorCode.SERVICE_UNAVAILABLE,
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details,
        )


class RateLimitError(AppException):
    """Raised when rate limit is exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code=ErrorCode.RATE_LIMIT,
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details,
        )


class StorageError(AppException):
    """Raised when storage operations fail."""

    def __init__(
        self,
        message: str = "Storage operation failed",
        details: Optional[ErrorDetail] = None,
    ) -> None:
        super().__init__(
            code=ErrorCode.STORAGE_ERROR,
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details,
        )
