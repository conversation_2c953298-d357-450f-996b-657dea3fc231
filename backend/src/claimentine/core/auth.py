"""Authentication utilities."""

import logging
import uuid
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional
from uuid import UUID

from fastapi import <PERSON><PERSON>, Depends, Form
from fastapi.security import OAuth2PasswordBearer
from jose import JW<PERSON>rror, jwt
from sqlalchemy import delete, select
from sqlalchemy.orm import Session

from claimentine.core.config import settings
from claimentine.core.exceptions import AuthenticationError
from claimentine.db.base import get_db
from claimentine.models.auth import UserSession
from claimentine.models.user import User, UserStatus

# OAuth2 schemes
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token")
oauth2_scheme_optional = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token", auto_error=False)

# JWT Configuration
JWT_SECRET_KEY = settings.JWT_SECRET_KEY
JWT_ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Setup logger
logger = logging.getLogger(__name__)


def create_access_token(data: dict, expire_minutes: Optional[int] = None) -> str:
    """Create a new access token.

    Args:
        data: The data to encode in the token
        expire_minutes: Optional custom expiration time in minutes (defaults to ACCESS_TOKEN_EXPIRE_MINUTES)
    """
    to_encode = data.copy()
    minutes = expire_minutes or ACCESS_TOKEN_EXPIRE_MINUTES
    expire = datetime.utcnow() + timedelta(minutes=minutes)
    to_encode.update({"exp": expire, "type": "access", "iat": datetime.utcnow()})
    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)


def create_refresh_token(data: dict, expire_days: Optional[int] = None) -> str:
    """Create a new refresh token.

    Args:
        data: The data to encode in the token
        expire_days: Optional custom expiration time in days (defaults to REFRESH_TOKEN_EXPIRE_DAYS)
    """
    to_encode = data.copy()
    days = expire_days or REFRESH_TOKEN_EXPIRE_DAYS
    expire = datetime.utcnow() + timedelta(days=days)
    # Add a unique random component to prevent token collisions
    to_encode.update({"exp": expire, "type": "refresh", "jti": str(uuid.uuid4()), "iat": datetime.utcnow()})
    return jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)


def verify_token(token: str) -> dict:
    """Verify and decode the token."""
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except JWTError as e:
        logger.error(f"Token validation error: {e}")
        raise AuthenticationError("Invalid or expired token")


async def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db),
) -> User:
    """Get the current authenticated user from a token."""
    payload = verify_token(token)
    if payload.get("type") != "access":
        raise AuthenticationError("Invalid token type")

    user_id = payload.get("sub")
    if not user_id:
        raise AuthenticationError("Invalid token content")

    user = db.query(User).filter(User.id == user_id, User.status == UserStatus.ACTIVE).first()
    if not user:
        logger.warning(f"User with ID {user_id} not found or inactive")
        raise AuthenticationError("User not found or inactive")

    return user


# Add a new dependency that can check both cookies and headers
async def get_current_user_from_header_or_cookie(
    token: Optional[str] = Depends(oauth2_scheme_optional),
    db: Session = Depends(get_db),
    access_token_cookie: Optional[str] = Cookie(None, alias="access_token"),
) -> User:
    """Get the current authenticated user from a token in header or cookie."""
    # Use token from header if it exists, otherwise use cookie
    token_to_use = token or access_token_cookie

    if not token_to_use:
        raise AuthenticationError("Authentication required")

    payload = verify_token(token_to_use)
    if payload.get("type") != "access":
        raise AuthenticationError("Invalid token type")

    user_id = payload.get("sub")
    if not user_id:
        raise AuthenticationError("Invalid token content")

    user = db.query(User).filter(User.id == user_id, User.status == UserStatus.ACTIVE).first()
    if not user:
        logger.warning(f"User with ID {user_id} not found or inactive")
        raise AuthenticationError("User not found or inactive")

    return user


def get_current_user_optional(
    db: Session = Depends(get_db),
    token: Optional[str] = Depends(oauth2_scheme_optional, use_cache=False),
) -> Optional[User]:
    """Get the current authenticated user, or None if not authenticated."""
    if not token:
        return None

    try:
        # Verify token
        payload = verify_token(token)
        user_id: UUID = payload.get("sub")
        token_type: str = payload.get("type")

        if not user_id or token_type != "access":
            # Don't raise, just return None for invalid claims in optional check
            return None

        # Get user from database
        stmt = select(User).where(User.id == user_id)
        user = db.scalar(stmt)
        if not user:
            # Don't raise, just return None if user not found
            return None

        # Check user status
        if user.status != UserStatus.ACTIVE:
            # Don't raise, just return None if user inactive
            return None

        return user

    except AuthenticationError:
        # If any step raises AuthenticationError (e.g., invalid token format)
        return None
    except Exception as e:
        # Log unexpected errors but still return None
        logger.error(f"Unexpected error during optional user retrieval: {e}", exc_info=True)
        return None


def create_user_session(
    db: Session,
    user: User,
    token: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
) -> UserSession:
    """Create a new user session."""
    # Calculate token expiry
    expires_at = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)

    # Create session
    session = UserSession(
        user_id=user.id, token=token, ip_address=ip_address, user_agent=user_agent, expires_at=expires_at
    )

    db.add(session)
    db.commit()
    db.refresh(session)
    return session


def end_user_session(db: Session, token: str) -> None:
    """End a user session by invalidating the refresh token."""
    try:
        # First verify the token to ensure it's valid before attempting to find it
        payload = verify_token(token)
        if payload.get("type") != "refresh":
            logger.warning("Attempted to end session with a non-refresh token")
            return

        # Find and delete the session
        session = db.query(UserSession).filter(UserSession.token == token).first()
        if session:
            db.delete(session)
            db.commit()
            logger.info(f"User session ended for user ID: {session.user_id}")
        else:
            logger.warning(f"No session found for token during logout")
    except Exception as e:
        logger.error(f"Error ending user session: {e}")
        # Don't raise - we want logout to succeed even if token is invalid


def cleanup_expired_sessions(db: Session) -> int:
    """Remove expired sessions from the database.

    Returns:
        int: Number of sessions removed
    """
    stmt = delete(UserSession).where(UserSession.expires_at < datetime.utcnow())
    result = db.execute(stmt)
    db.commit()
    return result.rowcount
