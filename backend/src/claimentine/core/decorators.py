"""Decorators for enhancing service methods."""

import functools
import inspect
import logging
from typing import Any, Callable, Dict, Optional, Type, TypeVar, Union, cast
from uuid import UUID

from claimentine.core.audit import AuditHelper
from claimentine.models.audit import ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim

logger = logging.getLogger(__name__)

T = TypeVar("T")
F = TypeVar("F", bound=Callable[..., Any])


def audit_changes(
    entity_type: Union[EntityType, str],
    change_type: Union[ChangeType, str],
    field_name: Optional[str] = None,
    description_template: Optional[str] = None,
    claim_param: str = "claim",
    entity_id_param: Optional[str] = None,
    old_data_param: Optional[str] = None,
    new_data_param: Optional[str] = None,
    current_user_param: str = "current_user",
    claim_extractor: Optional[Callable[[Any], BaseClaim]] = None,
) -> Callable[[F], F]:
    """Decorator to automatically create audit trail entries.

    This decorator wraps service methods to automatically generate
    audit trail entries based on the method inputs and outputs.

    Args:
        entity_type: Type of entity being audited
        change_type: Type of change (CREATE, UPDATE, DELETE)
        field_name: Optional name of the field being changed
        description_template: Optional template for audit description (e.g., "Updated {entity_type}")
        claim_param: Name of the parameter containing the claim object
        entity_id_param: Name of the parameter containing the entity ID
        old_data_param: Name of the parameter containing the old data
        new_data_param: Name of the parameter containing the new data
        current_user_param: Name of the parameter containing the current user
        claim_extractor: Optional function to extract a claim from a returned object

    Returns:
        Decorated function that creates audit trail entries
    """

    def decorator(func: F) -> F:
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Get the signature of the decorated function
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()
            params = bound_args.arguments

            # Try to get claim object and current user
            claim = params.get(claim_param)
            current_user = params.get(current_user_param)
            current_user_id = getattr(current_user, "id", None) if current_user else None

            # Get entity ID if parameter exists
            entity_id = params.get(entity_id_param) if entity_id_param else None

            # Get old and new data if parameters exist
            old_data = params.get(old_data_param) if old_data_param else None
            new_data = params.get(new_data_param) if new_data_param else None

            # Default description based on entity_type and change_type
            if description_template is None:
                entity_name = entity_type.value if isinstance(entity_type, EntityType) else entity_type
                ct = change_type.value if isinstance(change_type, ChangeType) else change_type
                description = f"{ct.capitalize()} {entity_name.lower()}"
            else:
                # Format description template with parameter values
                description = description_template.format(**params)

            # Call the original function
            result = func(*args, **kwargs)

            # If no claim was provided but we have a claim_extractor and a result,
            # try to extract the claim from the result
            if claim is None and claim_extractor and result:
                try:
                    claim = claim_extractor(result)
                except Exception as e:
                    logger.warning(f"Failed to extract claim from result: {str(e)}")

            # If we don't have a claim at this point, we can't create an audit entry
            if claim is None or not isinstance(claim, BaseClaim):
                logger.warning(f"Cannot create audit entry: no valid claim object found for {func.__name__}")
                return result

            # If we have a result that could be used as new_data, use it
            if new_data is None and result is not None:
                if isinstance(result, dict):
                    new_data = result
                elif hasattr(result, "__dict__"):
                    # For ORM objects or other custom objects
                    new_data = {k: v for k, v in result.__dict__.items() if not k.startswith("_")}

            # Create the audit helper and log the change
            audit_helper = AuditHelper(claim, current_user_id)

            if change_type == ChangeType.CREATE or change_type == "CREATE":
                # For CREATE operations, use the new_data from the result
                if entity_id is None and hasattr(result, "id"):
                    entity_id = result.id
                audit_helper.log_create(
                    entity_type=entity_type, entity_id=entity_id, data=new_data or {}, description=description
                )
            elif change_type == ChangeType.UPDATE or change_type == "UPDATE":
                # For UPDATE operations, compare old and new data
                if entity_id is None and hasattr(result, "id"):
                    entity_id = result.id
                audit_helper.log_update(
                    entity_type=entity_type,
                    entity_id=entity_id,
                    previous_data=old_data or {},
                    new_data=new_data or {},
                    description=description,
                    field_name=field_name,
                )
            elif change_type == ChangeType.DELETE or change_type == "DELETE":
                # For DELETE operations, use old_data
                audit_helper.log_delete(
                    entity_type=entity_type, entity_id=entity_id, data=old_data or {}, description=description
                )

            return result

        return cast(F, wrapper)

    return decorator
