"""Standards for consistent audit trail generation across the application.

This module defines constants and helper methods to ensure audit trail entries
are created consistently across all services.
"""

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

# Maximum length for string values in audit trail records
MAX_STRING_LENGTH = 500
# Maximum number of list items to include in audit records
MAX_LIST_ITEMS = 10


def format_value(value: Any) -> Any:
    """Format a value for consistent storage in audit entries.

    Args:
        value: Any Python value to be formatted for audit storage

    Returns:
        A consistently formatted value suitable for JSON storage
    """
    # Handle None values
    if value is None:
        return None

    # Handle basic types that don't need formatting
    if isinstance(value, (bool, int, float, str)):
        if isinstance(value, str) and len(value) > MAX_STRING_LENGTH:
            return value[:MAX_STRING_LENGTH] + "..."
        return value

    # Handle UUIDs
    if isinstance(value, UUID):
        return str(value)

    # Handle dates and datetimes
    if isinstance(value, datetime):
        return value.isoformat()
    if isinstance(value, date):
        return value.isoformat()

    # Handle Decimals
    if isinstance(value, Decimal):
        return str(value)

    # Handle Enums
    if isinstance(value, Enum):
        return value.value

    # Handle lists and tuples
    if isinstance(value, (list, tuple)):
        # Truncate long lists
        formatted_list = [format_value(item) for item in value[:MAX_LIST_ITEMS]]
        if len(value) > MAX_LIST_ITEMS:
            formatted_list.append(f"...and {len(value) - MAX_LIST_ITEMS} more items")
        return formatted_list

    # Handle dictionaries
    if isinstance(value, dict):
        return {k: format_value(v) for k, v in value.items()}

    # For other objects, try to convert to string
    try:
        return str(value)
    except Exception:
        return f"<Non-serializable {type(value).__name__}>"


def format_audit_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Format all values in a dictionary for consistent audit storage.

    Args:
        data: Dictionary of data to format

    Returns:
        Dictionary with all values consistently formatted
    """
    return {k: format_value(v) for k, v in data.items()}


# Standard description templates for common operations
DESCRIPTION_TEMPLATES = {
    # CREATE operations
    "create_entity": "Created {entity_type}: {entity_name}",
    "add_entity": "Added {entity_type}: {entity_name}",
    "upload_document": "Uploaded document: {document_name}",
    # UPDATE operations
    "update_entity": "Updated {entity_type}: {entity_name}",
    "update_field": "Updated {field_name} from {old_value} to {new_value}",
    "change_status": "Changed status from {old_status} to {new_status}",
    "assign_entity": "Assigned {entity_type} to {assignee_name}",
    "unassign_entity": "Unassigned {entity_type} from {assignee_name}",
    # DELETE operations
    "delete_entity": "Deleted {entity_type}: {entity_name}",
    "remove_entity": "Removed {entity_type}: {entity_name}",
}


def get_description(template_key: str, **kwargs) -> str:
    """Get a formatted description using a standard template.

    Args:
        template_key: Key of the template to use from DESCRIPTION_TEMPLATES
        **kwargs: Values to fill in the template placeholders

    Returns:
        Formatted description string
    """
    template = DESCRIPTION_TEMPLATES.get(
        template_key,
        # Default template if key not found
        kwargs.get("fallback_template", "{entity_type} {action}"),
    )

    # Format all values before using them in the template
    formatted_kwargs = {k: format_value(v) for k, v in kwargs.items()}

    try:
        return template.format(**formatted_kwargs)
    except KeyError as e:
        # Fall back to a simple description if template keys are missing
        return f"{kwargs.get('entity_type', 'Entity')} {kwargs.get('action', 'changed')}"


# Standard field name mappings to ensure consistency
STANDARD_FIELD_NAMES = {
    # User fields
    "user_id": "user_id",
    "user_email": "user_email",
    "user_name": "user_name",
    # Common fields
    "id": "id",
    "name": "name",
    "type": "type",
    "status": "status",
    "created_at": "created_at",
    "updated_at": "updated_at",
    # Claim fields
    "claim_id": "claim_id",
    "claim_number": "claim_number",
    "claim_type": "claim_type",
    "claim_status": "claim_status",
    # Financial fields
    "amount": "amount",
    "currency": "currency",
    "reserve_amount": "amount",  # Standardize on 'amount'
    "payment_amount": "amount",  # Standardize on 'amount'
    # Document fields
    "file_name": "file_name",
    "content_type": "content_type",
    "document_type": "document_type",
    # Task fields
    "task_id": "task_id",
    "task_status": "status",  # Standardize on 'status'
    "assigned_to": "assigned_to",
    "due_date": "due_date",
    # Note fields
    "note_id": "note_id",
    "content": "content",
}


def standardize_field_name(field_name: str) -> str:
    """Convert a field name to the standard version.

    Args:
        field_name: Original field name

    Returns:
        Standardized field name
    """
    return STANDARD_FIELD_NAMES.get(field_name, field_name)


def standardize_audit_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Standardize field names and format values in audit data.

    Args:
        data: Original audit data dictionary

    Returns:
        Dictionary with standardized field names and formatted values
    """
    result = {}
    for key, value in data.items():
        standard_key = standardize_field_name(key)
        result[standard_key] = format_value(value)
    return result
