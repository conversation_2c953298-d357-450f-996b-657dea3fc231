"""Custom JSON encoder for handling special types."""

import json
from datetime import datetime
from decimal import Decimal
from uuid import UUID


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Decimal and UUID and datetime."""

    def default(self, obj):
        """Convert special types to JSON serializable format."""
        if isinstance(obj, Decimal):
            # Convert Decimal to string to preserve precision
            return str(obj)
        if isinstance(obj, UUID):
            # Convert UUID to string
            return str(obj)
        if isinstance(obj, datetime):
            # Convert datetime to ISO 8601 string
            return obj.isoformat()
        # Let the base class default method raise the TypeError
        return super().default(obj)
