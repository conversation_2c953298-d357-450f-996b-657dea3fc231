"""Audit helper utilities for standardized audit trail generation."""

import logging
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from claimentine.core.audit_standards import format_audit_data, get_description, standardize_audit_data
from claimentine.core.exceptions import ValidationError
from claimentine.models.audit import AuditTrail, ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim

logger = logging.getLogger(__name__)


class AuditHelper:
    """Helper utility for creating standardized audit entries.

    This class provides methods to standardize the creation of audit trail entries
    across different services, ensuring consistent formatting and reducing code duplication.
    """

    def __init__(self, claim: BaseClaim, current_user_id: Optional[UUID] = None):
        """Initialize with the claim to audit and optional user ID.

        Args:
            claim: The BaseClaim instance to create audit entries for
            current_user_id: Optional UUID of the user making the changes
        """
        self.claim = claim
        self.current_user_id = current_user_id

    def create_entry(
        self,
        entity_type: Union[EntityType, str],
        change_type: Union[ChangeType, str],
        entity_id: Optional[UUID] = None,
        field_name: Optional[str] = None,
        previous_value: Optional[Dict[str, Any]] = None,
        new_value: Optional[Dict[str, Any]] = None,
        description: Optional[str] = None,
    ) -> None:
        """Create a single audit entry with provided details.

        A wrapper over claim.create_audit_entry with additional error handling.

        Args:
            entity_type: Type of entity being audited (from EntityType enum or string)
            change_type: Type of change (from ChangeType enum or string)
            entity_id: Optional UUID of the specific entity
            field_name: Optional name of the specific field changed
            previous_value: Optional dictionary of previous values
            new_value: Optional dictionary of new values
            description: Optional human-readable description of the change

        Raises:
            ValidationError: If the claim is not saved or other validation fails
        """
        try:
            # Convert string enum values to proper enum instances if needed
            if isinstance(entity_type, str):
                entity_type = EntityType(entity_type)
            if isinstance(change_type, str):
                change_type = ChangeType(change_type)

            # Standardize and format field values
            standardized_previous_value = standardize_audit_data(previous_value) if previous_value else None
            standardized_new_value = standardize_audit_data(new_value) if new_value else None

            self.claim.create_audit_entry(
                entity_type=entity_type,
                change_type=change_type,
                entity_id=entity_id,
                field_name=field_name,
                previous_value=standardized_previous_value,
                new_value=standardized_new_value,
                description=description,
                changed_by_id=self.current_user_id,
            )
        except Exception as e:
            logger.error(f"Error creating audit entry: {str(e)}")
            if isinstance(e, ValidationError):
                raise
            raise ValidationError(f"Failed to create audit entry: {str(e)}")

    def log_create(
        self,
        entity_type: Union[EntityType, str],
        entity_id: UUID,
        data: Dict[str, Any],
        description: Optional[str] = None,
    ) -> None:
        """Log the creation of a new entity.

        Args:
            entity_type: Type of entity being audited (from EntityType enum or string)
            entity_id: UUID of the created entity
            data: Dictionary of the created entity's data
            description: Optional description of the change
        """
        # Format the data for consistent storage
        formatted_data = format_audit_data(data)

        # Generate a standard description if none provided
        if description is None:
            entity_name = formatted_data.get("name", str(entity_id))
            entity_type_name = entity_type.value.lower() if isinstance(entity_type, EntityType) else entity_type.lower()
            description = get_description("create_entity", entity_type=entity_type_name, entity_name=entity_name)

        self.create_entry(
            entity_type=entity_type,
            change_type=ChangeType.CREATE,
            entity_id=entity_id,
            new_value=formatted_data,
            description=description,
        )

    def log_update(
        self,
        entity_type: Union[EntityType, str],
        entity_id: UUID,
        previous_data: Dict[str, Any],
        new_data: Dict[str, Any],
        description: Optional[str] = None,
        field_name: Optional[str] = None,
    ) -> None:
        """Log updates to an entity, automatically including only changed fields.

        Args:
            entity_type: Type of entity being audited (from EntityType enum or string)
            entity_id: UUID of the updated entity
            previous_data: Dictionary of the entity's data before changes
            new_data: Dictionary of the entity's data after changes
            description: Optional description of the change
            field_name: Optional specific field being updated
        """
        # Format the data for consistent storage
        formatted_previous = format_audit_data(previous_data)
        formatted_new = format_audit_data(new_data)

        # Get changes only (only include fields that actually changed)
        changes = self.get_diff(formatted_previous, formatted_new)

        # If nothing changed, don't create an audit entry
        if not changes:
            return

        # Create previous_value dict with only changed fields
        previous_value = {k: formatted_previous.get(k) for k in changes.keys() if k in formatted_previous}

        # Generate a standard description if none provided
        if description is None:
            entity_type_name = entity_type.value.lower() if isinstance(entity_type, EntityType) else entity_type.lower()
            if field_name and len(changes) == 1:
                # For single field updates, provide more specific description
                old_value = previous_value.get(field_name, "")
                new_value = changes.get(field_name, "")
                description = get_description(
                    "update_field", field_name=field_name, old_value=old_value, new_value=new_value
                )
            else:
                # For multi-field updates, use generic entity update description
                entity_name = formatted_new.get("name", str(entity_id))
                description = get_description("update_entity", entity_type=entity_type_name, entity_name=entity_name)

        self.create_entry(
            entity_type=entity_type,
            change_type=ChangeType.UPDATE,
            entity_id=entity_id,
            field_name=field_name,
            previous_value=previous_value,
            new_value=changes,
            description=description,
        )

    def log_delete(
        self,
        entity_type: Union[EntityType, str],
        entity_id: UUID,
        data: Dict[str, Any],
        description: Optional[str] = None,
    ) -> None:
        """Log the deletion of an entity.

        Args:
            entity_type: Type of entity being audited (from EntityType enum or string)
            entity_id: UUID of the deleted entity
            data: Dictionary of the deleted entity's data before deletion
            description: Optional description of the change
        """
        # Format the data for consistent storage
        formatted_data = format_audit_data(data)

        # Generate a standard description if none provided
        if description is None:
            entity_type_name = entity_type.value.lower() if isinstance(entity_type, EntityType) else entity_type.lower()
            entity_name = formatted_data.get("name", str(entity_id))
            description = get_description("delete_entity", entity_type=entity_type_name, entity_name=entity_name)

        self.create_entry(
            entity_type=entity_type,
            change_type=ChangeType.DELETE,
            entity_id=entity_id,
            previous_value=formatted_data,
            description=description,
        )

    def bulk_create_entries(self, entries: List[Dict[str, Any]]) -> None:
        """Create multiple audit entries in a single operation.

        This method is useful when many related changes need to be tracked,
        like when importing data or making batch updates.

        Args:
            entries: List of dictionaries with audit entry details.
                    Each dictionary should contain keys matching the parameters
                    for the create_entry method.
        """
        for entry in entries:
            # Format values if present
            if "previous_value" in entry:
                entry["previous_value"] = format_audit_data(entry["previous_value"])
            if "new_value" in entry:
                entry["new_value"] = format_audit_data(entry["new_value"])

            self.create_entry(**entry)

    @staticmethod
    def get_diff(previous_data: Dict[str, Any], new_data: Dict[str, Any]) -> Dict[str, Any]:
        """Get a dictionary of differences between previous and new data.

        Only includes fields that have changed.

        Args:
            previous_data: Dictionary of data before changes
            new_data: Dictionary of data after changes

        Returns:
            Dictionary with only the fields that changed and their new values
        """
        changes = {}

        # First handle fields that were added or changed
        for key, new_value in new_data.items():
            # Skip None values in new_data
            if new_value is None:
                continue

            # Field didn't exist before or value changed
            if key not in previous_data or previous_data[key] != new_value:
                changes[key] = new_value

        # Handle fields that were removed (set to None)
        for key in previous_data:
            if key not in new_data and previous_data[key] is not None:
                # Field was removed (technically set to None)
                changes[key] = None

        return changes
