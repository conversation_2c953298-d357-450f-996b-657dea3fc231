"""Google Cloud Storage integration for document storage."""

import logging
import os
from datetime import datetime, timed<PERSON><PERSON>
from typing import Op<PERSON>, <PERSON><PERSON>
from uuid import UUID

import google.auth
from google.auth.transport.requests import Request
from google.cloud import storage
from google.oauth2 import service_account

from claimentine.core.config import settings
from claimentine.core.exceptions import StorageError

logger = logging.getLogger(__name__)

# Constants
DEFAULT_EXPIRATION = 15 * 60  # 15 minutes in seconds


class StorageClient:
    """Client for interacting with Google Cloud Storage."""

    def __init__(self, bucket_name: str = settings.GCS_BUCKET_NAME):
        """Initialize the storage client with the specified bucket."""
        self.credentials = None

        # First try to use service account key file if provided
        service_account_path = settings.GOOGLE_APPLICATION_CREDENTIALS
        if service_account_path and os.path.exists(service_account_path):
            try:
                self.credentials = service_account.Credentials.from_service_account_file(service_account_path)
                self.client = storage.Client(credentials=self.credentials)
                print(f"Using service account credentials from file: {service_account_path}")
            except Exception as e:
                print(f"Error loading service account credentials: {e}")
                # Fall back to Application Default Credentials
                self._use_application_default_credentials()
        else:
            # Fall back to Application Default Credentials
            self._use_application_default_credentials()

        self.bucket = self.client.bucket(bucket_name)
        print(f"Using bucket: {bucket_name}")

    def _use_application_default_credentials(self):
        """Use Application Default Credentials as a fallback."""
        try:
            self.credentials, project = google.auth.default()
            # Refresh the credentials to ensure they're valid
            self.credentials.refresh(Request())
            self.client = storage.Client(credentials=self.credentials, project=project)
            print(f"Using Application Default Credentials for project: {project}")
        except Exception as e:
            print(f"Error using Application Default Credentials: {e}")
            # Fall back to default client without explicit credentials
            self.client = storage.Client()
            print("Using default client without explicit credentials")

    def generate_upload_url(
        self, claim_id: UUID, document_id: UUID, filename: str, content_type: str, expiration: int = DEFAULT_EXPIRATION
    ) -> Tuple[str, str, datetime]:
        """
        Generate a signed URL for uploading a file to GCS.

        Args:
            claim_id: The ID of the claim this document belongs to
            document_id: The ID to use for the document
            filename: The original filename
            content_type: The MIME type of the file
            expiration: URL expiration time in seconds

        Returns:
            Tuple containing:
            - The signed URL for uploading
            - The GCS path where the file will be stored
            - The expiration datetime
        """
        # Create a path that includes claim ID for organization
        storage_path = f"claims/{claim_id}/documents/{document_id}/{filename}"

        # Get a blob reference
        blob = self.bucket.blob(storage_path)

        # Generate a signed URL for uploading
        expires_at = datetime.utcnow() + timedelta(seconds=expiration)

        try:
            # Use the credentials to generate a signed URL
            signed_url = blob.generate_signed_url(
                version="v4",
                expiration=expiration,
                method="PUT",
                content_type=content_type,
            )

            return signed_url, storage_path, expires_at
        except Exception as e:
            print(f"Error generating signed URL: {e}")
            raise StorageError(f"Failed to generate signed URL: {e}")

    def direct_upload(
        self, claim_id: UUID, document_id: UUID, filename: str, content_type: str, file_content: bytes
    ) -> Tuple[str, datetime]:
        """
        Upload a file directly to GCS without using signed URLs.

        Args:
            claim_id: The ID of the claim this document belongs to
            document_id: The ID to use for the document
            filename: The original filename
            content_type: The MIME type of the file
            file_content: The content of the file to upload

        Returns:
            Tuple containing:
            - The GCS path where the file was stored
            - The upload datetime
        """
        # Create a path that includes claim ID for organization
        storage_path = f"claims/{claim_id}/documents/{document_id}/{filename}"

        # Get a blob reference
        blob = self.bucket.blob(storage_path)

        # Set content type
        blob.content_type = content_type

        # Upload the file with explicit content_type parameter to ensure it matches metadata
        blob.upload_from_string(file_content, content_type=content_type)

        # Return the storage path and current time
        upload_time = datetime.utcnow()

        return storage_path, upload_time

    def generate_download_url(self, storage_path: str, expiration: int = DEFAULT_EXPIRATION) -> Tuple[str, datetime]:
        """
        Generate a signed URL for downloading a file from GCS.

        Args:
            storage_path: The path to the file in GCS
            expiration: The expiration time in seconds

        Returns:
            Tuple containing:
            - The signed URL for downloading the file
            - The expiration datetime

        Raises:
            FileNotFoundError: If the file does not exist in GCS
        """
        # Get a blob reference
        blob = self.bucket.blob(storage_path)

        # Check if the blob exists
        if not blob.exists():
            raise FileNotFoundError(f"File not found at {storage_path}")

        # Generate a signed URL for downloading
        expires_at = datetime.utcnow() + timedelta(seconds=expiration)

        signed_url = blob.generate_signed_url(
            version="v4",
            expiration=expiration,
            method="GET",
        )

        return signed_url, expires_at

    def delete_file(self, storage_path: str) -> bool:
        """
        Delete a file from GCS.

        Args:
            storage_path: The GCS path where the file is stored

        Returns:
            True if the file was deleted, False if it didn't exist
        """
        # Get a blob reference
        blob = self.bucket.blob(storage_path)

        # Check if the blob exists
        if not blob.exists():
            return False

        # Delete the blob
        blob.delete()
        return True

    def get_file_metadata(self, storage_path: str) -> Optional[dict]:
        """
        Get metadata for a file in GCS.

        Args:
            storage_path: The GCS path where the file is stored

        Returns:
            A dictionary of metadata or None if the file doesn't exist
        """
        # Get a blob reference
        blob = self.bucket.blob(storage_path)

        # Check if the blob exists
        if not blob.exists():
            return None

        # Get metadata
        blob.reload()

        return {
            "size": blob.size,
            "content_type": blob.content_type,
            "created": blob.time_created,
            "updated": blob.updated,
            "md5_hash": blob.md5_hash,
        }


# Create a singleton instance
storage_client = StorageClient()
