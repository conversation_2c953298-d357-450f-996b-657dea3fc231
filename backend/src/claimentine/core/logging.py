"""Logging configuration for the application."""

import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path

from claimentine.core.config import settings


def setup_logging() -> None:
    """Configure logging for the application."""
    # Create logs directory if it doesn't exist
    log_dir = Path(settings.LOG_PATH)
    log_dir.mkdir(parents=True, exist_ok=True)

    # Create formatter for better readability
    formatter = logging.Formatter(
        fmt="%(asctime)s - %(levelname)s - [%(name)s] - %(message)s\n%(exc_info)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper()))

    # Calculate max bytes for file size
    max_bytes = settings.LOG_FILE_SIZE_MB * 1024 * 1024  # Convert MB to bytes
    backup_count = settings.LOG_RETENTION_DAYS

    # Single file handler for all logs
    file_handler = RotatingFileHandler(
        log_dir / "app.log",
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding="utf-8",
    )
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)  # Set to DEBUG to capture all logs

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.DEBUG)  # Set to DEBUG to capture all logs

    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Set SQLAlchemy logging level
    logging.getLogger("sqlalchemy.engine").setLevel(logging.INFO if settings.DB_ECHO_LOG else logging.WARNING)

    # Set uvicorn access log format
    uvicorn_logger = logging.getLogger("uvicorn.access")
    uvicorn_logger.handlers.clear()
    uvicorn_logger.addHandler(file_handler)
    uvicorn_logger.addHandler(console_handler)

    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging system initialized",
        extra={
            "log_level": settings.LOG_LEVEL,
            "log_path": str(settings.LOG_PATH),
            "rotation_size_mb": settings.LOG_FILE_SIZE_MB,
            "retention_days": settings.LOG_RETENTION_DAYS,
        },
    )
