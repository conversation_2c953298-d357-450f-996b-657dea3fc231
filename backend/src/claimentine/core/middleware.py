"""Middleware for the FastAPI application."""

import logging
import traceback
import uuid
from contextvars import ContextV<PERSON>
from typing import Any, Dict, List, Optional, Union

from fastapi import Fast<PERSON><PERSON>, Request, Response
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError as PydanticValidationError
from sqlalchemy.exc import SQLAlchemyError
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.types import ASGIApp

from claimentine.core.exceptions import AppException, ErrorCode, ErrorDetail, ErrorResponse, create_error_detail

# Context variable to store request ID
request_id_ctx_var: ContextVar[str] = ContextVar("request_id", default="")


def get_request_id() -> str:
    """Get the request ID from the context."""
    return request_id_ctx_var.get()


class RequestContextMiddleware(BaseHTTPMiddleware):
    """Middleware to add request context (like request ID) to each request."""

    def __init__(
        self,
        app: <PERSON><PERSON><PERSON><PERSON>,
        header_name: str = "X-Request-ID",
        validate_uuid: bool = True,
    ) -> None:
        super().__init__(app)
        self.header_name = header_name
        self.validate_uuid = validate_uuid
        self.logger = logging.getLogger(__name__)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process the request, adding request ID."""
        # Get or generate request ID (synchronous operation)
        request_id = self._get_or_generate_request_id(request)

        # Store in context (synchronous operation)
        ctx_token = request_id_ctx_var.set(request_id)

        try:
            # Log request (synchronous operation)
            self._log_request(request, request_id)

            # Process the request (async operation required by FastAPI)
            response = await call_next(request)

            # Add request ID to response headers (synchronous operation)
            response.headers[self.header_name] = request_id

            # Log response (synchronous operation)
            self._log_response(response, request_id)

            return response
        finally:
            # Reset context (synchronous operation)
            request_id_ctx_var.reset(ctx_token)

    def _get_or_generate_request_id(self, request: Request) -> str:
        """Get request ID from header or generate new one."""
        request_id = request.headers.get(self.header_name)
        if not request_id or (self.validate_uuid and not self._is_valid_uuid(request_id)):
            request_id = str(uuid.uuid4())
        return request_id

    def _log_request(self, request: Request, request_id: str) -> None:
        """Log incoming request details."""
        self.logger.info(
            f"Incoming request {request.method} {request.url.path}",
            extra={
                "req_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "client_host": request.client.host if request.client else None,
            },
        )

    def _log_response(self, response: Response, request_id: str) -> None:
        """Log response details."""
        self.logger.info(
            f"Request completed with status {response.status_code}",
            extra={
                "req_id": request_id,
                "status_code": response.status_code,
            },
        )

    @staticmethod
    def _is_valid_uuid(value: str) -> bool:
        """Check if a string is a valid UUID."""
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False


def create_error_response(
    code: Union[ErrorCode, str],
    message: str,
    status_code: int,
    details: Optional[Union[ErrorDetail, Dict[str, Any]]] = None,
) -> Response:
    """Create a standardized error response."""
    # Convert string code to ErrorCode enum if needed
    if isinstance(code, str):
        try:
            code = ErrorCode(code)
        except ValueError:
            code = ErrorCode.INTERNAL_ERROR

    # Convert dict details to ErrorDetail if needed
    if details and not isinstance(details, ErrorDetail):
        # Try to extract structured information from details dict
        if isinstance(details, dict):
            resource = details.get("resource", "unknown")
            identifier = details.get("identifier")
            field = details.get("field")
            reason = details.get("reason")
            context = details.get("context", details)

            details = ErrorDetail(
                resource=resource,
                identifier=identifier,
                field=field,
                reason=reason,
                context=context,
            )

    error_response = ErrorResponse(
        code=code,
        message=message,
        details=details,
        request_id=get_request_id(),
    )
    return Response(
        content=error_response.model_dump_json(),
        status_code=status_code,
        media_type="application/json",
    )


def setup_error_handlers(app: FastAPI) -> None:
    """Configure error handlers for the application."""
    logger = logging.getLogger(__name__)

    def log_error(error_type: str, details: dict) -> None:
        """Log error with consistent format."""
        logger.error(
            f"{error_type} error occurred",
            extra={"req_id": get_request_id(), **details},
        )

    @app.exception_handler(AppException)
    def app_exception_handler(request: Request, exc: AppException) -> Response:
        """Handle application-specific exceptions."""
        log_error(
            "Application",
            {
                "error_code": str(exc.code),
                "error_message": str(exc.detail),
                "status_code": exc.status_code,
                "details": exc.details.model_dump() if exc.details else None,
            },
        )
        return create_error_response(exc.code, str(exc.detail), exc.status_code, exc.details)

    @app.exception_handler(RequestValidationError)
    @app.exception_handler(PydanticValidationError)
    def validation_exception_handler(
        request: Request, exc: RequestValidationError | PydanticValidationError
    ) -> Response:
        """Handle validation errors."""
        # Create structured error details
        error_details = create_error_detail(
            resource="request",
            reason="validation_error",
            context={"errors": [{"loc": err["loc"], "msg": err["msg"]} for err in exc.errors()]},
        )

        log_error(
            "Validation",
            {
                "error_code": ErrorCode.VALIDATION_ERROR,
                "error_message": "Request validation failed",
                "status_code": 422,
                "details": error_details.model_dump(),
            },
        )
        return create_error_response(ErrorCode.VALIDATION_ERROR, "Request validation failed", 422, error_details)

    @app.exception_handler(SQLAlchemyError)
    def sqlalchemy_exception_handler(request: Request, exc: SQLAlchemyError) -> Response:
        """Handle database errors."""
        error_details = create_error_detail(resource="database", reason="database_error", context={"error": str(exc)})

        log_error(
            "Database",
            {
                "error_code": ErrorCode.DATABASE_ERROR,
                "error_message": str(exc),
                "status_code": 503,
                "traceback": "".join(traceback.format_exception(type(exc), exc, exc.__traceback__)),
            },
        )
        return create_error_response(ErrorCode.DATABASE_ERROR, "Database operation failed", 503, error_details)

    @app.exception_handler(Exception)
    def unhandled_exception_handler(request: Request, exc: Exception) -> Response:
        """Handle any unhandled exceptions."""
        error_details = create_error_detail(
            resource="system", reason="internal_error", context={"error_type": exc.__class__.__name__}
        )

        log_error(
            "Unhandled",
            {
                "error_type": exc.__class__.__name__,
                "error_message": str(exc),
                "traceback": "".join(traceback.format_exception(type(exc), exc, exc.__traceback__)),
            },
        )
        return create_error_response(ErrorCode.INTERNAL_ERROR, "An unexpected error occurred", 500, error_details)


class CustomCORSMiddleware(BaseHTTPMiddleware):
    """Custom CORS middleware with enhanced OPTIONS request handling."""

    def __init__(
        self,
        app: ASGIApp,
        allow_origins: List[str],
        allow_credentials: bool = True,
        allow_methods: List[str] = None,
        allow_headers: List[str] = None,
        max_age: int = 86400,
    ) -> None:
        super().__init__(app)
        self.allow_origins = allow_origins
        self.allow_credentials = allow_credentials
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
        self.allow_headers = allow_headers or ["Authorization", "Content-Type", "Accept"]
        self.max_age = max_age
        self.logger = logging.getLogger(__name__)

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process the request, handling CORS headers and OPTIONS requests."""
        origin = request.headers.get("Origin")

        # If this is not a cross-origin request, process it normally
        if not origin:
            return await call_next(request)

        # Check if the origin is allowed
        allowed_origin = None
        if "*" in self.allow_origins:
            allowed_origin = "*"
        elif origin in self.allow_origins:
            allowed_origin = origin

        # If origin is not allowed, just process normally without CORS headers
        if not allowed_origin:
            self.logger.warning(f"CORS request from disallowed origin: {origin}")
            return await call_next(request)

        # Handle preflight OPTIONS request
        if request.method == "OPTIONS":
            response = Response(status_code=200)
        else:
            # For regular requests, process normally
            response = await call_next(request)

        # Add CORS headers to all responses
        response.headers["Access-Control-Allow-Origin"] = allowed_origin

        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"

        if request.method == "OPTIONS":
            # Add preflight specific headers
            response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
            response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
            response.headers["Access-Control-Max-Age"] = str(self.max_age)

        return response
