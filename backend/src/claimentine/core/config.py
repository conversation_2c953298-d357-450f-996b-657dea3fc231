"""Application configuration."""

import json
from typing import Any, Dict, List, Optional

from pydantic import PostgresDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings."""

    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True, extra="allow")

    # Application
    PROJECT_NAME: str = "Claimentine"
    VERSION: str = "0.1.0"
    API_V1_STR: str = "/api/v1"

    # CORS configuration
    BACKEND_CORS_ORIGINS: List[str] = []  # For backward compatibility
    CORS_ORIGINS: List[str] = []
    CORS_METHODS: List[str] = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
    CORS_HEADERS: List[str] = ["Authorization", "Content-Type", "Accept", "X-Request-ID"]
    CORS_MAX_AGE: int = 86400  # 24 hours in seconds
    CORS_ALLOW_CREDENTIALS: bool = True

    # Database
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: str = "5432"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "postgres"
    POSTGRES_DB: str = "claimentine"
    POSTGRES_URL: Optional[PostgresDsn] = None

    # Database connection pool settings
    DB_POOL_SIZE: int = 5
    DB_MAX_OVERFLOW: int = 10
    DB_ECHO_LOG: bool = False

    # JWT settings
    JWT_SECRET_KEY: str = "secret"  # Change in production
    JWT_ALGORITHM: str = "HS256"
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Token settings
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = 24
    EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS: int = 48

    # Google Cloud Storage settings
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = None
    GCS_BUCKET_NAME: str = "claimentine-documents-dev"

    # Logging
    LOG_PATH: str = "backend/logs"
    LOG_LEVEL: str = "INFO"
    LOG_FILE_SIZE_MB: int = 10  # Default to 10MB per log file
    LOG_RETENTION_DAYS: int = 7  # Keep logs for 7 days

    @field_validator("POSTGRES_URL", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info: Dict[str, Any]) -> Any:
        """Assemble database connection URL."""
        if isinstance(v, str):
            return v

        # Get values from the validation context
        values = info.data
        return PostgresDsn.build(
            scheme="postgresql",
            username=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_HOST"),
            port=int(values.get("POSTGRES_PORT", 5432)),
            path=values.get("POSTGRES_DB", ""),
        )

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Any) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            try:
                # Try to parse as JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # If not JSON, split by comma
                return [i.strip() for i in v.split(",") if i.strip()]
        return v or []

    @field_validator("CORS_ORIGINS", mode="before")
    def assemble_cors_origins_new(cls, v: Any) -> List[str]:
        """Parse CORS origins from string or list for the new environment variable."""
        if isinstance(v, str):
            try:
                # Try to parse as JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # If not JSON, split by comma
                return [i.strip() for i in v.split(",") if i.strip()]
        return v or []

    @field_validator("CORS_METHODS", mode="before")
    def assemble_cors_methods(cls, v: Any) -> List[str]:
        """Parse CORS methods from string or list."""
        if isinstance(v, str):
            try:
                # Try to parse as JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # If not JSON, split by comma
                return [i.strip() for i in v.split(",") if i.strip()]
        return v or ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]

    @field_validator("CORS_HEADERS", mode="before")
    def assemble_cors_headers(cls, v: Any) -> List[str]:
        """Parse CORS headers from string or list."""
        if isinstance(v, str):
            try:
                # Try to parse as JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # If not JSON, split by comma
                return [i.strip() for i in v.split(",") if i.strip()]
        return v or ["Authorization", "Content-Type", "Accept", "X-Request-ID"]


settings = Settings()
