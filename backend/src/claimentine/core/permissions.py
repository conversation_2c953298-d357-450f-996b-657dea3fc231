"""Permission checking utilities."""

import inspect
from functools import wraps
from typing import Callable, List, Optional, Set, Union
from uuid import UUID

from fastapi import Depends
from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import AuthorizationError, ValidationError, create_error_detail
from claimentine.db.base import get_db
from claimentine.models.auth import ROLE_HIERARCHY, ROLE_PERMISSIONS, Permission
from claimentine.models.user import User, UserRole


def get_inherited_roles(role: UserRole) -> Set[UserRole]:
    """Get all roles inherited by the given role."""
    inherited = {role}
    for inherited_role in ROLE_HIERARCHY.get(role, []):
        inherited.update(get_inherited_roles(inherited_role))
    return inherited


def get_role_permissions(role: UserRole) -> Set[str]:
    """Get all permissions for a role including inherited ones."""
    permissions = set()
    roles = get_inherited_roles(role)
    for r in roles:
        permissions.update(ROLE_PERMISSIONS.get(r, []))
    return permissions


def get_user_permissions(db: Session, user: User) -> Set[str]:
    """Get all permissions for a user including role-based and explicit ones."""
    # Get role-based permissions
    permissions = get_role_permissions(user.role)

    # Get explicit permissions from database
    stmt = select(Permission).join(Permission.users).where(User.id == user.id)
    explicit_permissions = db.scalars(stmt).all()
    permissions.update(p.name for p in explicit_permissions)

    return permissions


def has_permission(
    user: User,
    db: Session,
    required_permission: Optional[str] = None,
    required_role: Optional[UserRole] = None,
) -> bool:
    """Check if user has required permission or role."""
    # System admins have all permissions
    if user.role == UserRole.ADMIN:
        return True

    # Check role hierarchy if role required
    if required_role:
        user_roles = get_inherited_roles(user.role)
        if required_role not in user_roles:
            return False

    # Check permission if required
    if required_permission:
        user_permissions = get_user_permissions(db, user)
        return required_permission in user_permissions

    return True


def require_permissions(
    required_permissions: Optional[Union[str, List[str]]] = None,
    required_role: Optional[UserRole] = None,
    require_all: bool = False,
) -> Callable:
    """
    Dependency for requiring specific permissions or roles.

    Args:
        required_permissions: Permission or list of permissions required
        required_role: Role required
        require_all: Whether all permissions are required (AND) or any (OR)

    Returns:
        Dependency function
    """
    if not required_permissions and not required_role:
        raise ValidationError("Either required_permissions or required_role must be provided")

    def permission_dependency(
        user: User = Depends(get_current_user),
        db: Session = Depends(get_db),
    ) -> None:
        # System admins have all permissions
        if user.role == UserRole.ADMIN:
            return

        # Check role if required
        if required_role and user.role != required_role:
            # Check if user has a higher role in the hierarchy
            user_roles = get_inherited_roles(user.role)
            if required_role not in user_roles:
                raise AuthorizationError(
                    message=f"Role {required_role} required",
                    details=create_error_detail(
                        resource="user",
                        identifier=str(user.id),
                        reason="insufficient_role",
                        context={"required_role": str(required_role), "user_role": str(user.role)},
                    ),
                )

        # Check permissions if required
        if required_permissions:
            # Convert to list if string
            permissions_list = [required_permissions] if isinstance(required_permissions, str) else required_permissions

            # Get user permissions
            user_permissions = get_user_permissions(db, user)

            # Check if user has required permissions
            if require_all:
                # All permissions required
                missing_permissions = [p for p in permissions_list if p not in user_permissions]
                if missing_permissions:
                    raise AuthorizationError(
                        message=f"All permissions required: {', '.join(permissions_list)}",
                        details=create_error_detail(
                            resource="user",
                            identifier=str(user.id),
                            reason="missing_permissions",
                            context={
                                "required_permissions": permissions_list,
                                "missing_permissions": missing_permissions,
                            },
                        ),
                    )
            else:
                # Any permission required
                if not any(p in user_permissions for p in permissions_list):
                    raise AuthorizationError(
                        message=f"One of these permissions required: {', '.join(permissions_list)}",
                        details=create_error_detail(
                            resource="user",
                            identifier=str(user.id),
                            reason="missing_permissions",
                            context={"required_permissions": permissions_list},
                        ),
                    )

    return permission_dependency


def create_permission_error(
    user_id: Union[UUID, str],
    user_email: str,
    permission: Union[str, List[str]],
    resource_type: Optional[str] = None,
    resource_id: Optional[Union[UUID, str]] = None,
    reason: str = "permission_denied",
    require_all: bool = False,
) -> AuthorizationError:
    """Create a standardized permission error with consistent context and formatting.

    Args:
        user_id: ID of the user
        user_email: Email of the user
        permission: Required permission or list of permissions
        resource_type: Type of resource being accessed
        resource_id: ID of the resource being accessed
        reason: Error reason code
        require_all: Whether all permissions are required (when permission is a list)

    Returns:
        AuthorizationError: Standardized permission error
    """
    # Format the permission message
    if isinstance(permission, list):
        if require_all:
            message = f"All permissions required: {', '.join(permission)}"
        else:
            message = f"One of these permissions required: {', '.join(permission)}"
    else:
        message = f"Permission denied: {permission} required"

    # Add resource context if provided
    if resource_type and resource_id:
        message += f" for {resource_type} {resource_id}"

    # Create context for error details
    context = {
        "user_id": str(user_id),
        "user_email": user_email,
    }

    if isinstance(permission, list):
        context["required_permissions"] = permission
    else:
        context["required_permission"] = permission

    if resource_type:
        context["resource_type"] = resource_type

    if resource_id:
        context["resource_id"] = str(resource_id) if resource_id else None

    # Create and return the error
    return AuthorizationError(
        message=message,
        details=create_error_detail(
            resource=resource_type or "permission",
            identifier=str(resource_id or user_id),
            reason=reason,
            context=context,
        ),
    )


def check_permission(db: Session, user: User, permission: str) -> None:
    """
    Check if a user has a specific permission.

    Args:
        db: Database session
        user: User to check
        permission: Permission to check

    Raises:
        AuthorizationError: If the user doesn't have the permission
    """
    # System admins have all permissions
    if user.role == UserRole.ADMIN:
        return

    # Get user permissions
    user_permissions = get_user_permissions(db, user)

    # Check permission
    if permission not in user_permissions:
        raise create_permission_error(
            user_id=user.id, user_email=user.email, permission=permission, reason="missing_permission"
        )


def check_claim_permission(db: Session, user: User, claim_id: UUID, permission: str) -> None:
    """
    Check if a user has permission to perform an action on a claim.

    Args:
        db: Database session
        user: User to check
        claim_id: Claim ID
        permission: Permission to check

    Raises:
        AuthorizationError: If the user doesn't have the permission
    """
    # System admins have all permissions
    if user.role == UserRole.ADMIN:
        return

    # Get user permissions
    user_permissions = get_user_permissions(db, user)

    # Check permission
    if permission not in user_permissions:
        raise create_permission_error(
            user_id=user.id,
            user_email=user.email,
            permission=permission,
            resource_type="claim",
            resource_id=claim_id,
            reason="missing_permission",
        )


def require_service_permission(
    permission: Optional[Union[str, List[str]]] = None,
    resource_type_param: Optional[str] = None,  # Parameter name for resource type
    resource_id_param: Optional[str] = None,  # Parameter name for resource ID
    any_permission: bool = False,  # If True, any permission in list is sufficient
):
    """Decorator for service methods to enforce permission checks.

    This decorator standardizes permission checking across service methods.
    It should be applied to methods of classes that inherit from BaseService.

    Args:
        permission: Required permission(s) - string or list of strings
        resource_type_param: Name of the parameter that contains the resource type
        resource_id_param: Name of the parameter that contains the resource ID
        any_permission: If True and permission is a list, any permission is sufficient
                       Otherwise all permissions are required

    Returns:
        Decorator function

    Example:
        @require_service_permission("VIEW_DOCUMENT", resource_id_param="document_id")
        def get_document(self, document_id: UUID):
            # Method implementation...
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, *args, **kwargs):
            # Ensure the service has current_user and db attributes
            if not hasattr(self, "current_user") or not hasattr(self, "db"):
                raise ValueError("The service must have current_user and db attributes")

            # Skip permission check if no user context (for internal operations)
            if not self.current_user:
                return func(self, *args, **kwargs)

            # Extract resource type and ID from parameters if specified
            resource_type = None
            resource_id = None

            if resource_type_param:
                if resource_type_param in kwargs:
                    resource_type = kwargs[resource_type_param]

            if resource_id_param:
                if resource_id_param in kwargs:
                    resource_id = kwargs[resource_id_param]

                    # Try to match parameter with positional args if not found in kwargs
                    if resource_id is None:
                        # Get the function signature
                        sig = inspect.signature(func)
                        parameters = list(sig.parameters.keys())

                        # Skip 'self'
                        parameters = parameters[1:]

                        # Find the position of the resource_id parameter
                        try:
                            idx = parameters.index(resource_id_param)
                            if idx < len(args):
                                resource_id = args[idx]
                        except ValueError:
                            pass

            # Check permission(s)
            if isinstance(permission, list):
                if any_permission:
                    # Check if user has any of the specified permissions
                    if hasattr(self, "check_any_permission"):
                        self.check_any_permission(permission, resource_type, resource_id)
                    else:
                        # Fallback if check_any_permission not available
                        for p in permission:
                            if hasattr(self, "check_permission"):
                                if self.check_permission(p, resource_type, resource_id, raise_exception=False):
                                    break
                        else:
                            # No permission was granted
                            raise create_permission_error(
                                str(self.current_user.id),
                                self.current_user.email,
                                permission,
                                resource_type,
                                resource_id,
                                require_all=False,
                            )
                else:
                    # Check if user has all specified permissions
                    for p in permission:
                        if hasattr(self, "check_permission"):
                            self.check_permission(p, resource_type, resource_id)
            elif permission:
                # Check single permission
                if hasattr(self, "check_permission"):
                    self.check_permission(permission, resource_type, resource_id)

            # Call the original function
            return func(self, *args, **kwargs)

        return wrapper

    return decorator
