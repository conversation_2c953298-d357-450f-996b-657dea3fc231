"""Service for performance-related reports."""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, case, func, select
from sqlalchemy.orm import Session

from claimentine.models.claim.base import BaseClaim, ClaimStatus
from claimentine.models.claim.financial import ClaimFinancials
from claimentine.models.task import Task, TaskStatus
from claimentine.models.user import User
from claimentine.schemas.reports.adjuster_performance import AdjusterPerformanceItem, ReportResponseAdjusterPerformance
from claimentine.schemas.reports.base import ReportRequest
from claimentine.services.reports.base import BaseReportService

logger = logging.getLogger(__name__)


class PerformanceReportService(BaseReportService):
    """Service for performance-related reports."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_adjuster_performance_report(self, request: ReportRequest) -> ReportResponseAdjusterPerformance:
        """Generate Adjuster Performance report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseAdjusterPerformance object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base query filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.claim_type == request.claim_type)

        # Define closed statuses condition
        is_closed_condition = BaseClaim.status == ClaimStatus.CLOSURE

        # Get list of adjusters
        adjusters_stmt = (
            select(User.id, User.first_name, User.last_name)
            .distinct()
            .join(BaseClaim, BaseClaim.assigned_to_id == User.id)
            .where(User.is_deleted == False, *claim_filters)
        )

        adjusters = self.db.execute(adjusters_stmt).all()

        # Prepare the report data
        adjuster_items = []
        for adjuster in adjusters:
            user_id = adjuster.id
            user_name = f"{adjuster.first_name} {adjuster.last_name}"

            # Count claims handled in period
            claims_handled_filters = [
                BaseClaim.assigned_to_id == user_id,
                BaseClaim.created_at <= end_date,
                *claim_filters,
            ]
            # Only add start_date filter if it's not None
            if start_date is not None:
                claims_handled_filters.append(BaseClaim.created_at >= start_date)

            claims_handled_stmt = select(func.count()).select_from(BaseClaim).where(*claims_handled_filters)
            claims_handled = self.db.scalar(claims_handled_stmt) or 0

            # Calculate average resolution time
            avg_resolution_filters = [
                BaseClaim.assigned_to_id == user_id,
                is_closed_condition,
                BaseClaim.closed_at <= end_date,
                *claim_filters,
            ]
            # Only add start_date filter if it's not None
            if start_date is not None:
                avg_resolution_filters.append(BaseClaim.closed_at >= start_date)

            avg_resolution_stmt = (
                select(func.avg(BaseClaim.closed_at - BaseClaim.created_at))
                .select_from(BaseClaim)
                .where(*avg_resolution_filters)
            )
            avg_resolution_result = self.db.scalar(avg_resolution_stmt)
            avg_resolution_days = avg_resolution_result.total_seconds() / (60 * 60 * 24) if avg_resolution_result else 0

            # Get total payments authorized
            payments_filters = [
                BaseClaim.assigned_to_id == user_id,
                ClaimFinancials.last_payment_date <= end_date,
                *claim_filters,
            ]
            # Only add start_date filter if it's not None
            if start_date is not None:
                payments_filters.append(ClaimFinancials.last_payment_date >= start_date)

            payments_stmt = (
                select(
                    func.sum(ClaimFinancials.indemnity_paid)
                    + func.sum(ClaimFinancials.expense_paid)
                    + func.sum(ClaimFinancials.defense_paid)
                )
                .select_from(ClaimFinancials)
                .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                .where(*payments_filters)
            )
            total_payments = self.db.scalar(payments_stmt) or Decimal("0.00")

            # Get pending tasks
            pending_tasks_stmt = (
                select(func.count())
                .select_from(Task)
                .where(Task.assigned_to == user_id, Task.status == TaskStatus.PENDING)
            )
            pending_tasks = self.db.scalar(pending_tasks_stmt) or 0

            # Get overdue tasks
            overdue_tasks_stmt = (
                select(func.count())
                .select_from(Task)
                .where(
                    Task.assigned_to == user_id,
                    Task.status == TaskStatus.PENDING,
                    Task.due_date < datetime.utcnow(),
                )
            )
            overdue_tasks = self.db.scalar(overdue_tasks_stmt) or 0

            # Get completed tasks
            completed_tasks_filters = [
                Task.assigned_to == user_id,
                Task.status == TaskStatus.COMPLETED,
                Task.updated_at <= end_date,
            ]
            # Only add start_date filter if it's not None
            if start_date is not None:
                completed_tasks_filters.append(Task.updated_at >= start_date)

            completed_tasks_stmt = select(func.count()).select_from(Task).where(*completed_tasks_filters)
            completed_tasks = self.db.scalar(completed_tasks_stmt) or 0

            # Add to report items
            adjuster_items.append(
                AdjusterPerformanceItem(
                    user_id=str(user_id),
                    user_name=user_name,
                    claims_handled=claims_handled,
                    avg_resolution_time=avg_resolution_days,
                    total_payments=total_payments,
                    pending_tasks=pending_tasks,
                    completed_tasks=completed_tasks,
                )
            )

        # Sort by claims handled (descending)
        adjuster_items.sort(key=lambda x: x.claims_handled, reverse=True)

        # Create column headers
        column_headers = [
            {"key": "user_name", "label": "Adjuster"},
            {"key": "claims_handled", "label": "Claims Handled"},
            {"key": "avg_resolution_time", "label": "Avg. Resolution Time (days)"},
            {"key": "total_payments", "label": "Total Payments"},
            {"key": "pending_tasks", "label": "Pending Tasks"},
            {"key": "completed_tasks", "label": "Completed Tasks"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="adjuster_performance", request=request, column_headers=column_headers
        )

        # Return report
        return ReportResponseAdjusterPerformance(report_metadata=metadata, data=adjuster_items)
