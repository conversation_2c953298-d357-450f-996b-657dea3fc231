"""Service for financial reports."""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy import and_, case, func, select
from sqlalchemy.orm import Session

from claimentine.models.claim.base import BaseClaim
from claimentine.models.claim.financial import ClaimFinancials, ClaimReserve, ReserveHistory
from claimentine.models.user import User
from claimentine.schemas.reports.base import ReportRequest
from claimentine.schemas.reports.financial_kpis import FinancialKpiData, ReportResponseFinancialKpis
from claimentine.schemas.reports.payments_vs_reserves import PaymentReserveTimeItem, ReportResponsePaymentsVsReserves
from claimentine.services.reports.base import BaseReportService

logger = logging.getLogger(__name__)


class FinancialReportService(BaseReportService):
    """Service for financial reports."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_financial_kpi_report(self, request: ReportRequest) -> ReportResponseFinancialKpis:
        """Generate Financial KPI report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponseFinancialKpis object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.claim_type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))

        # Get total reserves for current period
        # Use current ClaimReserve data instead of ReserveHistory for accurate totals
        reserves_stmt = (
            select(func.sum(ClaimReserve.amount))
            .select_from(ClaimReserve)
            .join(ClaimFinancials, ClaimReserve.financials_id == ClaimFinancials.id)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(*claim_filters)
        )

        # Only apply date filtering if explicitly requested with period/start_date/end_date
        # This fixes the issue where default 30-day filtering was hiding valid reserves
        if request.period or request.start_date or request.end_date:
            # Apply date filter to claim creation date for period-based reports
            if start_date is not None:
                reserves_stmt = reserves_stmt.where(BaseClaim.created_at >= start_date)
            reserves_stmt = reserves_stmt.where(BaseClaim.created_at <= end_date)

        total_reserves = self.db.scalar(reserves_stmt) or Decimal("0.00")

        # Get total payments in current period
        payments_stmt = (
            select(
                func.sum(ClaimFinancials.indemnity_paid)
                + func.sum(ClaimFinancials.expense_paid)
                + func.sum(ClaimFinancials.defense_paid)
            )
            .select_from(ClaimFinancials)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(*claim_filters)
        )

        # Apply date filtering for payments if requested
        if request.period or request.start_date or request.end_date:
            if start_date is not None:
                payments_stmt = payments_stmt.where(ClaimFinancials.last_payment_date >= start_date)
            payments_stmt = payments_stmt.where(ClaimFinancials.last_payment_date <= end_date)

        total_payments = self.db.scalar(payments_stmt) or Decimal("0.00")

        # Get average reserve per claim using current reserves
        avg_reserve_stmt = (
            select(func.avg(ClaimReserve.amount))
            .select_from(ClaimReserve)
            .join(ClaimFinancials, ClaimReserve.financials_id == ClaimFinancials.id)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(*claim_filters)
        )

        # Apply date filtering if requested
        if request.period or request.start_date or request.end_date:
            if start_date is not None:
                avg_reserve_stmt = avg_reserve_stmt.where(BaseClaim.created_at >= start_date)
            avg_reserve_stmt = avg_reserve_stmt.where(BaseClaim.created_at <= end_date)

        avg_reserve_per_claim = self.db.scalar(avg_reserve_stmt) or Decimal("0.00")

        # Get average payment per claim
        avg_payment_stmt = (
            select(
                func.avg(ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid)
            )
            .select_from(ClaimFinancials)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(
                *claim_filters,
                ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid > 0,
            )  # Only include claims with payments
        )

        # Apply date filtering if requested
        if request.period or request.start_date or request.end_date:
            if start_date is not None:
                avg_payment_stmt = avg_payment_stmt.where(BaseClaim.created_at >= start_date)
            avg_payment_stmt = avg_payment_stmt.where(BaseClaim.created_at <= end_date)

        avg_payment_per_claim = self.db.scalar(avg_payment_stmt) or Decimal("0.00")

        # Calculate metrics change if requested
        total_reserves_change = None
        total_payments_change = None
        avg_reserve_change = None
        avg_payment_change = None

        if request.compare_period and start_date is not None:
            # Calculate previous period
            period_duration = end_date - start_date
            prev_end_date = start_date - timedelta(microseconds=1)
            prev_start_date = prev_end_date - period_duration

            # Previous period reserves using current ClaimReserve data
            prev_reserves_stmt = (
                select(func.sum(ClaimReserve.amount))
                .select_from(ClaimReserve)
                .join(ClaimFinancials, ClaimReserve.financials_id == ClaimFinancials.id)
                .join(BaseClaim, ClaimReserve.claim_id == BaseClaim.id)
                .where(
                    *claim_filters,
                    BaseClaim.created_at >= prev_start_date,
                    BaseClaim.created_at <= prev_end_date,
                )
            )

            prev_total_reserves = self.db.scalar(prev_reserves_stmt) or Decimal("0.00")
            if prev_total_reserves > 0:
                total_reserves_change = self._calculate_metric_change(total_reserves, prev_total_reserves)

            # Previous period payments
            prev_payments_stmt = (
                select(
                    func.sum(
                        ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid
                    )
                )
                .select_from(ClaimFinancials)
                .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                .where(
                    *claim_filters,
                    ClaimFinancials.last_payment_date >= prev_start_date,
                    ClaimFinancials.last_payment_date <= prev_end_date,
                )
            )

            prev_total_payments = self.db.scalar(prev_payments_stmt) or Decimal("0.00")
            if prev_total_payments > 0:
                total_payments_change = self._calculate_metric_change(total_payments, prev_total_payments)

            # Previous period average reserve
            prev_avg_reserve_stmt = (
                select(func.avg(ClaimReserve.amount))
                .select_from(ClaimReserve)
                .join(ClaimFinancials, ClaimReserve.financials_id == ClaimFinancials.id)
                .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                .where(
                    *claim_filters,
                    BaseClaim.created_at >= prev_start_date,
                    BaseClaim.created_at <= prev_end_date,
                )
            )

            prev_avg_reserve = self.db.scalar(prev_avg_reserve_stmt) or Decimal("0.00")
            if prev_avg_reserve > 0:
                avg_reserve_change = self._calculate_metric_change(avg_reserve_per_claim, prev_avg_reserve)

            # Previous period average payment
            prev_avg_payment_stmt = (
                select(
                    func.avg(
                        ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid
                    )
                )
                .select_from(ClaimFinancials)
                .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                .where(
                    *claim_filters,
                    ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid > 0,
                    BaseClaim.created_at >= prev_start_date,
                    BaseClaim.created_at <= prev_end_date,
                )  # Only include claims with payments
            )

            prev_avg_payment = self.db.scalar(prev_avg_payment_stmt) or Decimal("0.00")
            if prev_avg_payment > 0:
                avg_payment_change = self._calculate_metric_change(avg_payment_per_claim, prev_avg_payment)

        # For now, we'll use a placeholder for avg_claim_value and recovery_amount since those aren't fully implemented
        avg_claim_value = Decimal("0.00")
        recovery_amount = Decimal("0.00")

        # Create column headers
        column_headers = [
            {"key": "metric", "label": "Metric"},
            {"key": "value", "label": "Value"},
            {"key": "change", "label": "Change"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="financial_kpis", request=request, column_headers=column_headers
        )

        # Create report data
        data = FinancialKpiData(
            total_reserves=total_reserves,
            total_reserves_change=total_reserves_change,
            total_payments=total_payments,
            total_payments_change=total_payments_change,
            avg_reserve_per_claim=avg_reserve_per_claim,
            avg_reserve_per_claim_change=avg_reserve_change,
            avg_payment_per_claim=avg_payment_per_claim,
            avg_payment_per_claim_change=avg_payment_change,
            avg_claim_value=avg_claim_value,
            avg_claim_value_change=None,
            recovery_amount=recovery_amount,
            recovery_amount_change=None,
        )

        # Return report
        return ReportResponseFinancialKpis(report_metadata=metadata, data=data)

    def get_payments_vs_reserves_report(self, request: ReportRequest) -> ReportResponsePaymentsVsReserves:
        """Generate Payments vs Reserves report.

        Args:
            request: Report request parameters

        Returns:
            ReportResponsePaymentsVsReserves object
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.claim_type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))

        # Determine time interval based on date range
        interval = "month"
        if start_date is not None and (end_date - start_date).days <= 60:  # If range is <= 60 days, use weekly
            interval = "week"

        # Calculate periods
        periods = []
        result_items = []

        if start_date is None:
            # If no start_date, we can't calculate meaningful periods, return empty data
            result_items = []
        elif interval == "month":
            current_date = start_date.replace(day=1)
            while current_date <= end_date:
                next_month = current_date.month + 1
                next_year = current_date.year
                if next_month > 12:
                    next_month = 1
                    next_year += 1

                period_end = datetime(next_year, next_month, 1) - timedelta(microseconds=1)
                periods.append((current_date, period_end))

                current_date = datetime(next_year, next_month, 1)
        else:  # weekly
            current_date = start_date
            while current_date <= end_date:
                period_end = current_date + timedelta(days=6, hours=23, minutes=59, seconds=59)
                if period_end > end_date:
                    period_end = end_date

                periods.append((current_date, period_end))
                current_date = current_date + timedelta(days=7)

        # Query data for each period (only if we have periods to query)
        if periods:
            for period_start, period_end in periods:
                # Payments in period
                payments_stmt = (
                    select(
                        func.sum(
                            ClaimFinancials.indemnity_paid + ClaimFinancials.expense_paid + ClaimFinancials.defense_paid
                        )
                    )
                    .select_from(ClaimFinancials)
                    .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                    .where(
                        *claim_filters,
                        ClaimFinancials.last_payment_date >= period_start,
                        ClaimFinancials.last_payment_date <= period_end,
                    )
                )

                payments = self.db.scalar(payments_stmt) or Decimal("0.00")

                # Reserves at end of period
                # Find the most recent reserve history for each claim as of period_end
                latest_period_reserves_subquery = (
                    select(ReserveHistory.claim_id, func.max(ReserveHistory.changed_at).label("max_changed_at"))
                    .select_from(ReserveHistory)
                    .where(ReserveHistory.changed_at <= period_end)
                    .group_by(ReserveHistory.claim_id)
                    .subquery()
                )

                # Join with the subquery to get the actual reserve amounts
                reserves_stmt = (
                    select(func.sum(ReserveHistory.new_amount))
                    .select_from(ReserveHistory)
                    .join(
                        latest_period_reserves_subquery,
                        and_(
                            ReserveHistory.claim_id == latest_period_reserves_subquery.c.claim_id,
                            ReserveHistory.changed_at == latest_period_reserves_subquery.c.max_changed_at,
                        ),
                    )
                    .join(BaseClaim, ReserveHistory.claim_id == BaseClaim.id)
                    .where(*claim_filters)
                )

                reserves = self.db.scalar(reserves_stmt) or Decimal("0.00")

                # Add to results
                result_items.append(
                    PaymentReserveTimeItem(period=period_start.date(), payments=payments, reserves=reserves)
                )

        # Create column headers
        column_headers = [
            {"key": "period", "label": "Period"},
            {"key": "payments", "label": "Payments"},
            {"key": "reserves", "label": "Reserves"},
        ]

        # Create report metadata
        metadata = self._create_report_metadata(
            report_name="payments_vs_reserves", request=request, column_headers=column_headers
        )

        # Return report
        return ReportResponsePaymentsVsReserves(report_metadata=metadata, data=result_items)

    def get_reserve_trend_over_time(self, request: ReportRequest) -> List[Dict[str, Union[str, Decimal]]]:
        """Generate Reserve Trend Over Time report.

        Args:
            request: Report request parameters

        Returns:
            List of dictionaries containing reserve trend data
        """
        # Check permission
        self.check_permission("VIEW_REPORTS", "reports")

        # Calculate date range
        start_date, end_date = self._calculate_date_range(request)

        # Base claim filters
        claim_filters = [BaseClaim.is_deleted == False]
        if request.customer_id:
            claim_filters.append(BaseClaim.customer_id == UUID(request.customer_id))
        if request.claim_type:
            claim_filters.append(BaseClaim.claim_type == request.claim_type)
        if request.user_id:
            claim_filters.append(BaseClaim.assigned_to_id == UUID(request.user_id))

        # Get reserve trend over time
        reserve_trend_data = []

        # Determine time interval for trend (monthly for now)
