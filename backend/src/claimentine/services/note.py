"""Service layer for managing notes."""

import logging
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session, selectinload

from claimentine.core.audit import AuditHelper
from claimentine.core.exceptions import AuthorizationError, NotFoundError
from claimentine.models.note import Note
from claimentine.models.user import User
from claimentine.schemas.note import NoteCreate, NoteUpdate
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class NoteService(BaseService):
    """Service for managing notes associated with claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Use BaseClaimService to check claim access permissions
        self.claim_service = BaseClaimService(db, current_user)

    def create_note(self, note_data: NoteCreate) -> Note:
        """Create a new note for a specific claim."""
        logger.debug(f"[NoteService] create_note called for claim_id: {note_data.claim_id}")

        # Ensure the claim exists and user can view it (uses VIEW_ALL_CLAIMS, VIEW_ASSIGNED_CLAIMS, VIEW_OWN_CLAIMS)
        claim = self.claim_service.get_claim_by_id(note_data.claim_id)
        logger.debug(
            f"[NoteService] Result of claim check in create_note for claim {note_data.claim_id}: {'Found' if claim else 'Not Found'}"
        )
        if not claim:
            logger.warning(
                f"[NoteService] Raising NotFoundError because claim {note_data.claim_id} was not found in create_note."
            )
            raise NotFoundError(f"Claim {note_data.claim_id} not found.")

        # The get_claim_by_id call above already checked view permissions
        # If we can view the claim, we can create notes for it

        if not self.current_user:
            # This should ideally not happen if claim check passed, but as a safeguard
            raise AuthorizationError("User context required to create a note.")

        # Create note instance
        note = Note(
            claim_id=note_data.claim_id,
            content=note_data.content,
            created_by=self.current_user.id,
        )

        try:
            self.db.add(note)
            self.db.flush()  # Flush to get the generated ID but keep transaction open

            # Create audit helper
            audit_helper = AuditHelper(claim, self.current_user.id)

            # Prepare data for audit log
            note_data_for_audit = {
                "id": str(note.id),
                "content": (
                    note.content[:100] + "..." if len(note.content) > 100 else note.content
                ),  # Truncate long content
                "created_by": str(note.created_by) if note.created_by else None,
            }

            # Create audit log entry
            audit_helper.log_create(
                entity_type="NOTE",
                entity_id=note.id,
                data=note_data_for_audit,
                description=f"Added note: {note_data_for_audit['content']}",
            )

            # Complete the transaction
            self.db.commit()
            self.db.refresh(note)

            logger.info(
                f"Note created: {note.id} for claim {note.claim_id} by user {self.current_user.id}",
                extra={
                    "note_id": str(note.id),
                    "claim_id": str(note.claim_id),
                    "user_id": str(self.current_user.id),
                },
            )

            # Add claim number to the note object
            note.claim_number = claim.claim_number

            return note
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Error creating note for claim {note_data.claim_id}: {e}",
                exc_info=True,
                extra={"claim_id": str(note_data.claim_id), "user_id": str(self.current_user.id)},
            )
            raise

    def get_note_by_id(self, note_id: UUID, load_author: bool = True) -> Optional[Note]:
        """Get a single note by its ID, checking claim access permissions."""
        query = select(Note).where(Note.id == note_id)
        if load_author:
            query = query.options(selectinload(Note.author))

        # Also load the claim to get the claim number
        query = query.options(selectinload(Note.claim))

        note = self.db.scalar(query)

        if not note:
            return None

        # Get the claim and check view permissions
        claim = self.claim_service.get_claim_by_id(note.claim_id)
        if not claim:
            # Claim not found or no permission to view it
            raise NotFoundError(f"Claim {note.claim_id} not found")

        # The get_claim_by_id call above already checked view permissions
        # Add claim number to the note object
        note.claim_number = claim.claim_number

        return note

    def list_notes_for_claim(self, claim_id: UUID, skip: int = 0, limit: int = 10) -> List[Note]:
        """List all notes for a specific claim, checking claim access permissions."""
        # Get the claim and check view permissions
        claim = self.claim_service.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # The get_claim_by_id call above already checked view permissions
        # If we can view the claim, we can view its notes

        # Build query for notes
        query = (
            select(Note)
            .where(Note.claim_id == claim_id)
            .options(selectinload(Note.author))
            .offset(skip)
            .limit(limit)
            .order_by(Note.created_at.desc())
        )

        notes = list(self.db.scalars(query))

        # Add claim number to each note object
        for note in notes:
            note.claim_number = claim.claim_number

        return notes

    def update_note(self, note_id: UUID, note_data: NoteUpdate) -> Note:
        """Update the content of an existing note."""
        note = self.get_note_by_id(note_id, load_author=False)  # This checks view permissions
        if not note:
            raise NotFoundError(f"Note {note_id} not found")

        if not self.current_user:
            raise AuthorizationError("User context required to update a note.")

        # Get the claim and check edit permissions
        claim = self.claim_service.get_claim_by_id(note.claim_id)
        if not claim:
            raise NotFoundError(f"Claim {note.claim_id} not found")

        # Check if user can edit the claim (uses EDIT_CLAIMS, EDIT_ASSIGNED_CLAIMS)
        self.claim_service._check_edit_claim_permission(claim)

        # Additional check: only the author can update their own note
        # (unless we implement supervisor override in the future)
        if note.created_by != self.current_user.id:
            logger.warning(
                f"User {self.current_user.id} attempted to update note {note_id} created by {note.created_by}",
                extra={
                    "note_id": str(note_id),
                    "user_id": str(self.current_user.id),
                    "author_id": str(note.created_by),
                },
            )
            raise AuthorizationError("Only the author can update this note.")

        # Save original content for audit log
        original_content = note.content

        # Update content
        note.content = note_data.content

        try:
            # Create audit helper
            audit_helper = AuditHelper(claim, self.current_user.id)

            # Prepare data for audit log
            previous_data = {
                "content": (
                    original_content[:100] + "..." if len(original_content) > 100 else original_content
                )  # Truncate long content
            }

            updated_data = {
                "content": (
                    note.content[:100] + "..." if len(note.content) > 100 else note.content
                )  # Truncate long content
            }

            # Create audit log entry
            audit_helper.log_update(
                entity_type="NOTE",
                entity_id=note.id,
                previous_data=previous_data,
                new_data=updated_data,
                description=f"Updated note content",
            )

            self.db.commit()
            self.db.refresh(note)
            logger.info(
                f"Note updated: {note.id} by user {self.current_user.id}",
                extra={
                    "note_id": str(note.id),
                    "claim_id": str(note.claim_id),
                    "user_id": str(self.current_user.id),
                },
            )

            # Make sure claim_number is still set after refresh
            note.claim_number = claim.claim_number

            return note
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Error updating note {note_id}: {e}",
                exc_info=True,
                extra={
                    "note_id": str(note_id),
                    "user_id": str(self.current_user.id),
                },
            )
            raise

    def delete_note(self, note_id: UUID) -> None:
        """Delete an existing note."""
        note = self.get_note_by_id(note_id, load_author=False)  # This checks view permissions
        if not note:
            raise NotFoundError(f"Note {note_id} not found")

        if not self.current_user:
            raise AuthorizationError("User context required to delete a note.")

        # Get the claim and check edit permissions
        claim = self.claim_service.get_claim_by_id(note.claim_id)
        if not claim:
            raise NotFoundError(f"Claim {note.claim_id} not found")

        # Check if user can edit the claim (uses EDIT_CLAIMS, EDIT_ASSIGNED_CLAIMS)
        self.claim_service._check_edit_claim_permission(claim)

        # Additional check: only the author can delete their own note
        # (unless we implement supervisor override in the future)
        if note.created_by != self.current_user.id:
            logger.warning(
                f"User {self.current_user.id} attempted to delete note {note_id} created by {note.created_by}",
                extra={
                    "note_id": str(note_id),
                    "user_id": str(self.current_user.id),
                    "author_id": str(note.created_by),
                },
            )
            raise AuthorizationError("Only the author can delete this note.")

        # Store note data for audit before deletion
        note_data_for_audit = {
            "id": str(note.id),
            "content": (
                note.content[:100] + "..." if len(note.content) > 100 else note.content
            ),  # Truncate long content
            "created_by": str(note.created_by) if note.created_by else None,
        }

        try:
            # Create audit helper
            audit_helper = AuditHelper(claim, self.current_user.id)

            # Delete note
            self.db.delete(note)

            # Create audit log entry
            audit_helper.log_delete(
                entity_type="NOTE",
                entity_id=note.id,
                data=note_data_for_audit,
                description=f"Deleted note: {note_data_for_audit['content']}",
            )

            self.db.commit()
            logger.info(
                f"Note deleted: {note_id} by user {self.current_user.id}",
                extra={
                    "note_id": str(note_id),
                    "claim_id": str(note.claim_id),
                    "user_id": str(self.current_user.id),
                },
            )
        except Exception as e:
            self.db.rollback()
            logger.error(
                f"Error deleting note {note_id}: {e}",
                exc_info=True,
                extra={
                    "note_id": str(note_id),
                    "user_id": str(self.current_user.id),
                },
            )
            raise
