"""Service layer for managing witnesses."""

import logging
from typing import List, Optional, Union
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from claimentine.core.audit import AuditHelper
from claimentine.core.exceptions import <PERSON><PERSON>rror, NotFoundError, ValidationError
from claimentine.models.audit import ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim
from claimentine.models.user import User
from claimentine.models.witness import Witness
from claimentine.schemas.witness import WitnessCreate, WitnessUpdate
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class WitnessService(BaseService):
    """Service for managing witnesses associated with claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Use BaseClaimService to check claim access permissions
        self.claim_service = BaseClaimService(db, current_user)

    def _get_claim(self, claim_id: Union[UUID, str], required_permission: str = "VIEW_ALL_CLAIMS") -> BaseClaim:
        """Get claim with permission check."""
        # Try to resolve claim_id first (handle string UUID or claim number)
        if isinstance(claim_id, str):
            try:
                claim_id = UUID(claim_id)
                claim = self.claim_service.get_claim_by_id(claim_id)
            except ValueError:
                claim = self.claim_service.get_claim_by_number(claim_id)
        else:
            claim = self.claim_service.get_claim_by_id(claim_id)

        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission
        self.check_permission(required_permission, resource_type="claim", resource_id=claim.id)

        return claim

    def get_witnesses(self, claim_id: Union[UUID, str]) -> List[Witness]:
        """Get all witnesses for a claim."""
        claim = self._get_claim(claim_id)

        stmt = (
            select(Witness)
            .where(Witness.claim_id == claim.id, Witness.is_deleted == False)
            .order_by(Witness.created_at)
        )

        witnesses = list(self.db.scalars(stmt))

        # Add claim number to each witness object
        for witness in witnesses:
            witness.claim_number = claim.claim_number

        return witnesses

    def get_witness(self, witness_id: UUID) -> Witness:
        """Get a specific witness by ID."""
        witness = self.db.get(Witness, witness_id)

        if not witness or witness.is_deleted:
            raise NotFoundError(f"Witness {witness_id} not found")

        # Check permissions on the claim this witness belongs to
        claim = self._get_claim(witness.claim_id)

        # Add claim number to witness object
        witness.claim_number = claim.claim_number

        return witness

    def create_witness(self, claim_id: Union[UUID, str], data: WitnessCreate) -> Witness:
        """Create a new witness for a claim."""
        claim = self._get_claim(claim_id, required_permission="EDIT_CLAIMS")

        try:
            witness = Witness(
                claim_id=claim.id,
                name=data.name,
                email=data.email,
                phone=data.phone,
                address=data.address,
                statement=data.statement,
            )

            self.db.add(witness)
            self.db.flush()

            # Create audit trail entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type=EntityType.WITNESS,
                change_type=ChangeType.CREATE,
                entity_id=witness.id,
                description=f"Added witness: {witness.name}",
            )

            self.db.commit()
            self.db.refresh(witness)

            # Add claim number to witness object
            witness.claim_number = claim.claim_number

            return witness

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error creating witness: {str(e)}")
            raise DatabaseError(f"Error creating witness: {str(e)}")

    def update_witness(self, witness_id: UUID, data: WitnessUpdate) -> Witness:
        """Update an existing witness."""
        witness = self.get_witness(witness_id)

        # Check edit permission on the claim
        claim = self._get_claim(witness.claim_id, required_permission="EDIT_CLAIMS")

        try:
            # Track the previous values for audit
            previous_values = {
                "name": witness.name,
                "email": witness.email,
                "phone": witness.phone,
                "address": witness.address,
                "statement": witness.statement,
            }

            # Update fields from data model
            update_data = data.model_dump(exclude_unset=True)

            # Track which fields actually changed
            changed_fields = {}

            for key, value in update_data.items():
                if value != getattr(witness, key):
                    setattr(witness, key, value)
                    changed_fields[key] = value

            self.db.flush()

            # Create audit trail entry if fields actually changed
            if changed_fields:
                audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
                audit_helper.create_entry(
                    entity_type=EntityType.WITNESS,
                    change_type=ChangeType.UPDATE,
                    entity_id=witness.id,
                    previous_value=previous_values,
                    new_value=update_data,
                    description=f"Updated witness: {witness.name}",
                )

            self.db.commit()
            self.db.refresh(witness)

            # Add claim number to witness object
            witness.claim_number = claim.claim_number

            return witness

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error updating witness: {str(e)}")
            raise DatabaseError(f"Error updating witness: {str(e)}")

    def delete_witness(self, witness_id: UUID) -> None:
        """Soft delete a witness."""
        witness = self.get_witness(witness_id)

        # Check edit permission on the claim
        claim = self._get_claim(witness.claim_id, required_permission="EDIT_CLAIMS")

        try:
            # Soft delete
            witness.is_deleted = True

            # Create audit trail entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type=EntityType.WITNESS,
                change_type=ChangeType.DELETE,
                entity_id=witness.id,
                description=f"Deleted witness: {witness.name}",
            )

            self.db.commit()

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error deleting witness: {str(e)}")
            raise DatabaseError(f"Error deleting witness: {str(e)}")
