"""Service for generating reports."""

import logging
from typing import Any, Dict, Optional

from sqlalchemy.orm import Session

from claimentine.core.exceptions import NotFoundError
from claimentine.models.user import User
from claimentine.schemas.reports.base import ReportRequest
from claimentine.services.reports.claims import ClaimReportService
from claimentine.services.reports.financial import FinancialReportService
from claimentine.services.reports.performance import PerformanceReportService

logger = logging.getLogger(__name__)


class ReportService:
    """Service for generating reports, delegating to specific report services."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        self.db = db
        self.current_user = current_user
        self.claim_report_service = ClaimReportService(db, current_user)
        self.financial_report_service = FinancialReportService(db, current_user)
        self.performance_report_service = PerformanceReportService(db, current_user)

    def get_report(self, report_name: str, request: ReportRequest) -> Dict[str, Any]:
        """Get a report by name.

        Args:
            report_name: Name of the report to generate
            request: Report request parameters

        Returns:
            Report response object

        Raises:
            NotFoundError: If report_name is invalid
        """
        # Map report names to their corresponding service methods
        report_map = {
            # Claims reports
            "claims_kpis": self.claim_report_service.get_claims_kpi_report,
            "claims_by_type": self.claim_report_service.get_claims_by_type_report,
            "claims_by_status": self.claim_report_service.get_claims_by_status_report,
            "claims_over_time": self.claim_report_service.get_claims_over_time_report,
            # Financial reports
            "financial_kpis": self.financial_report_service.get_financial_kpi_report,
            "payments_vs_reserves": self.financial_report_service.get_payments_vs_reserves_report,
            # Performance reports
            "adjuster_performance": self.performance_report_service.get_adjuster_performance_report,
        }

        # Get the appropriate report function
        report_func = report_map.get(report_name)
        if not report_func:
            raise NotFoundError(f"Report type '{report_name}' not found")

        # Generate and return the report
        return report_func(request)
