"""Service layer for managing counters."""

import logging

from sqlalchemy.future import select
from sqlalchemy.orm import Session

from claimentine.models.counter import ClientTask<PERSON>ounter
from claimentine.models.client import Client

logger = logging.getLogger(__name__)


def get_next_task_number(db: Session, client: Client) -> int:
    """Gets the next task sequence number for a client atomically.

    Assumes the provided client object is valid and exists in the session.
    Uses SELECT FOR UPDATE to lock the row, preventing race conditions.
    Creates the counter row if it doesn't exist.

    Args:
        db: The Session instance.
        client: The Client object.

    Returns:
        The next task number for the client.

    Raises:
        Exception: For database errors during the transaction.
    """

    # Client existence check is removed - assuming client object is valid

    client_id = client.id  # Get ID from the object

    with db.begin_nested():  # Use nested transaction for FOR UPDATE
        # Lock the counter row for the client
        stmt = select(ClientTaskCounter).where(ClientTaskCounter.client_id == client_id).with_for_update()
        result = db.execute(stmt)
        counter = result.scalar_one_or_none()

        if counter:
            # Increment the existing counter
            counter.last_task_number += 1
            next_number = counter.last_task_number
            logger.debug(f"Incremented task counter for client {client_id} to {next_number}")
        else:
            # Create a new counter for this client
            next_number = 1
            counter = ClientTaskCounter(client_id=client_id, last_task_number=next_number)
            db.add(counter)
            logger.info(f"Created new task counter for client {client_id}, starting at {next_number}")

        # The nested transaction commits here, releasing the lock.

    return next_number
