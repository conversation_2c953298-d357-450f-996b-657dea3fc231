"""Service layer for managing attorneys."""

import logging
from typing import List, Optional, Union
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from claimentine.core.audit import AuditHelper
from claimentine.core.exceptions import <PERSON><PERSON>rror, NotFoundError, ValidationError
from claimentine.models.attorney import Attorney
from claimentine.models.audit import ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim
from claimentine.models.user import User
from claimentine.schemas.attorney import AttorneyCreate, AttorneyUpdate
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class AttorneyService(BaseService):
    """Service for managing attorneys associated with claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Use BaseClaimService to check claim access permissions
        self.claim_service = BaseClaimService(db, current_user)

    def _get_claim(self, claim_id: Union[UUID, str], required_permission: str = "VIEW_ALL_CLAIMS") -> BaseClaim:
        """Get claim with permission check."""
        # Try to resolve claim_id first (handle string UUID or claim number)
        if isinstance(claim_id, str):
            try:
                claim_id = UUID(claim_id)
                claim = self.claim_service.get_claim_by_id(claim_id)
            except ValueError:
                claim = self.claim_service.get_claim_by_number(claim_id)
        else:
            claim = self.claim_service.get_claim_by_id(claim_id)

        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission
        self.check_permission(required_permission, resource_type="claim", resource_id=claim.id)

        return claim

    def get_attorneys(self, claim_id: Union[UUID, str]) -> List[Attorney]:
        """Get all attorneys for a claim."""
        claim = self._get_claim(claim_id)

        stmt = (
            select(Attorney)
            .where(Attorney.claim_id == claim.id, Attorney.is_deleted == False)
            .order_by(Attorney.created_at)
        )

        attorneys = list(self.db.scalars(stmt))

        # Add claim number to each attorney object
        for attorney in attorneys:
            attorney.claim_number = claim.claim_number

        return attorneys

    def get_attorney(self, attorney_id: UUID) -> Attorney:
        """Get a specific attorney by ID."""
        attorney = self.db.get(Attorney, attorney_id)

        if not attorney or attorney.is_deleted:
            raise NotFoundError(f"Attorney {attorney_id} not found")

        # Check permissions on the claim this attorney belongs to
        claim = self._get_claim(attorney.claim_id)

        # Add claim number to attorney object
        attorney.claim_number = claim.claim_number

        return attorney

    def create_attorney(self, claim_id: Union[UUID, str], data: AttorneyCreate) -> Attorney:
        """Create a new attorney for a claim."""
        claim = self._get_claim(claim_id, required_permission="EDIT_CLAIMS")

        try:
            attorney = Attorney(
                claim_id=claim.id,
                name=data.name,
                attorney_type=data.attorney_type,
                firm_name=data.firm_name,
                email=data.email,
                phone=data.phone,
                address=data.address,
                notes=data.notes,
            )

            self.db.add(attorney)
            self.db.flush()

            # Create audit trail entry using AuditHelper
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type=EntityType.ATTORNEY,
                change_type=ChangeType.CREATE,
                entity_id=attorney.id,
                description=f"Added {attorney.attorney_type} attorney: {attorney.name}",
            )

            self.db.commit()
            self.db.refresh(attorney)

            # Add claim number to attorney object
            attorney.claim_number = claim.claim_number

            return attorney

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error creating attorney: {str(e)}")
            raise DatabaseError(f"Error creating attorney: {str(e)}")

    def update_attorney(self, attorney_id: UUID, data: AttorneyUpdate) -> Attorney:
        """Update an existing attorney."""
        attorney = self.get_attorney(attorney_id)

        # Check edit permission on the claim
        claim = self._get_claim(attorney.claim_id, required_permission="EDIT_CLAIMS")

        try:
            # Track the previous values for audit
            previous_values = {
                "name": attorney.name,
                "attorney_type": attorney.attorney_type,
                "firm_name": attorney.firm_name,
                "email": attorney.email,
                "phone": attorney.phone,
                "address": attorney.address,
                "notes": attorney.notes,
            }

            # Update fields from data model
            update_data = data.model_dump(exclude_unset=True)

            # Track which fields actually changed
            changed_fields = {}

            for key, value in update_data.items():
                # Special handling for enum values that might be passed as strings
                if key == "attorney_type" and isinstance(value, str):
                    from claimentine.models.attorney import AttorneyType

                    value = AttorneyType(value)

                if value != getattr(attorney, key):
                    setattr(attorney, key, value)
                    changed_fields[key] = value

            self.db.flush()

            # Create audit trail entry if fields actually changed
            if changed_fields:
                audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
                audit_helper.create_entry(
                    entity_type=EntityType.ATTORNEY,
                    change_type=ChangeType.UPDATE,
                    entity_id=attorney.id,
                    previous_value=previous_values,
                    new_value=update_data,
                    description=f"Updated attorney: {attorney.name}",
                )

            self.db.commit()
            self.db.refresh(attorney)

            # Add claim number to attorney object
            attorney.claim_number = claim.claim_number

            return attorney

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error updating attorney: {str(e)}")
            raise DatabaseError(f"Error updating attorney: {str(e)}")

    def delete_attorney(self, attorney_id: UUID) -> None:
        """Soft delete an attorney."""
        attorney = self.get_attorney(attorney_id)

        # Check edit permission on the claim
        claim = self._get_claim(attorney.claim_id, required_permission="EDIT_CLAIMS")

        try:
            # Soft delete
            attorney.is_deleted = True

            # Create audit trail entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type=EntityType.ATTORNEY,
                change_type=ChangeType.DELETE,
                entity_id=attorney.id,
                description=f"Deleted attorney: {attorney.name}",
            )

            self.db.commit()

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error deleting attorney: {str(e)}")
            raise DatabaseError(f"Error deleting attorney: {str(e)}")
