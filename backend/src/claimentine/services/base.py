"""Base service class with common functionality."""

import logging
from typing import Dict, List, Optional, Set, Union
from uuid import UUID

from sqlalchemy.orm import Session

from claimentine.core.exceptions import AuthorizationError, create_error_detail
from claimentine.core.permissions import has_permission
from claimentine.models.user import User

logger = logging.getLogger(__name__)


class BaseService:
    """Base service with common functionality for all services."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize the base service.

        Args:
            db: Database session
            current_user: Current authenticated user (if any)
        """
        self.db = db
        self.current_user = current_user
        self._permission_cache: Dict[str, bool] = {}  # Permission cache for the current request

    def check_permission(
        self,
        permission: str,
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[UUID, str]] = None,
        raise_exception: bool = True,
    ) -> bool:
        """Check if current user has the required permission.

        Args:
            permission: The permission to check
            resource_type: Type of resource (e.g., 'claim', 'document')
            resource_id: ID of the resource being accessed
            raise_exception: Whether to raise an exception if permission denied

        Returns:
            True if permission granted, False otherwise (if raise_exception=False)

        Raises:
            AuthorizationError: If permission check fails and raise_exception=True
        """
        # Skip check if no user context (for internal operations)
        if not self.current_user:
            return True

        # Create cache key based on permission and resource
        resource_id_str = str(resource_id) if resource_id else "None"
        resource_type_str = resource_type or "None"
        cache_key = f"{permission}:{resource_type_str}:{resource_id_str}"

        # Check if we have a cached result
        if cache_key in self._permission_cache:
            has_perm = self._permission_cache[cache_key]
            logger.debug(f"Using cached permission result for {cache_key}: {has_perm}")
        else:
            # Perform the actual permission check and cache the result
            has_perm = has_permission(self.current_user, self.db, permission)
            self._permission_cache[cache_key] = has_perm
            logger.debug(f"Caching permission result for {cache_key}: {has_perm}")

        # Create structured log data for all permission checks (success or failure)
        log_data = {
            "user_id": str(self.current_user.id),
            "user_email": self.current_user.email,
            "permission": permission,
            "resource_type": resource_type,
            "resource_id": str(resource_id) if resource_id else None,
            "result": "granted" if has_perm else "denied",
            "cached": cache_key in self._permission_cache,
        }

        # Log at appropriate level based on result
        log_level = logging.INFO if has_perm else logging.WARNING
        logger.log(log_level, f"Permission check: {permission}", extra=log_data)

        if not has_perm and raise_exception:
            message = f"No permission to {permission}"
            if resource_type and resource_id:
                message += f" for {resource_type} {resource_id}"

            raise AuthorizationError(
                message=message,
                details=create_error_detail(
                    resource=resource_type or "permission",
                    identifier=str(resource_id or self.current_user.id),
                    reason="permission_denied",
                    context=log_data,
                ),
            )

        return has_perm

    def check_any_permission(
        self,
        permissions: List[str],
        resource_type: Optional[str] = None,
        resource_id: Optional[Union[UUID, str]] = None,
        raise_exception: bool = True,
    ) -> bool:
        """Check if current user has any of the specified permissions.

        Args:
            permissions: List of permissions to check (any one is sufficient)
            resource_type: Type of resource being accessed
            resource_id: ID of the resource being accessed
            raise_exception: Whether to raise an exception if permission denied

        Returns:
            True if any permission granted, False otherwise

        Raises:
            AuthorizationError: If no permission is granted and raise_exception=True
        """
        if not self.current_user:
            return True

        # Try each permission
        for permission in permissions:
            if self.check_permission(permission, resource_type, resource_id, raise_exception=False):
                return True

        # If we get here, no permission was granted
        if raise_exception:
            message = f"No permission to perform this action. Required one of: {', '.join(permissions)}"
            if resource_type and resource_id:
                message += f" for {resource_type} {resource_id}"

            log_data = {
                "user_id": str(self.current_user.id),
                "user_email": self.current_user.email,
                "permissions": permissions,
                "resource_type": resource_type,
                "resource_id": str(resource_id) if resource_id else None,
                "result": "denied",
            }

            logger.warning(f"Permission check failed (any of): {permissions}", extra=log_data)

            raise AuthorizationError(
                message=message,
                details=create_error_detail(
                    resource=resource_type or "permission",
                    identifier=str(resource_id or self.current_user.id),
                    reason="permission_denied",
                    context=log_data,
                ),
            )

        return False
