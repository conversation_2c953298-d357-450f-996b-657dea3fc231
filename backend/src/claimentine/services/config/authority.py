"""Service for managing client-specific authority thresholds."""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.authority import AuthorityRole
from claimentine.models.config.authority import ClientAuthorityThreshold
from claimentine.models.user import User
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class ClientAuthorityThresholdService(BaseService):
    """Service for managing client-specific authority thresholds."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def list_thresholds(
        self,
        client_id: Optional[UUID] = None,
        authority_role: Optional[AuthorityRole] = None,
    ) -> List[ClientAuthorityThreshold]:
        """List client authority thresholds with optional filtering."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        stmt = select(ClientAuthorityThreshold)

        # Apply filters
        if client_id:
            stmt = stmt.where(ClientAuthorityThreshold.client_id == client_id)
        if authority_role:
            stmt = stmt.where(ClientAuthorityThreshold.authority_role == authority_role)

        # Filter out soft-deleted records
        stmt = stmt.where(ClientAuthorityThreshold.is_deleted == False)

        return list(self.db.scalars(stmt))

    def get_threshold(self, threshold_id: UUID) -> Optional[ClientAuthorityThreshold]:
        """Get a specific client authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        threshold = self.db.get(ClientAuthorityThreshold, threshold_id)
        if not threshold:
            raise NotFoundError(f"Client authority threshold {threshold_id} not found")
        # Ensure soft-deleted are not returned
        if threshold.is_deleted:
            raise NotFoundError(f"Client authority threshold {threshold_id} not found")

        return threshold

    def get_threshold_by_client_and_role(
        self, client_id: UUID, authority_role: AuthorityRole
    ) -> Optional[ClientAuthorityThreshold]:
        """Get threshold for specific client and authority role combination."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        return self.db.scalar(
            select(ClientAuthorityThreshold).where(
                ClientAuthorityThreshold.client_id == client_id,
                ClientAuthorityThreshold.authority_role == authority_role,
                ClientAuthorityThreshold.is_deleted == False,
            )
        )

    def create_threshold(
        self,
        client_id: UUID,
        authority_role: AuthorityRole,
        reserve_limit: float,
        payment_limit: float,
        description: str = "",
    ) -> ClientAuthorityThreshold:
        """Create a new client-specific authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        # Check if threshold already exists, including soft-deleted ones
        existing_stmt = select(ClientAuthorityThreshold).where(
            ClientAuthorityThreshold.client_id == client_id,
            ClientAuthorityThreshold.authority_role == authority_role,
        )
        existing = self.db.scalar(existing_stmt)

        if existing:
            if existing.is_deleted:
                # Undelete and update the soft-deleted record
                logger.info(
                    f"Found soft-deleted threshold for client {client_id}, role {authority_role}. Undeleting and updating.",
                    extra={"threshold_id": str(existing.id)},
                )
                existing.is_deleted = False
                existing.deleted_at = None
                existing.reserve_limit = reserve_limit
                existing.payment_limit = payment_limit
                existing.description = description
                threshold = existing  # Use the existing object
            else:
                # Active threshold already exists, raise error
                raise DuplicateError(
                    f"Authority threshold already exists for client {client_id}, role {authority_role}"
                )
        else:
            # Create new threshold
            threshold = ClientAuthorityThreshold(
                client_id=client_id,
                authority_role=authority_role,
                reserve_limit=reserve_limit,
                payment_limit=payment_limit,
                description=description,
            )
            self.db.add(threshold)

        self.db.commit()
        self.db.refresh(threshold)

        logger.debug(
            f"Created client authority threshold for client {client_id}, role {authority_role}",
            extra={
                "threshold_id": str(threshold.id),
                "client_id": str(client_id),
                "authority_role": str(authority_role),
                "reserve_limit": str(threshold.reserve_limit),
                "payment_limit": str(threshold.payment_limit),
            },
        )

        return threshold

    def update_threshold(
        self,
        threshold_id: UUID,
        reserve_limit: Optional[float] = None,
        payment_limit: Optional[float] = None,
        description: Optional[str] = None,
    ) -> ClientAuthorityThreshold:
        """Update an existing client authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        threshold = self.get_threshold(threshold_id)
        if not threshold:
            raise NotFoundError(f"Client authority threshold {threshold_id} not found")

        # Update fields if provided
        if reserve_limit is not None:
            threshold.reserve_limit = reserve_limit
        if payment_limit is not None:
            threshold.payment_limit = payment_limit
        if description is not None:
            threshold.description = description

        self.db.commit()
        self.db.refresh(threshold)

        logger.debug(
            f"Updated client authority threshold {threshold_id}",
            extra={
                "threshold_id": str(threshold.id),
                "client_id": str(threshold.client_id),
                "authority_role": str(threshold.authority_role),
                "reserve_limit": str(threshold.reserve_limit),
                "payment_limit": str(threshold.payment_limit),
            },
        )

        return threshold

    def delete_threshold(self, threshold_id: UUID) -> None:
        """Delete a client authority threshold."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="client_authority_threshold")

        threshold = self.get_threshold(threshold_id)
        if not threshold:
            raise NotFoundError(f"Client authority threshold {threshold_id} not found")

        # Soft delete: Set flags instead of deleting
        threshold.is_deleted = True
        threshold.deleted_at = datetime.utcnow()
        self.db.commit()

        logger.debug(
            f"Soft deleted client authority threshold {threshold_id}",
            extra={
                "threshold_id": str(threshold.id),
                "client_id": str(threshold.client_id),
                "authority_role": str(threshold.authority_role),
            },
        )
