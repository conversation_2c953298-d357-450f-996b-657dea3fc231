"""Service for managing reserve configurations."""

import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType
from claimentine.models.config.reserve import ReserveConfiguration
from claimentine.models.user import User
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class ReserveConfigurationService(BaseService):
    """Service for managing reserve configurations."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def list_configurations(
        self,
        claim_type: Optional[ClaimType] = None,
        reserve_type: Optional[ReserveType] = None,
        is_required: Optional[bool] = None,
    ) -> List[ReserveConfiguration]:
        """List reserve configurations with optional filtering."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        stmt = select(ReserveConfiguration)

        # Apply filters
        if claim_type:
            stmt = stmt.where(ReserveConfiguration.claim_type == claim_type)
        if reserve_type:
            stmt = stmt.where(ReserveConfiguration.reserve_type == reserve_type)
        if is_required is not None:
            stmt = stmt.where(ReserveConfiguration.is_required == is_required)

        # Filter out soft-deleted records
        stmt = stmt.where(ReserveConfiguration.is_deleted == False)

        return list(self.db.scalars(stmt))

    def get_configuration(self, config_id: UUID) -> Optional[ReserveConfiguration]:
        """Get a specific reserve configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        config = self.db.get(ReserveConfiguration, config_id)
        if not config:
            raise NotFoundError(f"Reserve configuration {config_id} not found")
        # Ensure soft-deleted are not returned by get_configuration either
        if config.is_deleted:
            raise NotFoundError(f"Reserve configuration {config_id} not found")

        return config

    def get_configuration_by_types(
        self, claim_type: ClaimType, reserve_type: ReserveType
    ) -> Optional[ReserveConfiguration]:
        """Get configuration for specific claim and reserve type combination."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        return self.db.scalar(
            select(ReserveConfiguration).where(
                ReserveConfiguration.claim_type == claim_type,
                ReserveConfiguration.reserve_type == reserve_type,
                ReserveConfiguration.is_deleted == False,
            )
        )

    def create_configuration(
        self,
        claim_type: ClaimType,
        reserve_type: ReserveType,
        is_required: bool,
        minimum_amount: Optional[float] = None,
        description: str = "",
    ) -> ReserveConfiguration:
        """Create a new reserve configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        # Check if configuration already exists, including soft-deleted ones
        existing_stmt = select(ReserveConfiguration).where(
            ReserveConfiguration.claim_type == claim_type,
            ReserveConfiguration.reserve_type == reserve_type,
        )
        existing = self.db.scalar(existing_stmt)

        if existing:
            if existing.is_deleted:
                # Undelete and update the soft-deleted record
                logger.info(
                    f"Found soft-deleted config for {claim_type}:{reserve_type}. Undeleting and updating.",
                    extra={"config_id": str(existing.id)},
                )
                existing.is_deleted = False
                existing.deleted_at = None
                existing.is_required = is_required
                existing.minimum_amount = minimum_amount
                existing.description = description
                config = existing  # Use the existing object
            else:
                # Active configuration already exists, raise error
                raise DuplicateError(f"Configuration already exists for {claim_type}:{reserve_type}")
        else:
            # Create new configuration
            config = ReserveConfiguration(
                claim_type=claim_type,
                reserve_type=reserve_type,
                is_required=is_required,
                minimum_amount=minimum_amount,
                description=description,
            )
            self.db.add(config)

        self.db.commit()
        self.db.refresh(config)

        logger.debug(
            f"Created reserve configuration for {claim_type}:{reserve_type}",
            extra={
                "config_id": str(config.id),
                "claim_type": str(claim_type),
                "reserve_type": str(reserve_type),
                "is_required": is_required,
                "minimum_amount": str(minimum_amount) if minimum_amount else None,
            },
        )

        return config

    def update_configuration(
        self,
        config_id: UUID,
        is_required: Optional[bool] = None,
        minimum_amount: Optional[float] = None,
        description: Optional[str] = None,
    ) -> ReserveConfiguration:
        """Update an existing reserve configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        config = self.get_configuration(config_id)
        if not config:
            raise NotFoundError(f"Reserve configuration {config_id} not found")

        # Update fields if provided
        if is_required is not None:
            config.is_required = is_required
        if minimum_amount is not None:
            config.minimum_amount = minimum_amount
        if description is not None:
            config.description = description

        self.db.commit()
        self.db.refresh(config)

        logger.debug(
            f"Updated reserve configuration {config_id}",
            extra={
                "config_id": str(config.id),
                "claim_type": str(config.claim_type),
                "reserve_type": str(config.reserve_type),
                "is_required": config.is_required,
                "minimum_amount": str(config.minimum_amount) if config.minimum_amount else None,
            },
        )

        return config

    def delete_configuration(self, config_id: UUID) -> None:
        """Delete a reserve configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="reserve_configuration")

        config = self.get_configuration(config_id)
        if not config:
            raise NotFoundError(f"Reserve configuration {config_id} not found")

        # Soft delete: Set flags instead of deleting
        config.is_deleted = True
        config.deleted_at = datetime.utcnow()
        self.db.commit()

        logger.debug(
            f"Soft deleted reserve configuration {config_id}",
            extra={
                "config_id": str(config.id),
                "claim_type": str(config.claim_type),
                "reserve_type": str(config.reserve_type),
            },
        )
