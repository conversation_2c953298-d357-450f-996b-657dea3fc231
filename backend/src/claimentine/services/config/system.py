"""Service for managing system configurations."""

import logging
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.config.system import SystemConfiguration
from claimentine.models.user import User
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class SystemConfigurationService(BaseService):
    """Service for managing system configurations."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_by_key(self, key: str) -> Optional[SystemConfiguration]:
        """Get a configuration entry by key."""
        return self.db.scalar(select(SystemConfiguration).where(SystemConfiguration.key == key))

    def list_configurations(self) -> List[SystemConfiguration]:
        """List all system configurations."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        stmt = select(SystemConfiguration)
        return list(self.db.scalars(stmt))

    def get_configuration(self, config_id: UUID) -> SystemConfiguration:
        """Get a specific system configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        config = self.db.get(SystemConfiguration, config_id)
        if not config:
            raise NotFoundError(f"System configuration {config_id} not found")

        return config

    def create_configuration(self, key: str, value: str, description: str) -> SystemConfiguration:
        """Create a new system configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        # Check if configuration already exists
        existing = self.get_by_key(key)
        if existing:
            raise DuplicateError(f"Configuration already exists for key {key}")

        # Create new configuration
        config = SystemConfiguration(
            key=key,
            value=value,
            description=description,
        )
        self.db.add(config)
        self.db.commit()
        self.db.refresh(config)

        return config

    def update_configuration(
        self, config_id: UUID, value: str, description: Optional[str] = None
    ) -> SystemConfiguration:
        """Update an existing system configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        config = self.get_configuration(config_id)

        # Update fields
        config.value = value
        if description is not None:
            config.description = description

        self.db.commit()
        self.db.refresh(config)

        return config

    def update_by_key(self, key: str, value: str, description: Optional[str] = None) -> SystemConfiguration:
        """Update or create a configuration by key.

        Note:
            This method can be called without a current_user for initial setup.
        """
        # Only check permissions if there's a current user
        if self.current_user is not None:
            self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        existing = self.get_by_key(key)

        if existing:
            # Update existing
            existing.value = value
            if description is not None:
                existing.description = description
            config = existing
        else:
            # Create new
            config = SystemConfiguration(
                key=key,
                value=value,
                description=description or f"Configuration for {key}",
            )
            self.db.add(config)

        self.db.commit()
        self.db.refresh(config)

        return config

    def delete_configuration(self, config_id: UUID) -> None:
        """Delete a system configuration."""
        self.check_permission("SYSTEM_CONFIGURATION", resource_type="system_configuration")

        config = self.get_configuration(config_id)
        self.db.delete(config)
        self.db.commit()
