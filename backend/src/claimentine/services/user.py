"""User management service."""

import logging
from contextlib import contextmanager
from datetime import datetime, timedelta
from typing import List, Optional, Set
from uuid import UUID

import bcrypt
from sqlalchemy import func, or_, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from claimentine.core.exceptions import AuthenticationError, DatabaseError, DuplicateError, NotFoundError
from claimentine.models.auth import ROLE_PERMISSIONS, Permission
from claimentine.models.authority import AuthorityRole
from claimentine.models.user import User, UserPermission, UserRole, UserStatus
from claimentine.schemas.user import UserCreate, UserUpdate
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class UserService(BaseService):
    """Service for user management operations."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize with database session and user context."""
        super().__init__(db, current_user)

    @contextmanager
    def _transaction(self):
        """Transaction context manager for atomic operations."""
        try:
            yield
            self.db.commit()
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error in user service: {str(e)}", exc_info=True)
            raise DatabaseError(f"Database operation failed: {str(e)}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Unexpected error in user service: {str(e)}", exc_info=True)
            raise

    # Helper method for specific view checks
    def _check_view_user_permission(self, user_to_view: User) -> None:
        """Check if the current user can view the target user's profile."""
        if not self.current_user:
            return  # Allow internal/unauthenticated access

        if self.current_user.id == user_to_view.id:
            # Check if user can view their own profile
            self.check_permission("VIEW_OWN_PROFILE", resource_type="user", resource_id=user_to_view.id)
        else:
            # Check if user can view other users' profiles
            self.check_permission("VIEW_USERS", resource_type="user", resource_id=user_to_view.id)

    def get_user_permissions(self, user: User) -> Set[str]:
        """Get all permissions for a user including role-based and explicit ones."""
        # Get role-based permissions
        from claimentine.core.permissions import get_role_permissions

        permissions = get_role_permissions(user.role)

        # Get explicit permissions from database
        stmt = select(Permission).join(Permission.users).where(User.id == user.id)
        explicit_permissions = self.db.scalars(stmt).all()
        permissions.update(p.name for p in explicit_permissions)

        return permissions

    @staticmethod
    def hash_password(password: str) -> str:
        """Hash a password using bcrypt."""
        return bcrypt.hashpw(password.encode(), bcrypt.gensalt()).decode()

    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """Verify a password against a hash."""
        return bcrypt.checkpw(password.encode(), password_hash.encode())

    def authenticate_user(self, email: str, password: str) -> User:
        """Authenticate a user with email and password."""
        user = self.get_user_by_email(email)
        if not user:
            raise AuthenticationError("Invalid email or password")

        # Check password
        if not self.verify_password(password, user.password_hash):
            # Increment failed login attempts
            user.failed_login_attempts += 1
            self.db.commit()

            # Lock account after 5 failed attempts
            if user.failed_login_attempts >= 5:
                user.status = UserStatus.SUSPENDED
                user.locked_until = datetime.utcnow() + timedelta(minutes=30)
                self.db.commit()
                raise AuthenticationError("Account locked due to too many failed attempts")

            raise AuthenticationError("Invalid email or password")

        # Reset failed attempts on successful login
        user.failed_login_attempts = 0
        user.last_login_at = datetime.utcnow()
        self.db.commit()

        return user

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get a user by email."""
        stmt = select(User).where(User.email == email, User.is_deleted == False)
        return self.db.scalar(stmt)

    def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """Get a user by ID."""
        stmt = select(User).where(User.id == user_id)
        user = self.db.scalar(stmt)
        if user:
            self._check_view_user_permission(user)
        # Ensure soft-deleted are not returned
        if user and user.is_deleted:
            return None
        return user

    def list_users(
        self,
        *,
        skip: int = 0,
        limit: int = 10,
        search: Optional[str] = None,
        role: Optional[UserRole] = None,
        authority_role: Optional[AuthorityRole] = None,
        status: Optional[UserStatus] = None,
        department: Optional[str] = None,
        email: Optional[str] = None,
    ) -> List[User]:
        """List users with optional filtering and search."""
        self.check_permission("VIEW_USERS", resource_type="user")
        query = select(User)

        # Add search functionality
        if search and search.strip():
            search_term = f"%{search.lower().strip()}%"
            query = query.where(
                or_(
                    func.lower(func.concat(User.first_name, " ", User.last_name)).like(search_term),
                    func.lower(User.email).like(search_term),
                    func.lower(User.first_name).like(search_term),
                    func.lower(User.last_name).like(search_term),
                    func.lower(User.department).like(search_term),
                )
            )

        if role:
            query = query.where(User.role == role)
        if authority_role:
            query = query.where(User.authority_role == authority_role)
        if status:
            query = query.where(User.status == status)
        if department:
            query = query.where(User.department == department)
        if email:
            query = query.where(User.email == email)

        # Filter out soft-deleted records
        query = query.where(User.is_deleted == False)

        query = query.offset(skip).limit(limit)
        return list(self.db.scalars(query))

    def create_user(self, user_data: UserCreate) -> User:
        """Create a new user."""
        self.check_permission("CREATE_USERS", resource_type="user")

        with self._transaction():
            # Check if email exists (and is not soft-deleted)
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user:
                raise DuplicateError("Email already registered")

            # Hash password
            password_hash = self.hash_password(user_data.password)

            # Create user instance
            user = User(
                email=user_data.email,
                password_hash=password_hash,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                role=user_data.role,
                authority_role=user_data.authority_role,
                status=user_data.status or UserStatus.ACTIVE,
                department=user_data.department,
                job_title=user_data.job_title,
                phone_number=user_data.phone_number,
                timezone=user_data.timezone or "UTC",
                preferences=user_data.preferences or {},
            )

            self.db.add(user)
            self.db.flush()

            # Assign role-based permissions
            role_permissions = ROLE_PERMISSIONS.get(user_data.role, [])
            if role_permissions:
                permissions_to_assign = self.db.scalars(
                    select(Permission).where(Permission.name.in_(role_permissions))
                ).all()
                for perm in permissions_to_assign:
                    user.user_permissions.append(UserPermission(permission_id=perm.id))

            self.db.refresh(user)

        return user

    def update_user(self, user_id: UUID, user_data: UserUpdate) -> User:
        """Update a user."""
        with self._transaction():
            user = self.db.get(User, user_id)
            if not user:
                raise NotFoundError(f"User {user_id} not found")

            # Check edit permissions
            if self.current_user and self.current_user.id == user_id:
                self.check_permission("EDIT_OWN_PROFILE", resource_type="user", resource_id=user_id)
            else:
                # Requires permission to edit any user
                self.check_permission("EDIT_USERS", resource_type="user", resource_id=user_id)

            # Update fields if provided
            update_data = user_data.model_dump(exclude_unset=True)

            # Hash new password if provided
            if "password" in update_data:
                update_data["password_hash"] = self.hash_password(update_data.pop("password"))
                update_data["last_password_change_at"] = datetime.utcnow()
                update_data["force_password_change"] = False

            for field, value in update_data.items():
                setattr(user, field, value)

            # Update the updated_at timestamp
            user.updated_at = datetime.utcnow()

            # Removing this refresh call that was causing updates to be lost
            # self.db.refresh(user)

        # Now the transaction has been committed, it's safe to refresh the user
        self.db.refresh(user)
        return user

    def change_user_password(self, user_id: UUID, current_password: str, new_password: str) -> User:
        """Change a user's password after verifying current password."""
        with self._transaction():
            user = self.db.get(User, user_id)
            if not user:
                raise NotFoundError(f"User {user_id} not found")

            # Only allow changing own password unless admin
            if self.current_user.id != user_id:
                # Check if current user has permission to edit other users
                self.check_permission("EDIT_USERS", resource_type="user", resource_id=user_id)
            else:
                # Check if user can edit own profile
                self.check_permission("EDIT_OWN_PROFILE", resource_type="user", resource_id=user_id)

            # Verify current password (skip for admins changing others' passwords)
            if self.current_user.id == user_id and not self.verify_password(current_password, user.password_hash):
                raise AuthenticationError("Current password is incorrect")

            # Update password
            user.password_hash = self.hash_password(new_password)
            user.last_password_change_at = datetime.utcnow()
            user.force_password_change = False
            user.updated_at = datetime.utcnow()

        # Refresh after transaction
        self.db.refresh(user)
        return user

    def delete_user(self, user_id: UUID) -> None:
        """Delete a user."""
        with self._transaction():
            # Check permission before fetching user
            self.check_permission("DELETE_USERS", resource_type="user", resource_id=user_id)

            user = self.db.get(User, user_id)
            if not user:
                raise NotFoundError(f"User {user_id} not found")

            # Check if already deleted
            if user.is_deleted:
                logger.info(f"User {user_id} is already marked as deleted.")
                return

            # Soft delete: Mark as deleted and deactivate account
            user.is_deleted = True
            user.deleted_at = datetime.utcnow()
            user.status = UserStatus.INACTIVE

            # Security: Clear sensitive authentication data only
            user.password_hash = ""

            # Preserve all other data (email, names, etc.) for historical integrity
            # This maintains audit trails, claim references, and business context

            # Note: We intentionally DO NOT modify:
            # - email (preserves historical references and avoids constraint issues)
            # - first_name/last_name (preserves audit trail and claim assignments)
            # - phone_number (may be needed for historical context)
            # - preferences (may contain important historical settings)
