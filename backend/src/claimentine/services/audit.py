"""Service layer for managing the audit trail."""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DatabaseError, NotFoundError
from claimentine.models.audit import AuditTrail, ChangeType, EntityType
from claimentine.models.claim.base import BaseClaim
from claimentine.models.user import User
from claimentine.schemas.audit import AuditSummaryResponse, AuditTrailCreate, AuditTrailFilter, AuditTrailResponse
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class AuditService(BaseService):
    """Service for managing audit trail."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Use BaseClaimService to check claim access permissions
        self.claim_service = BaseClaimService(db, current_user)

    def _get_claim(self, claim_id: Union[UUID, str], required_permission: str = "VIEW_CLAIM_AUDIT") -> BaseClaim:
        """Get claim with permission check."""
        # Try to resolve claim_id first (handle string UUID or claim number)
        if isinstance(claim_id, str):
            try:
                claim_id = UUID(claim_id)
                claim = self.claim_service.get_claim_by_id(claim_id)
            except ValueError:
                claim = self.claim_service.get_claim_by_number(claim_id)
        else:
            claim = self.claim_service.get_claim_by_id(claim_id)

        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission
        self.check_permission(required_permission, resource_type="claim", resource_id=claim.id)

        return claim

    def get_audit_entries(self, claim_id: Union[UUID, str], filters: Optional[AuditTrailFilter] = None) -> Dict:
        """Get audit trail entries for a claim with optional filtering."""
        claim = self._get_claim(claim_id)

        # Default values if filters not provided
        skip = filters.skip if filters and hasattr(filters, "skip") else 0
        limit = filters.limit if filters and hasattr(filters, "limit") else 50

        # Use the claim's get_audit_entries method
        result = claim.get_audit_entries(
            skip=skip,
            limit=limit,
            entity_type=filters.entity_type.value if filters and filters.entity_type else None,
            change_type=filters.change_type.value if filters and filters.change_type else None,
            from_date=filters.from_date if filters else None,
            to_date=filters.to_date if filters else None,
            changed_by_id=filters.changed_by_id if filters else None,
        )

        # Convert model instances to response schemas
        result["items"] = [AuditTrailResponse.model_validate(entry) for entry in result["items"]]

        return result

    def get_audit_entry(self, audit_id: UUID) -> AuditTrailResponse:
        """Get a specific audit entry by ID."""
        audit_entry = self.db.get(AuditTrail, audit_id)

        if not audit_entry:
            raise NotFoundError(f"Audit entry {audit_id} not found")

        # Check permissions on the claim this audit entry belongs to
        self._get_claim(audit_entry.claim_id)

        # Convert to response model
        return AuditTrailResponse.model_validate(audit_entry)

    def create_manual_audit_entry(self, claim_id: Union[UUID, str], data: AuditTrailCreate) -> AuditTrailResponse:
        """Create a manual audit entry.

        This would typically be used for administrative purposes or to document
        actions that occurred outside the system.
        """
        claim = self._get_claim(claim_id, required_permission="CREATE_AUDIT_ENTRIES")

        try:
            audit_entry = AuditTrail(
                claim_id=claim.id,
                entity_type=data.entity_type,
                entity_id=data.entity_id,
                change_type=data.change_type,
                field_name=data.field_name,
                previous_value=data.previous_value,
                new_value=data.new_value,
                description=data.description,
                changed_by_id=self.current_user.id if self.current_user else None,
            )

            self.db.add(audit_entry)
            self.db.commit()
            self.db.refresh(audit_entry)

            # Convert to response model
            return AuditTrailResponse.model_validate(audit_entry)

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error creating audit entry: {str(e)}")
            raise DatabaseError(f"Error creating audit entry: {str(e)}")

    def get_audit_summary(self, claim_id: Union[UUID, str], entries_limit: int = 5) -> AuditSummaryResponse:
        """Get a summary of audit activity for a claim.

        Args:
            claim_id: ID or claim number of the claim to summarize
            entries_limit: Maximum number of recent entries to include in the summary

        Returns:
            Dictionary with counts of changes by entity type and change type.
        """
        claim = self._get_claim(claim_id)

        # Apply a filter with just a limit to get recent activity
        filters = AuditTrailFilter(limit=entries_limit)

        # Get recent audit entries - now returns a dict with 'items' key
        entries_result = self.get_audit_entries(claim.id, filters)
        recent_entries = entries_result["items"]

        # Get total count with a separate query
        total_count_stmt = select(AuditTrail).where(AuditTrail.claim_id == claim.id)
        total_entries = self.db.scalar(select(func.count()).select_from(total_count_stmt.subquery()))

        # Build summary data
        summary = AuditSummaryResponse(
            total_entries=total_entries,
            by_entity={},
            by_change_type={
                ChangeType.CREATE.value: 0,
                ChangeType.UPDATE.value: 0,
                ChangeType.DELETE.value: 0,
            },
            by_user={},
            recent_activity=recent_entries,
        )

        # Count by entity type - use the entries we already have for this
        for entry in recent_entries:
            # Entity type counts
            entity_key = entry.entity_type.value
            if entity_key not in summary.by_entity:
                summary.by_entity[entity_key] = 0
            summary.by_entity[entity_key] += 1

            # Change type counts
            summary.by_change_type[entry.change_type.value] += 1

            # User counts
            if entry.changed_by_id:
                user_key = str(entry.changed_by_id)
                if user_key not in summary.by_user:
                    summary.by_user[user_key] = 0
                summary.by_user[user_key] += 1

        return summary
