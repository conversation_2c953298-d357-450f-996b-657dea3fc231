"""Client service."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import DuplicateError, NotFoundError
from claimentine.models.client import Client
from claimentine.models.user import User
from claimentine.schemas.client import ClientCreate, ClientUpdate
from claimentine.services.base import BaseService


class ClientService(BaseService):
    """Service for managing clients."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def get_client_by_id(self, client_id: UUID) -> Optional[Client]:
        """Get client by ID."""
        stmt = select(Client).where(Client.id == client_id, Client.is_deleted == False)
        return self.db.scalar(stmt)

    def get_client_by_prefix(self, prefix: str) -> Optional[Client]:
        """Get client by prefix."""
        stmt = select(Client).where(Client.prefix == prefix, Client.is_deleted == False)
        return self.db.scalar(stmt)

    def list_clients(self, active_only: bool = False) -> List[Client]:
        """List all clients."""
        # Check permission to view clients
        self.check_permission("VIEW_CLIENTS", "client")

        stmt = select(Client)
        if active_only:
            stmt = stmt.where(Client.active)

        # Filter out soft-deleted records
        stmt = stmt.where(Client.is_deleted == False)

        return list(self.db.scalars(stmt))

    def create_client(self, data: ClientCreate) -> Client:
        """Create a new client."""
        # Check permission to create clients
        self.check_permission("CREATE_CLIENTS", "client")

        # Check if prefix already exists (and is not soft-deleted)
        if self.get_client_by_prefix(data.prefix):
            raise DuplicateError(f"Client with prefix {data.prefix} already exists")

        client = Client(**data.model_dump())
        self.db.add(client)
        self.db.commit()
        self.db.refresh(client)
        return client

    def update_client(self, client_id: UUID, data: ClientUpdate) -> Client:
        """Update a client."""
        # Check permission to edit clients
        self.check_permission("EDIT_CLIENTS", "client", client_id)

        client = self.get_client_by_id(client_id)
        if not client:
            raise NotFoundError(f"Client {client_id} not found")

        # Update fields
        update_data = data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(client, key, value)

        self.db.commit()
        self.db.refresh(client)
        return client

    def delete_client(self, client_id: UUID) -> None:
        """Delete a client.

        Note: This will fail if there are any claims associated with the client
        due to the RESTRICT foreign key constraint.
        """
        # Check permission to delete clients
        self.check_permission("DELETE_CLIENTS", "client", client_id)

        client = self.get_client_by_id(client_id)
        if not client:
            raise NotFoundError(f"Client {client_id} not found")

        # Soft delete: Set flags
        client.is_deleted = True
        client.deleted_at = datetime.utcnow()
        client.active = False
        self.db.commit()
