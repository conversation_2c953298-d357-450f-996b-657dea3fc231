"""Service for document management."""

import logging
import uuid
from contextlib import contextmanager
from datetime import datetime
from typing import List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from claimentine.core.audit import <PERSON>tHelper
from claimentine.core.exceptions import (
    AuthorizationError,
    BadRequestError,
    DatabaseError,
    NotFoundError,
    StorageError,
    ValidationError,
    create_error_detail,
)
from claimentine.core.permissions import require_service_permission
from claimentine.core.storage import storage_client
from claimentine.models.document import Document, DocumentType
from claimentine.models.user import User
from claimentine.schemas.document import DocumentCreate, DocumentUpdate
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class DocumentService(BaseService):
    """Service for managing document operations."""

    def __init__(
        self, db: Session, current_user: Optional[User] = None, claim_service: Optional[BaseClaimService] = None
    ):
        """Initialize the document service with dependencies."""
        super().__init__(db, current_user)
        # Store injected service, or create default if not provided
        self.claim_service = claim_service or BaseClaimService(db, current_user)

    @contextmanager
    def _transaction(self):
        """Transaction context manager for atomic operations."""
        try:
            yield
            self.db.commit()
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error in document service: {str(e)}", exc_info=True)
            raise DatabaseError(f"Database operation failed: {str(e)}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Unexpected error in document service: {str(e)}", exc_info=True)
            raise

    def _resolve_claim_id(self, claim_id: Union[UUID, str]) -> UUID:
        """Resolve claim ID from either UUID or claim number.

        Args:
            claim_id: Claim ID or claim number

        Returns:
            Resolved UUID

        Raises:
            NotFoundError: If claim not found
        """
        logger.debug(f"Resolving claim identifier: {claim_id}")

        # Use injected claim service
        # claim_service = BaseClaimService(self.db, self.current_user)

        # If already a UUID, use directly
        if isinstance(claim_id, UUID):
            claim = self.claim_service.get_claim_by_id(claim_id)
            if not claim:
                raise NotFoundError(
                    message=f"Claim with ID {claim_id} not found",
                    details=create_error_detail(resource="claim", identifier=str(claim_id), reason="not_found"),
                )
            return claim.id

        # If string that's a valid UUID, convert and use
        try:
            uuid_obj = UUID(claim_id)
            claim = self.claim_service.get_claim_by_id(uuid_obj)
            if claim:
                return claim.id
        except ValueError:
            # Not a valid UUID string, continue to check claim number
            pass

        # Try as claim number
        claim = self.claim_service.get_claim_by_number(claim_id)
        if not claim:
            raise NotFoundError(
                message=f"Claim with identifier {claim_id} not found",
                details=create_error_detail(resource="claim", identifier=str(claim_id), reason="not_found"),
            )

        return claim.id

    @require_service_permission("VIEW_CLAIM_DOCUMENTS", resource_id_param="document_id", resource_type_param=None)
    def get_document_by_id(self, document_id: UUID) -> Document:
        """Get a document by ID.

        Args:
            document_id: Document ID

        Returns:
            Document

        Raises:
            NotFoundError: If document not found
            AuthorizationError: If user lacks permission to view the document
        """
        # Query document with claim relationship
        stmt = select(Document).where(Document.id == document_id).options(selectinload(Document.claim))
        document = self.db.scalar(stmt)

        if not document:
            raise NotFoundError(
                message=f"Document with ID {document_id} not found",
                details=create_error_detail(resource="document", identifier=str(document_id), reason="not_found"),
            )

        # Add claim number to document object
        document.claim_number = document.claim.claim_number

        return document

    @require_service_permission("VIEW_CLAIM_DOCUMENTS", resource_id_param="claim_id")
    def get_document_by_claim(self, claim_id: Union[UUID, str], document_id: UUID) -> Document:
        """Get a document by ID that belongs to a specific claim.

        Args:
            claim_id: Claim ID or claim number
            document_id: Document ID

        Returns:
            Document

        Raises:
            NotFoundError: If claim or document not found
            AuthorizationError: If user lacks permission to view the document
        """
        # Resolve claim ID
        resolved_claim_id = self._resolve_claim_id(claim_id)

        # Query document
        stmt = (
            select(Document)
            .where(Document.id == document_id, Document.claim_id == resolved_claim_id)
            .options(selectinload(Document.claim))
        )
        document = self.db.scalar(stmt)

        if not document:
            raise NotFoundError(
                message=f"Document with ID {document_id} not found for claim {claim_id}",
                details=create_error_detail(
                    resource="document",
                    identifier=str(document_id),
                    reason="not_found",
                    context={"claim_id": str(resolved_claim_id)},
                ),
            )

        # Add claim number to document object
        document.claim_number = document.claim.claim_number

        return document

    @require_service_permission("VIEW_CLAIM_DOCUMENTS", resource_id_param="claim_id")
    def list_documents(
        self,
        claim_id: Union[UUID, str],
        skip: int = 0,
        limit: int = 100,
        document_type: Optional[DocumentType] = None,
    ) -> Tuple[List[Document], int]:
        """List documents for a specific claim with optional filtering.

        Args:
            claim_id: Claim ID or claim number
            skip: Number of records to skip
            limit: Maximum number of records to return
            document_type: Filter by document type

        Returns:
            Tuple of (list of documents, total count)

        Raises:
            NotFoundError: If claim not found
            AuthorizationError: If user lacks permission to view documents for the claim
        """
        # Resolve claim ID
        resolved_claim_id = self._resolve_claim_id(claim_id)

        # Build base query for documents
        stmt = select(Document).where(Document.claim_id == resolved_claim_id)

        # Add document type filter if specified
        if document_type:
            stmt = stmt.where(Document.type == document_type)

        # Get total count
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_count = self.db.scalar(count_stmt) or 0

        # Apply pagination
        stmt = stmt.offset(skip).limit(limit)

        # Include claim relationship
        stmt = stmt.options(selectinload(Document.claim))

        # Execute query
        documents = list(self.db.scalars(stmt).all())

        # Add claim number to each document
        for doc in documents:
            doc.claim_number = doc.claim.claim_number

        return documents, total_count

    @require_service_permission("VIEW_ALL_DOCUMENTS")
    def list_all_documents(
        self,
        skip: int = 0,
        limit: int = 100,
        document_type: Optional[DocumentType] = None,
    ) -> Tuple[List[Document], int]:
        """List all documents irrespective of claim with optional filtering.

        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            document_type: Filter by document type

        Returns:
            Tuple of (list of documents, total count)

        Raises:
            AuthorizationError: If user lacks permission to view all documents
        """
        # Build the base query (without pagination)
        query = select(Document).order_by(Document.created_at.desc())

        # Apply type filter if provided
        if document_type:
            query = query.where(Document.type == document_type)

        # Count total records using the filtered query
        count_query = select(func.count(Document.id)).select_from(query.subquery())
        total = self.db.scalar(count_query) or 0

        # Apply pagination and load relationships
        paginated_query = (
            query.options(selectinload(Document.claim), selectinload(Document.uploader)).offset(skip).limit(limit)
        )

        # Execute query for documents
        documents = list(self.db.scalars(paginated_query))

        # Add claim number to each document object
        for document in documents:
            document.claim_number = document.claim.claim_number

        return documents, total

    @require_service_permission("UPLOAD_CLAIM_DOCUMENTS", resource_id_param="claim_id")
    def create_document(self, claim_id: Union[UUID, str], document_data: DocumentCreate, uploaded_by: UUID) -> Document:
        """Create a new document for a claim.

        Args:
            claim_id: Claim ID or claim number
            document_data: Document data
            uploaded_by: User ID of uploader

        Returns:
            Created document

        Raises:
            NotFoundError: If claim not found
            ValidationError: If document data is invalid
            AuthorizationError: If user lacks permission to create documents
            DatabaseError: If database operation fails
        """
        # Resolve claim ID
        resolved_claim_id = self._resolve_claim_id(claim_id)

        # Create document
        document = Document(
            id=uuid.uuid4(),  # Always generate a new UUID for the document
            claim_id=resolved_claim_id,
            type=document_data.type,
            name=document_data.name,
            description=document_data.description,
            file_path=document_data.file_path,
            file_size=document_data.file_size,
            mime_type=document_data.mime_type,
            uploaded_by=uploaded_by,
        )

        # Verify file path includes the proper structure
        expected_prefix = f"claims/{resolved_claim_id}/documents/"
        if not document.file_path.startswith(expected_prefix):
            raise ValidationError(
                message=f"Invalid file path format. Path must start with '{expected_prefix}'",
                details=create_error_detail(resource="document", field="file_path", reason="invalid_format"),
            )

        try:
            # Validate document
            document.validate_document()
        except ValidationError as e:
            # ValidationError is already a custom exception, pass it through
            raise e
        except Exception as e:
            # Convert other validation errors
            raise ValidationError(
                message=str(e), details=create_error_detail(resource="document", reason="validation_failed")
            )

        # Save to database with transaction
        with self._transaction():
            self.db.add(document)
            self.db.flush()  # Flush to get generated values

            # Get the claim for audit purposes
            claim = self.claim_service.get_claim_by_id(resolved_claim_id)

            # Create audit entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Capture document metadata for audit
            document_data_for_audit = {
                "id": str(document.id),
                "type": document.type.value,
                "name": document.name,
                "description": document.description,
                "file_size": document.file_size,
                "mime_type": document.mime_type,
                "uploaded_by": str(document.uploaded_by) if document.uploaded_by else None,
            }

            # Create audit log entry for document upload
            audit_helper.log_create(
                entity_type="DOCUMENT",
                entity_id=document.id,
                data=document_data_for_audit,
                description=f"Created {document.type.value} document: {document.name}",
            )

            # Log document creation
            logger.info(
                f"Document created: {document.id}",
                extra={
                    "document_id": str(document.id),
                    "claim_id": str(resolved_claim_id),
                    "document_type": document.type,
                    "file_size": document.file_size,
                    "user_id": str(self.current_user.id) if self.current_user else None,
                },
            )

            # Refresh the document to get all computed fields
            self.db.refresh(document)

            # Add claim number to document object
            document.claim_number = self.claim_service.get_claim_by_id(resolved_claim_id).claim_number

        return document

    @require_service_permission("EDIT_CLAIM_DOCUMENTS", resource_id_param="document_id")
    def update_document(self, document_id: UUID, document_data: DocumentUpdate) -> Document:
        """Update document metadata.

        Args:
            document_id: Document ID
            document_data: Document data to update

        Returns:
            Updated document

        Raises:
            NotFoundError: If document not found
            ValidationError: If document data is invalid
            AuthorizationError: If user lacks permission to update documents
            DatabaseError: If database operation fails
        """
        # Get document with permission check
        document = self.get_document_by_id(document_id)

        # Store original document data for audit
        original_data = {"type": document.type.value, "name": document.name, "description": document.description}

        # Apply updates only for provided fields
        update_data = document_data.model_dump(exclude_unset=True)

        if not update_data:
            # No changes requested
            return document

        # Update fields
        for key, value in update_data.items():
            setattr(document, key, value)

        # Validate updated document
        try:
            document.validate_document()
        except ValidationError as e:
            # ValidationError is already a custom exception, pass it through
            raise e
        except Exception as e:
            # Convert other validation errors
            raise ValidationError(
                message=str(e), details=create_error_detail(resource="document", reason="validation_failed")
            )

        # Save to database with transaction
        try:
            # Get the claim for audit purposes
            claim = self.claim_service.get_claim_by_id(document.claim_id)

            # Create audit entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Capture updated document metadata for audit
            updated_data = {"type": document.type.value, "name": document.name, "description": document.description}

            # Create audit log entry for document update
            audit_helper.log_update(
                entity_type="DOCUMENT",
                entity_id=document.id,
                previous_data=original_data,
                new_data=updated_data,
                description=f"Updated document metadata for {document.name}",
            )

            # Use direct transaction instead of context manager for clearer control
            self.db.add(document)

            # Log document update
            logger.info(
                f"Document updated: {document.id}",
                extra={
                    "document_id": str(document.id),
                    "claim_id": str(document.claim_id),
                    "updated_fields": list(update_data.keys()),
                    "user_id": str(self.current_user.id) if self.current_user else None,
                },
            )

            # Explicitly commit the transaction
            self.db.commit()

            # Refresh the document to get all computed fields
            self.db.refresh(document)

            # Add claim number to document object
            document.claim_number = document.claim.claim_number

            return document

        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error updating document {document_id}: {str(e)}", exc_info=True)
            raise DatabaseError(f"Database operation failed: {str(e)}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Unexpected error updating document {document_id}: {str(e)}", exc_info=True)
            raise

    @require_service_permission("DELETE_CLAIM_DOCUMENTS", resource_id_param="document_id")
    def delete_document(self, document_id: UUID) -> bool:
        """Delete a document.

        Args:
            document_id: Document ID

        Returns:
            True if document was deleted

        Raises:
            NotFoundError: If document not found
            AuthorizationError: If user lacks permission to delete documents
            StorageError: If storage operation fails
            DatabaseError: If database operation fails
        """
        # Get document with permission check
        document = self.get_document_by_id(document_id)

        # Store document data for audit before deletion
        document_data_for_audit = {
            "id": str(document.id),
            "type": document.type.value,
            "name": document.name,
            "description": document.description,
            "file_path": document.file_path,
            "file_size": document.file_size,
            "mime_type": document.mime_type,
            "uploaded_by": str(document.uploaded_by) if document.uploaded_by else None,
        }

        claim_id = document.claim_id

        file_path_to_delete = document.file_path  # Store path before DB transaction

        # Use transaction for the whole operation
        with self._transaction():
            # Get the claim for audit purposes
            claim = self.claim_service.get_claim_by_id(claim_id)

            # Create audit entry
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Create audit log entry for document deletion
            audit_helper.log_delete(
                entity_type="DOCUMENT",
                entity_id=document.id,
                data=document_data_for_audit,
                description=f"Deleted {document.type.value} document: {document.name}",
            )

            # Log intent to delete
            logger.info(
                f"Attempting to delete document: {document.id} (File: {file_path_to_delete})",
                extra={
                    "document_id": str(document.id),
                    "claim_id": str(document.claim_id),
                    "file_path": file_path_to_delete,
                    "user_id": str(self.current_user.id) if self.current_user else None,
                },
            )

            # Delete DB record first (within transaction)
            self.db.delete(document)
            # Flush to ensure DB constraints are checked before storage deletion attempt
            # If a FK constraint fails here, the transaction rolls back before storage is touched.
            try:
                self.db.flush()
            except SQLAlchemyError as db_exc:
                # Rollback is handled by context manager
                logger.error(
                    f"Database error deleting document record {document.id}: {db_exc}",
                    extra={
                        "document_id": str(document.id),
                        "claim_id": str(claim_id),
                    },
                )
                # Re-raise to ensure transaction rollback and indicate failure
                raise DatabaseError(f"Failed to delete document record: {db_exc}")

            # If DB delete (and flush) succeeds, attempt storage deletion
            try:
                deleted_from_storage = storage_client.delete_file(file_path_to_delete)
                if not deleted_from_storage:
                    # File didn't exist in storage - this might be acceptable depending on requirements
                    # For now, log a warning but allow the transaction to commit (DB record is deleted)
                    logger.warning(
                        f"File not found in storage during deletion: {file_path_to_delete}",
                        extra={"document_id": str(document.id), "file_path": file_path_to_delete},
                    )
                else:
                    logger.info(
                        f"Successfully deleted file from storage: {file_path_to_delete}",
                        extra={"document_id": str(document.id), "file_path": file_path_to_delete},
                    )
            except Exception as storage_exc:
                # Storage deletion failed! Transaction will be rolled back by the context manager.
                logger.error(
                    f"Critical: Storage deletion failed for {file_path_to_delete} after DB flush. Rolling back. Error: {storage_exc}",
                    exc_info=True,
                    extra={"document_id": str(document.id), "file_path": file_path_to_delete},
                )
                # Raise StorageError, which will trigger rollback via the context manager
                raise StorageError(
                    message=f"Failed to delete file from storage: {storage_exc}",
                    details=create_error_detail(
                        resource="document", identifier=str(document.id), reason="storage_delete_failed"
                    ),
                )

        # If we reach here, the transaction committed successfully
        return True

    @require_service_permission("UPLOAD_CLAIM_DOCUMENTS", resource_id_param="claim_id")
    def generate_upload_url(
        self,
        claim_id: Union[UUID, str],
        file_name: str,
        content_type: str,
        document_type: DocumentType,
    ) -> Tuple[str, UUID, str, datetime]:
        """Generate a signed URL for uploading a document.

        Args:
            claim_id: Claim ID or claim number
            file_name: Original file name
            content_type: MIME type
            document_type: Document type

        Returns:
            Tuple of (upload URL, document ID, storage path, expiration datetime)

        Raises:
            NotFoundError: If claim not found
            AuthorizationError: If user lacks permission to upload documents
            StorageError: If storage operation fails
        """
        # Resolve claim ID
        resolved_claim_id = self._resolve_claim_id(claim_id)

        # Generate document ID
        document_id = uuid.uuid4()

        try:
            # Generate upload URL with storage path in expected format
            upload_url, storage_path, expires_at = storage_client.generate_upload_url(
                claim_id=resolved_claim_id,
                document_id=document_id,
                filename=file_name,
                content_type=content_type,
            )

            # Log URL generation
            logger.info(
                f"Upload URL generated for claim {resolved_claim_id}",
                extra={
                    "claim_id": str(resolved_claim_id),
                    "document_id": str(document_id),
                    "document_file_name": file_name,
                    "content_type": content_type,
                    "storage_path": storage_path,
                    "expires_at": expires_at.isoformat(),
                },
            )

            return upload_url, document_id, storage_path, expires_at

        except StorageError as e:
            # StorageError is already a custom exception, pass it through
            logger.error(
                f"Storage error generating upload URL: {str(e)}",
                exc_info=True,
                extra={"claim_id": str(resolved_claim_id), "document_file_name": file_name},
            )
            raise e
        except Exception as e:
            # Handle other exceptions
            logger.error(
                f"Error generating upload URL: {str(e)}",
                exc_info=True,
                extra={"claim_id": str(resolved_claim_id), "document_file_name": file_name},
            )
            raise StorageError(
                message=f"Failed to generate upload URL: {str(e)}",
                details=create_error_detail(resource="document", reason="upload_url_generation_failed"),
            )

    def generate_download_url(self, document_id: UUID) -> Tuple[str, datetime]:
        """Generate a signed URL for downloading a document.

        Args:
            document_id: Document ID

        Returns:
            Tuple of (download URL, expiration datetime)

        Raises:
            NotFoundError: If document not found
            AuthorizationError: If user lacks permission to view documents
            StorageError: If storage operation fails
        """
        # Get document with permission check
        document = self.get_document_by_id(document_id)

        try:
            # Generate download URL
            download_url, expires_at = storage_client.generate_download_url(document.file_path)

            # Log URL generation
            logger.info(
                f"Download URL generated for document {document_id}",
                extra={
                    "document_id": str(document_id),
                    "claim_id": str(document.claim_id),
                    "file_path": document.file_path,
                    "expires_at": expires_at.isoformat(),
                },
            )

            return download_url, expires_at

        except StorageError as e:
            # StorageError is already a custom exception, pass it through
            logger.error(
                f"Storage error generating download URL: {str(e)}",
                exc_info=True,
                extra={"document_id": str(document_id), "file_path": document.file_path},
            )
            raise e
        except FileNotFoundError as e:
            # File not found in storage
            logger.error(
                f"File not found in storage: {document.file_path}",
                exc_info=True,
                extra={"document_id": str(document_id)},
            )
            raise StorageError(
                message=f"File not found in storage: {str(e)}",
                details=create_error_detail(resource="document", identifier=str(document_id), reason="file_not_found"),
            )
        except Exception as e:
            # Handle other exceptions
            logger.error(
                f"Error generating download URL: {str(e)}", exc_info=True, extra={"document_id": str(document_id)}
            )
            raise StorageError(
                message=f"Failed to generate download URL: {str(e)}",
                details=create_error_detail(
                    resource="document", identifier=str(document_id), reason="download_url_generation_failed"
                ),
            )

    @require_service_permission("UPLOAD_CLAIM_DOCUMENTS", resource_id_param="claim_id")
    def direct_upload(
        self,
        claim_id: Union[UUID, str],
        file_name: str,
        content_type: str,
        file_content: bytes,
        document_type: DocumentType,
        document_name: Optional[str] = None,
        document_description: Optional[str] = None,
    ) -> Document:
        """
        Upload a file directly to storage and create a document record.

        Args:
            claim_id: Claim ID or claim number
            file_name: Original file name
            content_type: MIME type
            file_content: File content as bytes
            document_type: Document type
            document_name: Display name (defaults to file_name if not provided)
            document_description: Optional description

        Returns:
            Created document

        Raises:
            NotFoundError: If claim not found
            ValidationError: If document data is invalid
            AuthorizationError: If user lacks permission to upload documents
            StorageError: If storage operation fails
            DatabaseError: If database operation fails
        """
        if self.current_user is None:
            raise AuthorizationError("Authentication required for document upload")

        # Resolve claim ID
        resolved_claim_id = self._resolve_claim_id(claim_id)

        # Generate document ID
        document_id = uuid.uuid4()

        try:
            # Upload file directly to storage
            storage_path, upload_time = storage_client.direct_upload(
                claim_id=resolved_claim_id,
                document_id=document_id,
                filename=file_name,
                content_type=content_type,
                file_content=file_content,
            )

            # Create document record
            document_data = DocumentCreate(
                name=document_name or file_name,
                description=document_description,
                type=document_type,
                file_path=storage_path,
                file_size=len(file_content),
                mime_type=content_type,
            )

            # Create the document record
            document = self.create_document(
                claim_id=resolved_claim_id, document_data=document_data, uploaded_by=self.current_user.id
            )

            return document

        except StorageError as e:
            # StorageError is already a custom exception, pass it through
            logger.error(
                f"Storage error in direct upload: {str(e)}",
                exc_info=True,
                extra={"claim_id": str(resolved_claim_id), "document_file_name": file_name},
            )
            raise e
        except Exception as e:
            # Handle other exceptions
            logger.error(
                f"Error in direct upload: {str(e)}",
                exc_info=True,
                extra={"claim_id": str(resolved_claim_id), "document_file_name": file_name},
            )
            raise BadRequestError(
                message=f"Failed to upload document: {str(e)}",
                details=create_error_detail(resource="document", reason="upload_failed"),
            )
