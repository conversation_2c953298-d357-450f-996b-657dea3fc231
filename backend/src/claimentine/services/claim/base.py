"""Base claim service."""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Type, Union
from uuid import UUID

from sqlalchemy import func, or_, select
from sqlalchemy.orm import Session, joinedload, selectinload

from claimentine.core.audit import <PERSON>t<PERSON>elper
from claimentine.core.exceptions import AuthorizationError, DuplicateError, NotFoundError, ValidationError
from claimentine.models.attorney import Attorney
from claimentine.models.claim.base import BaseClaim, ClaimStatus, ClaimType, RecoveryStatus
from claimentine.models.claim.types import AutoClaim, GeneralLiabilityClaim, PropertyClaim
from claimentine.models.document import Document
from claimentine.models.fnol import FNOL
from claimentine.models.note import Note
from claimentine.models.status_history import ClaimStatusHistory
from claimentine.models.task import Task
from claimentine.models.user import User
from claimentine.models.witness import Witness
from claimentine.schemas.claim.base import CloseClaimSchema, CreateClaimSchema, UpdateClaimSchema
from claimentine.services.base import BaseService
from claimentine.services.client import ClientService

logger = logging.getLogger(__name__)


class BaseClaimService(BaseService):
    """Service for managing base claims."""

    def __init__(
        self, db: Session, current_user: Optional[User] = None, client_service: Optional[ClientService] = None
    ):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Store injected service, or create default if not provided
        self.client_service = client_service or ClientService(db, current_user)

    # --- Hook methods for subclasses ---

    def _get_details_field_name(self) -> Optional[str]:
        """Return the attribute name for the claim details relation. Override in subclasses."""
        return None

    def _get_detail_model_class(self) -> Optional[Type]:
        """Return the model class for claim details. Override in subclasses."""
        return None

    def _extract_details_data(self, data: Union[CreateClaimSchema, UpdateClaimSchema]) -> Optional[Dict[str, Any]]:
        """Extract the details dictionary from the input schema. Override in subclasses."""
        return None

    def _validate_details(self, claim: BaseClaim) -> None:
        """Validate type-specific details. Override in subclasses."""
        pass  # Base claim has no details to validate

    # --- Core Methods ---

    def _get_claim_class(self, claim_type: ClaimType) -> Type[BaseClaim]:
        """Get the appropriate claim class based on type."""
        if claim_type == ClaimType.AUTO:
            return AutoClaim
        elif claim_type == ClaimType.PROPERTY:
            return PropertyClaim
        elif claim_type == ClaimType.GENERAL_LIABILITY:
            return GeneralLiabilityClaim
        else:
            # Should we raise an error for unknown types or return BaseClaim?
            # Returning BaseClaim might hide errors if a type isn't handled.
            # Let's raise for now, assuming subclasses handle known types.
            raise ValueError(f"Unsupported claim type: {claim_type}")

    def _apply_includes(self, query: select, includes: Optional[Set[str]] = None) -> select:
        """Apply includes to the query."""
        if not includes:
            return query

        # Client relationship
        if "client" in includes:
            query = query.options(selectinload(BaseClaim.client))

        # Documents relationship
        if "documents" in includes:
            query = query.options(selectinload(BaseClaim.documents.and_(Document.is_deleted == False)))

        # Notes relationship
        if "notes" in includes:
            query = query.options(selectinload(BaseClaim.notes.and_(Note.is_deleted == False)))

        # Tasks relationship
        if "tasks" in includes:
            query = query.options(selectinload(BaseClaim.tasks.and_(Task.is_deleted == False)))

        # Status history relationship
        if "status_history" in includes:
            query = query.options(selectinload(BaseClaim.status_history))

        # Witnesses relationship
        if "witnesses" in includes:
            query = query.options(selectinload(BaseClaim.witnesses.and_(Witness.is_deleted == False)))

        # Attorneys relationship
        if "attorneys" in includes:
            query = query.options(selectinload(BaseClaim.attorneys.and_(Attorney.is_deleted == False)))

        # Financials relationship
        if "financials" in includes:
            query = query.options(selectinload(BaseClaim.financials))

        # User relationships
        if "assigned_to" in includes:
            query = query.options(selectinload(BaseClaim.assigned_to))

        if "supervisor" in includes:
            query = query.options(selectinload(BaseClaim.supervisor))

        if "created_by" in includes:
            query = query.options(selectinload(BaseClaim.created_by))

        return query

    def _check_view_claim_permission(self, claim: BaseClaim) -> None:
        """Check if the current user has permission to view a specific claim."""
        if not self.current_user:
            # Internal calls or unauthenticated access allowed for retrieval
            # Specific action permissions will be checked elsewhere if needed.
            return

        # Check permissions using check_permission(raise_exception=False)
        can_view_all = self.check_permission("VIEW_ALL_CLAIMS", raise_exception=False)
        can_view_assigned = self.check_permission("VIEW_ASSIGNED_CLAIMS", raise_exception=False)
        can_view_own = self.check_permission("VIEW_OWN_CLAIMS", raise_exception=False)

        if can_view_all:
            return
        if can_view_assigned and claim.assigned_to_id == self.current_user.id:
            return
        if can_view_own and claim.created_by_id == self.current_user.id:
            return

        # If none of the conditions met, raise error (delegating message/logging to check_permission)
        # Re-check VIEW_ALL_CLAIMS without raise_exception=False to trigger standard error
        self.check_permission("VIEW_ALL_CLAIMS", resource_type="claim", resource_id=claim.id)

    # Helper method for specific edit checks
    def _check_edit_claim_permission(self, claim: BaseClaim) -> None:
        """Check if the current user has permission to edit a specific claim."""
        if not self.current_user:
            raise AuthorizationError("Authentication required to edit claims")

        # Check permissions using check_permission(raise_exception=False)
        can_edit_all = self.check_permission("EDIT_CLAIMS", raise_exception=False)
        can_edit_assigned = self.check_permission("EDIT_ASSIGNED_CLAIMS", raise_exception=False)

        if can_edit_all:
            return
        if can_edit_assigned and claim.assigned_to_id == self.current_user.id:
            return

        # If none of the conditions met, raise error
        # Re-check EDIT_CLAIMS without raise_exception=False to trigger standard error
        self.check_permission("EDIT_CLAIMS", resource_type="claim", resource_id=claim.id)

    def list_claims(
        self,
        skip: int = 0,
        limit: int = 10,
        status: Optional[ClaimStatus] = None,
        type: Optional[ClaimType] = None,
        client_id: Optional[UUID] = None,
        assigned_to_id: Optional[UUID] = None,
        supervisor_id: Optional[UUID] = None,
        team: Optional[str] = None,
        search: Optional[str] = None,
        includes: Optional[Set[str]] = None,
    ) -> tuple[List[BaseClaim], int]:
        """List claims with filtering based on user permissions.

        Args:
            skip: Number of records to skip for pagination
            limit: Maximum number of records to return
            status: Filter by claim status
            type: Filter by claim type
            client_id: Filter by client ID
            assigned_to_id: Filter by assigned user ID
            supervisor_id: Filter by supervisor ID
            team: Filter by team
            search: Search text across claim_number, claimant_name, and description (case-insensitive)
            includes: Set of related objects to include in the response

        Returns:
            tuple: (claims, total_count) where claims is the paginated list and total_count is the total number of claims matching the filters
        """
        from sqlalchemy import func, or_

        stmt = select(BaseClaim)

        # Apply permission-based filtering
        if self.current_user:
            # Use check_permission with raise_exception=False to determine filtering
            can_view_all = self.check_permission("VIEW_ALL_CLAIMS", raise_exception=False)
            can_view_assigned = self.check_permission("VIEW_ASSIGNED_CLAIMS", raise_exception=False)
            can_view_own = self.check_permission("VIEW_OWN_CLAIMS", raise_exception=False)

            if can_view_all:
                # Can view all claims, no filtering needed
                pass
            elif can_view_assigned:
                # Can only view assigned claims
                # Combine with potential user filter: only assign if not already filtered by user
                if assigned_to_id is None:
                    stmt = stmt.where(BaseClaim.assigned_to_id == self.current_user.id)
                elif assigned_to_id != self.current_user.id:
                    # User is filtering by someone else but only has assigned permission -> No results
                    # Alternatively, raise AuthorizationError? For now, return no results.
                    return [], 0
            elif can_view_own:
                # Can only view own created claims
                # Note: This doesn't filter by created_by_id if user provides other filters
                stmt = stmt.where(BaseClaim.created_by_id == self.current_user.id)
            else:
                # No view permissions - raise error using check_permission
                self.check_permission("VIEW_ALL_CLAIMS")  # Will raise AuthorizationError

        # Apply filters
        if status:
            stmt = stmt.where(BaseClaim.status == status)
        if type:
            stmt = stmt.where(BaseClaim.type == type)
        if client_id:
            stmt = stmt.where(BaseClaim.client_id == client_id)
        if assigned_to_id:
            stmt = stmt.where(BaseClaim.assigned_to_id == assigned_to_id)
        if supervisor_id:
            stmt = stmt.where(BaseClaim.supervisor_id == supervisor_id)
        if team:
            stmt = stmt.where(BaseClaim.team == team)

        # Apply search filter
        if search:
            search_term = f"%{search.strip()}%"
            search_filter = or_(
                BaseClaim.claim_number.ilike(search_term),
                BaseClaim.claimant_name.ilike(search_term),
                BaseClaim.description.ilike(search_term),
            )
            stmt = stmt.where(search_filter)

        # Filter out soft-deleted records
        stmt = stmt.where(BaseClaim.is_deleted == False)

        # Get total count before applying includes and pagination
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_count = self.db.scalar(count_stmt)

        # Apply includes
        if includes:
            stmt = self._apply_includes(stmt, includes)

        # Apply pagination and ordering
        stmt = stmt.offset(skip).limit(limit).order_by(BaseClaim.created_at.desc())

        # Execute query
        claims = list(self.db.scalars(stmt))

        # Add claim_number to status_history items for each claim
        for claim in claims:
            if hasattr(claim, "status_history") and claim.status_history:
                for history_item in claim.status_history:
                    history_item.claim_number = claim.claim_number

        return claims, total_count

    def _generate_claim_number(self, client_id: UUID) -> str:
        """Generate a sequential claim number in format PREFIX-YYYY-NNNNNNN."""
        # Get client prefix using injected service
        client = self.client_service.get_client_by_id(client_id)
        if not client:
            raise NotFoundError(f"Client {client_id} not found")

        current_year = datetime.utcnow().year

        # Get the highest claim number for the client and year
        query = (
            select(BaseClaim.claim_number)
            .where(BaseClaim.claim_number.like(f"{client.prefix}-{current_year}-%"))
            .order_by(BaseClaim.claim_number.desc())
            .limit(1)
        )
        last_claim = self.db.scalar(query)

        if last_claim:
            # Extract the sequence number and increment
            try:
                sequence = int(last_claim.split("-")[-1]) + 1
            except (ValueError, IndexError):
                # If parsing fails, start from 1
                sequence = 1
        else:
            # First claim of the year for this client
            sequence = 1

        return f"{client.prefix}-{current_year}-{sequence:07d}"

    def get_claim_by_id(self, claim_id: UUID, includes: Optional[Set[str]] = None) -> Optional[BaseClaim]:
        """Get claim by ID with permission check."""
        logger.debug(f"[BaseClaimService] Attempting to get claim by ID: {claim_id}")
        # Fetches the base claim, subclasses might need to fetch specific type if details are required immediately
        stmt = select(BaseClaim).where(BaseClaim.id == claim_id)
        stmt = self._apply_includes(stmt, includes)
        claim = self.db.scalar(stmt)
        logger.debug(f"[BaseClaimService] DB query result for claim ID {claim_id}: {'Found' if claim else 'Not Found'}")

        if not claim:
            logger.warning(f"[BaseClaimService] Claim ID {claim_id} not found in DB.")
            return None

        # Ensure soft-deleted are not returned
        if claim.is_deleted:
            return None

        self._check_view_claim_permission(claim)

        # Add claim_number to status_history items
        if claim and hasattr(claim, "status_history") and claim.status_history:
            for history_item in claim.status_history:
                history_item.claim_number = claim.claim_number

        return claim

    def get_claim_by_number(self, claim_number: str, includes: Optional[Set[str]] = None) -> Optional[BaseClaim]:
        """Get claim by number with permission check."""
        # Fetches the base claim
        stmt = select(BaseClaim).where(BaseClaim.claim_number == claim_number)
        stmt = self._apply_includes(stmt, includes)
        claim = self.db.scalar(stmt)

        if not claim:
            return None

        # Ensure soft-deleted are not returned
        if claim.is_deleted:
            return None

        self._check_view_claim_permission(claim)

        # Add claim_number to status_history items
        if claim and hasattr(claim, "status_history") and claim.status_history:
            for history_item in claim.status_history:
                history_item.claim_number = claim.claim_number

        return claim

    def create_claim(self, claim_data: CreateClaimSchema) -> BaseClaim:
        """Create a new claim using appropriate type and details."""
        self.check_permission("CREATE_CLAIMS", resource_type="claim")

        claim_class = self._get_claim_class(claim_data.type)
        details_field_name = self._get_details_field_name()
        details_data = self._extract_details_data(claim_data)
        detail_model_class = self._get_detail_model_class()

        # Prepare base claim data, excluding details if they exist
        base_data = claim_data.model_dump(
            exclude={details_field_name} if details_field_name else set(), exclude_unset=True
        )

        # Enhanced FNOL-to-Claims conversion logic
        fnol_id = base_data.get("fnol_id")
        if fnol_id:
            fnol = self.db.get(FNOL, fnol_id)
            if fnol:
                # Track what fields we're populating from FNOL for logging
                populated_fields = []

                # Populate policy_number from FNOL if not provided in claim_data
                if not base_data.get("policy_number") and fnol.policy_number:
                    base_data["policy_number"] = fnol.policy_number
                    populated_fields.append("policy_number")

                # Map FNOL incident_state to Claims jurisdiction if not provided in claim_data
                if not base_data.get("jurisdiction") and fnol.incident_state:
                    base_data["jurisdiction"] = fnol.incident_state
                    populated_fields.append("jurisdiction")

                # Map FNOL reporter contact fields to Claims if not provided in claim_data
                if not base_data.get("reporter_phone") and fnol.reporter_phone:
                    base_data["reporter_phone"] = fnol.reporter_phone
                    populated_fields.append("reporter_phone")

                if not base_data.get("reporter_email") and fnol.reporter_email:
                    base_data["reporter_email"] = fnol.reporter_email
                    populated_fields.append("reporter_email")

                # Log the field mappings if any were populated
                if populated_fields:
                    logger.info(
                        f"Populated claim fields from FNOL {fnol.fnol_number}: {', '.join(populated_fields)}",
                        extra={
                            "fnol_id": str(fnol_id),
                            "fnol_number": fnol.fnol_number,
                            "populated_fields": populated_fields,
                        },
                    )

                # Validate that we have at least one reporter contact method after FNOL mapping
                if not base_data.get("reporter_phone") and not base_data.get("reporter_email"):
                    logger.warning(
                        f"No reporter contact information available after FNOL conversion for FNOL {fnol.fnol_number}",
                        extra={"fnol_id": str(fnol_id), "fnol_number": fnol.fnol_number},
                    )

        # Handle claim number
        if "claim_number" not in base_data or not base_data["claim_number"]:
            base_data["claim_number"] = self._generate_claim_number(claim_data.client_id)
        # Check if claim number already exists (and is not soft-deleted)
        elif self.db.scalar(
            select(BaseClaim).where(BaseClaim.claim_number == base_data["claim_number"], BaseClaim.is_deleted == False)
        ):
            raise DuplicateError(f"Claim number {base_data['claim_number']} already exists")

        # Create claim instance
        claim = claim_class(**base_data, status=ClaimStatus.INITIAL_REVIEW)

        # Set created_by_id
        if self.current_user:
            claim.created_by_id = self.current_user.id

        # Create and attach details if applicable
        if details_field_name and detail_model_class and details_data is not None:
            detail_obj = detail_model_class(**details_data)
            setattr(claim, details_field_name, detail_obj)
            # Ensure detail validation is called if needed (might be redundant if model validates)
            self._validate_details(claim)

        # Add to session, create history, commit
        self.db.add(claim)
        self.db.flush()  # Assigns ID

        # Create initial status history
        claim.create_initial_status_history(
            created_by_id=self.current_user.id if self.current_user else None, reason="Initial claim creation"
        )

        self.db.commit()
        self.db.refresh(claim)

        # Create enhanced audit entry for claim creation
        audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
        audit_data = {
            "claim_number": claim.claim_number,
            "type": str(claim.type),
            "status": str(claim.status),
            "client_id": str(claim.client_id),
            "claimant_name": claim.claimant_name,
            "created_by_id": str(claim.created_by_id) if claim.created_by_id else None,
            "fnol_id": str(claim.fnol_id) if claim.fnol_id else None,
        }

        # Include reporter contact fields and jurisdiction in audit data
        if claim.reporter_phone:
            audit_data["reporter_phone"] = claim.reporter_phone
        if claim.reporter_email:
            audit_data["reporter_email"] = claim.reporter_email
        if claim.jurisdiction:
            audit_data["jurisdiction"] = str(claim.jurisdiction)
        if claim.policy_number:
            audit_data["policy_number"] = claim.policy_number

        audit_helper.log_create(
            entity_type="CLAIM",
            entity_id=claim.id,
            data=audit_data,
            description=f"Created new {claim.type} claim: {claim.claim_number}",
        )

        # Optionally refresh details relationship if needed
        # if details_field_name: self.db.refresh(claim, attribute_names=[details_field_name])

        # Add claim_number to status_history items
        if hasattr(claim, "status_history") and claim.status_history:
            for history_item in claim.status_history:
                history_item.claim_number = claim.claim_number

        return claim

    def update_claim(
        self,
        claim_id: UUID,
        claim_data: UpdateClaimSchema,
        includes: Optional[Set[str]] = None,  # Includes might be less relevant here if we fetch specific type
    ) -> BaseClaim:
        """Update a claim with the provided data."""
        # Get the claim by ID
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Convert Pydantic model to dict, excluding unset fields
        data_dict = claim_data.model_dump(exclude_unset=True)

        # Handle special status transitions if status is changing
        if "status" in data_dict and data_dict["status"] != claim.status:
            old_status = claim.status
            new_status = data_dict["status"]

            # If the new status is a closing status, use the close_claim method
            if new_status == ClaimStatus.CLOSURE:
                # Call the specialized method for closing claims
                return self.close_claim(claim_id, new_status)

            # Otherwise, just transition the status directly
            claim.transition_to(
                new_status=new_status,
                changed_by_id=self.current_user.id if self.current_user else None,
            )

            # Remove status from data_dict to avoid double setting
            del data_dict["status"]

        # Track fields that changed for audit
        changed_fields = {}
        previous_values = {}

        # Extract any details data (this will be handled by the derived service's _extract_details_data method)
        details_data = self._extract_details_data(claim_data)
        details_field = self._get_details_field_name()

        # Update the claim with the provided data
        for key, value in data_dict.items():
            # Skip details fields as they are handled separately
            if key == details_field:
                continue

            # Check if the field is actually changing
            old_value = getattr(claim, key, None)
            if old_value != value:
                # Store the change for audit
                previous_values[key] = old_value
                changed_fields[key] = value

                # Update the field
                setattr(claim, key, value)

        # If there are any details to update, get the details object
        if details_data and details_field:
            details_obj = getattr(claim, details_field, None)

            if details_obj:
                # Record what we're changing for audit
                old_details = {}
                for key, value in details_data.items():
                    old_value = getattr(details_obj, key, None)
                    if old_value != value:
                        old_details[key] = old_value

                # Process updates
                for key, value in details_data.items():
                    setattr(details_obj, key, value)

                # Create audit entry for details changes if there were any
                if old_details:
                    audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
                    audit_helper.create_entry(
                        entity_type="CLAIM_DETAILS",
                        change_type="UPDATE",
                        field_name=details_field,
                        previous_value=old_details,
                        new_value=details_data,
                        description=f"{details_field} updated",
                    )

        # Create audit entry for main claim changes if there were any
        if changed_fields:
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type="CLAIM",
                change_type="UPDATE",
                previous_value=previous_values,
                new_value=changed_fields,
                description="Claim details updated",
            )

        # Check if the updated claim is valid
        self._validate_details(claim)

        # Save to the database
        self.db.commit()
        self.db.refresh(claim)

        # Add claim_number to status_history items
        if hasattr(claim, "status_history") and claim.status_history:
            for history_item in claim.status_history:
                history_item.claim_number = claim.claim_number

        return claim

    def delete_claim(self, claim_id: UUID) -> None:
        """Delete a claim (soft delete)."""
        self.check_permission("DELETE_CLAIMS", resource_type="claim", resource_id=claim_id)

        claim = self.get_claim_by_id(claim_id)  # Will return None if not found or already deleted
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check if already marked as deleted - if so, raise NotFoundError as it's effectively gone.
        if claim.is_deleted:
            # Treat as not found if already deleted
            raise NotFoundError(f"Claim {claim_id} not found (already deleted)")

        # Soft delete: Set flags
        claim.is_deleted = True
        claim.deleted_at = datetime.utcnow()
        # Track who deleted it (optional, depends on user session)
        claim.deleted_by_id = self.current_user.id if self.current_user else None
        # Optional: Set status to a specific 'deleted' status if needed?
        # Or just rely on the is_deleted flag for filtering.
        # claim.status = SomeDeletedStatus?

        try:
            self.db.commit()
        except Exception as e:
            self.db.rollback()
            # Log the detailed exception e
            print(f"Error committing delete for claim {claim_id}: {e}")
            # Raise a generic server error
            raise Exception(f"Database error during claim deletion: {e}")

        # Verify the change is reflected in the session state immediately after commit
        try:
            self.db.refresh(claim)
            if not claim.is_deleted:
                # This should ideally not happen if commit was successful
                print(f"[ERROR] Claim {claim_id} is_deleted flag is still False after commit and refresh!")
                # Raise an error to make the failure explicit if commit didn't persist
                raise Exception(f"Soft delete failed to persist for claim {claim_id}")
        except Exception as e:
            # Handle potential errors during refresh (e.g., object detached)
            print(f"[ERROR] Error refreshing claim {claim_id} after delete commit: {e}")
            # Depending on policy, might want to raise here too
            raise Exception(f"Error verifying delete persistence for claim {claim_id}: {e}")

    def close_claim(self, claim_id: UUID, status: ClaimStatus) -> BaseClaim:
        """Close a claim with the specified status.

        This is a special case of status transition that also sets the closed_at timestamp.
        """
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission to close claims
        self.check_permission("CLOSE_CLAIMS", resource_type="claim", resource_id=claim_id)

        # Store previous status for audit
        previous_status = claim.status

        # Validate that the status is a closing status
        if status != ClaimStatus.CLOSURE:
            raise ValidationError(f"Status {status} is not a closing status")

        # Set the closed_at timestamp and change the status
        now = datetime.now()
        claim.closed_at = now
        claim.status = status

        # Create a status history entry
        history_entry = ClaimStatusHistory(
            claim_id=claim.id,
            from_status=previous_status.value,
            to_status=status.value,
            reason="Claim closed",
            changed_by=self.current_user.id if self.current_user else None,
        )
        claim.status_history.append(history_entry)

        # Create audit entry for claim closure
        audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
        audit_helper.create_entry(
            entity_type="CLAIM",
            change_type="UPDATE",
            field_name="status",
            previous_value={"status": previous_status.value, "closed_at": None},
            new_value={"status": status.value, "closed_at": now.isoformat()},
            description=f"Claim closed with status: {status.value}",
        )

        self.db.commit()
        self.db.refresh(claim)

        # Add claim_number to status_history items
        if hasattr(claim, "status_history") and claim.status_history:
            for history_item in claim.status_history:
                history_item.claim_number = claim.claim_number

        return claim

    def update_recovery_status(self, claim_id: UUID, recovery_status: RecoveryStatus) -> BaseClaim:
        """Update the recovery status of a claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Record the previous value for audit
        previous_status = claim.recovery_status

        # Update the status
        claim.recovery_status = recovery_status

        # Create audit entry for the change
        if previous_status != recovery_status:
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)
            audit_helper.create_entry(
                entity_type="CLAIM",
                change_type="UPDATE",
                field_name="recovery_status",
                previous_value={"recovery_status": previous_status.value if previous_status else None},
                new_value={"recovery_status": recovery_status.value},
                description=f"Recovery status updated from {previous_status or 'None'} to {recovery_status}",
            )

        self.db.commit()
        self.db.refresh(claim)
        return claim

    def update_carrier_details(
        self,
        claim_id: UUID,
        carrier_name: Optional[str],
        carrier_contact: Optional[str],
        carrier_claim_number: Optional[str] = None,
        carrier_adjuster: Optional[str] = None,
    ) -> BaseClaim:
        """Update third-party carrier details of a claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim with ID {claim_id} not found.")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Record previous values for audit
        previous_name = claim.carrier_name
        previous_contact = claim.carrier_contact
        previous_claim_number = claim.carrier_claim_number
        previous_adjuster = claim.carrier_adjuster

        # Update the carrier details
        claim.carrier_name = carrier_name
        claim.carrier_contact = carrier_contact
        claim.carrier_claim_number = carrier_claim_number
        claim.carrier_adjuster = carrier_adjuster

        # Create audit entry if anything changed
        if (
            previous_name != carrier_name
            or previous_contact != carrier_contact
            or previous_claim_number != carrier_claim_number
            or previous_adjuster != carrier_adjuster
        ):
            claim.create_audit_entry(
                entity_type="CLAIM",
                change_type="UPDATE",
                field_name="carrier_details",
                previous_value={
                    "carrier_name": previous_name,
                    "carrier_contact": previous_contact,
                    "carrier_claim_number": previous_claim_number,
                    "carrier_adjuster": previous_adjuster,
                },
                new_value={
                    "carrier_name": carrier_name,
                    "carrier_contact": carrier_contact,
                    "carrier_claim_number": carrier_claim_number,
                    "carrier_adjuster": carrier_adjuster,
                },
                description="Carrier details updated",
                changed_by_id=self.current_user.id if self.current_user else None,
            )

        self.db.commit()
        self.db.refresh(claim)
        return claim

    def update_recovery_amounts(
        self, claim_id: UUID, expected_amount: Optional[str] = None, received_amount: Optional[str] = None
    ) -> BaseClaim:
        """Update recovery amounts for a claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim with ID {claim_id} not found.")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Record previous values for audit
        previous_expected = claim.expected_amount
        previous_received = claim.received_amount

        # Update the recovery amounts
        claim.expected_amount = expected_amount
        claim.received_amount = received_amount

        # Create audit entry if anything changed
        if previous_expected != expected_amount or previous_received != received_amount:
            claim.create_audit_entry(
                entity_type="CLAIM",
                change_type="UPDATE",
                field_name="recovery_amounts",
                previous_value={"expected_amount": previous_expected, "received_amount": previous_received},
                new_value={"expected_amount": expected_amount, "received_amount": received_amount},
                description="Recovery amounts updated",
                changed_by_id=self.current_user.id if self.current_user else None,
            )

        self.db.commit()
        self.db.refresh(claim)
        return claim

    def assign_claim(self, claim_id: UUID, assigned_to_id: UUID, supervisor_id: Optional[UUID] = None) -> BaseClaim:
        """Assign a claim to a user and optionally a supervisor."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission to assign claims
        self.check_permission("ASSIGN_CLAIMS", resource_type="claim", resource_id=claim_id)

        # Validate assigned user exists
        assigned_user = self.db.get(User, assigned_to_id)
        if not assigned_user:
            raise NotFoundError(f"User {assigned_to_id} not found")

        claim.assigned_to_id = assigned_to_id
        claim.supervisor_id = supervisor_id
        self.db.commit()
        self.db.refresh(claim)
        return claim

    def unassign_claim(self, claim_id: UUID) -> BaseClaim:
        """Unassign a claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission to unassign claims
        self.check_permission("UNASSIGN_CLAIMS", resource_type="claim", resource_id=claim_id)

        claim.assigned_to_id = None
        claim.supervisor_id = None
        self.db.commit()
        self.db.refresh(claim)
        return claim

    def change_claim_status(self, claim_id: UUID, new_status: ClaimStatus, reason: Optional[str] = None) -> BaseClaim:
        """Change the status of a claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Check permission to change claim status
        self.check_permission("CHANGE_CLAIM_STATUS", resource_type="claim", resource_id=claim_id)

        if new_status != ClaimStatus.CLOSURE:
            raise ValidationError(f"Status {new_status} is not a closed status")

        claim.transition_to(
            new_status=new_status,
            changed_by_id=self.current_user.id if self.current_user else None,
            reason=reason,
        )

        self.db.commit()
        self.db.refresh(claim)
        return claim
