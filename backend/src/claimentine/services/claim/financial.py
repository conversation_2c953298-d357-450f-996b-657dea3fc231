"""Service for managing claim financials."""

import json
import logging
from contextlib import contextmanager
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy import func, select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session, selectinload

from claimentine.core.audit import AuditHelper
from claimentine.core.exceptions import AuthorizationError, DatabaseError, NotFoundError, ValidationError
from claimentine.core.json import CustomJSONEncoder
from claimentine.models.authority import AuthorityLevel
from claimentine.models.claim.base import BaseClaim, ClaimType
from claimentine.models.claim.financial import (
    ClaimFinancials,
    ClaimReserve,
    Payment,
    PaymentType,
    ReserveHistory,
    ReserveType,
)
from claimentine.models.config.authority import ClientAuthorityThreshold
from claimentine.models.config.reserve import ReserveConfiguration
from claimentine.models.user import User
from claimentine.schemas.claim.financial import ClaimFinancialsCreate, ClaimFinancialsUpdate, PaymentCreate
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class ClaimFinancialsService(BaseService):
    """Service for managing claim financials."""

    def __init__(
        self, db: Session, current_user: Optional[User] = None, claim_service: Optional[BaseClaimService] = None
    ):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Store injected service, or create default if not provided
        self.claim_service = claim_service or BaseClaimService(db, current_user)

    @contextmanager
    def _transaction(self):
        """Handle database transaction with error logging and rollback."""
        try:
            yield
            self.db.commit()
        except SQLAlchemyError as e:
            self.db.rollback()
            logger.error(f"Database error in claim financials service: {str(e)}", exc_info=True)
            raise DatabaseError(f"Database operation failed: {str(e)}")
        except Exception as e:
            self.db.rollback()
            logger.error(f"Unexpected error in claim financials service: {str(e)}", exc_info=True)
            raise

    def _validate_authority(self, amount: Decimal, claim_id: Optional[UUID] = None) -> None:
        """Validate if user has authority for the given amount, considering client-specific thresholds if applicable."""
        if not self.current_user:
            logger.error("No user context available for authority validation")
            raise AuthorizationError("No user context available")

        # Get claim and client if claim_id provided
        client_id = None
        if claim_id:
            claim = self.db.get(BaseClaim, claim_id)
            if claim:
                client_id = claim.client_id

        # First try to find client-specific threshold if we have a client_id
        authority_limit = None
        if client_id:
            # Try to get client-specific authority threshold
            client_threshold = self.db.scalar(
                select(ClientAuthorityThreshold).where(
                    ClientAuthorityThreshold.client_id == client_id,
                    ClientAuthorityThreshold.authority_role == self.current_user.authority_role,
                    ClientAuthorityThreshold.is_deleted == False,
                )
            )

            if client_threshold:
                logger.debug(
                    f"Using client-specific authority threshold for client {client_id}",
                    extra={
                        "user": self.current_user.email,
                        "authority_role": str(self.current_user.authority_role),
                        "client_id": str(client_id),
                        "reserve_limit": str(client_threshold.reserve_limit),
                    },
                )
                authority_limit = client_threshold.reserve_limit

        # If no client-specific threshold found, fall back to global authority level
        if authority_limit is None:
            # Get global authority level for user's authority role
            authority = self.db.scalar(
                select(AuthorityLevel).where(AuthorityLevel.role == self.current_user.authority_role)
            )
            if not authority:
                logger.error(
                    f"No authority level defined for authority role {self.current_user.authority_role}",
                    extra={
                        "user": self.current_user.email,
                        "authority_role": str(self.current_user.authority_role),
                    },
                )
                raise AuthorizationError(
                    f"No authority level defined for authority role {self.current_user.authority_role}"
                )
            authority_limit = authority.reserve_limit

        # Check reserve limit - ensure we're comparing Decimal to Decimal
        if not isinstance(amount, Decimal):
            amount = Decimal(str(amount))

        # Debug information to check the comparison
        logger.debug(
            f"Authority check: Amount: {amount} (type: {type(amount)}), Limit: {authority_limit} (type: {type(authority_limit)})",
            extra={
                "amount_value": str(amount),
                "amount_type": str(type(amount)),
                "limit_value": str(authority_limit),
                "limit_type": str(type(authority_limit)),
                "comparison_result": str(amount > authority_limit),
            },
        )

        if amount > authority_limit:
            error_msg = f"Amount {amount} exceeds user's reserve limit of {authority_limit}"
            logger.error(
                error_msg,
                extra={
                    "user": self.current_user.email,
                    "authority_role": str(self.current_user.authority_role),
                    "amount": str(amount),
                    "reserve_limit": str(authority_limit),
                    "client_id": str(client_id) if client_id else None,
                },
            )
            # Ensure we're not swallowing this exception
            raise AuthorizationError(error_msg)

        logger.debug(
            f"Authority validated for amount {amount}",
            extra={
                "user": self.current_user.email,
                "authority_role": str(self.current_user.authority_role),
                "amount": str(amount),
                "reserve_limit": str(authority_limit),
                "client_id": str(client_id) if client_id else None,
            },
        )

    def _update_timestamps(self, financials: ClaimFinancials, is_reserve_change: bool = False) -> None:
        """Update last_reserve_change or last_payment_date."""
        if is_reserve_change:
            financials.last_reserve_change = datetime.utcnow()
            logger.debug(
                f"Updated last_reserve_change for financials {financials.id}",
                extra={
                    "financials_id": str(financials.id),
                    "claim_id": str(financials.claim_id),
                    "timestamp": str(financials.last_reserve_change),
                },
            )
        else:
            financials.last_payment_date = datetime.utcnow()
            logger.debug(
                f"Updated last_payment_date for financials {financials.id}",
                extra={
                    "financials_id": str(financials.id),
                    "claim_id": str(financials.claim_id),
                    "timestamp": str(financials.last_payment_date),
                },
            )

    def _create_reserve_history(
        self,
        financials: ClaimFinancials,
        reserve_type: ReserveType,
        previous_amount: Decimal,
        new_amount: Decimal,
        notes: Optional[str],
    ) -> None:
        """Create reserve history entry."""
        history = ReserveHistory(
            claim_id=financials.claim_id,
            financials_id=financials.id,
            reserve_type=reserve_type,
            previous_amount=previous_amount,
            new_amount=new_amount,
            changed_by_id=self.current_user.id if self.current_user else None,
            notes=notes,
        )
        self.db.add(history)
        logger.debug(
            f"Created reserve history for financials {financials.id}",
            extra={
                "financials_id": str(financials.id),
                "claim_id": str(financials.claim_id),
                "reserve_type": str(reserve_type),
                "previous_amount": str(previous_amount),
                "new_amount": str(new_amount),
            },
        )

    def get_total_reserves(self, financials: ClaimFinancials) -> Decimal:
        """Get total reserves across all types."""
        return financials.total_reserves

    def get_reserves_by_type(self, financials: ClaimFinancials) -> Dict[ReserveType, Decimal]:
        """Get reserves grouped by type."""
        return {reserve.reserve_type: reserve.amount for reserve in financials.reserves}

    def validate_reserves(self, claim_type: ClaimType, reserves: List[ClaimReserve]) -> None:
        """Validate reserves against configuration requirements."""
        # Get reserve configurations for claim type
        configs = self.db.scalars(
            select(ReserveConfiguration).where(ReserveConfiguration.claim_type == claim_type)
        ).all()

        # Create lookup of provided reserves
        provided_reserves = {r.reserve_type: r.amount for r in reserves}

        # Check required reserves
        for config in configs:
            if config.is_required and config.reserve_type not in provided_reserves:
                raise ValidationError(f"Required reserve type {config.reserve_type} not provided")

            if config.minimum_amount and config.reserve_type in provided_reserves:
                if provided_reserves[config.reserve_type] < config.minimum_amount:
                    raise ValidationError(
                        f"Reserve amount {provided_reserves[config.reserve_type]} for {config.reserve_type} "
                        f"is below minimum requirement of {config.minimum_amount}"
                    )

    def _get_claim(
        self, claim_identifier: Union[UUID, str], required_permission: Optional[str] = None
    ) -> Optional[BaseClaim]:
        """Internal helper to get claim and check base view + optional additional permission."""
        # Use injected claim_service
        # claim_service = BaseClaimService(self.db, self.current_user)

        # Try resolving by ID or number, BaseClaimService handles basic view permission
        claim = None
        if isinstance(claim_identifier, UUID):
            claim = self.claim_service.get_claim_by_id(claim_identifier)
        elif isinstance(claim_identifier, str):
            claim = self.claim_service.get_claim_by_number(claim_identifier)

        if not claim:
            # Let BaseClaimService raise NotFound or AuthorizationError if appropriate
            # Or if identifier type was wrong
            raise NotFoundError(f"Claim {claim_identifier} not found or access denied")

        # If an additional permission is required, check it now
        if required_permission:
            self.check_permission(required_permission, resource_type="claim", resource_id=claim.id)

        return claim

    def get_financials(self, claim_id: Union[UUID, str]) -> Optional[ClaimFinancials]:
        """Get financials for a claim by ID or claim number."""
        # Check permission to view financials for this claim
        claim = self._get_claim(claim_id, required_permission="VIEW_CLAIM_FINANCIALS")

        stmt = select(ClaimFinancials).where(ClaimFinancials.claim_id == claim.id)
        return self.db.scalar(stmt)

    def get_reserve_history(self, claim_id: Union[UUID, str]) -> List[ReserveHistory]:
        """Get reserve history for a claim."""
        # Check permission to view financials for this claim
        claim = self._get_claim(claim_id, required_permission="VIEW_CLAIM_FINANCIALS")

        stmt = (
            select(ReserveHistory).where(ReserveHistory.claim_id == claim.id).order_by(ReserveHistory.changed_at.desc())
        )
        history_entries = list(self.db.scalars(stmt))

        # Add claim_number to each reserve history entry
        for entry in history_entries:
            entry.claim_number = claim.claim_number

        return history_entries

    def create_financials(self, claim_id: Union[UUID, str], data: ClaimFinancialsCreate) -> ClaimFinancials:
        """Create financial details for a claim."""
        with self._transaction():
            # Check permission to set initial reserves for this claim
            claim = self._get_claim(claim_id, required_permission="SET_INITIAL_RESERVE")

            # Check if financials already exist
            existing = self.db.scalar(select(ClaimFinancials).where(ClaimFinancials.claim_id == claim.id))
            if existing:
                raise ValidationError(f"Financials already exist for claim {claim_id}")

            logger.debug(
                f"Creating financials for claim {claim_id}",
                extra={
                    "data": json.loads(json.dumps(data.model_dump(), cls=CustomJSONEncoder)),
                    "claim_id": str(claim.id),
                },
            )

            # --- Auto-create required reserves --- #
            required_configs = self.db.scalars(
                select(ReserveConfiguration).where(
                    ReserveConfiguration.claim_type == claim.type,
                    ReserveConfiguration.is_required == True,
                    ReserveConfiguration.is_deleted == False,
                )
            ).all()

            initial_reserves = []
            for config in required_configs:
                initial_amount = config.minimum_amount if config.minimum_amount is not None else Decimal("0.00")
                initial_reserves.append(ClaimReserve(reserve_type=config.reserve_type, amount=initial_amount))
                logger.debug(
                    f"Auto-creating required reserve {config.reserve_type} with amount {initial_amount}",
                    extra={"claim_id": str(claim.id), "reserve_type": str(config.reserve_type)},
                )
            # ------------------------------------ #

            # Use auto-created reserves. If data.reserves is ever expected, merge logic would be needed here.
            reserves = initial_reserves
            # Old logic: reserves = [ClaimReserve(reserve_type=r.reserve_type, amount=r.amount) for r in data.reserves]

            # Validate reserves against configuration
            self.validate_reserves(claim.type, reserves)

            # Validate authority for total reserve amount
            total_reserves = sum(r.amount for r in reserves)
            self._validate_authority(total_reserves, claim.id)

            # Create financials
            financials = ClaimFinancials(claim_id=claim.id, **data.model_dump(exclude={"reserves"}, exclude_unset=True))
            financials.reserves = reserves

            # Set initial timestamps
            self._update_timestamps(financials, is_reserve_change=True)

            # Validate financials for business rules
            try:
                financials.validate_financials()
            except ValueError as e:
                raise ValidationError(str(e))

            self.db.add(financials)
            self.db.flush()  # Flush to get IDs before potential refresh
            self.db.refresh(financials)

            # Create history for initial reserves
            for reserve in financials.reserves:
                self._create_reserve_history(
                    financials=financials,
                    reserve_type=reserve.reserve_type,
                    previous_amount=Decimal("0.00"),
                    new_amount=reserve.amount,
                    notes="Initial reserve setting",
                )

            # Create audit entries for the initial financials and reserves
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Log the financials creation
            financials_data = {
                "estimated_value": str(financials.estimated_value),
                "currency": financials.currency,
                "deductible_amount": str(financials.deductible_amount) if financials.deductible_amount else None,
                "coverage_limit": str(financials.coverage_limit) if financials.coverage_limit else None,
                "total_reserves": str(total_reserves),
            }

            audit_helper.log_create(
                entity_type="FINANCIAL",
                entity_id=financials.id,
                data=financials_data,
                description=f"Created initial financials with estimated value {financials.estimated_value}",
            )

            # Log each initial reserve
            for reserve in financials.reserves:
                reserve_data = {"reserve_type": reserve.reserve_type.value, "amount": str(reserve.amount)}

                audit_helper.log_create(
                    entity_type="RESERVE",
                    entity_id=reserve.id,
                    data=reserve_data,
                    description=f"Created initial {reserve.reserve_type.value} reserve of {reserve.amount}",
                )

            logger.info(
                f"Successfully created financials for claim {claim.claim_number}",
                extra={"claim_id": str(claim.id), "financials_id": str(financials.id)},
            )

        # Refresh again outside transaction if needed, though usually not necessary after commit
        # self.db.refresh(financials)
        return financials

    def update_financials(self, claim_id: Union[UUID, str], data: ClaimFinancialsUpdate) -> ClaimFinancials:
        """Update financial details (excluding reserves)."""
        with self._transaction():
            # Permission check should happen based on what fields are allowed to be updated
            # Using generic EDIT_CLAIMS for now, needs refinement
            claim = self._get_claim(claim_id, required_permission="EDIT_CLAIMS")

            financials = self.db.scalar(select(ClaimFinancials).where(ClaimFinancials.claim_id == claim.id))
            if not financials:
                raise NotFoundError(f"Financials not found for claim {claim_id}")

            logger.debug(
                f"Updating financials for claim {claim_id}",
                extra={
                    "data": json.loads(json.dumps(data.model_dump(), cls=CustomJSONEncoder)),
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                },
            )

            # Update fields
            update_data = data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(financials, key, value)

            # Validate financials for business rules
            try:
                financials.validate_financials()
            except ValueError as e:
                raise ValidationError(str(e))

            self.db.flush()
            self.db.refresh(financials)
            logger.info(
                f"Successfully updated financials for claim {claim.claim_number}",
                extra={"claim_id": str(claim.id), "financials_id": str(financials.id)},
            )

        return financials

    def update_reserve(
        self,
        claim_id: Union[UUID, str],
        reserve_type: ReserveType,
        amount: Decimal,
        notes: Optional[str] = None,
    ) -> ClaimFinancials:
        """Update a specific reserve type for a claim."""
        with self._transaction():
            claim = self._get_claim(claim_id, required_permission="UPDATE_RESERVES")

            # Ensure amount is a Decimal
            if not isinstance(amount, Decimal):
                amount = Decimal(str(amount))

            # Validate authority for the new reserve amount BEFORE making any changes
            # This is critical to enforce authority limits properly
            logger.debug(
                f"Validating authority for reserve update: {reserve_type}={amount}",
                extra={
                    "claim_id": str(claim.id),
                    "reserve_type": str(reserve_type),
                    "amount": str(amount),
                    "user_email": self.current_user.email if self.current_user else None,
                    "authority_role": str(self.current_user.authority_role) if self.current_user else None,
                },
            )

            try:
                # This should raise AuthorizationError if amount exceeds threshold
                self._validate_authority(amount, claim.id)
            except AuthorizationError as e:
                logger.error(
                    f"Authority validation failed for reserve update: {e}",
                    extra={
                        "claim_id": str(claim.id),
                        "reserve_type": str(reserve_type),
                        "amount": str(amount),
                        "user_email": self.current_user.email if self.current_user else None,
                        "authority_role": str(self.current_user.authority_role) if self.current_user else None,
                    },
                )
                # Re-raise the exception to ensure it propagates to the API
                raise

            financials = self.db.scalar(
                select(ClaimFinancials)
                .where(ClaimFinancials.claim_id == claim.id)
                .options(selectinload(ClaimFinancials.reserves))
            )
            if not financials:
                raise NotFoundError(f"Financials not found for claim {claim_id}")

            logger.debug(
                f"Updating {reserve_type} reserve for claim {claim_id} to {amount}",
                extra={
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                    "reserve_type": str(reserve_type),
                    "new_amount": str(amount),
                },
            )

            # Find existing reserve or create new one
            existing_reserve = next((r for r in financials.reserves if r.reserve_type == reserve_type), None)

            previous_amount = Decimal("0.00")
            previous_data = {}
            new_data = {}
            change_type = None
            entity_id = None

            if existing_reserve:
                previous_amount = existing_reserve.amount
                previous_data = {"amount": str(previous_amount), "reserve_type": existing_reserve.reserve_type.value}
                existing_reserve.amount = amount
                new_data = {"amount": str(amount), "reserve_type": existing_reserve.reserve_type.value}
                change_type = "UPDATE"
                entity_id = existing_reserve.id

                logger.debug(
                    f"Updated existing reserve {reserve_type} from {previous_amount} to {amount}",
                    extra={
                        "claim_id": str(claim.id),
                        "financials_id": str(financials.id),
                        "reserve_id": str(existing_reserve.id),
                    },
                )
            else:
                # Check if this reserve type is valid for the claim type
                configs = self.db.scalars(
                    select(ReserveConfiguration)
                    .where(ReserveConfiguration.claim_type == claim.type)
                    .where(ReserveConfiguration.reserve_type == reserve_type)
                ).all()
                if not configs:
                    raise ValidationError(f"Reserve type {reserve_type} is not valid for claim type {claim.type}")

                new_reserve = ClaimReserve(reserve_type=reserve_type, amount=amount)
                financials.reserves.append(new_reserve)
                self.db.add(new_reserve)  # Add explicitly if needed, though relationship should handle it

                # We need to flush to get the ID for the audit entry
                self.db.flush()

                new_data = {"amount": str(amount), "reserve_type": reserve_type.value}
                change_type = "CREATE"
                entity_id = new_reserve.id

                logger.debug(
                    f"Created new reserve {reserve_type} with amount {amount}",
                    extra={"claim_id": str(claim.id), "financials_id": str(financials.id)},
                )

            # Update timestamp
            self._update_timestamps(financials, is_reserve_change=True)

            # Create history record
            self._create_reserve_history(
                financials=financials,
                reserve_type=reserve_type,
                previous_amount=previous_amount,
                new_amount=amount,
                notes=notes,
            )

            # Update estimated_value to match the new total_reserves
            # This ensures the estimated_value displayed in the UI matches the sum of reserves
            previous_estimated_value = financials.estimated_value
            financials.estimated_value = financials.total_reserves

            # Create audit entry for reserve change
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Create audit entry for the reserve change
            description = f"{change_type.capitalize()} {reserve_type.value} reserve: {previous_amount} → {amount}"
            if notes:
                description += f" - Note: {notes}"

            # Log the reserve change
            audit_helper.create_entry(
                entity_type="RESERVE",
                change_type=change_type,
                entity_id=entity_id,
                field_name="amount",
                previous_value=previous_data,
                new_value=new_data,
                description=description,
            )

            # Log the estimated value change if it's different
            if previous_estimated_value != financials.estimated_value:
                audit_helper.create_entry(
                    entity_type="FINANCIAL",
                    change_type="UPDATE",
                    entity_id=financials.id,
                    field_name="estimated_value",
                    previous_value={"estimated_value": str(previous_estimated_value)},
                    new_value={"estimated_value": str(financials.estimated_value)},
                    description=f"Estimated value updated from {previous_estimated_value} to {financials.estimated_value}",
                )

            logger.debug(
                f"Updated estimated_value to match total reserves: {financials.estimated_value}",
                extra={
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                    "estimated_value": str(financials.estimated_value),
                },
            )

            # Validate financials for business rules
            try:
                financials.validate_financials()
            except ValueError as e:
                raise ValidationError(str(e))

            self.db.flush()
            self.db.refresh(financials)
            logger.info(
                f"Successfully updated reserve {reserve_type} for claim {claim.claim_number}",
                extra={
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                    "reserve_type": str(reserve_type),
                    "new_amount": str(amount),
                },
            )

        return financials

    def add_payment(self, claim_id: Union[UUID, str], payment_data: PaymentCreate) -> Payment:
        """Add a payment to the claim."""
        with self._transaction():
            claim = self._get_claim(claim_id, required_permission="PROCESS_PAYMENTS")

            financials = self.db.scalar(
                select(ClaimFinancials)
                .where(ClaimFinancials.claim_id == claim.id)
                .options(selectinload(ClaimFinancials.reserves))
            )
            if not financials:
                raise NotFoundError(f"Financials not found for claim {claim_id}")

            logger.debug(
                f"Adding payment to claim {claim_id}",
                extra={
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                    "payment_data": json.loads(json.dumps(payment_data.model_dump(), cls=CustomJSONEncoder)),
                    "user_id": str(self.current_user.id) if self.current_user else None,
                },
            )

            # Validate authority for the payment amount
            self._validate_authority(payment_data.amount, claim.id)

            # Create Payment record
            payment = Payment(
                financials_id=financials.id,
                payment_type=payment_data.payment_type,
                amount=payment_data.amount,
                payee=payment_data.payee,
                payment_date=payment_data.payment_date,
                notes=payment_data.notes,
                created_by_id=self.current_user.id,
            )

            # Store the previous values for audit
            previous_financial_data = {
                "indemnity_paid": str(financials.indemnity_paid or 0),
                "expense_paid": str(financials.expense_paid or 0),
                "defense_paid": str(financials.defense_paid or 0),
            }

            # Update aggregate fields on ClaimFinancials
            if payment.payment_type == PaymentType.INDEMNITY:
                financials.indemnity_paid = (financials.indemnity_paid or Decimal(0)) + payment.amount
            elif payment.payment_type == PaymentType.EXPENSE:
                financials.expense_paid = (financials.expense_paid or Decimal(0)) + payment.amount
            elif payment.payment_type == PaymentType.DEFENSE:
                financials.defense_paid = (financials.defense_paid or Decimal(0)) + payment.amount

            # Store the updated values for audit
            updated_financial_data = {
                "indemnity_paid": str(financials.indemnity_paid or 0),
                "expense_paid": str(financials.expense_paid or 0),
                "defense_paid": str(financials.defense_paid or 0),
            }

            # Update last payment timestamp
            financials.last_payment_date = max(
                financials.last_payment_date or datetime.min,
                payment.payment_date,  # Use payment_date from request
            )
            self._update_timestamps(financials, is_reserve_change=False)  # Updates last_payment_date to now()

            # Validate financials after update
            try:
                financials.validate_financials()
            except ValueError as e:
                raise ValidationError(str(e))

            # Add payment to session (financials update handled via object modification)
            self.db.add(payment)

            # Flush to get the payment ID
            self.db.flush()

            # Create audit entries
            audit_helper = AuditHelper(claim, self.current_user.id if self.current_user else None)

            # Create audit entry for the new payment
            payment_data_dict = {
                "payment_type": payment.payment_type.value,
                "amount": str(payment.amount),
                "payee": payment.payee,
                "payment_date": payment.payment_date.isoformat(),
                "notes": payment.notes,
            }

            # Log the payment creation
            audit_helper.log_create(
                entity_type="PAYMENT",
                entity_id=payment.id,
                data=payment_data_dict,
                description=f"Added {payment.payment_type.value} payment of {payment.amount} to {payment.payee}",
            )

            # Log the financials update if any values changed
            changed_fields = {
                k: updated_financial_data[k]
                for k in updated_financial_data
                if previous_financial_data.get(k) != updated_financial_data.get(k)
            }

            if changed_fields:
                previous_values = {k: previous_financial_data[k] for k in changed_fields}

                audit_helper.log_update(
                    entity_type="FINANCIAL",
                    entity_id=financials.id,
                    previous_data=previous_values,
                    new_data=changed_fields,
                    description=f"Updated financials after {payment.payment_type.value} payment of {payment.amount}",
                )

            self.db.refresh(payment)
            logger.info(
                f"Successfully added payment to claim {claim.claim_number}",
                extra={
                    "claim_id": str(claim.id),
                    "financials_id": str(financials.id),
                    "payment_id": str(payment.id),
                    "payment_type": str(payment.payment_type),
                    "amount": str(payment.amount),
                },
            )

        return payment

    def list_payments(
        self,
        claim_id: Union[UUID, str],
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[Payment], int]:
        """List payments for a specific claim."""
        # Get claim and check view financials permission
        claim = self._get_claim(claim_id, required_permission="VIEW_CLAIM_FINANCIALS")

        # Get associated financials record ID
        financials = self.db.scalar(select(ClaimFinancials.id).where(ClaimFinancials.claim_id == claim.id))
        if not financials:
            # If no financials, there are no payments
            return [], 0

        financials_id = financials  # scalar returns the ID directly here

        # Build base query for payments linked to this financials record
        payment_query = (
            select(Payment)
            .where(Payment.financials_id == financials_id)
            .order_by(Payment.payment_date.desc())  # Order by date, most recent first
        )

        # Count total payments for this financials record - FIXED to avoid cartesian product
        count_query = select(func.count()).where(Payment.financials_id == financials_id)
        total = self.db.scalar(count_query) or 0

        # Apply pagination
        paginated_query = payment_query.offset(skip).limit(limit)

        # Execute query
        payments = list(self.db.scalars(paginated_query))

        return payments, total

    @staticmethod
    def _is_uuid(value: str) -> bool:
        """Check if a string is a valid UUID."""
        try:
            UUID(value)
            return True
        except ValueError:
            return False
