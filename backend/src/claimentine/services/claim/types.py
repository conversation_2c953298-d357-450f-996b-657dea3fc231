"""Service for specific claim types."""

import logging
from typing import Any, Dict, List, Optional, Set, Tuple, Type, Union
from uuid import UUID

from fastapi import HTTPException, status
from sqlalchemy import and_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session, joinedload, selectinload

from claimentine.core.exceptions import NotFoundError, ValidationError
from claimentine.models.claim.base import BaseClaim, ClaimType
from claimentine.models.claim.damage import DamagedPropertyAsset, DamageInstance
from claimentine.models.claim.details import (
    AutoDetails,
    AutoIncidentType,
    AutoPropertyDamage,
    BodilyInjuryDetails,
    GeneralLiabilityDetails,
    InjuredPerson,
    InjuredPersonType,
    Injury,
    PropertyDetails,
)
from claimentine.models.claim.types import AutoClaim, GeneralLiabilityClaim, PropertyClaim
from claimentine.models.document import Document
from claimentine.models.fnol import FNOL
from claimentine.models.user import User
from claimentine.schemas.claim.base import CreateClaimSchema, UpdateClaimSchema
from claimentine.schemas.claim.damage import (
    DamagedPropertyAssetCreate,
    DamagedPropertyAssetResponse,
    DamagedPropertyAssetUpdate,
    DamageInstanceCreate,
    DamageInstanceResponse,
    DamageInstanceUpdate,
)
from claimentine.schemas.claim.details import (
    AutoDetailsCreate,
    AutoDetailsResponse,
    AutoDetailsUpdate,
    AutoPropertyDamageCreate,
    AutoPropertyDamageResponse,
    AutoPropertyDamageUpdate,
    BodilyInjuryDetailsCreate,
    BodilyInjuryDetailsResponse,
    BodilyInjuryDetailsUpdate,
    GeneralLiabilityDetailsCreate,
    GeneralLiabilityDetailsResponse,
    GeneralLiabilityDetailsUpdate,
    InjuredPersonCreate,
    InjuredPersonResponse,
    InjuredPersonUpdate,
    InjuryCreate,
    InjuryResponse,
    InjuryUpdate,
    PropertyDetailsCreate,
    PropertyDetailsResponse,
    PropertyDetailsUpdate,
)
from claimentine.schemas.claim.types import (
    AutoClaimCreate,
    AutoClaimUpdate,
    GeneralLiabilityClaimCreate,
    GeneralLiabilityClaimUpdate,
    PropertyClaimCreate,
    PropertyClaimUpdate,
)
from claimentine.services.claim.base import BaseClaimService

logger = logging.getLogger(__name__)


class AutoClaimService(BaseClaimService):
    """Service for managing auto claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    # --- Implement BaseClaimService Hooks ---

    def _get_details_field_name(self) -> Optional[str]:
        """Return the attribute name for auto claim details."""
        return "auto_details"

    def _get_detail_model_class(self) -> Optional[Type]:
        """Return the model class for auto claim details."""
        return AutoDetails

    def _extract_details_data(self, data: Union[CreateClaimSchema, UpdateClaimSchema]) -> Optional[Dict[str, Any]]:
        """Extract auto details data from the input schema."""
        if isinstance(data, (AutoClaimCreate, AutoClaimUpdate)) and data.auto_details:
            # Exclude nested relationship fields that will be handled separately
            auto_details_data = data.auto_details.model_dump(
                exclude_unset=True, exclude={"bodily_injury_details", "property_damage"}
            )

            return auto_details_data
        return None

    def _validate_details(self, claim: BaseClaim) -> None:
        """Validate auto-specific details."""
        if isinstance(claim, AutoClaim):
            claim.validate_auto_details()
        else:
            # This might indicate a logic error if called with the wrong claim type
            pass

    # --- Methods for bodily injury details ---

    def get_bodily_injury_details(self, claim_id: UUID) -> Optional[BodilyInjuryDetailsResponse]:
        """Get bodily injury details for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return None

        # Check edit permission for this specific claim
        self._check_view_claim_permission(claim)

        bodily_injury = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.auto_details_id == claim.auto_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if not bodily_injury:
            return None

        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def create_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Create bodily injury details for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        bodily_injury_data = data.model_dump(exclude_unset=True)
        bodily_injury = self._create_bodily_injury_details(claim.auto_details.id, bodily_injury_data)

        self.db.commit()
        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def update_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Update bodily injury details for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        bodily_injury_data = data.model_dump(exclude_unset=True)
        bodily_injury = self._update_bodily_injury_details(claim.auto_details.id, bodily_injury_data)

        self.db.commit()
        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def delete_bodily_injury_details(self, claim_id: UUID) -> None:
        """Delete bodily injury details for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing bodily injury details
        existing_details = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.auto_details_id == claim.auto_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if existing_details:
            # Soft delete by setting is_deleted flag
            existing_details.is_deleted = True
            self.db.commit()

    def _create_bodily_injury_details(self, auto_details_id, bodily_injury_data):
        """Create bodily injury details if provided."""
        if not bodily_injury_data:
            return None

        logger.debug(
            f"Creating new bodily injury details for Auto details ID {auto_details_id} with data: {bodily_injury_data}"
        )
        bodily_injury_details = BodilyInjuryDetails(auto_details_id=auto_details_id, **bodily_injury_data)
        self.db.add(bodily_injury_details)
        self.db.flush()
        self.db.commit()
        # Log the created details for debugging
        logger.debug(
            f"Created bodily injury details with ID {bodily_injury_details.id} for Auto details {auto_details_id}"
        )
        return bodily_injury_details

    def _update_bodily_injury_details(self, auto_details_id, bodily_injury_data):
        """Update existing bodily injury details or create if not present."""
        if not bodily_injury_data:
            logger.debug(f"No bodily injury data provided for Auto details ID {auto_details_id}")
            return None

        logger.debug(
            f"Updating bodily injury details for Auto details ID {auto_details_id} with data: {bodily_injury_data}"
        )

        # First check if bodily injury details exist
        existing_details = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.auto_details_id == auto_details_id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if existing_details:
            # Update existing details
            logger.debug(f"Found existing bodily injury details with ID {existing_details.id}")
            for key, value in bodily_injury_data.items():
                logger.debug(f"Setting {key}={value} on bodily injury details")
                setattr(existing_details, key, value)

            # Make sure to flush the session to persist changes
            self.db.flush()
            self.db.commit()
            return existing_details
        else:
            # Create new details if they don't exist
            logger.debug(f"No existing bodily injury details found, creating new entry")
            return self._create_bodily_injury_details(auto_details_id, bodily_injury_data)

    # --- Methods for property damage details ---

    def _create_property_damage_details(self, auto_details_id, property_damage_data):
        """Create property damage details if provided."""
        if not property_damage_data:
            return None

        logger.debug(
            f"Creating new property damage details for Auto details ID {auto_details_id} with data: {property_damage_data}"
        )
        property_damage_details = AutoPropertyDamage(auto_details_id=auto_details_id, **property_damage_data)
        self.db.add(property_damage_details)
        self.db.flush()
        self.db.commit()
        # Log the created details for debugging
        logger.debug(
            f"Created property damage details with ID {property_damage_details.id} for Auto details {auto_details_id}"
        )
        return property_damage_details

    def _update_property_damage_details(self, auto_details_id, property_damage_data):
        """Update property damage details if they exist, or create them if they don't."""
        if not property_damage_data:
            return None

        # Check if property damage details already exist
        existing_details = self.db.scalar(
            select(AutoPropertyDamage).where(AutoPropertyDamage.auto_details_id == auto_details_id)
        )

        if not existing_details:
            # Create new property damage details
            return self._create_property_damage_details(auto_details_id, property_damage_data)
        else:
            # Update existing property damage details
            for key, value in property_damage_data.items():
                setattr(existing_details, key, value)
            self.db.flush()
            self.db.commit()
            return existing_details

    def create_claim(self, claim_data: AutoClaimCreate) -> AutoClaim:
        """Create a new auto claim with details and handle nested relationships."""
        claim = super().create_claim(claim_data)

        # Handle special nested data that needs separate creation
        if claim.auto_details:
            # Handle bodily injury details
            if (
                hasattr(claim_data.auto_details, "bodily_injury_details")
                and claim_data.auto_details.bodily_injury_details
            ):
                bodily_injury_data = claim_data.auto_details.bodily_injury_details.model_dump(exclude_unset=True)
                self._create_bodily_injury_details(claim.auto_details.id, bodily_injury_data)

            # Handle property damage details
            if hasattr(claim_data.auto_details, "property_damage") and claim_data.auto_details.property_damage:
                property_damage_data = claim_data.auto_details.property_damage.model_dump(exclude_unset=True)
                self._create_property_damage_details(claim.auto_details.id, property_damage_data)

        self.db.commit()
        self.db.refresh(claim)
        return claim

    def update_claim(
        self, claim_id: UUID, claim_data: AutoClaimUpdate, includes: Optional[Set[str]] = None
    ) -> AutoClaim:
        """Update an auto claim with details and handle nested relationships."""
        claim = super().update_claim(claim_id, claim_data)

        # Handle special nested data updates
        if claim.auto_details and isinstance(claim_data.auto_details, dict):
            # If auto_details is passed as a dictionary, convert it to proper object
            auto_details_update = AutoDetailsUpdate(**claim_data.auto_details)

            # Handle bodily injury details
            if hasattr(auto_details_update, "bodily_injury_details") and auto_details_update.bodily_injury_details:
                bodily_injury_data = auto_details_update.bodily_injury_details.model_dump(exclude_unset=True)
                self._update_bodily_injury_details(claim.auto_details.id, bodily_injury_data)

            # Handle property damage details
            if hasattr(auto_details_update, "property_damage") and auto_details_update.property_damage:
                property_damage_data = auto_details_update.property_damage.model_dump(exclude_unset=True)
                self._update_property_damage_details(claim.auto_details.id, property_damage_data)
        elif claim.auto_details and isinstance(claim_data.auto_details, AutoDetailsUpdate):
            # Handle bodily injury details
            if (
                hasattr(claim_data.auto_details, "bodily_injury_details")
                and claim_data.auto_details.bodily_injury_details
            ):
                bodily_injury_data = claim_data.auto_details.bodily_injury_details.model_dump(exclude_unset=True)
                self._update_bodily_injury_details(claim.auto_details.id, bodily_injury_data)

            # Handle property damage details
            if hasattr(claim_data.auto_details, "property_damage") and claim_data.auto_details.property_damage:
                property_damage_data = claim_data.auto_details.property_damage.model_dump(exclude_unset=True)
                self._update_property_damage_details(claim.auto_details.id, property_damage_data)

        self.db.commit()
        self.db.refresh(claim)
        return claim

    # New methods for injured persons management

    def get_injured_persons(self, claim_id: UUID) -> List[InjuredPersonResponse]:
        """Get all injured persons for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        injured_persons = self.db.scalars(
            select(InjuredPerson)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
            .order_by(InjuredPerson.created_at)
        ).all()

        result = []
        for person in injured_persons:
            # Get injuries for this person
            injuries = self.db.scalars(
                select(Injury)
                .where(Injury.injured_person_id == person.id)
                .where(Injury.is_deleted == False)
                .order_by(Injury.created_at)
            ).all()

            # Convert to response schema with injuries included
            person_dict = InjuredPersonResponse.model_validate(person).model_dump()
            person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]
            result.append(InjuredPersonResponse.model_validate(person_dict))

        return result

    def get_injured_person(self, claim_id: UUID, person_id: UUID) -> Optional[InjuredPersonResponse]:
        """Get a specific injured person for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return None

        # Get injuries for this person
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person.id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        # Convert to response schema with injuries included
        person_dict = InjuredPersonResponse.model_validate(person).model_dump()
        person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]

        return InjuredPersonResponse.model_validate(person_dict)

    def create_injured_person(self, claim_id: UUID, data: InjuredPersonCreate) -> InjuredPersonResponse:
        """Create an injured person for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Create new injured person
        person_data = data.model_dump(exclude_unset=True)
        person = InjuredPerson(auto_details_id=claim.auto_details.id, **person_data)
        self.db.add(person)
        self.db.flush()
        self.db.commit()

        logger.info(f"Created injured person with ID {person.id} for claim {claim_id}")

        return InjuredPersonResponse.model_validate(person)

    def update_injured_person(
        self, claim_id: UUID, person_id: UUID, data: InjuredPersonUpdate
    ) -> InjuredPersonResponse:
        """Update an injured person for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing person
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for claim {claim_id}")

        # Update person data
        person_data = data.model_dump(exclude_unset=True)
        for key, value in person_data.items():
            setattr(person, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated injured person {person_id} for claim {claim_id}")

        # Get injuries for this person to include in response
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person.id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        # Convert to response schema with injuries included
        person_dict = InjuredPersonResponse.model_validate(person).model_dump()
        person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]

        return InjuredPersonResponse.model_validate(person_dict)

    def delete_injured_person(self, claim_id: UUID, person_id: UUID) -> None:
        """Delete an injured person for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing person
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if person:
            # Soft delete by setting is_deleted flag
            person.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted injured person {person_id} for claim {claim_id}")

            # Also soft-delete all associated injuries
            injuries = self.db.scalars(
                select(Injury).where(Injury.injured_person_id == person.id).where(Injury.is_deleted == False)
            ).all()

            for injury in injuries:
                injury.is_deleted = True
                logger.info(f"Soft-deleted injury {injury.id} as part of person deletion")

            self.db.commit()

    # New methods for injuries management

    def get_injuries(self, claim_id: UUID, person_id: UUID) -> List[InjuryResponse]:
        """Get all injuries for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return []

        # Get all injuries for this person
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        return [InjuryResponse.model_validate(injury) for injury in injuries]

    def get_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> Optional[InjuryResponse]:
        """Get a specific injury for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return None

        # Get the specific injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if not injury:
            return None

        return InjuryResponse.model_validate(injury)

    def create_injury(self, claim_id: UUID, person_id: UUID, data: InjuryCreate) -> InjuryResponse:
        """Create an injury for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for claim {claim_id}")

        # Create new injury
        injury_data = data.model_dump(exclude_unset=True)
        injury = Injury(injured_person_id=person_id, **injury_data)
        self.db.add(injury)
        self.db.flush()
        self.db.commit()

        logger.info(f"Created injury with ID {injury.id} for person {person_id}")
        return InjuryResponse.model_validate(injury)

    def update_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID, data: InjuryUpdate) -> InjuryResponse:
        """Update an injury for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for claim {claim_id}")

        # Find the injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if not injury:
            raise NotFoundError(f"Injury {injury_id} not found for person {person_id}")

        # Update injury data
        injury_data = data.model_dump(exclude_unset=True)
        for key, value in injury_data.items():
            setattr(injury, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated injury {injury_id} for person {person_id}")
        return InjuryResponse.model_validate(injury)

    def delete_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> None:
        """Delete an injury for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify injured person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.auto_details_id == claim.auto_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return

        # Find existing injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if injury:
            # Soft delete by setting is_deleted flag
            injury.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted injury {injury_id} for injured person {person_id} in claim {claim_id}")

    # Methods for damaged property assets management

    def get_damaged_property_assets(self, claim_id: UUID) -> List[DamagedPropertyAssetResponse]:
        """Get all damaged property assets for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        assets = self.db.scalars(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
            .order_by(DamagedPropertyAsset.created_at)
        ).all()

        result = []
        for asset in assets:
            # Get damage instances for this asset
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
                .order_by(DamageInstance.created_at)
            ).all()

            # Convert to response schema with damage instances included
            asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
            asset_dict["damage_instances"] = [
                DamageInstanceResponse.model_validate(instance) for instance in damage_instances
            ]
            result.append(DamagedPropertyAssetResponse.model_validate(asset_dict))

        return result

    def get_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> Optional[DamagedPropertyAssetResponse]:
        """Get a specific damaged property asset for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get damage instances for this asset
        damage_instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset.id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        # Convert to response schema with damage instances included
        asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
        asset_dict["damage_instances"] = [
            DamageInstanceResponse.model_validate(instance) for instance in damage_instances
        ]

        return DamagedPropertyAssetResponse.model_validate(asset_dict)

    def create_damaged_property_asset(
        self, claim_id: UUID, data: DamagedPropertyAssetCreate
    ) -> DamagedPropertyAssetResponse:
        """Create a damaged property asset for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Extract damage instances data if present
        damage_instances_data = None
        if hasattr(data, "damage_instances") and data.damage_instances:
            damage_instances_data = [instance.model_dump(exclude_unset=True) for instance in data.damage_instances]

        # Create new damaged property asset
        asset_data = data.model_dump(exclude={"damage_instances"}, exclude_unset=True)
        asset = DamagedPropertyAsset(auto_details_id=claim.auto_details.id, **asset_data)
        self.db.add(asset)
        self.db.flush()

        # Create damage instances if they were provided
        if damage_instances_data:
            for instance_data in damage_instances_data:
                instance = DamageInstance(damaged_property_asset_id=asset.id, **instance_data)
                self.db.add(instance)

        self.db.commit()
        logger.info(f"Created damaged property asset with ID {asset.id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset.id)

    def update_damaged_property_asset(
        self, claim_id: UUID, asset_id: UUID, data: DamagedPropertyAssetUpdate
    ) -> DamagedPropertyAssetResponse:
        """Update a damaged property asset for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Update asset data
        asset_data = data.model_dump(exclude_unset=True)
        for key, value in asset_data.items():
            if key != "damage_instances":  # Skip damage instances, they're updated separately
                setattr(asset, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated damaged property asset {asset_id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset_id)

    def delete_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> None:
        """Delete a damaged property asset for an auto claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if asset:
            # Soft delete by setting is_deleted flag
            asset.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted damaged property asset {asset_id} for claim {claim_id}")

            # Also soft-delete all associated damage instances
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
            ).all()

            for instance in damage_instances:
                instance.is_deleted = True
                logger.info(f"Soft-deleted damage instance {instance.id} as part of asset deletion")

            self.db.commit()

    # Methods for damage instances management

    def get_damage_instances(self, claim_id: UUID, asset_id: UUID) -> List[DamageInstanceResponse]:
        """Get all damage instances for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return []

        # Get all damage instances for this asset
        instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        return [DamageInstanceResponse.model_validate(instance) for instance in instances]

    def get_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID
    ) -> Optional[DamageInstanceResponse]:
        """Get a specific damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get the specific damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if not instance:
            return None

        return DamageInstanceResponse.model_validate(instance)

    def create_damage_instance(
        self, claim_id: UUID, asset_id: UUID, data: DamageInstanceCreate
    ) -> DamageInstanceResponse:
        """Create a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Create new damage instance
        instance_data = data.model_dump(exclude_unset=True)
        instance = DamageInstance(damaged_property_asset_id=asset_id, **instance_data)
        self.db.add(instance)
        self.db.commit()

        logger.info(f"Created damage instance with ID {instance.id} for asset {asset_id} in claim {claim_id}")

        return DamageInstanceResponse.model_validate(instance)

    def update_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID, data: DamageInstanceUpdate
    ) -> DamageInstanceResponse:
        """Update a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            raise NotFoundError(f"Auto details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Find existing damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if not instance:
            raise NotFoundError(f"Damage instance {instance_id} not found for asset {asset_id}")

        # Update instance data
        instance_data = data.model_dump(exclude_unset=True)
        for key, value in instance_data.items():
            setattr(instance, key, value)

        self.db.commit()
        logger.info(f"Updated damage instance {instance_id} for asset {asset_id} in claim {claim_id}")

        return DamageInstanceResponse.model_validate(instance)

    def delete_damage_instance(self, claim_id: UUID, asset_id: UUID, instance_id: UUID) -> None:
        """Delete a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.auto_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.auto_details_id == claim.auto_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return

        # Find existing damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if instance:
            # Soft delete by setting is_deleted flag
            instance.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted damage instance {instance_id} for asset {asset_id} in claim {claim_id}")


class PropertyClaimService(BaseClaimService):
    """Service for managing property claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    # --- Implement BaseClaimService Hooks ---

    def _get_details_field_name(self) -> Optional[str]:
        """Return the attribute name for property claim details."""
        return "property_details"

    def _get_detail_model_class(self) -> Optional[Type]:
        """Return the model class for property claim details."""
        return PropertyDetails

    def _extract_details_data(self, data: Union[CreateClaimSchema, UpdateClaimSchema]) -> Optional[Dict[str, Any]]:
        """Extract property details data from the input schema."""
        if isinstance(data, (PropertyClaimCreate, PropertyClaimUpdate)) and data.property_details:
            details_data = data.property_details.model_dump(exclude_unset=True)

            # Handle case-insensitive conversion for enum fields
            enum_fields = ["property_type", "damage_type", "property_state"]
            for field in enum_fields:
                if field in details_data and isinstance(details_data[field], str):
                    details_data[field] = details_data[field].upper()
                    logger.debug(f"Converted {field} to uppercase: {details_data[field]}")

            return details_data
        return None

    def _validate_details(self, claim: BaseClaim) -> None:
        """Validate property-specific details."""
        if isinstance(claim, PropertyClaim):
            claim.validate_property_details()
        else:
            # This might indicate a logic error if called with the wrong claim type
            pass

    # Methods for damaged property assets management

    def get_damaged_property_assets(self, claim_id: UUID) -> List[DamagedPropertyAssetResponse]:
        """Get all damaged property assets for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        assets = self.db.scalars(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
            .order_by(DamagedPropertyAsset.created_at)
        ).all()

        result = []
        for asset in assets:
            # Get damage instances for this asset
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
                .order_by(DamageInstance.created_at)
            ).all()

            # Convert to response schema with damage instances included
            asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
            asset_dict["damage_instances"] = [
                DamageInstanceResponse.model_validate(instance) for instance in damage_instances
            ]
            result.append(DamagedPropertyAssetResponse.model_validate(asset_dict))

        return result

    def get_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> Optional[DamagedPropertyAssetResponse]:
        """Get a specific damaged property asset for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get damage instances for this asset
        damage_instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset.id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        # Convert to response schema with damage instances included
        asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
        asset_dict["damage_instances"] = [
            DamageInstanceResponse.model_validate(instance) for instance in damage_instances
        ]

        return DamagedPropertyAssetResponse.model_validate(asset_dict)

    def create_damaged_property_asset(
        self, claim_id: UUID, data: DamagedPropertyAssetCreate
    ) -> DamagedPropertyAssetResponse:
        """Create a damaged property asset for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Extract damage instances data if present
        damage_instances_data = None
        if hasattr(data, "damage_instances") and data.damage_instances:
            damage_instances_data = [instance.model_dump(exclude_unset=True) for instance in data.damage_instances]

        # Create new damaged property asset
        asset_data = data.model_dump(exclude={"damage_instances"}, exclude_unset=True)
        asset = DamagedPropertyAsset(property_details_id=claim.property_details.id, **asset_data)
        self.db.add(asset)
        self.db.flush()

        # Create damage instances if they were provided
        if damage_instances_data:
            for instance_data in damage_instances_data:
                instance = DamageInstance(damaged_property_asset_id=asset.id, **instance_data)
                self.db.add(instance)

        self.db.commit()
        logger.info(f"Created damaged property asset with ID {asset.id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset.id)

    def update_damaged_property_asset(
        self, claim_id: UUID, asset_id: UUID, data: DamagedPropertyAssetUpdate
    ) -> DamagedPropertyAssetResponse:
        """Update a damaged property asset for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Update asset data
        asset_data = data.model_dump(exclude_unset=True)
        for key, value in asset_data.items():
            if key != "damage_instances":  # Skip damage instances, they're updated separately
                setattr(asset, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated damaged property asset {asset_id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset_id)

    def delete_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> None:
        """Delete a damaged property asset for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if asset:
            # Soft delete by setting is_deleted flag
            asset.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted damaged property asset {asset_id} for claim {claim_id}")

            # Also soft-delete all associated damage instances
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
            ).all()

            for instance in damage_instances:
                instance.is_deleted = True
                logger.info(f"Soft-deleted damage instance {instance.id} as part of asset deletion")

            self.db.commit()

    # Methods for damage instances management

    def get_damage_instances(self, claim_id: UUID, asset_id: UUID) -> List[DamageInstanceResponse]:
        """Get all damage instances for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return []

        # Get all damage instances for this asset
        instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        return [DamageInstanceResponse.model_validate(instance) for instance in instances]

    def get_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID
    ) -> Optional[DamageInstanceResponse]:
        """Get a specific damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get the specific damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if not instance:
            return None

        return DamageInstanceResponse.model_validate(instance)

    def create_damage_instance(
        self, claim_id: UUID, asset_id: UUID, data: DamageInstanceCreate
    ) -> DamageInstanceResponse:
        """Create a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Create new damage instance
        instance_data = data.model_dump(exclude_unset=True)
        instance = DamageInstance(damaged_property_asset_id=asset_id, **instance_data)
        self.db.add(instance)
        self.db.commit()

        logger.info(f"Created damage instance with ID {instance.id} for asset {asset_id} in claim {claim_id}")

        return DamageInstanceResponse.model_validate(instance)

    def update_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID, data: DamageInstanceUpdate
    ) -> DamageInstanceResponse:
        """Update a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Find existing damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if not instance:
            raise NotFoundError(f"Damage instance {instance_id} not found for asset {asset_id}")

        # Update instance data
        instance_data = data.model_dump(exclude_unset=True)
        for key, value in instance_data.items():
            setattr(instance, key, value)

        self.db.commit()
        logger.info(f"Updated damage instance {instance_id} for asset {asset_id} in claim {claim_id}")

        return DamageInstanceResponse.model_validate(instance)

    def delete_damage_instance(self, claim_id: UUID, asset_id: UUID, instance_id: UUID) -> None:
        """Delete a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.property_details_id == claim.property_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return

        # Find existing damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if instance:
            # Soft delete by setting is_deleted flag
            instance.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted damage instance {instance_id} for asset {asset_id} in claim {claim_id}")

    # Methods for bodily injury management

    def get_bodily_injury_details(self, claim_id: UUID) -> Optional[BodilyInjuryDetailsResponse]:
        """Get bodily injury details for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        bodily_injury = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.property_details_id == claim.property_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        return BodilyInjuryDetailsResponse.model_validate(bodily_injury) if bodily_injury else None

    def create_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Create bodily injury details for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Create new bodily injury details
        bodily_injury_data = data.model_dump(exclude_unset=True)
        bodily_injury = BodilyInjuryDetails(property_details_id=claim.property_details.id, **bodily_injury_data)
        self.db.add(bodily_injury)
        self.db.commit()
        logger.info(f"Created bodily injury details for property claim {claim_id}")

        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def update_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Update bodily injury details for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing bodily injury details
        bodily_injury = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.property_details_id == claim.property_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if not bodily_injury:
            raise NotFoundError(f"Bodily injury details not found for property claim {claim_id}")

        # Update bodily injury details
        bodily_injury_data = data.model_dump(exclude_unset=True)
        for key, value in bodily_injury_data.items():
            setattr(bodily_injury, key, value)

        self.db.commit()
        logger.info(f"Updated bodily injury details for property claim {claim_id}")

        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def delete_bodily_injury_details(self, claim_id: UUID) -> None:
        """Delete bodily injury details for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing bodily injury details
        bodily_injury = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.property_details_id == claim.property_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if bodily_injury:
            # Soft delete by setting is_deleted flag
            bodily_injury.is_deleted = True
            self.db.commit()
            logger.info(f"Deleted bodily injury details for property claim {claim_id}")

    def get_injured_persons(self, claim_id: UUID) -> List[InjuredPersonResponse]:
        """Get all injured persons for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        injured_persons = self.db.scalars(
            select(InjuredPerson)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
            .order_by(InjuredPerson.created_at)
        ).all()

        result = []
        for person in injured_persons:
            # Get injuries for this person
            injuries = self.db.scalars(
                select(Injury)
                .where(Injury.injured_person_id == person.id)
                .where(Injury.is_deleted == False)
                .order_by(Injury.created_at)
            ).all()

            # Convert to response schema with injuries included
            person_dict = InjuredPersonResponse.model_validate(person).model_dump()
            person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]
            result.append(InjuredPersonResponse.model_validate(person_dict))

        return result

    def get_injured_person(self, claim_id: UUID, person_id: UUID) -> Optional[InjuredPersonResponse]:
        """Get a specific injured person for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            return None

        # Get injuries for this person
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == injured_person.id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        # Convert to response schema with injuries included
        person_dict = InjuredPersonResponse.model_validate(injured_person).model_dump()
        person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]

        return InjuredPersonResponse.model_validate(person_dict)

    def create_injured_person(self, claim_id: UUID, data: InjuredPersonCreate) -> InjuredPersonResponse:
        """Create an injured person for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Create new injured person
        person_data = data.model_dump(exclude_unset=True)
        injured_person = InjuredPerson(property_details_id=claim.property_details.id, **person_data)
        self.db.add(injured_person)
        self.db.commit()
        logger.info(f"Created injured person with ID {injured_person.id} for property claim {claim_id}")

        return self.get_injured_person(claim_id, injured_person.id)

    def update_injured_person(
        self, claim_id: UUID, person_id: UUID, data: InjuredPersonUpdate
    ) -> InjuredPersonResponse:
        """Update an injured person for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing injured person
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            raise NotFoundError(f"Injured person {person_id} not found for property claim {claim_id}")

        # Update injured person data
        person_data = data.model_dump(exclude_unset=True)
        for key, value in person_data.items():
            setattr(injured_person, key, value)

        self.db.commit()
        logger.info(f"Updated injured person {person_id} for property claim {claim_id}")

        return self.get_injured_person(claim_id, person_id)

    def delete_injured_person(self, claim_id: UUID, person_id: UUID) -> None:
        """Delete an injured person for a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing injured person
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if injured_person:
            # Soft delete by setting is_deleted flag
            injured_person.is_deleted = True
            self.db.commit()
            logger.info(f"Deleted injured person {person_id} for property claim {claim_id}")

    def get_injuries(self, claim_id: UUID, person_id: UUID) -> List[InjuryResponse]:
        """Get all injuries for a specific injured person in a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the injured person belongs to this property claim
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            return []

        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        return [InjuryResponse.model_validate(injury) for injury in injuries]

    def get_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> Optional[InjuryResponse]:
        """Get a specific injury for an injured person in a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the injured person belongs to this property claim
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            return None

        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        return InjuryResponse.model_validate(injury) if injury else None

    def create_injury(self, claim_id: UUID, person_id: UUID, data: InjuryCreate) -> InjuryResponse:
        """Create an injury for an injured person in a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the injured person belongs to this property claim
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            raise NotFoundError(f"Injured person {person_id} not found for property claim {claim_id}")

        # Create new injury
        injury_data = data.model_dump(exclude_unset=True)
        injury = Injury(injured_person_id=person_id, **injury_data)
        self.db.add(injury)
        self.db.commit()
        logger.info(f"Created injury with ID {injury.id} for injured person {person_id} in property claim {claim_id}")

        return InjuryResponse.model_validate(injury)

    def update_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID, data: InjuryUpdate) -> InjuryResponse:
        """Update an injury for an injured person in a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            raise NotFoundError(f"Property details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the injured person belongs to this property claim
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            raise NotFoundError(f"Injured person {person_id} not found for property claim {claim_id}")

        # Find existing injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if not injury:
            raise NotFoundError(
                f"Injury {injury_id} not found for injured person {person_id} in property claim {claim_id}"
            )

        # Update injury data
        injury_data = data.model_dump(exclude_unset=True)
        for key, value in injury_data.items():
            setattr(injury, key, value)

        self.db.commit()
        logger.info(f"Updated injury {injury_id} for injured person {person_id} in property claim {claim_id}")

        return InjuryResponse.model_validate(injury)

    def delete_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> None:
        """Delete an injury for an injured person in a property claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.property_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the injured person belongs to this property claim
        injured_person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.property_details_id == claim.property_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not injured_person:
            return

        # Find existing injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if injury:
            # Soft delete by setting is_deleted flag
            injury.is_deleted = True
            self.db.commit()
            logger.info(f"Deleted injury {injury_id} for injured person {person_id} in property claim {claim_id}")


class GeneralLiabilityClaimService(BaseClaimService):
    """Service for managing general liability claims."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    # --- Implement BaseClaimService Hooks ---

    def _get_details_field_name(self) -> Optional[str]:
        """Return the attribute name for general liability claim details."""
        return "gl_details"

    def _get_detail_model_class(self) -> Optional[Type]:
        """Return the model class for general liability claim details."""
        return GeneralLiabilityDetails

    def _extract_details_data(self, data: Union[CreateClaimSchema, UpdateClaimSchema]) -> Optional[Dict[str, Any]]:
        """Extract general liability details data from the input schema."""
        if isinstance(data, (GeneralLiabilityClaimCreate, GeneralLiabilityClaimUpdate)) and data.gl_details:
            gl_details_data = data.gl_details.model_dump(exclude_unset=True)

            # Check if premises details are included in the update
            if hasattr(data.gl_details, "premises_details") and data.gl_details.premises_details:
                # Get premises data and store it separately
                premises_data = data.gl_details.premises_details.model_dump(exclude_unset=True)
                if premises_data:
                    gl_details_data["premises_details"] = premises_data

            # Similarly, check for products, operations, and advertising details
            if hasattr(data.gl_details, "products_details") and data.gl_details.products_details:
                products_data = data.gl_details.products_details.model_dump(exclude_unset=True)
                if products_data:
                    gl_details_data["products_details"] = products_data

            if hasattr(data.gl_details, "operations_details") and data.gl_details.operations_details:
                operations_data = data.gl_details.operations_details.model_dump(exclude_unset=True)
                if operations_data:
                    gl_details_data["operations_details"] = operations_data

            if hasattr(data.gl_details, "advertising_details") and data.gl_details.advertising_details:
                advertising_data = data.gl_details.advertising_details.model_dump(exclude_unset=True)
                if advertising_data:
                    gl_details_data["advertising_details"] = advertising_data

            # Check for bodily injury details
            if hasattr(data.gl_details, "bodily_injury_details") and data.gl_details.bodily_injury_details:
                # Get bodily injury data and store it separately
                bodily_injury_data = data.gl_details.bodily_injury_details.model_dump(exclude_unset=True)
                if bodily_injury_data:
                    gl_details_data["bodily_injury_details"] = bodily_injury_data

            return gl_details_data
        return None

    def _validate_details(self, claim: BaseClaim) -> None:
        """Validate general liability-specific details."""
        if isinstance(claim, GeneralLiabilityClaim):
            claim.validate_gl_details()
        else:
            # This might indicate a logic error if called with the wrong claim type
            pass

    # --- Helpers for sub-type details ---

    def _create_premises_details(self, gl_details_id, premises_data):
        """Create premises details if provided."""
        from claimentine.models.claim.details import PremisesLiabilityDetails

        if not premises_data:
            return None

        logger.debug(f"Creating new premises details for GL details ID {gl_details_id} with data: {premises_data}")
        premises_details = PremisesLiabilityDetails(gl_details_id=gl_details_id, **premises_data)
        self.db.add(premises_details)
        self.db.flush()
        self.db.commit()
        # Log the created premises details for debugging
        logger.debug(
            f"Created premises details with ID {premises_details.id} and owner_name '{premises_details.owner_name}'"
        )
        return premises_details

    def _update_premises_details(self, gl_details_id, premises_data):
        """Update existing premises details or create if not present."""
        from claimentine.models.claim.details import PremisesLiabilityDetails

        if not premises_data:
            logger.debug(f"No premises data provided for GL details ID {gl_details_id}")
            return None

        logger.debug(f"Updating premises details for GL details ID {gl_details_id} with data: {premises_data}")

        # First check if premises details exist
        existing_premises = self.db.scalar(
            select(PremisesLiabilityDetails).where(PremisesLiabilityDetails.gl_details_id == gl_details_id)
        )

        if existing_premises:
            # Update existing premises details
            logger.debug(f"Found existing premises details with ID {existing_premises.id}")
            for key, value in premises_data.items():
                logger.debug(f"Setting {key}={value} on premises details")
                setattr(existing_premises, key, value)

            # Make sure to flush the session to persist changes
            self.db.flush()
            self.db.commit()
            return existing_premises
        else:
            # Create new premises details if they don't exist
            logger.debug(f"No existing premises details found, creating new entry with data: {premises_data}")
            premises_details = self._create_premises_details(gl_details_id, premises_data)
            # Make sure to flush the session to persist changes
            self.db.flush()
            self.db.commit()
            return premises_details

    def _create_products_details(self, gl_details_id, products_data):
        """Create products details if provided."""
        from claimentine.models.claim.details import ProductsLiabilityDetails

        if not products_data:
            return None

        products_details = ProductsLiabilityDetails(gl_details_id=gl_details_id, **products_data)
        self.db.add(products_details)
        return products_details

    def _create_operations_details(self, gl_details_id, operations_data):
        """Create operations details if provided."""
        from claimentine.models.claim.details import CompletedOperationsDetails

        if not operations_data:
            return None

        operations_details = CompletedOperationsDetails(gl_details_id=gl_details_id, **operations_data)
        self.db.add(operations_details)
        return operations_details

    def _create_advertising_details(self, gl_details_id, advertising_data):
        """Create advertising injury details if provided."""
        from claimentine.models.claim.details import PersonalAdvertisingInjuryDetails

        if not advertising_data:
            return None

        advertising_details = PersonalAdvertisingInjuryDetails(gl_details_id=gl_details_id, **advertising_data)
        self.db.add(advertising_details)
        return advertising_details

    def _create_bodily_injury_details(self, gl_details_id, bodily_injury_data):
        """Create bodily injury details if provided."""
        if not bodily_injury_data:
            return None

        logger.debug(
            f"Creating new bodily injury details for GL details ID {gl_details_id} with data: {bodily_injury_data}"
        )
        bodily_injury_details = BodilyInjuryDetails(gl_details_id=gl_details_id, **bodily_injury_data)
        self.db.add(bodily_injury_details)
        self.db.flush()
        self.db.commit()
        # Log the created details for debugging
        logger.debug(f"Created bodily injury details with ID {bodily_injury_details.id} for GL details {gl_details_id}")
        return bodily_injury_details

    def _update_bodily_injury_details(self, gl_details_id, bodily_injury_data):
        """Update existing bodily injury details or create if not present."""
        if not bodily_injury_data:
            logger.debug(f"No bodily injury data provided for GL details ID {gl_details_id}")
            return None

        logger.debug(
            f"Updating bodily injury details for GL details ID {gl_details_id} with data: {bodily_injury_data}"
        )

        # First check if bodily injury details exist
        existing_details = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.gl_details_id == gl_details_id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if existing_details:
            # Update existing details
            logger.debug(f"Found existing bodily injury details with ID {existing_details.id}")
            for key, value in bodily_injury_data.items():
                logger.debug(f"Setting {key}={value} on bodily injury details")
                setattr(existing_details, key, value)

            # Make sure to flush the session to persist changes
            self.db.flush()
            self.db.commit()
            return existing_details
        else:
            # Create new details if they don't exist
            logger.debug(f"No existing bodily injury details found, creating new entry with data: {bodily_injury_data}")
            return self._create_bodily_injury_details(gl_details_id, bodily_injury_data)

    def get_bodily_injury_details(self, claim_id: UUID) -> Optional[BodilyInjuryDetailsResponse]:
        """Get bodily injury details for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        bodily_injury = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.gl_details_id == claim.gl_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if not bodily_injury:
            return None

        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def create_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Create bodily injury details for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        bodily_injury_data = data.model_dump(exclude_unset=True)
        bodily_injury = self._create_bodily_injury_details(claim.gl_details.id, bodily_injury_data)

        self.db.commit()
        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def update_bodily_injury_details(
        self, claim_id: UUID, data: BodilyInjuryDetailsUpdate
    ) -> BodilyInjuryDetailsResponse:
        """Update bodily injury details for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        bodily_injury_data = data.model_dump(exclude_unset=True)
        bodily_injury = self._update_bodily_injury_details(claim.gl_details.id, bodily_injury_data)

        self.db.commit()
        return BodilyInjuryDetailsResponse.model_validate(bodily_injury)

    def delete_bodily_injury_details(self, claim_id: UUID) -> None:
        """Delete bodily injury details for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing bodily injury details
        existing_details = self.db.scalar(
            select(BodilyInjuryDetails)
            .where(BodilyInjuryDetails.gl_details_id == claim.gl_details.id)
            .where(BodilyInjuryDetails.is_deleted == False)
        )

        if existing_details:
            # Soft delete by setting is_deleted flag
            existing_details.is_deleted = True
            self.db.commit()

    def update_claim(
        self,
        claim_id: UUID,
        claim_data: GeneralLiabilityClaimUpdate,
        includes: Optional[Set[str]] = None,
    ) -> GeneralLiabilityClaim:
        """Update GL claim with specialized handling for premises details and other GL-specific subtypes."""
        # Get the GL claim to update
        claim = self.db.get(GeneralLiabilityClaim, claim_id)
        if not claim:
            raise NotFoundError(f"Claim {claim_id} not found")
        if claim.is_deleted:
            raise NotFoundError(f"Claim {claim_id} not found")

        # Call parent class update to update base fields
        updated_claim = super().update_claim(claim_id, claim_data, includes)

        # Now check if specific GL subtype details need to be updated
        details_data = self._extract_details_data(claim_data)
        if not details_data or not claim.gl_details:
            # If no GL details to update or GL details don't exist, return
            return updated_claim

        # Handle GL-specific subtype details
        gl_details = claim.gl_details

        # Check for premises details
        if "premises_details" in details_data:
            premises_data = details_data.pop("premises_details")
            self._update_premises_details(gl_details.id, premises_data)

        # Check for products details
        if "products_details" in details_data:
            products_data = details_data.pop("products_details")
            if gl_details.products_details:
                # Update existing products details
                for key, value in products_data.items():
                    setattr(gl_details.products_details, key, value)
            else:
                # Create new products details
                self._create_products_details(gl_details.id, products_data)

        # Check for operations details
        if "operations_details" in details_data:
            operations_data = details_data.pop("operations_details")
            if gl_details.operations_details:
                # Update existing operations details
                for key, value in operations_data.items():
                    setattr(gl_details.operations_details, key, value)
            else:
                # Create new operations details
                self._create_operations_details(gl_details.id, operations_data)

        # Check for advertising details
        if "advertising_details" in details_data:
            advertising_data = details_data.pop("advertising_details")
            if gl_details.advertising_details:
                # Update existing advertising details
                for key, value in advertising_data.items():
                    setattr(gl_details.advertising_details, key, value)
            else:
                # Create new advertising details
                self._create_advertising_details(gl_details.id, advertising_data)

        # Check for bodily injury details
        if "bodily_injury_details" in details_data:
            bodily_injury_data = details_data.pop("bodily_injury_details")
            self._update_bodily_injury_details(gl_details.id, bodily_injury_data)

        self.db.commit()
        return updated_claim

    # New methods for injured persons management

    def get_injured_persons(self, claim_id: UUID) -> List[InjuredPersonResponse]:
        """Get all injured persons for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        injured_persons = self.db.scalars(
            select(InjuredPerson)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
            .order_by(InjuredPerson.created_at)
        ).all()

        result = []
        for person in injured_persons:
            # Get injuries for this person
            injuries = self.db.scalars(
                select(Injury)
                .where(Injury.injured_person_id == person.id)
                .where(Injury.is_deleted == False)
                .order_by(Injury.created_at)
            ).all()

            # Convert to response schema with injuries included
            person_dict = InjuredPersonResponse.model_validate(person).model_dump()
            person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]
            result.append(InjuredPersonResponse.model_validate(person_dict))

        return result

    def get_injured_person(self, claim_id: UUID, person_id: UUID) -> Optional[InjuredPersonResponse]:
        """Get a specific injured person for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return None

        # Get injuries for this person
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person.id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        # Convert to response schema with injuries included
        person_dict = InjuredPersonResponse.model_validate(person).model_dump()
        person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]

        return InjuredPersonResponse.model_validate(person_dict)

    def create_injured_person(self, claim_id: UUID, data: InjuredPersonCreate) -> InjuredPersonResponse:
        """Create an injured person for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Create new injured person
        person_data = data.model_dump(exclude_unset=True)
        person = InjuredPerson(gl_details_id=claim.gl_details.id, **person_data)
        self.db.add(person)
        self.db.flush()
        self.db.commit()

        logger.info(f"Created injured person with ID {person.id} for GL claim {claim_id}")

        return InjuredPersonResponse.model_validate(person)

    def update_injured_person(
        self, claim_id: UUID, person_id: UUID, data: InjuredPersonUpdate
    ) -> InjuredPersonResponse:
        """Update an injured person for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing person
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for claim {claim_id}")

        # Update person data
        person_data = data.model_dump(exclude_unset=True)
        for key, value in person_data.items():
            setattr(person, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated injured person {person_id} for GL claim {claim_id}")

        # Get injuries for this person to include in response
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person.id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        # Convert to response schema with injuries included
        person_dict = InjuredPersonResponse.model_validate(person).model_dump()
        person_dict["injuries"] = [InjuryResponse.model_validate(injury) for injury in injuries]

        return InjuredPersonResponse.model_validate(person_dict)

    def delete_injured_person(self, claim_id: UUID, person_id: UUID) -> None:
        """Delete an injured person for a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing person
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if person:
            # Soft delete by setting is_deleted flag
            person.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted injured person {person_id} for GL claim {claim_id}")

            # Also soft-delete all associated injuries
            injuries = self.db.scalars(
                select(Injury).where(Injury.injured_person_id == person.id).where(Injury.is_deleted == False)
            ).all()

            for injury in injuries:
                injury.is_deleted = True
                logger.info(f"Soft-deleted injury {injury.id} as part of person deletion")

            self.db.commit()

    # New methods for injuries management

    def get_injuries(self, claim_id: UUID, person_id: UUID) -> List[InjuryResponse]:
        """Get all injuries for an injured person in a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return []

        # Get all injuries for this person
        injuries = self.db.scalars(
            select(Injury)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
            .order_by(Injury.created_at)
        ).all()

        return [InjuryResponse.model_validate(injury) for injury in injuries]

    def get_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> Optional[InjuryResponse]:
        """Get a specific injury for an injured person in a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return None

        # Get the specific injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if not injury:
            return None

        return InjuryResponse.model_validate(injury)

    def create_injury(self, claim_id: UUID, person_id: UUID, data: InjuryCreate) -> InjuryResponse:
        """Create an injury for an injured person in a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for GL claim {claim_id}")

        # Create new injury
        injury_data = data.model_dump(exclude_unset=True)
        injury = Injury(injured_person_id=person_id, **injury_data)
        self.db.add(injury)
        self.db.flush()
        self.db.commit()

        logger.info(f"Created injury with ID {injury.id} for person {person_id} in GL claim {claim_id}")
        return InjuryResponse.model_validate(injury)

    def update_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID, data: InjuryUpdate) -> InjuryResponse:
        """Update an injury for an injured person in a GL claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            raise NotFoundError(f"Injured person {person_id} not found for GL claim {claim_id}")

        # Find the injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if not injury:
            raise NotFoundError(f"Injury {injury_id} not found for person {person_id}")

        # Update injury data
        injury_data = data.model_dump(exclude_unset=True)
        for key, value in injury_data.items():
            setattr(injury, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated injury {injury_id} for person {person_id}")
        return InjuryResponse.model_validate(injury)

    def delete_injury(self, claim_id: UUID, person_id: UUID, injury_id: UUID) -> None:
        """Delete an injury for an injured person."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify the person exists and belongs to this claim
        person = self.db.scalar(
            select(InjuredPerson)
            .where(InjuredPerson.id == person_id)
            .where(InjuredPerson.gl_details_id == claim.gl_details.id)
            .where(InjuredPerson.is_deleted == False)
        )

        if not person:
            return

        # Find the injury
        injury = self.db.scalar(
            select(Injury)
            .where(Injury.id == injury_id)
            .where(Injury.injured_person_id == person_id)
            .where(Injury.is_deleted == False)
        )

        if injury:
            # Soft delete by setting is_deleted flag
            injury.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted injury {injury_id} for person {person_id}")

    def _create_advertising_details(self, gl_details_id, advertising_data):
        """Create advertising injury details if provided."""
        from claimentine.models.claim.details import PersonalAdvertisingInjuryDetails

        if not advertising_data:
            return None

        advertising_details = PersonalAdvertisingInjuryDetails(gl_details_id=gl_details_id, **advertising_data)
        self.db.add(advertising_details)
        return advertising_details

    # Methods for damaged property assets management

    def get_damaged_property_assets(self, claim_id: UUID) -> List[DamagedPropertyAssetResponse]:
        """Get all damaged property assets for a general liability claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        assets = self.db.scalars(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
            .order_by(DamagedPropertyAsset.created_at)
        ).all()

        result = []
        for asset in assets:
            # Get damage instances for this asset
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
                .order_by(DamageInstance.created_at)
            ).all()

            # Convert to response schema with damage instances included
            asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
            asset_dict["damage_instances"] = [
                DamageInstanceResponse.model_validate(instance) for instance in damage_instances
            ]
            result.append(DamagedPropertyAssetResponse.model_validate(asset_dict))

        return result

    def get_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> Optional[DamagedPropertyAssetResponse]:
        """Get a specific damaged property asset for a general liability claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get damage instances for this asset
        damage_instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset.id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        # Convert to response schema with damage instances included
        asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()
        asset_dict["damage_instances"] = [
            DamageInstanceResponse.model_validate(instance) for instance in damage_instances
        ]

        return DamagedPropertyAssetResponse.model_validate(asset_dict)

    def create_damaged_property_asset(
        self, claim_id: UUID, data: DamagedPropertyAssetCreate
    ) -> DamagedPropertyAssetResponse:
        """Create a damaged property asset for a general liability claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Extract damage instances data if present
        damage_instances_data = None
        if hasattr(data, "damage_instances") and data.damage_instances:
            damage_instances_data = [instance.model_dump(exclude_unset=True) for instance in data.damage_instances]

        # Create new damaged property asset
        asset_data = data.model_dump(exclude={"damage_instances"}, exclude_unset=True)
        asset = DamagedPropertyAsset(gl_details_id=claim.gl_details.id, **asset_data)
        self.db.add(asset)
        self.db.flush()

        # Create damage instances if they were provided
        if damage_instances_data:
            for instance_data in damage_instances_data:
                instance = DamageInstance(damaged_property_asset_id=asset.id, **instance_data)
                self.db.add(instance)

        self.db.commit()
        logger.info(f"Created damaged property asset with ID {asset.id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset.id)

    def update_damaged_property_asset(
        self, claim_id: UUID, asset_id: UUID, data: DamagedPropertyAssetUpdate
    ) -> DamagedPropertyAssetResponse:
        """Update a damaged property asset for a general liability claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Update asset data
        asset_data = data.model_dump(exclude_unset=True)
        for key, value in asset_data.items():
            if key != "damage_instances":  # Skip damage instances, they're updated separately
                setattr(asset, key, value)

        self.db.flush()
        self.db.commit()
        logger.info(f"Updated damaged property asset {asset_id} for claim {claim_id}")

        # Return full response with damage instances
        return self.get_damaged_property_asset(claim_id, asset_id)

    def delete_damaged_property_asset(self, claim_id: UUID, asset_id: UUID) -> None:
        """Delete a damaged property asset for a general liability claim."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Find existing asset
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if asset:
            # Soft delete by setting is_deleted flag
            asset.is_deleted = True
            self.db.commit()
            logger.info(f"Soft-deleted damaged property asset {asset_id} for claim {claim_id}")

            # Also soft-delete all associated damage instances
            damage_instances = self.db.scalars(
                select(DamageInstance)
                .where(DamageInstance.damaged_property_asset_id == asset.id)
                .where(DamageInstance.is_deleted == False)
            ).all()

            for instance in damage_instances:
                instance.is_deleted = True
                logger.info(f"Soft-deleted damage instance {instance.id} as part of asset deletion")

            self.db.commit()

    # Methods for damage instances management

    def get_damage_instances(self, claim_id: UUID, asset_id: UUID) -> List[DamageInstanceResponse]:
        """Get all damage instances for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return []

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return []

        # Get all damage instances for this asset
        instances = self.db.scalars(
            select(DamageInstance)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
            .order_by(DamageInstance.created_at)
        ).all()

        return [DamageInstanceResponse.model_validate(instance) for instance in instances]

    def get_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID
    ) -> Optional[DamageInstanceResponse]:
        """Get a specific damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            return None

        # Check view permission for this specific claim
        self._check_view_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            return None

        # Get the specific damage instance
        instance = self.db.scalar(
            select(DamageInstance)
            .where(DamageInstance.id == instance_id)
            .where(DamageInstance.damaged_property_asset_id == asset_id)
            .where(DamageInstance.is_deleted == False)
        )

        if not instance:
            return None

        return DamageInstanceResponse.model_validate(instance)

    def create_damage_instance(
        self, claim_id: UUID, asset_id: UUID, data: DamageInstanceCreate
    ) -> DamageInstanceResponse:
        """Create a damage instance for a damaged property asset."""
        claim = self.get_claim_by_id(claim_id)
        if not claim or not claim.gl_details:
            raise NotFoundError(f"GL details not found for claim {claim_id}")

        # Check edit permission for this specific claim
        self._check_edit_claim_permission(claim)

        # Verify asset exists and belongs to this claim
        asset = self.db.scalar(
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.gl_details_id == claim.gl_details.id)
            .where(DamagedPropertyAsset.is_deleted == False)
        )

        if not asset:
            raise NotFoundError(f"Damaged property asset {asset_id} not found for claim {claim_id}")

        # Create new damage instance
        instance_data = data.model_dump(exclude_unset=True)
        instance = DamageInstance(damaged_property_asset_id=asset_id, **instance_data)
        self.db.add(instance)
        self.db.commit()
