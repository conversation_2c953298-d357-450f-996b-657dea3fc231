"""Service for managing damage-related operations across all claim types."""

from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from fastapi import HTTPException, status
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from claimentine.models.claim.base import BaseClaim, ClaimType
from claimentine.models.claim.damage import DamagedPropertyAsset, DamageInstance
from claimentine.schemas.claim.damage import (
    DamagedPropertyAssetCreate,
    DamagedPropertyAssetResponse,
    DamagedPropertyAssetUpdate,
    DamageInstanceCreate,
    DamageInstanceResponse,
    DamageInstanceUpdate,
)
from claimentine.services.base import BaseService
from claimentine.services.claim.base import BaseClaimService


class DamageService(BaseService):
    """Service for managing damage-related entities across all claim types."""

    def __init__(self, db: AsyncSession, claim_service: BaseClaimService) -> None:
        """Initialize the service with a session and claim service."""
        super().__init__(db)
        self.claim_service = claim_service

    def _get_claim_and_validate_access(self, claim_id: UUID, user_id: UUID, action: str) -> BaseClaim:
        """Get a claim and validate user access."""
        claim = self.claim_service.get_claim_by_id(claim_id)
        if not claim:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Claim {claim_id} not found",
            )

        if action == "view":
            self.claim_service._check_view_claim_permission(claim)
        elif action == "edit":
            self.claim_service._check_edit_claim_permission(claim)
        else:
            raise ValueError(f"Invalid action: {action}")

        return claim

    def _get_parent_details_id(self, claim: BaseClaim) -> Tuple[Optional[UUID], Optional[UUID], Optional[UUID]]:
        """Get parent detail IDs for a claim."""
        auto_details_id = claim.auto_details.id if claim.auto_details else None
        property_details_id = claim.property_details.id if claim.property_details else None
        gl_details_id = claim.gl_details.id if claim.gl_details else None

        return auto_details_id, property_details_id, gl_details_id

    def get_damaged_property_assets(self, claim_id: UUID, user_id: UUID) -> List[DamagedPropertyAssetResponse]:
        """Get all damaged property assets for a claim."""
        # Validate user permission
        claim = self._get_claim_and_validate_access(claim_id, user_id, "view")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Build query based on claim type
        conditions = []
        if auto_details_id:
            conditions.append((DamagedPropertyAsset.auto_details_id == auto_details_id))
        if property_details_id:
            conditions.append((DamagedPropertyAsset.property_details_id == property_details_id))
        if gl_details_id:
            conditions.append((DamagedPropertyAsset.gl_details_id == gl_details_id))

        if not conditions:
            # This should not happen as the claim should have at least one of the details
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claim with ID {claim_id} does not have appropriate details",
            )

        # Use OR to combine the conditions
        stmt = (
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.is_deleted == False)
            .filter(*conditions)
            .order_by(DamagedPropertyAsset.created_at)
        )

        result = self.db.execute(stmt)
        assets = result.scalars().all()

        # Load damage instances for each asset
        result_list = []
        for asset in assets:
            # Convert to response schema first
            asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()

            # Add damage instances if they exist
            if asset.damage_instances:
                asset_dict["damage_instances"] = [
                    DamageInstanceResponse.model_validate(instance)
                    for instance in asset.damage_instances
                    if not instance.is_deleted
                ]

            result_list.append(DamagedPropertyAssetResponse.model_validate(asset_dict))

        return result_list

    def get_damaged_property_asset(
        self, claim_id: UUID, asset_id: UUID, user_id: UUID
    ) -> Optional[DamagedPropertyAssetResponse]:
        """Get a specific damaged property asset by ID."""
        # Validate user permission
        claim = self._get_claim_and_validate_access(claim_id, user_id, "view")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Build query based on claim type
        conditions = []
        if auto_details_id:
            conditions.append((DamagedPropertyAsset.auto_details_id == auto_details_id))
        if property_details_id:
            conditions.append((DamagedPropertyAsset.property_details_id == property_details_id))
        if gl_details_id:
            conditions.append((DamagedPropertyAsset.gl_details_id == gl_details_id))

        if not conditions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claim with ID {claim_id} does not have appropriate details",
            )

        # Use OR to combine the conditions
        stmt = (
            select(DamagedPropertyAsset)
            .where(DamagedPropertyAsset.id == asset_id)
            .where(DamagedPropertyAsset.is_deleted == False)
            .filter(*conditions)
        )

        result = self.db.execute(stmt)
        asset = result.scalars().first()

        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Damaged property asset with ID {asset_id} not found for claim {claim_id}",
            )

        # Convert to response schema
        asset_dict = DamagedPropertyAssetResponse.model_validate(asset).model_dump()

        # Add damage instances if they exist
        if asset.damage_instances:
            asset_dict["damage_instances"] = [
                DamageInstanceResponse.model_validate(instance)
                for instance in asset.damage_instances
                if not instance.is_deleted
            ]

        return DamagedPropertyAssetResponse.model_validate(asset_dict)

    def create_damaged_property_asset(
        self, claim_id: UUID, data: DamagedPropertyAssetCreate, user_id: UUID
    ) -> DamagedPropertyAssetResponse:
        """Create a new damaged property asset for a claim."""
        # Validate user permission
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Convert to dict and remove damage_instances for asset creation
        asset_data = data.model_dump(exclude={"damage_instances"})
        damage_instances_data = data.damage_instances or []

        # Determine which parent to use based on claim type
        if claim.type == ClaimType.AUTO:
            if not auto_details_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Auto details not found for this claim",
                )
            asset = DamagedPropertyAsset(auto_details_id=auto_details_id, **asset_data)
        elif claim.type == ClaimType.PROPERTY:
            if not property_details_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Property details not found for this claim",
                )
            asset = DamagedPropertyAsset(property_details_id=property_details_id, **asset_data)
        elif claim.type == ClaimType.GENERAL_LIABILITY:
            if not gl_details_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="General liability details not found for this claim",
                )
            asset = DamagedPropertyAsset(gl_details_id=gl_details_id, **asset_data)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported claim type: {claim.type}",
            )

        # Add and flush to get the ID
        self.db.add(asset)
        self.db.flush()

        # Create damage instances if provided
        for instance_data in damage_instances_data:
            # Extract damage data excluding nested objects
            instance = DamageInstance(
                damaged_property_asset_id=asset.id,
                **instance_data.model_dump(),
            )
            self.db.add(instance)

        self.db.commit()
        self.db.refresh(asset)

        # Load damage instances for the response
        result = DamagedPropertyAssetResponse.model_validate(asset)
        return result

    def update_damaged_property_asset(
        self, claim_id: UUID, asset_id: UUID, data: DamagedPropertyAssetUpdate, user_id: UUID
    ) -> Optional[DamagedPropertyAssetResponse]:
        """Update a damaged property asset."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Find the asset
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return None

        if not asset:
            return None

        # Update the asset attributes
        asset_data = data.model_dump(exclude_unset=True)
        for key, value in asset_data.items():
            setattr(asset, key, value)

        self.db.commit()
        self.db.refresh(asset)

        return DamagedPropertyAssetResponse.model_validate(asset)

    def delete_damaged_property_asset(self, claim_id: UUID, asset_id: UUID, user_id: UUID) -> bool:
        """Delete a damaged property asset."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Find the asset
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return False

        if not asset:
            return False

        # Delete all related damage instances first
        self.db.query(DamageInstance).filter(DamageInstance.damaged_property_asset_id == asset_id).delete()

        # Now delete the asset
        self.db.delete(asset)
        self.db.commit()

        return True

    def get_damage_instances(
        self, claim_id: UUID, asset_id: UUID, user_id: UUID, skip: int = 0, limit: int = 100
    ) -> list[DamageInstanceResponse]:
        """Get all damage instances for a property asset."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "view")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Verify the asset belongs to this claim
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return []

        if not asset:
            return []

        instances = (
            self.db.query(DamageInstance)
            .filter(DamageInstance.damaged_property_asset_id == asset_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

        return [DamageInstanceResponse.model_validate(instance) for instance in instances]

    def get_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID, user_id: UUID
    ) -> Optional[DamageInstanceResponse]:
        """Get a specific damage instance by ID."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "view")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Verify the asset belongs to this claim
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return None

        if not asset:
            return None

        instance = (
            self.db.query(DamageInstance)
            .filter(
                DamageInstance.id == instance_id,
                DamageInstance.damaged_property_asset_id == asset_id,
            )
            .first()
        )

        if not instance:
            return None

        return DamageInstanceResponse.model_validate(instance)

    def update_damage_instance(
        self, claim_id: UUID, asset_id: UUID, instance_id: UUID, data: DamageInstanceUpdate, user_id: UUID
    ) -> Optional[DamageInstanceResponse]:
        """Update a damage instance."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Verify the asset belongs to this claim
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return None

        if not asset:
            return None

        instance = (
            self.db.query(DamageInstance)
            .filter(
                DamageInstance.id == instance_id,
                DamageInstance.damaged_property_asset_id == asset_id,
            )
            .first()
        )

        if not instance:
            return None

        # Update instance attributes
        instance_data = data.model_dump(exclude_unset=True)
        for key, value in instance_data.items():
            setattr(instance, key, value)

        self.db.commit()
        self.db.refresh(instance)

        return DamageInstanceResponse.model_validate(instance)

    def delete_damage_instance(self, claim_id: UUID, asset_id: UUID, instance_id: UUID, user_id: UUID) -> bool:
        """Delete a damage instance."""
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Verify the asset belongs to this claim
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            return False

        if not asset:
            return False

        instance = (
            self.db.query(DamageInstance)
            .filter(
                DamageInstance.id == instance_id,
                DamageInstance.damaged_property_asset_id == asset_id,
            )
            .first()
        )

        if not instance:
            return False

        self.db.delete(instance)
        self.db.commit()

        return True

    def create_damage_instance(
        self, claim_id: UUID, asset_id: UUID, data: DamageInstanceCreate, user_id: UUID
    ) -> DamageInstanceResponse:
        """Create a new damage instance for a property asset."""
        # Validate user permission
        claim = self._get_claim_and_validate_access(claim_id, user_id, "edit")
        auto_details_id, property_details_id, gl_details_id = self._get_parent_details_id(claim)

        # Verify the asset belongs to this claim
        if claim.type == ClaimType.AUTO and auto_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.auto_details_id == auto_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.PROPERTY and property_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.property_details_id == property_details_id,
                )
                .first()
            )
        elif claim.type == ClaimType.GENERAL_LIABILITY and gl_details_id:
            asset = (
                self.db.query(DamagedPropertyAsset)
                .filter(
                    DamagedPropertyAsset.id == asset_id,
                    DamagedPropertyAsset.gl_details_id == gl_details_id,
                )
                .first()
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Claim type {claim.type} does not support damaged property assets",
            )

        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Damaged property asset with ID {asset_id} not found for claim {claim_id}",
            )

        # Create the damage instance
        instance = DamageInstance(
            damaged_property_asset_id=asset_id,
            **data.model_dump(),
        )
        self.db.add(instance)
        self.db.commit()
        self.db.refresh(instance)

        return DamageInstanceResponse.model_validate(instance)
