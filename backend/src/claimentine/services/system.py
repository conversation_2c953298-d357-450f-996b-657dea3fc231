"""Service for system-wide operations."""

import logging
from typing import Optional

from sqlalchemy import create_engine
from sqlalchemy.orm import Session

from claimentine.core.config import settings
from claimentine.models.user import User
from claimentine.scripts.create_superuser import create_superuser
from claimentine.scripts.insert_initial_data import insert_initial_data
from claimentine.scripts.setup_db import create_schema_objects
from claimentine.services.base import BaseService
from claimentine.services.config.system import SystemConfigurationService

logger = logging.getLogger(__name__)

# Key for tracking database initialization
DB_INIT_KEY = "database_initialized"


class SystemService(BaseService):
    """Service for system-wide operations."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def is_database_initialized(self) -> bool:
        """Check if the database has been initialized."""
        system_config_service = SystemConfigurationService(self.db, self.current_user)
        config = system_config_service.get_by_key(DB_INIT_KEY)
        return config is not None and config.value.lower() == "true"

    def initialize_database(self, force: bool = False) -> bool:
        """Initialize the database if not already initialized.

        Args:
            force: If True, initialize even if already initialized

        Returns:
            bool: True if initialization was performed, False if skipped

        Note:
            This method can be called without a current_user for initial setup.
        """
        # Only check permissions if there's a current user
        if self.current_user is not None:
            self.check_permission("SYSTEM_CONFIGURATION", resource_type="system")

        system_config_service = SystemConfigurationService(self.db, self.current_user)

        # Check if already initialized
        if not force and self.is_database_initialized():
            logger.info("Database already initialized, skipping initialization")
            return False

        logger.info("Starting database initialization...")

        # Create engine for setup tasks
        engine = create_engine(str(settings.POSTGRES_URL), echo=False)

        try:
            # Step 1: Create schema objects (tables, extensions)
            logger.info("Creating schema objects...")
            create_schema_objects(engine)

            # Step 2: Insert initial data
            logger.info("Inserting initial data...")
            insert_initial_data()

            # Step 3: Create initial superuser (if not exists)
            logger.info("Creating initial superuser...")
            create_superuser()

            # Update initialization flag
            system_config_service.update_by_key(
                key=DB_INIT_KEY, value="true", description="Flag indicating the database has been initialized"
            )

            logger.info("Database initialization completed successfully")
            return True

        except Exception as e:
            logger.error(f"Database initialization failed: {e}", exc_info=True)
            raise
        finally:
            engine.dispose()
