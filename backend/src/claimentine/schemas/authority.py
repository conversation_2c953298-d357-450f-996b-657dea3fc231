"""Authority level schemas."""

from datetime import datetime
from decimal import Dec<PERSON>l
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from claimentine.models.authority import AuthorityRole


class AuthorityLevelBase(BaseModel):
    """Base schema for authority levels."""

    role: AuthorityRole
    reserve_limit: Decimal
    payment_limit: Decimal
    description: Optional[str] = None


class AuthorityLevelCreate(AuthorityLevelBase):
    """Schema for creating authority levels."""


class AuthorityLevelUpdate(BaseModel):
    """Schema for updating authority levels."""

    reserve_limit: Optional[Decimal] = None
    payment_limit: Optional[Decimal] = None
    description: Optional[str] = None


class AuthorityLevelInDB(AuthorityLevelBase):
    """Schema for authority levels in database."""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
