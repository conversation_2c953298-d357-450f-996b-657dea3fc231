"""Pydantic schemas for Task management."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from claimentine.models.task import TaskPriority, TaskStatus


# Basic User Representation (minimal for embedding)
class UserReadBasic(BaseModel):
    """Minimal user representation for embedding in other schemas."""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    email: str  # Or first_name/last_name depending on preference


# Base schema with fields common to create and read
class TaskBase(BaseModel):
    """Base schema for task data."""

    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    due_date: Optional[datetime] = None


# Schema for creating a new task (input)
class TaskCreate(TaskBase):
    """Schema for creating a new task.

    Requires claim_id, accepts optional assigned_to.
    """

    claim_id: UUID
    assigned_to: Optional[UUID] = None
    # hr_id is generated by the backend, not provided on create
    # created_by is set by the backend based on the logged-in user


# Schema for the request body when creating a task via the API
# claim_id will be derived from the path parameter
class TaskCreateRequestBody(TaskBase):
    """Schema for the request body when creating a task.

    claim_id is not included here as it's derived from the path.
    """

    assigned_to: Optional[UUID] = None


# Schema for updating an existing task (input)
class TaskUpdate(BaseModel):
    """Schema for updating an existing task.

    All fields are optional.
    """

    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    priority: Optional[TaskPriority] = None
    status: Optional[TaskStatus] = None
    due_date: Optional[datetime] = None
    # assigned_to is handled by a dedicated endpoint/service method


# Schema for reading/representing a task (output)
class TaskRead(TaskBase):
    """Schema for representing a task in API responses."""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    hr_id: str
    claim_id: UUID
    claim_number: Optional[str] = Field(None, description="Claim number associated with this task")
    assignee: Optional[UserReadBasic] = None
    creator: Optional[UserReadBasic] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None


# Schema for task assignment
class TaskAssignSchema(BaseModel):
    """Schema for assigning a task to a user.

    Null value unassigns the task.
    """

    assignee_id: Optional[UUID] = Field(..., description="UUID of the user to assign the task to, or null to unassign")


# Schema for task status updates
class TaskStatusSchema(BaseModel):
    """Schema for updating a task's status."""

    status: TaskStatus = Field(..., description="The new status for the task")


# Paginated task response schema
class PaginatedTaskResponse(BaseModel):
    """Paginated response for task listings."""

    items: list[TaskRead]
    total: int
    skip: int
    limit: int
