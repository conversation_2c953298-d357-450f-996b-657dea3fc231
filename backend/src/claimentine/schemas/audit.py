"""Audit trail schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field

from claimentine.models.audit import ChangeType, EntityType


class AuditTrailBase(BaseModel):
    """Base schema for audit trail data."""

    model_config = ConfigDict(from_attributes=True)

    entity_type: EntityType = Field(..., description="Type of entity being audited")
    entity_id: Optional[UUID] = Field(None, description="ID of the specific entity if applicable")
    change_type: ChangeType = Field(..., description="Type of change (CREATE, UPDATE, DELETE)")
    field_name: Optional[str] = Field(None, description="Name of the field that changed")
    previous_value: Optional[Dict[str, Any]] = Field(None, description="Previous value of the field")
    new_value: Optional[Dict[str, Any]] = Field(None, description="New value of the field")
    description: Optional[str] = Field(None, description="Human-readable description of the change")


class AuditTrailCreate(AuditTrailBase):
    """Schema for creating a new audit trail entry."""

    # Fields that shouldn't be provided by API users but added by the service
    claim_id: UUID = Field(..., description="ID of the claim this audit entry belongs to")
    changed_by_id: Optional[UUID] = Field(None, description="ID of the user who made the change")


class AuditTrailInDB(AuditTrailBase):
    """Schema for audit trail data from database."""

    id: UUID
    claim_id: UUID
    changed_by_id: Optional[UUID] = None
    changed_at: datetime


class AuditTrailResponse(AuditTrailInDB):
    """Schema for audit trail response data."""

    pass


class PaginatedAuditTrailResponse(BaseModel):
    """Schema for paginated audit trail response."""

    model_config = ConfigDict(from_attributes=True)

    items: List[AuditTrailResponse]
    total: int
    skip: int
    limit: int


class AuditTrailFilter(BaseModel):
    """Schema for filtering audit trail entries."""

    entity_type: Optional[EntityType] = Field(None, description="Filter by entity type")
    change_type: Optional[ChangeType] = Field(None, description="Filter by change type")
    from_date: Optional[datetime] = Field(None, description="Filter by date range start")
    to_date: Optional[datetime] = Field(None, description="Filter by date range end")
    changed_by_id: Optional[UUID] = Field(None, description="Filter by user who made the change")
    skip: int = Field(0, description="Number of items to skip for pagination")
    limit: int = Field(50, description="Maximum number of items to return")


class AuditSummaryResponse(BaseModel):
    """Schema for audit summary response."""

    model_config = ConfigDict(from_attributes=True)

    total_entries: int = Field(..., description="Total number of audit entries")
    by_entity: Dict[str, int] = Field(..., description="Count of entries by entity type")
    by_change_type: Dict[str, int] = Field(..., description="Count of entries by change type")
    by_user: Dict[str, int] = Field(..., description="Count of entries by user")
    recent_activity: List[AuditTrailResponse] = Field([], description="Most recent audit entries")
