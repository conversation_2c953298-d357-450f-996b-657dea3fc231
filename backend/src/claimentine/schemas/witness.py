"""Witness schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field


class WitnessBase(BaseModel):
    """Base schema for witness data."""

    model_config = ConfigDict(from_attributes=True)

    name: str = Field(..., min_length=1, max_length=200, description="Full name of the witness")
    email: Optional[EmailStr] = Field(None, description="Email address of the witness")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone number of the witness")
    address: Optional[str] = Field(None, max_length=500, description="Address of the witness")
    statement: Optional[str] = Field(None, description="Statement provided by the witness")


class WitnessCreate(WitnessBase):
    """Schema for creating a new witness."""

    pass


class WitnessUpdate(WitnessBase):
    """Schema for updating an existing witness."""

    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Full name of the witness")


class WitnessInDB(WitnessBase):
    """Schema for witness data from database."""

    id: UUID
    claim_id: UUID
    created_at: datetime
    updated_at: datetime


class WitnessResponse(WitnessInDB):
    """Schema for witness response data."""

    claim_number: Optional[str] = Field(None, description="Claim number associated with this witness")
