"""Pydantic schemas for document management."""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, HttpUrl, validator

from claimentine.models.document import DocumentType


class DocumentBase(BaseModel):
    """Base schema for document data."""

    type: DocumentType = Field(..., description="Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)")
    name: str = Field(..., min_length=1, max_length=255, description="Display name of the document")
    description: Optional[str] = Field(None, description="Optional description of the document contents")


class DocumentCreate(DocumentBase):
    """Schema for creating a document after upload."""

    file_path: str = Field(..., min_length=1, max_length=1024, description="Server path where the file is stored")
    file_size: int = Field(..., gt=0, le=100 * 1024 * 1024, description="Size of the file in bytes, maximum 100MB")
    mime_type: str = Field(
        ..., min_length=1, max_length=100, description="MIME type of the file (e.g., application/pdf)"
    )

    @validator("file_path")
    def validate_file_path(cls, v):
        """Validate file path structure."""
        if not v.startswith("claims/"):
            raise ValueError("File path must start with 'claims/'")

        parts = v.split("/")
        if len(parts) < 4:
            raise ValueError("File path must follow pattern: claims/{claim_id}/documents/{document_id}/{filename}")

        if parts[2] != "documents":
            raise ValueError("File path must contain 'documents' directory")

        return v

    @validator("mime_type")
    def validate_mime_type(cls, v):
        """Validate MIME type for security."""
        high_risk_types = [
            "application/x-msdownload",
            "application/x-dosexec",
            "application/java",
            "application/x-javascript",
            "application/x-shockwave-flash",
            "application/exe",
            "application/x-exe",
            "application/bat",
            "application/x-bat",
        ]

        if any(risk_type in v.lower() for risk_type in high_risk_types):
            raise ValueError(f"Security policy prohibits uploading {v} files")

        return v


class DocumentUpdate(BaseModel):
    """Schema for updating document metadata."""

    type: Optional[DocumentType] = Field(None, description="Type of document (e.g., POLICY, STATEMENT, PHOTO, etc.)")
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Display name of the document")
    description: Optional[str] = Field(None, description="Optional description of the document contents")

    @validator("name")
    def validate_name_not_empty(cls, v):
        """Ensure name is not empty if provided."""
        if v is not None and not v.strip():
            raise ValueError("Document name cannot be empty")
        return v

    model_config = ConfigDict(extra="forbid")  # Prevent additional unknown fields


class DocumentResponse(DocumentBase):
    """Schema for document response."""

    id: UUID = Field(..., description="Unique identifier for the document")
    claim_id: UUID = Field(..., description="ID of the claim the document belongs to")
    claim_number: Optional[str] = Field(None, description="Claim number associated with this document")
    file_path: str = Field(..., description="Server path where the file is stored")
    file_size: int = Field(..., description="Size of the file in bytes")
    mime_type: str = Field(..., description="MIME type of the file (e.g., application/pdf)")
    uploaded_by: Optional[UUID] = Field(None, description="ID of the user who uploaded the document")
    created_at: datetime = Field(..., description="When the document was created")
    updated_at: datetime = Field(..., description="When the document was last updated")

    model_config = ConfigDict(from_attributes=True)


class DocumentList(BaseModel):
    """Schema for a list of documents."""

    items: List[DocumentResponse] = Field(..., description="List of document objects")
    total: int = Field(..., ge=0, description="Total number of documents matching the query")


class DocumentUploadUrlRequest(BaseModel):
    """Schema for requesting a document upload URL."""

    file_name: str = Field(..., min_length=1, max_length=255, description="Original name of the file being uploaded")
    content_type: str = Field(..., min_length=1, max_length=100, description="MIME type of the file being uploaded")
    document_type: DocumentType = Field(..., description="Type of document being uploaded")

    @validator("file_name")
    def validate_filename(cls, v):
        """Validate filename doesn't contain path traversal attempts."""
        if ".." in v or "/" in v or "\\" in v:
            raise ValueError("Filename contains invalid characters")
        return v

    @validator("content_type")
    def validate_content_type(cls, v):
        """Validate content type for security."""
        high_risk_types = [
            "application/x-msdownload",
            "application/x-dosexec",
            "application/java",
            "application/x-javascript",
            "application/x-shockwave-flash",
            "application/exe",
            "application/x-exe",
            "application/bat",
            "application/x-bat",
        ]

        if any(risk_type in v.lower() for risk_type in high_risk_types):
            raise ValueError(f"Security policy prohibits uploading {v} files")

        return v

    model_config = ConfigDict(extra="forbid")  # Prevent additional unknown fields


class DocumentUploadUrlResponse(BaseModel):
    """Schema for document upload URL response."""

    upload_url: HttpUrl = Field(..., description="Pre-signed URL for uploading the file")
    document_id: UUID = Field(..., description="ID of the document record created")
    expires_at: datetime = Field(..., description="When the upload URL will expire")


class DocumentDownloadUrlResponse(BaseModel):
    """Schema for document download URL response."""

    download_url: HttpUrl = Field(..., description="Pre-signed URL for downloading the file")
    expires_at: datetime = Field(..., description="When the download URL will expire")
