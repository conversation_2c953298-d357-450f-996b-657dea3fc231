"""Note-related Pydantic schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field


# Base Schema
class NoteBase(BaseModel):
    """Base schema for note data."""

    content: str = Field(..., description="The content of the note.")


# Schema for Creation
class NoteCreate(NoteBase):
    """Schema for creating a new note."""

    claim_id: UUID = Field(..., description="The ID of the claim this note belongs to.")
    # created_by will be inferred from the logged-in user in the service layer


# Schema for the request body when creating a note via the API
# claim_id will be derived from the path parameter
class NoteCreateRequestBody(NoteBase):
    """Schema for the request body when creating a note.

    claim_id is not included here as it's derived from the path.
    """

    pass  # Inherits content from NoteBase


# Schema for Updating
class NoteUpdate(BaseModel):
    """Schema for updating an existing note. Only content can be updated."""

    content: str = Field(..., description="The updated content of the note.")


# Schema for Response
class NoteResponse(NoteBase):
    """Schema for returning a note via the API."""

    id: UUID
    claim_id: UUID
    claim_number: Optional[str] = Field(None, description="Claim number associated with this note")
    created_at: datetime
    updated_at: datetime
    author_id: Optional[UUID] = Field(None, description="ID of the user who created the note.")
    author_email: Optional[EmailStr] = Field(None, description="Email of the user who created the note.")
    author: Optional[str] = Field(None, description="Name of the user who created the note.")

    model_config = ConfigDict(from_attributes=True)
