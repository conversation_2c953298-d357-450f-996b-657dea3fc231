"""Attorney schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field

from claimentine.models.attorney import AttorneyType


class AttorneyBase(BaseModel):
    """Base schema for attorney data."""

    model_config = ConfigDict(from_attributes=True)

    name: str = Field(..., min_length=1, max_length=200, description="Full name of the attorney")
    attorney_type: AttorneyType = Field(..., description="Type of attorney (PLAINTIFF, DEFENSE, etc.)")
    firm_name: Optional[str] = Field(None, max_length=200, description="Name of the law firm")
    email: Optional[EmailStr] = Field(None, description="Email address of the attorney")
    phone: Optional[str] = Field(None, max_length=20, description="Contact phone number of the attorney")
    address: Optional[str] = Field(None, max_length=500, description="Address of the attorney or law firm")
    notes: Optional[str] = Field(None, description="Additional notes about the attorney")


class AttorneyCreate(AttorneyBase):
    """Schema for creating a new attorney."""

    pass


class AttorneyUpdate(AttorneyBase):
    """Schema for updating an existing attorney."""

    name: Optional[str] = Field(None, min_length=1, max_length=200, description="Full name of the attorney")
    attorney_type: Optional[AttorneyType] = Field(None, description="Type of attorney (PLAINTIFF, DEFENSE, etc.)")


class AttorneyInDB(AttorneyBase):
    """Schema for attorney data from database."""

    id: UUID
    claim_id: UUID
    created_at: datetime
    updated_at: datetime


class AttorneyResponse(AttorneyInDB):
    """Schema for attorney response data."""

    claim_number: Optional[str] = Field(None, description="Claim number associated with this attorney")
