"""Client schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.core.exceptions import ValidationError


class ClientBase(BaseModel):
    """Base client schema."""

    name: str = Field(..., description="Name of the client/insurance company")
    prefix: str = Field(..., description="Unique 4-character prefix used in claim numbering")
    description: Optional[str] = Field(None, description="Additional details about the client")
    active: bool = Field(True, description="Whether the client is active in the system")

    @field_validator("prefix")
    @classmethod
    def validate_prefix(cls, v: str) -> str:
        """Validate prefix format."""
        if not v.isalnum() or len(v) != 4 or not v.isupper():
            raise ValidationError("Prefix must be exactly 4 uppercase alphanumeric characters")
        return v


class ClientCreate(ClientBase):
    """Create client schema."""


class ClientUpdate(BaseModel):
    """Update client schema."""

    name: Optional[str] = Field(None, description="Name of the client/insurance company")
    description: Optional[str] = Field(None, description="Additional details about the client")
    active: Optional[bool] = Field(None, description="Whether the client is active in the system")


class ClientResponse(ClientBase):
    """Client response schema."""

    id: UUID = Field(..., description="Unique identifier for the client")
    created_at: datetime = Field(..., description="When the client was created")
    updated_at: datetime = Field(..., description="When the client was last updated")

    model_config = ConfigDict(from_attributes=True)
