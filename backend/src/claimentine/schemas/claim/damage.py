"""Schemas for property damage and damage instances."""

from datetime import datetime
from decimal import Decimal, InvalidOperation
from enum import Enum
from typing import List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.models.claim.damage import PropertyAssetType, RepairStatus
from claimentine.models.claim.details import DamageType, USState


class DamagedPropertyAssetBase(BaseModel):
    """Base schema for damaged property asset."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    # Asset details
    name: str = Field(..., max_length=200, description="Name of the damaged property asset")
    asset_type: PropertyAssetType = Field(..., description="Type of property asset")
    description: Optional[str] = Field(None, description="Description of the property asset")

    # Location information
    location: Optional[str] = Field(None, max_length=500, description="Location of the property asset")
    address: Optional[str] = Field(None, max_length=500, description="Address of the property asset")
    city: Optional[str] = Field(None, max_length=100, description="City where the property asset is located")
    state: Optional[USState] = Field(None, description="State where the property asset is located")
    zip_code: Optional[str] = Field(None, max_length=20, description="ZIP/postal code of the property asset")

    # Ownership details
    owner_name: Optional[str] = Field(None, max_length=200, description="Name of the property owner")
    owner_type: Optional[str] = Field(None, max_length=100, description="Type of owner (Insured, Third Party, etc.)")
    owned_by_insured: Optional[bool] = Field(None, description="Whether the property is owned by the insured")

    # Financial information
    estimated_value: Optional[Decimal] = Field(None, description="Estimated value of the property asset")
    purchase_date: Optional[datetime] = Field(None, description="Date when the property was purchased")

    # Documentation
    police_report_filed: Optional[bool] = Field(None, description="Whether a police report was filed")
    police_report_number: Optional[str] = Field(None, max_length=100, description="Police report reference number")

    @field_validator("estimated_value", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class DamagedPropertyAssetCreate(DamagedPropertyAssetBase):
    """Schema for creating a damaged property asset."""

    # Nested damage instances can be created at the same time
    damage_instances: Optional[List["DamageInstanceCreate"]] = None


class DamagedPropertyAssetUpdate(DamagedPropertyAssetBase):
    """Schema for updating a damaged property asset."""

    # Override required fields to be optional for updates
    name: Optional[str] = Field(None, max_length=200, description="Name of the damaged property asset")
    asset_type: Optional[PropertyAssetType] = Field(None, description="Type of property asset")


class DamageInstanceBase(BaseModel):
    """Base schema for damage instance."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    # Damage details
    damage_type: DamageType = Field(..., description="Type of damage (e.g., FIRE, WATER, WIND)")
    damage_description: str = Field(..., description="Description of the damage")
    damage_severity: Optional[str] = Field(
        None, max_length=100, description="Severity of the damage (Minor, Moderate, Severe)"
    )
    affected_area: Optional[str] = Field(None, max_length=200, description="Specific area affected by the damage")

    # Cause information
    damage_cause: Optional[str] = Field(None, max_length=200, description="Cause of the damage")
    date_of_damage: Optional[datetime] = Field(None, description="Date when the damage occurred")

    # Repair information
    repair_status: Optional[RepairStatus] = Field(None, description="Status of repairs")
    repair_description: Optional[str] = Field(None, description="Description of the repair work")
    repair_vendor: Optional[str] = Field(None, max_length=200, description="Vendor performing the repairs")
    estimated_repair_cost: Optional[Decimal] = Field(None, description="Estimated cost of repairs")
    actual_repair_cost: Optional[Decimal] = Field(None, description="Actual cost of repairs")
    repair_start_date: Optional[datetime] = Field(None, description="Date when repairs started")
    repair_completion_date: Optional[datetime] = Field(None, description="Date when repairs were completed")

    # Financial
    estimated_replacement_cost: Optional[Decimal] = Field(None, description="Estimated cost to replace the item")
    deductible_applied: Optional[bool] = Field(None, description="Whether a deductible was applied")
    depreciation_amount: Optional[Decimal] = Field(None, description="Amount of depreciation applied")

    @field_validator(
        "estimated_repair_cost",
        "actual_repair_cost",
        "estimated_replacement_cost",
        "depreciation_amount",
        mode="before",
    )
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class DamageInstanceCreate(DamageInstanceBase):
    """Schema for creating a damage instance."""

    pass


class DamageInstanceUpdate(DamageInstanceBase):
    """Schema for updating a damage instance."""

    # Override required fields to be optional for updates
    damage_type: Optional[DamageType] = Field(None, description="Type of damage (e.g., FIRE, WATER, WIND)")
    damage_description: Optional[str] = Field(None, description="Description of the damage")


class DamageInstanceResponse(DamageInstanceBase):
    """Schema for damage instance in responses."""

    id: UUID
    damaged_property_asset_id: UUID
    created_at: datetime
    updated_at: datetime


class DamagedPropertyAssetResponse(DamagedPropertyAssetBase):
    """Schema for damaged property asset in responses."""

    id: UUID
    property_details_id: Optional[UUID] = None
    auto_details_id: Optional[UUID] = None
    gl_details_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    damage_instances: Optional[List[DamageInstanceResponse]] = None


# Update forward references
DamagedPropertyAssetCreate.model_rebuild()
DamagedPropertyAssetResponse.model_rebuild()
