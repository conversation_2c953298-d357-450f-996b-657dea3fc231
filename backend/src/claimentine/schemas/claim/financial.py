"""Claim financial schemas."""

from datetime import datetime
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.core.exceptions import ValidationError
from claimentine.models.claim.financial import PaymentType, ReserveType


class ReserveBase(BaseModel):
    """Base schema for reserves."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    reserve_type: ReserveType = Field(..., description="Type of reserve")
    amount: Decimal = Field(..., description="Reserve amount")

    @field_validator("amount", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class ReserveCreate(ReserveBase):
    """Schema for creating a reserve."""


class ReserveUpdate(ReserveBase):
    """Schema for updating a reserve."""

    notes: Optional[str] = Field(None, description="Notes about the change")


class ReserveResponse(ReserveBase):
    """Schema for reserve in responses."""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class ClaimFinancialsBase(BaseModel):
    """Base schema for claim financials."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    estimated_value: Decimal = Field(..., description="Estimated total value of the claim")
    indemnity_paid: Optional[Decimal] = Field(None, description="Amount paid for indemnity")
    expense_paid: Optional[Decimal] = Field(None, description="Amount paid for expenses")
    defense_paid: Optional[Decimal] = Field(None, description="Amount paid for defense costs")
    recovery_expected: Optional[Decimal] = Field(None, description="Expected recovery amount")
    recovery_received: Optional[Decimal] = Field(None, description="Received recovery amount")
    deductible_amount: Optional[Decimal] = Field(None, description="Deductible amount")
    coverage_limit: Optional[Decimal] = Field(None, description="Coverage limit")
    currency: str = Field("USD", description="Currency code")

    @field_validator(
        "estimated_value",
        "indemnity_paid",
        "expense_paid",
        "defense_paid",
        "recovery_expected",
        "recovery_received",
        "deductible_amount",
        "coverage_limit",
        mode="before",
    )
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class ClaimFinancialsCreate(ClaimFinancialsBase):
    """Schema for creating claim financials."""

    reserves: List[ReserveCreate] = Field(..., description="Initial reserves")


class ClaimFinancialsUpdate(ClaimFinancialsBase):
    """Schema for updating claim financials."""

    estimated_value: Optional[Decimal] = Field(None, description="Estimated total value of the claim")
    reserves: Optional[List[ReserveUpdate]] = Field(None, description="Updated reserves")


class ClaimFinancialsInDB(ClaimFinancialsBase):
    """Schema for claim financials in database."""

    id: UUID = Field(..., description="Unique identifier")
    claim_id: UUID = Field(..., description="Associated claim ID")
    claim_number: str = Field(..., description="Associated claim number")
    reserves: List[ReserveResponse] = Field(..., description="Current reserves")
    last_reserve_change: Optional[datetime] = Field(None, description="Last reserve change date")
    last_payment_date: Optional[datetime] = Field(None, description="Last payment date")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    @field_validator("last_payment_date")
    @classmethod
    def validate_last_payment_date(cls, value):
        """Validate that last_payment_date is within reasonable bounds."""
        if value is None:
            return value

        now = datetime.now()

        # Check if date is in the future
        if value > now:
            raise ValidationError("Last payment date cannot be in the future")

        # Check if date is unreasonably old (before 1900)
        if value.year < 1900:
            raise ValidationError("Last payment date cannot be before 1900")

        return value


class ReserveHistoryInDB(BaseModel):
    """Schema for reserve history in database."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    id: UUID = Field(..., description="Unique identifier")
    claim_id: UUID = Field(..., description="Associated claim ID")
    claim_number: Optional[str] = Field(None, description="Claim number associated with this reserve history")
    financials_id: UUID = Field(..., description="Associated financials ID")
    reserve_type: ReserveType = Field(..., description="Type of reserve")
    previous_amount: Decimal = Field(..., description="Previous reserve amount")
    new_amount: Decimal = Field(..., description="New reserve amount")
    changed_by_id: UUID = Field(..., description="User who made the change")
    changed_at: datetime = Field(..., description="When the change was made")
    notes: Optional[str] = Field(None, description="Notes about the change")


# --- Payment Schemas ---


class PaymentBase(BaseModel):
    """Base schema for payments."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    payment_type: PaymentType = Field(..., description="Type of payment (INDEMNITY, EXPENSE, or DEFENSE)")
    amount: Decimal = Field(..., gt=0, description="Payment amount (must be positive)")
    payee: str = Field(..., min_length=1, max_length=255, description="Recipient of the payment")
    payment_date: datetime = Field(..., description="Date the payment was issued")
    notes: Optional[str] = Field(None, max_length=500, description="Notes about the payment")

    @field_validator("amount", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            try:
                dec_val = Decimal(str(value))
                if dec_val <= 0:
                    raise ValueError("Payment amount must be positive")
                return dec_val
            except Exception as e:
                raise ValueError(f"Invalid decimal value for amount: {e}")
        return value

    @field_validator("payment_date")
    @classmethod
    def validate_payment_date(cls, value):
        """Validate payment_date (e.g., not in the future)."""
        if value > datetime.utcnow():
            # Allow a small buffer for clock skew?
            # For now, strictly disallow future dates.
            raise ValueError("Payment date cannot be in the future")
        return value


class PaymentCreate(PaymentBase):
    """Schema for creating a payment."""


class PaymentResponse(PaymentBase):
    """Schema for payment response."""

    id: UUID = Field(..., description="Unique identifier")
    financials_id: UUID = Field(..., description="Associated financials ID")
    created_by_id: UUID = Field(..., description="User who created the payment")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")


class PaymentList(BaseModel):
    """Schema for listing payments."""

    items: List[PaymentResponse]
    total: int
