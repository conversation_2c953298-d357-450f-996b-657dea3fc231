"""Type-specific claim schemas."""

from typing import Optional

from pydantic import ConfigDict

from claimentine.models.claim.base import ClaimType
from claimentine.schemas.claim.base import BaseClaimSchema, ClaimResponseSchema, CreateClaimSchema, UpdateClaimSchema
from claimentine.schemas.claim.details import (
    AutoDetailsCreate,
    AutoDetailsResponse,
    AutoDetailsUpdate,
    CompletedOperationsDetailsResponse,
    GeneralLiabilityDetailsCreate,
    GeneralLiabilityDetailsResponse,
    GeneralLiabilityDetailsUpdate,
    PersonalAdvertisingInjuryDetailsResponse,
    PremisesLiabilityDetailsResponse,
    ProductsLiabilityDetailsResponse,
    PropertyDetailsCreate,
    PropertyDetailsResponse,
    PropertyDetailsUpdate,
)


class AutoClaimBase(BaseClaimSchema):
    """Base schema for auto claims."""

    model_config = ConfigDict(from_attributes=True)
    type: ClaimType = ClaimType.AUTO
    auto_details: Optional[AutoDetailsResponse] = None


class AutoClaimCreate(CreateClaimSchema):
    """Schema for creating auto claims."""

    type: ClaimType = ClaimType.AUTO
    auto_details: Optional[AutoDetailsCreate] = None


class AutoClaimUpdate(UpdateClaimSchema):
    """Schema for updating auto claims."""

    type: ClaimType = ClaimType.AUTO
    auto_details: Optional[AutoDetailsUpdate] = None


class AutoClaimResponse(ClaimResponseSchema):
    """Schema for auto claims in responses."""

    type: ClaimType = ClaimType.AUTO
    auto_details: Optional[AutoDetailsResponse] = None


class PropertyClaimBase(BaseClaimSchema):
    """Base schema for property claims."""

    model_config = ConfigDict(from_attributes=True)
    type: ClaimType = ClaimType.PROPERTY
    property_details: Optional[PropertyDetailsResponse] = None


class PropertyClaimCreate(CreateClaimSchema):
    """Schema for creating property claims."""

    type: ClaimType = ClaimType.PROPERTY
    property_details: Optional[PropertyDetailsCreate] = None


class PropertyClaimUpdate(UpdateClaimSchema):
    """Schema for updating property claims."""

    type: ClaimType = ClaimType.PROPERTY
    property_details: Optional[PropertyDetailsUpdate] = None


class PropertyClaimResponse(ClaimResponseSchema):
    """Schema for property claims in responses."""

    type: ClaimType = ClaimType.PROPERTY
    property_details: Optional[PropertyDetailsResponse] = None


class GeneralLiabilityClaimBase(BaseClaimSchema):
    """Base schema for general liability claims."""

    model_config = ConfigDict(from_attributes=True)
    type: ClaimType = ClaimType.GENERAL_LIABILITY
    gl_details: Optional[GeneralLiabilityDetailsResponse] = None


class GeneralLiabilityClaimCreate(CreateClaimSchema):
    """Schema for creating general liability claims."""

    type: ClaimType = ClaimType.GENERAL_LIABILITY
    gl_details: Optional[GeneralLiabilityDetailsCreate] = None


class GeneralLiabilityClaimUpdate(UpdateClaimSchema):
    """Schema for updating general liability claims."""

    type: ClaimType = ClaimType.GENERAL_LIABILITY
    gl_details: Optional[GeneralLiabilityDetailsUpdate] = None


class GeneralLiabilityClaimResponse(ClaimResponseSchema):
    """Schema for general liability claims in responses."""

    type: ClaimType = ClaimType.GENERAL_LIABILITY
    gl_details: Optional[GeneralLiabilityDetailsResponse] = None
