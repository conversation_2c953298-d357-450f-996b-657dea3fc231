"""Claim schemas package."""

from claimentine.schemas.claim.base import ClaimResponseSchema as ClaimResponse
from claimentine.schemas.claim.base import ClaimStatus, ClaimType, CloseClaimSchema
from claimentine.schemas.claim.base import CreateClaimSchema as ClaimCreate
from claimentine.schemas.claim.base import UpdateClaimSchema as ClaimUpdate
from claimentine.schemas.claim.damage import (
    DamagedPropertyAssetBase,
    DamagedPropertyAssetCreate,
    DamagedPropertyAssetResponse,
    DamagedPropertyAssetUpdate,
    DamageInstanceBase,
    DamageInstanceCreate,
    DamageInstanceResponse,
    DamageInstanceUpdate,
)
from claimentine.schemas.claim.details import (
    AutoDetailsBase,
    AutoDetailsCreate,
    AutoDetailsResponse,
    AutoDetailsUpdate,
    AutoIncidentType,
    AutoPropertyDamageBase,
    AutoPropertyDamageCreate,
    AutoPropertyDamageResponse,
    AutoPropertyDamageUpdate,
    BodilyInjuryDetailsBase,
    BodilyInjuryDetailsCreate,
    BodilyInjuryDetailsResponse,
    BodilyInjuryDetailsUpdate,
    CollisionType,
    CompletedOperationsDetailsBase,
    CompletedOperationsDetailsCreate,
    CompletedOperationsDetailsResponse,
    CompletedOperationsDetailsUpdate,
    DamageType,
    GeneralLiabilityDetailsBase,
    GeneralLiabilityDetailsCreate,
    GeneralLiabilityDetailsResponse,
    GeneralLiabilityDetailsUpdate,
    GeneralLiabilityIncidentType,
    IncidentReportStatus,
    InjuredPersonBase,
    InjuredPersonCreate,
    InjuredPersonResponse,
    InjuredPersonType,
    InjuredPersonUpdate,
    InjuryBase,
    InjuryCreate,
    InjuryResponse,
    InjuryUpdate,
    InsuranceBillingStatus,
    MedicalTreatmentRequirements,
    PersonalAdvertisingInjuryDetailsBase,
    PersonalAdvertisingInjuryDetailsCreate,
    PersonalAdvertisingInjuryDetailsResponse,
    PersonalAdvertisingInjuryDetailsUpdate,
    PointOfImpact,
    PremisesLiabilityDetailsBase,
    PremisesLiabilityDetailsCreate,
    PremisesLiabilityDetailsResponse,
    PremisesLiabilityDetailsUpdate,
    ProductsLiabilityDetailsBase,
    ProductsLiabilityDetailsCreate,
    ProductsLiabilityDetailsResponse,
    ProductsLiabilityDetailsUpdate,
    PropertyDetailsBase,
    PropertyDetailsCreate,
    PropertyDetailsResponse,
    PropertyDetailsUpdate,
    PropertyType,
    USState,
)

# Re-export everything from models.claim for convenience
__all__ = [
    # Base claim schemas
    "ClaimCreate",
    "ClaimResponse",
    "ClaimUpdate",
    "ClaimType",
    "ClaimStatus",
    # Detail schemas
    "AutoDetailsBase",
    "AutoDetailsCreate",
    "AutoDetailsResponse",
    "AutoDetailsUpdate",
    "AutoIncidentType",
    "AutoPropertyDamageBase",
    "AutoPropertyDamageCreate",
    "AutoPropertyDamageResponse",
    "AutoPropertyDamageUpdate",
    "BodilyInjuryDetailsBase",
    "BodilyInjuryDetailsCreate",
    "BodilyInjuryDetailsResponse",
    "BodilyInjuryDetailsUpdate",
    "CollisionType",
    "CompletedOperationsDetailsBase",
    "CompletedOperationsDetailsCreate",
    "CompletedOperationsDetailsResponse",
    "CompletedOperationsDetailsUpdate",
    "GeneralLiabilityDetailsBase",
    "GeneralLiabilityDetailsCreate",
    "GeneralLiabilityDetailsResponse",
    "GeneralLiabilityDetailsUpdate",
    "GeneralLiabilityIncidentType",
    "PersonalAdvertisingInjuryDetailsBase",
    "PersonalAdvertisingInjuryDetailsCreate",
    "PersonalAdvertisingInjuryDetailsResponse",
    "PersonalAdvertisingInjuryDetailsUpdate",
    "PointOfImpact",
    "PremisesLiabilityDetailsBase",
    "PremisesLiabilityDetailsCreate",
    "PremisesLiabilityDetailsResponse",
    "PremisesLiabilityDetailsUpdate",
    "ProductsLiabilityDetailsBase",
    "ProductsLiabilityDetailsCreate",
    "ProductsLiabilityDetailsResponse",
    "ProductsLiabilityDetailsUpdate",
    "PropertyDetailsBase",
    "PropertyDetailsCreate",
    "PropertyDetailsResponse",
    "PropertyDetailsUpdate",
    "PropertyType",
    # Damage schemas
    "DamagedPropertyAssetBase",
    "DamagedPropertyAssetCreate",
    "DamagedPropertyAssetResponse",
    "DamagedPropertyAssetUpdate",
    "DamageInstanceBase",
    "DamageInstanceCreate",
    "DamageInstanceResponse",
    "DamageInstanceUpdate",
    # Enums and types
    "DamageType",
    "USState",
    "InjuredPersonType",
    "IncidentReportStatus",
    "MedicalTreatmentRequirements",
    "InsuranceBillingStatus",
    # Injured Person and Injury schemas
    "InjuredPersonBase",
    "InjuredPersonCreate",
    "InjuredPersonResponse",
    "InjuredPersonUpdate",
    "InjuryBase",
    "InjuryCreate",
    "InjuryResponse",
    "InjuryUpdate",
]
