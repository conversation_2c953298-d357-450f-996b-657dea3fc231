"""Base claim schemas."""

import re
from datetime import date, datetime
from typing import TYPE_CHECKING, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator, model_validator

from claimentine.core.exceptions import ValidationError
from claimentine.models.claim.base import ClaimStatus, ClaimType, RecoveryStatus
from claimentine.models.claim.details import USState
from claimentine.schemas.client import ClientResponse

# Import the schemas explicitly to avoid circular imports
if TYPE_CHECKING:
    from claimentine.schemas.attorney import AttorneyResponse
    from claimentine.schemas.claim.financial import ClaimFinancialsInDB
    from claimentine.schemas.user import UserResponse
    from claimentine.schemas.witness import WitnessResponse
else:
    # At runtime, these are imported via the schemas/__init__.py
    from claimentine.schemas import AttorneyResponse, WitnessResponse
    from claimentine.schemas.claim.financial import ClaimFinancialsInDB
    from claimentine.schemas.user import UserResponse


class DocumentSchema(BaseModel):
    """Schema for document responses."""

    id: UUID
    name: str
    type: str
    description: Optional[str] = None
    file_path: str
    file_size: int
    mime_type: str
    uploaded_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class NoteSchema(BaseModel):
    """Schema for note responses."""

    id: UUID
    content: str
    created_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class TaskSchema(BaseModel):
    """Schema for task responses."""

    id: UUID
    title: str
    description: Optional[str] = None
    priority: str
    status: str
    due_date: Optional[datetime] = None
    assigned_to: Optional[UUID] = None
    created_by: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator("due_date")
    @classmethod
    def validate_due_date(cls, value):
        """Validate that due_date is within reasonable bounds."""
        if value is None:
            return value

        # Check if date is unreasonably old (before 1900)
        if value.year < 1900:
            raise ValidationError("Due date cannot be before 1900")

        return value


class StatusHistorySchema(BaseModel):
    """Schema for status history responses."""

    id: UUID
    from_status: Optional[str] = None
    to_status: str
    reason: Optional[str] = None
    changed_by: Optional[UUID] = None
    created_at: datetime
    claim_number: Optional[str] = Field(None, description="Claim number associated with this status history")

    model_config = ConfigDict(from_attributes=True)


class BaseClaimSchema(BaseModel):
    """Base schema for all claim operations."""

    model_config = ConfigDict(from_attributes=True)

    # Optional fields
    description: Optional[str] = Field(None, max_length=1000, description="Detailed description of the claim")
    claimant_name: Optional[str] = Field(None, max_length=200, description="Full name of the claimant")
    claimant_email: Optional[EmailStr] = Field(None, description="Email address of the claimant")
    claimant_phone: Optional[str] = Field(None, max_length=20, description="Contact phone number of the claimant")
    insured_name: Optional[str] = Field(None, max_length=200, description="Full name of the insured")
    insured_email: Optional[EmailStr] = Field(None, description="Email address of the insured")
    insured_phone: Optional[str] = Field(None, max_length=20, description="Contact phone number of the insured")

    # Reporter contact fields (new)
    reporter_phone: Optional[str] = Field(
        None,
        max_length=20,
        description="Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')",
    )
    reporter_email: Optional[EmailStr] = Field(
        None, description="Reporter's email address (e.g., '<EMAIL>')"
    )

    incident_date: Optional[date] = Field(None, description="Date when the incident occurred")
    jurisdiction: Optional[USState] = Field(None, description="Legal jurisdiction of the claim (US state)")
    incident_location: Optional[str] = Field(
        None, max_length=500, description="Physical location where the incident occurred"
    )
    policy_number: Optional[str] = Field(None, max_length=100, description="Policy number associated with the claim")
    assigned_to_id: Optional[UUID] = Field(None, description="ID of the adjuster assigned to the claim")
    supervisor_id: Optional[UUID] = Field(None, description="ID of the supervisor overseeing the claim")
    recovery_status: Optional[RecoveryStatus] = Field(
        None, description="Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"
    )
    carrier_name: Optional[str] = Field(None, max_length=200, description="Third-party carrier name")
    carrier_contact: Optional[str] = Field(None, max_length=200, description="Third-party carrier contact information")

    @field_validator("incident_date")
    @classmethod
    def validate_incident_date(cls, value):
        """Validate that incident_date is within reasonable bounds."""
        if value is None:
            return value

        today = date.today()

        # Check if date is in the future
        if value > today:
            raise ValidationError("Incident date cannot be in the future")

        # Check if date is unreasonably old (before 1900)
        if value.year < 1900:
            raise ValidationError("Incident date cannot be before 1900")

        return value

    @field_validator("reporter_phone")
    @classmethod
    def validate_reporter_phone(cls, value):
        """Validate US phone number format."""
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"[^\d]", "", value)

        # Check if it's a valid US phone number (10 digits, optionally with country code)
        if len(digits_only) == 10:
            # Valid 10-digit US number
            return value
        elif len(digits_only) == 11 and digits_only.startswith("1"):
            # Valid US number with country code
            return value
        else:
            raise ValidationError(
                "Phone number must be a valid US format (e.g., '************', '(*************', or '5551234567')"
            )

    @field_validator("claimant_phone")
    @classmethod
    def validate_claimant_phone(cls, value):
        """Validate US phone number format."""
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"[^\d]", "", value)

        # Check if it's a valid US phone number (10 digits, optionally with country code)
        if len(digits_only) == 10:
            # Valid 10-digit US number
            return value
        elif len(digits_only) == 11 and digits_only.startswith("1"):
            # Valid US number with country code
            return value
        else:
            raise ValidationError(
                "Phone number must be a valid US format (e.g., '************', '(*************', or '5551234567')"
            )

    @field_validator("insured_phone")
    @classmethod
    def validate_insured_phone(cls, value):
        """Validate US phone number format."""
        if value is None:
            return value

        # Remove all non-digit characters for validation
        digits_only = re.sub(r"[^\d]", "", value)

        # Check if it's a valid US phone number (10 digits, optionally with country code)
        if len(digits_only) == 10:
            # Valid 10-digit US number
            return value
        elif len(digits_only) == 11 and digits_only.startswith("1"):
            # Valid US number with country code
            return value
        else:
            raise ValidationError(
                "Phone number must be a valid US format (e.g., '************', '(*************', or '5551234567')"
            )


class ClaimResponseSchema(BaseClaimSchema):
    """Schema for claim responses."""

    id: UUID
    client_id: UUID
    claim_number: str
    type: ClaimType
    status: ClaimStatus
    created_by_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    closed_at: Optional[datetime] = None

    # Optional related objects
    client: Optional[ClientResponse] = None
    documents: Optional[List[DocumentSchema]] = None
    notes: Optional[List[NoteSchema]] = None
    tasks: Optional[List[TaskSchema]] = None
    status_history: Optional[List[StatusHistorySchema]] = None

    # Include financials in the response schema
    financials: Optional[ClaimFinancialsInDB] = None

    # Include witnesses and attorneys in the response schema
    witnesses: Optional[List[WitnessResponse]] = None
    attorneys: Optional[List[AttorneyResponse]] = None

    # Include user relationships in the response schema
    assigned_to: Optional["UserResponse"] = None
    supervisor: Optional["UserResponse"] = None
    created_by: Optional["UserResponse"] = None

    @field_validator("documents", "notes", "tasks", "status_history", "witnesses", "attorneys", mode="before")
    @classmethod
    def ensure_empty_list(cls, v):
        """Convert None to empty list."""
        return v or []


class RecoveryDetailsResponse(BaseModel):
    """Schema for recovery details response."""

    model_config = ConfigDict(from_attributes=True)

    recovery_status: Optional[RecoveryStatus] = Field(
        None, description="Recovery workflow status (NOT_STARTED, INITIATED, IN_PROGRESS, COMPLETED)"
    )
    carrier_name: Optional[str] = Field(None, max_length=200, description="Third-party carrier name")
    carrier_contact: Optional[str] = Field(None, max_length=200, description="Third-party carrier contact information")
    carrier_claim_number: Optional[str] = Field(None, max_length=100, description="Third-party carrier's claim number")
    carrier_adjuster: Optional[str] = Field(None, max_length=200, description="Third-party carrier's adjuster name")
    expected_amount: Optional[str] = Field(None, description="Expected recovery amount")
    received_amount: Optional[str] = Field(None, description="Amount actually recovered")
    claim_id: UUID = Field(..., description="ID of the claim this recovery data belongs to")
    claim_number: str = Field(..., description="Claim number for reference")


class CreateClaimSchema(BaseClaimSchema):
    """Schema for creating a new claim."""

    # Required fields for creation
    type: ClaimType = Field(..., description="Type of claim (AUTO, PROPERTY, etc.)")
    client_id: UUID = Field(..., description="ID of the client the claim belongs to")

    # Optional claim number (will be generated if not provided)
    claim_number: Optional[str] = Field(
        None,
        pattern=r"^[A-Z0-9]{4}-\d{4}-\d{7}$",
        description="Unique claim identifier in format PREFIX-YYYY-NNNNNNN (auto-generated if not provided)",
    )

    # Optional FNOL ID to link this claim to an existing FNOL
    fnol_id: Optional[UUID] = Field(
        None,
        description="ID of the FNOL this claim is being created from (for FNOL conversion)",
    )

    @model_validator(mode="after")
    def validate_contact_info(self):
        """Ensure at least one reporter contact method (phone or email) and at least one contact person (claimant or insured) are provided."""
        if not self.reporter_phone and not self.reporter_email:
            raise ValidationError(
                "At least one reporter contact method (reporter_phone or reporter_email) must be provided"
            )

        if not self.claimant_name and not self.insured_name:
            raise ValidationError("At least one contact person (claimant_name or insured_name) must be provided")
        return self


class UpdateClaimSchema(BaseClaimSchema):
    """Schema for updating a claim."""

    # All fields are optional for updates
    type: Optional[ClaimType] = Field(None, description="Type of claim (AUTO, PROPERTY, etc.)")
    status: Optional[ClaimStatus] = Field(None, description="Current status of the claim")

    @model_validator(mode="after")
    def validate_contact_info(self):
        """Ensure at least one reporter contact method (phone or email) is provided if either is being updated.
        Also ensure at least one contact person (claimant or insured) is provided if any contact person fields are being updated.
        """
        # Only validate if at least one reporter field is being updated
        if self.reporter_phone is not None or self.reporter_email is not None:
            if not self.reporter_phone and not self.reporter_email:
                raise ValidationError(
                    "At least one reporter contact method (reporter_phone or reporter_email) must be provided"
                )

        # Only validate contact persons if any contact person field is being updated
        contact_person_fields_updated = any(
            [
                self.claimant_name is not None,
                self.claimant_email is not None,
                self.claimant_phone is not None,
                self.insured_name is not None,
                self.insured_email is not None,
                self.insured_phone is not None,
            ]
        )

        if contact_person_fields_updated:
            if not self.claimant_name and not self.insured_name:
                raise ValidationError("At least one contact person (claimant_name or insured_name) must be provided")

        return self


class CloseClaimSchema(BaseModel):
    """Schema for closing a claim."""

    model_config = ConfigDict(from_attributes=True)

    status: ClaimStatus = Field(
        ...,
        description="Closing status (CLOSURE)",
    )

    @field_validator("status")
    @classmethod
    def validate_closing_status(cls, v):
        """Validate that the status is a valid closing status."""
        if v != ClaimStatus.CLOSURE:
            raise ValidationError(f"Status must be: {ClaimStatus.CLOSURE.value}")
        return v


class PaginatedClaimResponse(BaseModel):
    """Schema for paginated claim response."""

    model_config = ConfigDict(from_attributes=True)

    items: List[ClaimResponseSchema]
    total: int
    skip: int
    limit: int
