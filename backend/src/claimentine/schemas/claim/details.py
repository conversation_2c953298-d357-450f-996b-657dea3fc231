"""Detail-specific claim schemas."""

from datetime import datetime
from decimal import Decimal, InvalidOperation
from enum import Enum
from typing import ForwardRef, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.models.claim.details import (
    AutoIncidentType,
    CollisionType,
    DamageType,
    GeneralLiabilityIncidentType,
    PointOfImpact,
    PropertyType,
    USState,
)
from claimentine.schemas.claim.damage import DamagedPropertyAssetResponse


class AutoDetailsBase(BaseModel):
    """Base schema for auto details."""

    model_config = ConfigDict(from_attributes=True)

    # Vehicle Information
    vehicle_year: Optional[int] = Field(
        None, ge=1900, le=datetime.now().year + 1, description="Manufacturing year of the vehicle"
    )
    vehicle_make: Optional[str] = Field(None, max_length=100, description="Make/manufacturer of the vehicle")
    vehicle_model: Optional[str] = Field(None, max_length=100, description="Model of the vehicle")
    vehicle_vin: Optional[str] = Field(
        None, min_length=17, max_length=17, description="Vehicle Identification Number (17 characters)"
    )
    vehicle_license_plate: Optional[str] = Field(None, max_length=20, description="License plate number of the vehicle")
    vehicle_state: Optional[USState] = Field(None, description="State where the vehicle is registered")

    # Driver Information
    driver_name: Optional[str] = Field(
        None, max_length=200, description="Full name of the driver at the time of incident"
    )
    driver_license_number: Optional[str] = Field(None, max_length=50, description="Driver's license number")
    driver_state: Optional[USState] = Field(None, description="State that issued the driver's license")
    driver_relationship: Optional[str] = Field(
        None, max_length=50, description="Relationship of driver to the insured (e.g., OWNER, FAMILY_MEMBER, FRIEND)"
    )

    # Accident Details
    point_of_impact: Optional[PointOfImpact] = Field(
        None, description="Area of the vehicle that was impacted (e.g., FRONT, REAR, LEFT, RIGHT)"
    )
    airbags_deployed: Optional[bool] = Field(None, description="Whether any airbags deployed during the incident")
    vehicle_driveable: Optional[bool] = Field(None, description="Whether the vehicle was driveable after the incident")
    towed: Optional[bool] = Field(None, description="Whether the vehicle was towed from the scene")
    tow_location: Optional[str] = Field(None, max_length=500, description="Location where the vehicle was towed to")

    # New fields for auto claim enhancements
    # Incident type classification
    incident_type: Optional[AutoIncidentType] = Field(
        None, description="Type of auto incident (COLLISION, COMPREHENSIVE)"
    )

    # Collision type categorization
    collision_type: Optional[CollisionType] = Field(None, description="Type of collision if incident_type is COLLISION")

    # Passenger details tracking
    passenger_count: Optional[int] = Field(
        None, ge=0, description="Number of passengers in the vehicle at time of incident"
    )
    passenger_details: Optional[str] = Field(
        None, description="Details about passengers including names, ages, positions, etc."
    )

    # Cargo theft tracking
    cargo_theft: Optional[bool] = Field(None, description="Whether cargo theft occurred during the incident")
    cargo_description: Optional[str] = Field(None, description="Description of stolen cargo, if applicable")

    # Property damage indicators
    has_property_damage: Optional[bool] = Field(None, description="Whether there was property damage in the incident")

    # Relationship to property damage details
    property_damage: Optional["AutoPropertyDamageResponse"] = None


class AutoDetailsCreate(AutoDetailsBase):
    """Schema for creating auto details."""

    # Add property damage nested creation
    property_damage: Optional["AutoPropertyDamageCreate"] = None


class AutoDetailsUpdate(AutoDetailsBase):
    """Schema for updating auto details."""

    # Add property damage nested update
    property_damage: Optional["AutoPropertyDamageUpdate"] = None


class AutoDetailsResponse(AutoDetailsBase):
    """Schema for auto details in responses."""

    id: UUID
    claim_id: UUID
    created_at: datetime
    updated_at: datetime
    bodily_injury_details: Optional["BodilyInjuryDetailsResponse"] = None
    injured_persons: Optional[List["InjuredPersonResponse"]] = None
    damaged_property_assets: Optional[List[DamagedPropertyAssetResponse]] = None


# New schemas for AutoPropertyDamage
class AutoPropertyDamageBase(BaseModel):
    """Base schema for auto property damage."""

    model_config = ConfigDict(from_attributes=True)

    damage_description: Optional[str] = Field(None, description="Description of the property damage")
    property_type: Optional[str] = Field(None, max_length=100, description="Type of property damaged")
    property_address: Optional[str] = Field(None, max_length=500, description="Address of the damaged property")
    property_owner: Optional[str] = Field(None, max_length=200, description="Owner of the damaged property")
    estimated_damage_value: Optional[Decimal] = Field(None, ge=0, description="Estimated value of the property damage")

    @field_validator("estimated_damage_value", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert value to Decimal if needed."""
        if value is None:
            return None
        if isinstance(value, Decimal):
            return value
        try:
            return Decimal(str(value))
        except (ValueError, TypeError, InvalidOperation):
            raise ValueError("Invalid decimal value")


class AutoPropertyDamageCreate(AutoPropertyDamageBase):
    """Schema for creating auto property damage."""

    pass


class AutoPropertyDamageUpdate(AutoPropertyDamageBase):
    """Schema for updating auto property damage."""

    pass


class AutoPropertyDamageResponse(AutoPropertyDamageBase):
    """Schema for auto property damage in responses."""

    id: UUID
    auto_details_id: UUID
    created_at: datetime
    updated_at: datetime


class PropertyDetailsBase(BaseModel):
    """Base schema for property details."""

    model_config = ConfigDict(from_attributes=True)

    # Property Information
    property_type: Optional[PropertyType] = Field(
        None, description="Type of property (e.g., RESIDENTIAL, COMMERCIAL, RENTAL)"
    )
    property_address: Optional[str] = Field(None, max_length=500, description="Full address of the property")
    property_city: Optional[str] = Field(None, max_length=100, description="City where the property is located")
    property_state: Optional[USState] = Field(None, description="State where the property is located")
    property_zip: Optional[str] = Field(None, max_length=10, description="ZIP/postal code of the property")

    # Damage Information
    damage_type: Optional[DamageType] = Field(None, description="Type of damage (e.g., FIRE, WATER, WIND, HAIL, THEFT)")
    damage_description: Optional[str] = Field(None, description="Detailed description of the damage")
    affected_areas: Optional[str] = Field(
        None, max_length=500, description="Specific areas of the property affected by the damage"
    )
    inhabitable: Optional[bool] = Field(None, description="Whether the property is currently inhabitable")
    temporary_repairs_needed: Optional[bool] = Field(
        None, description="Whether temporary repairs are needed to secure the property"
    )

    # Additional Details
    police_report_filed: Optional[bool] = Field(None, description="Whether a police report was filed")
    police_report_number: Optional[str] = Field(None, max_length=100, description="Police report reference number")
    emergency_services_called: Optional[bool] = Field(
        None, description="Whether emergency services (fire, medical) were called"
    )


class PropertyDetailsCreate(PropertyDetailsBase):
    """Schema for creating property details."""

    # Add bodily injury details nested creation
    bodily_injury_details: Optional["BodilyInjuryDetailsCreate"] = None


class PropertyDetailsUpdate(PropertyDetailsBase):
    """Schema for updating property details."""

    # Add bodily injury details nested update
    bodily_injury_details: Optional["BodilyInjuryDetailsUpdate"] = None


class PropertyDetailsResponse(PropertyDetailsBase):
    """Schema for property details in responses."""

    id: UUID
    claim_id: UUID
    created_at: datetime
    updated_at: datetime
    damaged_property_assets: Optional[List[DamagedPropertyAssetResponse]] = None
    bodily_injury_details: Optional["BodilyInjuryDetailsResponse"] = None
    injured_persons: Optional[List["InjuredPersonResponse"]] = None


class GeneralLiabilityDetailsBase(BaseModel):
    """Base schema for general liability details."""

    model_config = ConfigDict(from_attributes=True)

    # Incident type
    incident_type: GeneralLiabilityIncidentType = Field(..., description="Type of general liability incident")


class GeneralLiabilityDetailsCreate(GeneralLiabilityDetailsBase):
    """Schema for creating general liability details."""


class GeneralLiabilityDetailsUpdate(GeneralLiabilityDetailsBase):
    """Schema for updating general liability details."""


class GeneralLiabilityDetailsResponse(GeneralLiabilityDetailsBase):
    """Schema for general liability details in responses."""

    id: UUID
    claim_id: UUID
    created_at: datetime
    updated_at: datetime
    premises_details: Optional["PremisesLiabilityDetailsResponse"] = None
    products_details: Optional["ProductsLiabilityDetailsResponse"] = None
    operations_details: Optional["CompletedOperationsDetailsResponse"] = None
    advertising_details: Optional["PersonalAdvertisingInjuryDetailsResponse"] = None
    bodily_injury_details: Optional["BodilyInjuryDetailsResponse"] = None
    injured_persons: Optional[List["InjuredPersonResponse"]] = None
    damaged_property_assets: Optional[List[DamagedPropertyAssetResponse]] = None


class PremisesLiabilityDetailsBase(BaseModel):
    """Base schema for premises liability details."""

    model_config = ConfigDict(from_attributes=True)

    # Premises Information
    owner_name: Optional[str] = Field(None, max_length=200, description="Name of the property owner")
    owner_address: Optional[str] = Field(None, max_length=500, description="Address of the property owner")
    premises_type: Optional[str] = Field(None, max_length=100, description="Type of premises")
    incident_location: Optional[str] = Field(None, max_length=500, description="Location of incident on premises")
    is_inside: Optional[bool] = Field(None, description="Whether the incident occurred inside or outside")
    weather_type: Optional[str] = Field(None, max_length=100, description="Weather conditions at time of incident")
    third_party_property_involved: Optional[bool] = Field(None, description="Whether third party property was involved")
    hazard_signs_present: Optional[bool] = Field(None, description="Whether signs/warnings about hazards were present")
    claimant_activity_description: Optional[str] = Field(
        None, description="Description of claimant's activity at time of incident"
    )
    security_cameras_present: Optional[bool] = Field(None, description="Whether security cameras were present")
    insured_relationship: Optional[str] = Field(
        None, max_length=100, description="Insured relationship to premises (owner, tenant, other, unknown)"
    )


class PremisesLiabilityDetailsCreate(PremisesLiabilityDetailsBase):
    """Schema for creating premises liability details."""


class PremisesLiabilityDetailsUpdate(PremisesLiabilityDetailsBase):
    """Schema for updating premises liability details."""


class PremisesLiabilityDetailsResponse(PremisesLiabilityDetailsBase):
    """Schema for premises liability details in responses."""

    id: UUID
    gl_details_id: UUID
    created_at: datetime
    updated_at: datetime


class ProductsLiabilityDetailsBase(BaseModel):
    """Base schema for products liability details."""

    model_config = ConfigDict(from_attributes=True)

    # Product Information
    manufacturer_name: Optional[str] = Field(None, max_length=200, description="Name of the manufacturer")
    manufacturer_address: Optional[str] = Field(None, max_length=500, description="Address of the manufacturer")
    product_type: Optional[str] = Field(None, max_length=100, description="Type of product")
    purchase_date: Optional[datetime] = Field(None, description="Date of purchase")
    installation_date: Optional[datetime] = Field(None, description="Date of installation if applicable")
    usage_complies_with_intent: Optional[bool] = Field(None, description="Whether product was used as intended")
    product_modified: Optional[bool] = Field(None, description="Whether product was modified")
    serial_number: Optional[str] = Field(None, max_length=100, description="Product serial number")
    warnings_present: Optional[bool] = Field(None, description="Whether warnings were present on product")
    third_party_materials_used: Optional[bool] = Field(None, description="Whether third party materials were used")
    similar_incidents_history: Optional[str] = Field(None, description="History of similar incidents or complaints")


class ProductsLiabilityDetailsCreate(ProductsLiabilityDetailsBase):
    """Schema for creating products liability details."""


class ProductsLiabilityDetailsUpdate(ProductsLiabilityDetailsBase):
    """Schema for updating products liability details."""


class ProductsLiabilityDetailsResponse(ProductsLiabilityDetailsBase):
    """Schema for products liability details in responses."""

    id: UUID
    gl_details_id: UUID
    created_at: datetime
    updated_at: datetime


class CompletedOperationsDetailsBase(BaseModel):
    """Base schema for completed operations details."""

    model_config = ConfigDict(from_attributes=True)

    # Operations Information
    work_type: Optional[str] = Field(None, max_length=200, description="Type of work completed")
    completion_date: Optional[datetime] = Field(None, description="Date work was completed")
    customer_acceptance_date: Optional[datetime] = Field(None, description="Date customer accepted work")
    contract_status: Optional[str] = Field(None, max_length=100, description="Status of contract")
    contract_details: Optional[str] = Field(None, description="Details of the contract")
    defect_description: Optional[str] = Field(None, description="Description of the alleged defect in work")
    problem_detection_timeline: Optional[str] = Field(None, description="Timeline of problem detection")
    repair_attempts: Optional[str] = Field(None, description="Details of repair attempts made")
    repair_descriptions: Optional[str] = Field(None, description="Descriptions of repairs performed")
    applicable_warranties: Optional[str] = Field(None, description="Applicable warranties for the work")
    subcontractor_involved: Optional[bool] = Field(None, description="Whether subcontractors were involved")
    subcontractor_details: Optional[str] = Field(None, description="Details about subcontractors")


class CompletedOperationsDetailsCreate(CompletedOperationsDetailsBase):
    """Schema for creating completed operations details."""


class CompletedOperationsDetailsUpdate(CompletedOperationsDetailsBase):
    """Schema for updating completed operations details."""


class CompletedOperationsDetailsResponse(CompletedOperationsDetailsBase):
    """Schema for completed operations details in responses."""

    id: UUID
    gl_details_id: UUID
    created_at: datetime
    updated_at: datetime


class PersonalAdvertisingInjuryDetailsBase(BaseModel):
    """Base schema for personal and advertising injury details."""

    model_config = ConfigDict(from_attributes=True)

    # Injury Information
    injury_nature: Optional[str] = Field(None, max_length=200, description="Nature of alleged injury")
    offense_date: Optional[datetime] = Field(None, description="Date of alleged offense")
    offensive_material_description: Optional[str] = Field(
        None, description="Description of alleged offensive material/action"
    )
    publication_location: Optional[str] = Field(
        None, max_length=500, description="Location of publication/distribution"
    )
    material_creator: Optional[str] = Field(None, max_length=200, description="Identification of material creator")
    material_removed: Optional[bool] = Field(None, description="Whether the infringing material was removed")
    removal_date: Optional[datetime] = Field(None, description="Date of material removal")


class PersonalAdvertisingInjuryDetailsCreate(PersonalAdvertisingInjuryDetailsBase):
    """Schema for creating personal and advertising injury details."""


class PersonalAdvertisingInjuryDetailsUpdate(PersonalAdvertisingInjuryDetailsBase):
    """Schema for updating personal and advertising injury details."""


class PersonalAdvertisingInjuryDetailsResponse(PersonalAdvertisingInjuryDetailsBase):
    """Schema for personal and advertising injury details in responses."""

    id: UUID
    gl_details_id: UUID
    created_at: datetime
    updated_at: datetime


class InjuredPersonType(str, Enum):
    """Types of injured persons."""

    # General Liability specific
    GUEST = "GUEST"
    PATRON = "PATRON"
    EMPLOYEE = "EMPLOYEE"
    TENANT = "TENANT"
    CONTRACTOR = "CONTRACTOR"
    VENDOR = "VENDOR"
    TRESPASSER = "TRESPASSER"
    VISITOR = "VISITOR"
    CUSTOMER = "CUSTOMER"
    SUBCONTRACTOR = "SUBCONTRACTOR"
    DELIVERY_PERSON = "DELIVERY_PERSON"
    SERVICE_PROVIDER = "SERVICE_PROVIDER"
    PROPERTY_OWNER = "PROPERTY_OWNER"
    STUDENT = "STUDENT"
    PATIENT = "PATIENT"

    # Auto specific
    DRIVER = "DRIVER"
    INSURED_DRIVER = "INSURED_DRIVER"
    THIRD_PARTY_DRIVER = "THIRD_PARTY_DRIVER"
    PASSENGER = "PASSENGER"
    PEDESTRIAN = "PEDESTRIAN"
    CYCLIST = "CYCLIST"
    OTHER_DRIVER = "OTHER_DRIVER"
    OTHER_PASSENGER = "OTHER_PASSENGER"
    COMMERCIAL_DRIVER = "COMMERCIAL_DRIVER"
    TRUCKER = "TRUCKER"

    # General categories
    BYSTANDER = "BYSTANDER"
    CHILD = "CHILD"
    ELDERLY = "ELDERLY"
    UNKNOWN = "UNKNOWN"
    OTHER = "OTHER"


class IncidentReportStatus(str, Enum):
    """Status of incident reports."""

    FILED = "FILED"
    PENDING = "PENDING"
    NOT_REQUIRED = "NOT_REQUIRED"


class MedicalTreatmentRequirements(str, Enum):
    """Medical treatment requirements."""

    NONE = "NONE"
    FIRST_AID = "FIRST_AID"
    OUTPATIENT = "OUTPATIENT"
    HOSPITALIZATION = "HOSPITALIZATION"
    SURGERY = "SURGERY"
    PHYSICAL_THERAPY = "PHYSICAL_THERAPY"
    ONGOING_CARE = "ONGOING_CARE"


class InsuranceBillingStatus(str, Enum):
    """Insurance billing status."""

    NOT_SUBMITTED = "NOT_SUBMITTED"
    PENDING = "PENDING"
    PARTIAL_PAYMENT = "PARTIAL_PAYMENT"
    PAID = "PAID"
    DENIED = "DENIED"


class BodilyInjuryDetailsBase(BaseModel):
    """Base schema for bodily injury details."""

    model_config = ConfigDict(from_attributes=True)

    # Required fields from TODO list
    injury_description: Optional[str] = Field(None, description="Description of the injury occurrence")
    incident_location: Optional[str] = Field(None, max_length=500, description="Location where the injury occurred")
    injured_person_type: Optional[InjuredPersonType] = Field(None, description="Type of injured person")

    # Equipment details
    equipment_involved: Optional[bool] = Field(None, description="Whether equipment was involved in the incident")
    equipment_details: Optional[str] = Field(None, description="Details about the equipment involved")
    equipment_owned_by_insured: Optional[bool] = Field(
        None, description="Whether the equipment is owned by the insured"
    )

    # Safety information
    safety_measures_involved: Optional[bool] = Field(None, description="Whether safety measures were involved")
    safety_measures_description: Optional[str] = Field(None, description="Description of the safety measures")

    # Reporting
    incident_report_status: Optional[IncidentReportStatus] = Field(None, description="Status of the incident report")
    report_filer_name: Optional[str] = Field(
        None, max_length=200, description="Name of the person who filed the report"
    )
    report_filer_contact: Optional[str] = Field(None, max_length=200, description="Contact details of the report filer")

    # Medical information
    medical_treatment_requirements: Optional[MedicalTreatmentRequirements] = Field(
        None, description="Type of medical treatment required"
    )
    treatment_nature: Optional[str] = Field(None, description="Nature of the medical treatment")
    medical_provider_name: Optional[str] = Field(None, max_length=200, description="Name of the medical provider")
    medical_provider_address: Optional[str] = Field(None, max_length=500, description="Address of the medical provider")

    # Financial
    estimated_cost: Optional[Decimal] = Field(None, description="Estimated cost of medical treatment")
    insurance_billing_status: Optional[InsuranceBillingStatus] = Field(None, description="Status of insurance billing")

    @field_validator("estimated_cost", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class BodilyInjuryDetailsCreate(BodilyInjuryDetailsBase):
    """Schema for creating bodily injury details."""

    pass


class BodilyInjuryDetailsUpdate(BodilyInjuryDetailsBase):
    """Schema for updating bodily injury details."""

    pass


class BodilyInjuryDetailsResponse(BodilyInjuryDetailsBase):
    """Schema for bodily injury details in responses."""

    id: UUID
    gl_details_id: Optional[UUID] = None
    auto_details_id: Optional[UUID] = None
    property_details_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime


# New schemas for the injured person and injury models


class InjuryBase(BaseModel):
    """Base schema for injury details."""

    model_config = ConfigDict(from_attributes=True)

    # Injury details
    injury_description: Optional[str] = Field(None, description="Description of the injury")
    injury_type: Optional[str] = Field(None, max_length=100, description="Type of injury")
    injury_severity: Optional[str] = Field(None, max_length=100, description="Severity of the injury")

    # Equipment details
    equipment_involved: Optional[bool] = Field(None, description="Whether equipment was involved in the incident")
    equipment_details: Optional[str] = Field(None, description="Details about the equipment involved")
    equipment_owned_by_insured: Optional[bool] = Field(
        None, description="Whether the equipment is owned by the insured"
    )

    # Safety information
    safety_measures_involved: Optional[bool] = Field(None, description="Whether safety measures were involved")
    safety_measures_description: Optional[str] = Field(None, description="Description of the safety measures")

    # Medical information
    medical_treatment_requirements: Optional[MedicalTreatmentRequirements] = Field(
        None, description="Type of medical treatment required"
    )
    treatment_nature: Optional[str] = Field(None, description="Nature of the medical treatment")
    medical_provider_name: Optional[str] = Field(None, max_length=200, description="Name of the medical provider")
    medical_provider_address: Optional[str] = Field(None, max_length=500, description="Address of the medical provider")

    # Financial
    estimated_cost: Optional[Decimal] = Field(None, description="Estimated cost of medical treatment")
    insurance_billing_status: Optional[InsuranceBillingStatus] = Field(None, description="Status of insurance billing")


class InjuryCreate(InjuryBase):
    """Schema for creating a new injury."""

    pass


class InjuryUpdate(InjuryBase):
    """Schema for updating an injury."""

    pass


class InjuryResponse(InjuryBase):
    """Schema for injury in responses."""

    id: UUID
    injured_person_id: UUID
    created_at: datetime
    updated_at: datetime


class InjuredPersonBase(BaseModel):
    """Base schema for injured person details."""

    model_config = ConfigDict(from_attributes=True)

    # Person details
    name: Optional[str] = Field(None, max_length=200, description="Name of the injured person")
    person_type: Optional[InjuredPersonType] = Field(None, description="Type of injured person")
    contact_info: Optional[str] = Field(None, max_length=200, description="Contact information of the injured person")
    age: Optional[int] = Field(None, description="Age of the injured person")

    # Incident information
    incident_location: Optional[str] = Field(None, max_length=500, description="Location where the incident occurred")
    incident_description: Optional[str] = Field(None, description="Description of the incident")

    # Report information
    incident_report_status: Optional[IncidentReportStatus] = Field(None, description="Status of the incident report")
    report_filer_name: Optional[str] = Field(
        None, max_length=200, description="Name of the person who filed the report"
    )
    report_filer_contact: Optional[str] = Field(None, max_length=200, description="Contact details of the report filer")


class InjuredPersonCreate(InjuredPersonBase):
    """Schema for creating a new injured person."""

    pass


class InjuredPersonUpdate(InjuredPersonBase):
    """Schema for updating an injured person."""

    pass


class InjuredPersonResponse(InjuredPersonBase):
    """Schema for injured person in responses."""

    id: UUID
    gl_details_id: Optional[UUID] = None
    auto_details_id: Optional[UUID] = None
    property_details_id: Optional[UUID] = None
    created_at: datetime
    updated_at: datetime
    injuries: Optional[List[InjuryResponse]] = None


# At the end of the file, after all classes are defined
# Handling for forward references in Pydantic v2
AutoDetailsResponse.model_rebuild()
PropertyDetailsResponse.model_rebuild()
GeneralLiabilityDetailsResponse.model_rebuild()
