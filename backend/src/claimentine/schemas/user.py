"""User-related Pydantic schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, constr, field_serializer

from claimentine.models.authority import AuthorityRole
from claimentine.models.user import UserRole, UserStatus


class UserBase(BaseModel):
    """Base schema for user data."""

    model_config = ConfigDict(from_attributes=True)

    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    role: UserRole
    authority_role: AuthorityRole = Field(default=AuthorityRole.NO_AUTHORITY)
    status: UserStatus = Field(default=UserStatus.PENDING)
    department: Optional[str] = Field(None, max_length=100)
    job_title: Optional[str] = Field(None, max_length=100)
    phone_number: Optional[constr(max_length=20)] = None
    timezone: str = Field(default="UTC", max_length=50)
    preferences: Optional[Dict[str, Any]] = None


class UserCreate(UserBase):
    """Schema for creating a new user."""

    password: str = Field(
        ...,
        min_length=8,
        max_length=64,
        description="User password in plain text",
        examples=["StrongP@ssw0rd!"],
    )


class UserUpdate(BaseModel):
    """Schema for updating user data."""

    model_config = ConfigDict(from_attributes=True)

    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    role: Optional[UserRole] = None
    authority_role: Optional[AuthorityRole] = None
    status: Optional[UserStatus] = None
    department: Optional[str] = Field(None, max_length=100)
    job_title: Optional[str] = Field(None, max_length=100)
    phone_number: Optional[constr(max_length=20)] = None
    timezone: Optional[str] = Field(None, max_length=50)
    preferences: Optional[Dict[str, Any]] = None
    password: Optional[str] = Field(
        None,
        min_length=8,
        max_length=64,
        description="New password in plain text",
        examples=["NewStrongP@ssw0rd!"],
    )


class UserInDB(UserBase):
    """Schema for user data from database."""

    id: UUID
    created_at: datetime
    updated_at: datetime
    last_login_at: Optional[datetime] = None
    email_verified_at: Optional[datetime] = None
    last_password_change_at: Optional[datetime] = None
    force_password_change: bool
    locked_until: Optional[datetime] = None
    failed_login_attempts: int


class UserResponse(UserInDB):
    """Schema for user data in API responses."""

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "email": "<EMAIL>",
                "first_name": "John",
                "last_name": "Doe",
                "role": "ADJUSTER",
                "authority_role": "BASIC",
                "status": "ACTIVE",
                "department": "Claims",
                "job_title": "Senior Adjuster",
                "phone_number": "+1234567890",
                "timezone": "America/New_York",
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "permissions": ["VIEW_OWN_CLAIMS", "CREATE_CLAIMS", "EDIT_ASSIGNED_CLAIMS"],
            }
        },
    )

    permissions: List[Union[str, Any]] = []

    @field_serializer("permissions")
    def serialize_permissions(self, value: List[Union[str, Any]]) -> List[str]:
        """Serialize permissions to a list of strings."""
        result = []
        for p in value:
            if hasattr(p, "name"):
                # Permission object with name attribute
                result.append(p.name)
            else:
                # Already a string or other type
                result.append(str(p))
        return result


class PasswordChange(BaseModel):
    """Schema for changing user password."""

    model_config = ConfigDict(from_attributes=True)

    current_password: str = Field(..., description="Current password for verification", examples=["CurrentP@ssw0rd"])
    new_password: str = Field(
        ..., min_length=8, max_length=64, description="New password", examples=["NewStrongP@ssw0rd!"]
    )
