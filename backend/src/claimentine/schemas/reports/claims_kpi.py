"""Pydantic schemas for claims KPI reports."""

from typing import Optional

from pydantic import BaseModel, Field

from claimentine.schemas.metrics.dashboard import MetricChange
from claimentine.schemas.reports.base import ReportMetadata


class ClaimsKpiData(BaseModel):
    """Data for Claims KPI report."""

    total_claims: int = Field(..., description="Total number of claims")
    total_claims_change: Optional[MetricChange] = Field(
        None, description="Change in total claims compared to previous period"
    )
    open_claims: int = Field(..., description="Number of open claims")
    open_claims_percentage: float = Field(..., description="Percentage of open claims out of all claims")
    closed_claims: int = Field(..., description="Number of closed claims")
    closed_claims_percentage: float = Field(..., description="Percentage of closed claims out of all claims")
    avg_resolution_time_days: Optional[float] = Field(None, description="Average time to close claims in days")
    avg_resolution_time_days_change: Optional[MetricChange] = Field(
        None, description="Change in average resolution time compared to previous period"
    )


class ReportResponseClaimsKpis(BaseModel):
    """Response schema for Claims KPI report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: ClaimsKpiData = Field(..., description="Claims KPI data")
