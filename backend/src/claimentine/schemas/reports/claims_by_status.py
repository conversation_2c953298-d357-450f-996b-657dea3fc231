"""Pydantic schemas for claims by status reports."""

from typing import List

from pydantic import BaseModel, Field

from claimentine.schemas.reports.base import ReportMetadata


class ClaimsByStatusItem(BaseModel):
    """Item in Claims by Status report."""

    status: str = Field(..., description="Status of claim")
    count: int = Field(..., description="Number of claims with this status")
    percentage: float = Field(..., description="Percentage of claims with this status")
    color: str = Field(..., description="Color for consistent chart rendering")


class ReportResponseClaimsByStatus(BaseModel):
    """Response schema for Claims by Status report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: List[ClaimsByStatusItem] = Field(..., description="Claims by status data")
