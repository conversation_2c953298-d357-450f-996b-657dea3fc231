"""Pydantic schemas for payments vs reserves reports."""

from datetime import date
from decimal import Decimal
from typing import List

from pydantic import BaseModel, Field

from claimentine.schemas.reports.base import ReportMetadata


class PaymentReserveTimeItem(BaseModel):
    """Item in Payments vs Reserves report."""

    period: date = Field(..., description="Date period (month/week)")
    payments: Decimal = Field(..., description="Total payments in this period")
    reserves: Decimal = Field(..., description="Total reserves in this period")


class ReportResponsePaymentsVsReserves(BaseModel):
    """Response schema for Payments vs Reserves report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: List[PaymentReserveTimeItem] = Field(..., description="Payments vs reserves data")
