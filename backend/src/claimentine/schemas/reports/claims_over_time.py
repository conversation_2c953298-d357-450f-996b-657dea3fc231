"""Pydantic schemas for claims over time reports."""

from datetime import date
from typing import List

from pydantic import BaseModel, Field

from claimentine.schemas.reports.base import ReportMetadata


class ClaimsOverTimeItem(BaseModel):
    """Item in Claims Over Time report."""

    period_start: date = Field(..., description="Start date of the period")
    new_claims_count: int = Field(..., description="Number of new claims in this period")
    closed_claims_count: int = Field(..., description="Number of closed claims in this period")


class ReportResponseClaimsOverTime(BaseModel):
    """Response schema for Claims Over Time report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: List[ClaimsOverTimeItem] = Field(..., description="Claims over time data")
