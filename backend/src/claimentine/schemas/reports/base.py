"""Pydantic schemas for base report functionality."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ReportMetadata(BaseModel):
    """Common metadata for all report responses."""

    report_name: str = Field(..., description="Name of the report")
    generated_at: datetime = Field(..., description="Timestamp when the report was generated")
    filters_applied: Dict[str, Any] = Field(
        default_factory=dict, description="Filters that were applied to generate the report"
    )
    column_headers: List[Dict[str, Any]] = Field(default_factory=list, description="Headers for report data columns")


class ReportRequest(BaseModel):
    """Common request parameters for all reports."""

    start_date: Optional[datetime] = Field(None, description="Start date for report period")
    end_date: Optional[datetime] = Field(None, description="End date for report period")
    period: Optional[str] = Field(
        None, description="Predefined period (last_30_days, last_6_months, last_1_year, etc.)"
    )
    compare_period: bool = Field(False, description="Include comparison with previous period of same length")
    customer_id: Optional[str] = Field(None, description="Filter by customer ID")
    claim_type: Optional[str] = Field(None, description="Filter by claim type")
    user_id: Optional[str] = Field(None, description="Filter by user ID")
    group_by: Optional[str] = Field(None, description="Grouping option (day, week, month, status, type, user, etc.)")
