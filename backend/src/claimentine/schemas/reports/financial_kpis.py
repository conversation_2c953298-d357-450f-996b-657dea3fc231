"""Pydantic schemas for financial KPI reports."""

from decimal import Decimal
from typing import Optional

from pydantic import BaseModel, Field

from claimentine.schemas.metrics.dashboard import MetricChange
from claimentine.schemas.reports.base import ReportMetadata


class FinancialKpiData(BaseModel):
    """Data for Financial KPI report."""

    total_reserves: Decimal = Field(..., description="Total reserves amount")
    total_reserves_change: Optional[MetricChange] = Field(
        None, description="Change in total reserves compared to previous period"
    )
    total_payments: Decimal = Field(..., description="Total payments amount")
    total_payments_change: Optional[MetricChange] = Field(
        None, description="Change in total payments compared to previous period"
    )
    avg_claim_value: Decimal = Field(..., description="Average claim value")
    avg_claim_value_change: Optional[MetricChange] = Field(
        None, description="Change in average claim value compared to previous period"
    )
    recovery_amount: Decimal = Field(..., description="Total recovery amount")
    recovery_amount_change: Optional[MetricChange] = Field(
        None, description="Change in recovery amount compared to previous period"
    )


class ReportResponseFinancialKpis(BaseModel):
    """Response schema for Financial KPI report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: FinancialKpiData = Field(..., description="Financial KPI data")
