"""Pydantic schemas for claims by type reports."""

from typing import List

from pydantic import BaseModel, Field

from claimentine.schemas.reports.base import ReportMetadata


class ClaimsByTypeItem(BaseModel):
    """Item in Claims by Type report."""

    claim_type: str = Field(..., description="Type of claim")
    count: int = Field(..., description="Number of claims of this type")
    percentage: float = Field(..., description="Percentage of claims of this type")
    color: str = Field(..., description="Color for consistent chart rendering")


class ReportResponseClaimsByType(BaseModel):
    """Response schema for Claims by Type report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: List[ClaimsByTypeItem] = Field(..., description="Claims by type data")
