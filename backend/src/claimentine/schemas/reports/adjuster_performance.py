"""Pydantic schemas for adjuster performance reports."""

from decimal import Decimal
from typing import List

from pydantic import BaseModel, Field

from claimentine.schemas.reports.base import ReportMetadata


class AdjusterPerformanceItem(BaseModel):
    """Item in Adjuster Performance report."""

    user_id: str = Field(..., description="User ID of the adjuster")
    user_name: str = Field(..., description="Name of the adjuster")
    claims_handled: int = Field(..., description="Number of claims handled by this adjuster")
    avg_resolution_time: float = Field(..., description="Average resolution time in days")
    total_payments: Decimal = Field(..., description="Total payments authorized")
    pending_tasks: int = Field(..., description="Number of pending tasks")
    completed_tasks: int = Field(..., description="Number of completed tasks")


class ReportResponseAdjusterPerformance(BaseModel):
    """Response schema for Adjuster Performance report."""

    report_metadata: ReportMetadata = Field(..., description="Report metadata")
    data: List[AdjusterPerformanceItem] = Field(..., description="Adjuster performance data")
