"""Import all schemas here to avoid circular imports."""

# This file helps prevent circular imports by importing all schemas at once

from claimentine.schemas.attorney import AttorneyResponse
from claimentine.schemas.metrics.dashboard import DashboardMetricsResponse, MetricChange
from claimentine.schemas.reports.base import ReportMetadata, ReportRequest
from claimentine.schemas.witness import WitnessResponse

# Add other imports if needed for circular import resolution

__all__ = [
    "AttorneyResponse",
    "WitnessResponse",
    "DashboardMetricsResponse",
    "MetricChange",
    "ReportMetadata",
    "ReportRequest",
]
