"""Reserve configuration schemas."""

from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator

from claimentine.models.claim.base import ClaimType
from claimentine.models.claim.financial import ReserveType


class ReserveConfigurationBase(BaseModel):
    """Base schema for reserve configurations."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    claim_type: ClaimType = Field(..., description="Type of claim")
    reserve_type: ReserveType = Field(..., description="Type of reserve")
    is_required: bool = Field(..., description="Whether this reserve is required")
    minimum_amount: Optional[Decimal] = Field(None, description="Minimum required amount")
    description: str = Field(..., description="Description of the configuration")

    @field_validator("minimum_amount", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class ReserveConfigurationCreate(ReserveConfigurationBase):
    """Schema for creating a reserve configuration."""


class ReserveConfigurationUpdate(BaseModel):
    """Schema for updating a reserve configuration."""

    model_config = ConfigDict(from_attributes=True, json_encoders={Decimal: str})

    is_required: Optional[bool] = Field(None, description="Whether this reserve is required")
    minimum_amount: Optional[Decimal] = Field(None, description="Minimum required amount")
    description: Optional[str] = Field(None, description="Description of the configuration")

    @field_validator("minimum_amount", mode="before")
    @classmethod
    def convert_to_decimal(cls, value):
        """Convert string or float to Decimal."""
        if value is None:
            return value
        if isinstance(value, (str, float)):
            return Decimal(str(value))
        return value


class ReserveConfigurationInDB(ReserveConfigurationBase):
    """Schema for reserve configuration in database."""

    id: UUID = Field(..., description="Unique identifier")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
