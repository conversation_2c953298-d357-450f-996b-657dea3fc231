"""System configuration schemas."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class SystemConfigurationBase(BaseModel):
    """Base schema for system configuration."""

    key: str = Field(..., description="Configuration key")
    value: str = Field(..., description="Configuration value")
    description: str = Field(..., description="Configuration description")


class SystemConfigurationCreate(SystemConfigurationBase):
    """Schema for creating a system configuration."""

    pass


class SystemConfigurationUpdate(BaseModel):
    """Schema for updating a system configuration."""

    value: str = Field(..., description="Configuration value")
    description: str | None = Field(None, description="Configuration description")


class SystemConfigurationInDB(SystemConfigurationBase):
    """Schema for system configuration in DB."""

    id: UUID
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)
