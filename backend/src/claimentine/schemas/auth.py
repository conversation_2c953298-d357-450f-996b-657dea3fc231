"""Authentication schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class TokenResponse(BaseModel):
    """Token response schema."""

    access_token: str = Field(..., description="JWT access token for authentication")
    refresh_token: str = Field(..., description="Token used to refresh the access token")
    token_type: str = Field(..., description="Type of token (usually 'bearer')")


class UserSessionResponse(BaseModel):
    """User session response schema."""

    id: UUID = Field(..., description="Unique identifier for the session")
    ip_address: Optional[str] = Field(None, description="IP address the session was created from")
    user_agent: Optional[str] = Field(None, description="Browser/client user agent information")
    created_at: datetime = Field(..., description="When the session was created")
    last_active_at: datetime = Field(..., description="When the session was last active")
    expires_at: datetime = Field(..., description="When the session will expire")

    model_config = ConfigDict(from_attributes=True)
