# Configuration variables
BACKEND_DIR := backend
FRONTEND_DIR := frontend
PYTHON_SERVER := claimentine.main:app
LOG_DIR := $(BACKEND_DIR)/logs
LOG_FILE := $(LOG_DIR)/app.log
FRONTEND_LOG_DIR := $(FRONTEND_DIR)/logs
FRONTEND_LOG_FILE := $(FRONTEND_LOG_DIR)/frontend.log
FRONTEND_PROCESS := next dev
OPENAPI_SPEC_FILE := openapi.json
OPENAPI_SPEC_DIR := openapi-spec

# Colors and formatting (using tput)
BLUE := $(shell tput setaf 4)
GREEN := $(shell tput setaf 2)
YELLOW := $(shell tput setaf 3)
RED := $(shell tput setaf 1)
BOLD := $(shell tput bold)
RESET := $(shell tput sgr0)

.DEFAULT_GOAL := help

# Mark all non-file-producing targets as PHONY
.PHONY: help test lint \
        backend-start backend-stop backend-force-stop backend-status backend-restart \
        frontend-dev frontend-stop frontend-force-stop frontend-status frontend-restart frontend-logs frontend-tail-log frontend-build frontend-start frontend-lint frontend-lint-fix frontend-format-check frontend-format-fix frontend-type-check frontend-install \
        test-backend test-frontend test-cli \
        format-backend format-cli format-frontend \
        lint-backend lint-cli lint-frontend \
        setup-db populate-data log-flush logs log-tail clean openapi-spec \
        bs br fs fb fi tc lb lc lf ff pd

#################
# Quick Aliases #
#################

bs: clean backend-start ## [alias] Quick start backend (with clean)
br: backend-restart ## [alias] Restart backend and show logs
fs: frontend-dev ## [alias] Start frontend dev server
fb: frontend-build ## [alias] Build frontend for production
fi: frontend-install ## [alias] Install frontend dependencies
tc: test-cli ## [alias] Run CLI tests
lb: lint-backend ## [alias] Run backend linters
lc: lint-cli ## [alias] Run CLI linters
lf: lint-frontend ## [alias] Run frontend linters
ff: format-frontend ## [alias] Format frontend code
pd: populate-data ## [alias] Populate database with sample data

# Default target when just running 'make'
help: ## Show this help
	@printf '$(BLUE)$(BOLD)Usage:$(RESET)\n'
	@printf '  make <target>\n\n'
	@printf '$(BLUE)$(BOLD)Quick Aliases:$(RESET)\n'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## \[alias\]/ {printf "  $(GREEN)%-15s$(RESET) %s\n", $$1, substr($$2, 9)}' $(MAKEFILE_LIST)
	@printf '\n'
	@printf '$(BLUE)$(BOLD)All Targets:$(RESET)\n'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## [^[]/ {printf "  $(BLUE)%-15s$(RESET) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

###################
# Backend Targets #
###################

# Backend Commands
BACKEND_DIR := backend
BACKEND_SRC_DIR := $(BACKEND_DIR)/src
BACKEND_START_CMD := uvicorn claimentine.main:app --reload --log-level debug

backend-start: log-flush ## Start backend development server
	@echo "$(BLUE)→ Checking if server is already running...$(RESET)"
	@if pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
		echo "$(RED)✗ Server is already running!$(RESET)"; \
		exit 1; \
	fi
	@echo "$(BLUE)→ Creating log directory...$(RESET)"
	@mkdir -p $(LOG_DIR)
	@touch $(LOG_FILE)
	@echo "$(BLUE)→ Starting backend server...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run python -m uvicorn $(PYTHON_SERVER) --reload --log-level debug >> ../$(LOG_FILE) 2>&1 &
	@sleep 2
	@echo "$(GREEN)✓ Backend server started$(RESET)"
	@echo "$(BLUE)→ Waiting for logs...$(RESET)"
	@sleep 1
	@while [ ! -f $(LOG_FILE) ]; do sleep 0.1; done
	@echo "$(BLUE)→ Showing logs:$(RESET)"
	@$(MAKE) logs

backend-stop: ## Stop the backend server gracefully
	@echo "$(BLUE)→ Stopping backend server...$(RESET)"
	@-pkill -TERM -f "$(PYTHON_SERVER)" || true
	@echo "$(BLUE)→ Waiting for server to stop...$(RESET)"
	@for i in 1 2 3 4 5; do \
		if pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
			echo "$(YELLOW)  Still waiting for server to stop (attempt $$i)...$(RESET)"; \
			sleep 1; \
		else \
			break; \
		fi; \
	done
	@if pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
		echo "$(RED)✗ Server did not stop gracefully after 5 seconds, forcing kill...$(RESET)"; \
		pkill -KILL -f "$(PYTHON_SERVER)" || true; \
		sleep 1; \
	fi
	@echo "$(GREEN)✓ Backend server stopped$(RESET)"

backend-force-stop: ## Force kill the backend server
	@echo "$(RED)→ Force killing backend server...$(RESET)"
	@-pkill -KILL -f "$(PYTHON_SERVER)" || true
	@echo "$(GREEN)✓ Backend server killed$(RESET)"

backend-status: ## Check if the backend server is running
	@echo "$(BLUE)→ Checking server status...$(RESET)"
	@if pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
		echo "$(GREEN)✓ Server is running$(RESET)"; \
	else \
		echo "$(YELLOW)! Server is not running$(RESET)"; \
	fi

backend-restart: backend-status backend-stop backend-start ## Restart the backend server and show logs after a delay

####################
# Frontend Targets #
####################

frontend-dev: ## Start frontend development server
	@echo "$(BLUE)→ Checking if frontend server is already running...$(RESET)"
	@if pgrep -f "$(FRONTEND_PROCESS)" > /dev/null; then \
		echo "$(RED)✗ Frontend server is already running!$(RESET)"; \
		exit 1; \
	fi
	@echo "$(BLUE)→ Creating frontend log directory...$(RESET)"
	@mkdir -p $(FRONTEND_LOG_DIR)
	@touch $(FRONTEND_LOG_FILE)
	@echo "$(BLUE)→ Starting frontend development server...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn dev >> logs/frontend.log 2>&1 &
	@sleep 2
	@echo "$(GREEN)✓ Frontend development server started$(RESET)"
	@echo "$(BLUE)→ Waiting for logs...$(RESET)"
	@sleep 1
	@while [ ! -f $(FRONTEND_LOG_FILE) ]; do sleep 0.1; done
	@echo "$(BLUE)→ Showing frontend logs:$(RESET)"
	@$(MAKE) frontend-logs

frontend-stop: ## Stop the frontend server gracefully
	@echo "$(BLUE)→ Stopping frontend server...$(RESET)"
	@-pkill -TERM -f "yarn dev" || true
	@-pkill -TERM -f "next dev" || true
	@-pkill -TERM -f "next-server" || true
	@echo "$(BLUE)→ Waiting for frontend server to stop...$(RESET)"
	@for i in 1 2 3 4 5; do \
		if pgrep -f "next dev" > /dev/null || pgrep -f "yarn dev" > /dev/null || pgrep -f "next-server" > /dev/null; then \
			echo "$(YELLOW)  Still waiting for frontend server to stop (attempt $$i)...$(RESET)"; \
			sleep 1; \
		else \
			break; \
		fi; \
	done
	@if pgrep -f "next dev" > /dev/null || pgrep -f "yarn dev" > /dev/null || pgrep -f "next-server" > /dev/null; then \
		echo "$(RED)✗ Frontend server did not stop gracefully after 5 seconds, forcing kill...$(RESET)"; \
		pkill -KILL -f "yarn dev" || true; \
		pkill -KILL -f "next dev" || true; \
		pkill -KILL -f "next-server" || true; \
		sleep 1; \
	fi
	@echo "$(GREEN)✓ Frontend server stopped$(RESET)"

frontend-force-stop: ## Force kill the frontend server
	@echo "$(RED)→ Force killing frontend server...$(RESET)"
	@-pkill -KILL -f "yarn dev" || true
	@-pkill -KILL -f "next dev" || true
	@-pkill -KILL -f "next-server" || true
	@echo "$(GREEN)✓ Frontend server killed$(RESET)"

frontend-status: ## Check if the frontend server is running
	@echo "$(BLUE)→ Checking frontend server status...$(RESET)"
	@if pgrep -f "next dev" > /dev/null || pgrep -f "yarn dev" > /dev/null || pgrep -f "next-server" > /dev/null; then \
		echo "$(GREEN)✓ Frontend server is running$(RESET)"; \
	else \
		echo "$(YELLOW)! Frontend server is not running$(RESET)"; \
	fi

frontend-restart: frontend-status frontend-stop frontend-dev ## Restart the frontend server and show logs after a delay

frontend-build: ## Build frontend for production
	@echo "$(BLUE)→ Building frontend for production...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn build
	@echo "$(GREEN)✓ Frontend build complete$(RESET)"

frontend-start: ## Start frontend production server
	@echo "$(BLUE)→ Starting frontend production server...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn start

frontend-lint: ## Run frontend linting check without fixing
	@echo "$(BLUE)→ Running frontend linters (check only)...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn lint
	@echo "$(GREEN)✓ Frontend linting check complete$(RESET)"

frontend-lint-fix: ## Run frontend linting with auto-fix
	@echo "$(BLUE)→ Running frontend linters with auto-fix...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn lint:fix
	@echo "$(GREEN)✓ Frontend linting and fixing complete$(RESET)"

frontend-format-check: ## Check frontend Prettier formatting
	@echo "$(BLUE)→ Checking frontend Prettier formatting...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn format:check
	@echo "$(GREEN)✓ Frontend format check complete$(RESET)"

frontend-format-fix: ## Fix frontend formatting with Prettier
	@echo "$(BLUE)→ Fixing frontend code formatting with Prettier...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn format
	@echo "$(GREEN)✓ Frontend code formatted$(RESET)"

frontend-type-check: ## Run TypeScript type checking
	@echo "$(BLUE)→ Running TypeScript type checking...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn type-check
	@echo "$(GREEN)✓ Frontend type checking complete$(RESET)"

frontend-install: ## Install frontend dependencies
	@echo "$(BLUE)→ Installing frontend dependencies...$(RESET)"
	@cd $(FRONTEND_DIR) && yarn install
	@echo "$(GREEN)✓ Frontend dependencies installed$(RESET)"

format-frontend: frontend-format-fix ## Alias for frontend-format-fix

#################
# Test Targets #
################

test-cli: ## Run CLI tests with pytest
	@echo "$(BLUE)→ Running CLI tests...$(RESET)"
	@cd cli && poetry run pytest

#################
# Lint Targets #
################

format-backend: ## Format backend code with black and isort
	@echo "$(BLUE)→ Formatting backend code...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run black . && poetry run isort .
	@echo "$(GREEN)✓ Backend code formatted$(RESET)"

format-cli: ## Format CLI code with black and isort
	@echo "$(BLUE)→ Formatting CLI code...$(RESET)"
	@cd cli && poetry run black . && poetry run isort .
	@echo "$(GREEN)✓ CLI code formatted$(RESET)"

lint-backend: ## Run backend linters (black, isort, ruff)
	@echo "$(BLUE)→ Running black...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run black .
	@echo "$(BLUE)→ Running isort...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run isort .
	@echo "$(GREEN)✓ Backend linting complete$(RESET)"

lint-cli: ## Run CLI linters (black, isort, ruff)
	@echo "$(BLUE)→ Running black on CLI...$(RESET)"
	@cd cli && poetry run black .
	@echo "$(BLUE)→ Running isort on CLI...$(RESET)"
	@cd cli && poetry run isort .
	@echo "$(GREEN)✓ CLI linting complete$(RESET)"

lint-frontend: frontend-lint ## Run frontend linting checks

test: test-backend test-frontend test-cli ## Run all tests

format: format-backend format-cli format-frontend ## Format all code

lint: lint-backend lint-cli lint-frontend ## Run all linters

##################
# Database Setup #
##################

setup-db: ## Run database setup script
	# 1. Reset database schema: DROP SCHEMA public CASCADE; CREATE SCHEMA public;
	# 2. Run setup script: poetry run python -m claimentine.scripts.setup
	# 3. Restart backend server: make backend-restart
	@echo "$(BLUE)→ Resetting database schema...$(RESET)"
	@echo "$(BLUE)→ Using database: $${POSTGRES_DB:-claimentine} on host: $${POSTGRES_HOST:-localhost}$(RESET)"
	@-PGPASSWORD=$${POSTGRES_PASSWORD:-postgres} psql -h $${POSTGRES_HOST:-localhost} -p $${POSTGRES_PORT:-5432} -U $${POSTGRES_USER:-postgres} -d $${POSTGRES_DB:-claimentine} -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;" || (echo "$(RED)✗ Failed to reset database schema$(RESET)" && exit 1)
	@echo "$(BLUE)→ Running database setup...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run python -m claimentine.scripts.setup
	@echo "$(GREEN)✓ Database setup complete$(RESET)"
	@$(MAKE) backend-restart

populate-data: ## Populate database with realistic sample data (⚠️ DESTRUCTIVE - resets all data!)
	@echo "$(BLUE)→ Checking if backend server is running...$(RESET)"
	@if ! pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
		echo "$(RED)✗ Backend server is not running! Start it first with 'make backend-start'$(RESET)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)⚠️  WARNING: This will completely reset the database!$(RESET)"
	@echo "$(YELLOW)   Check ALLOW_EXECUTION variable in scripts/data_populator/main.py$(RESET)"
	@echo "$(BLUE)→ Running data populator script...$(RESET)"
	@cd $(BACKEND_DIR) && poetry run python ../scripts/data_populator/main.py
	@echo "$(GREEN)✓ Database populated with sample data$(RESET)"

###############
# Log Targets #
###############

log-flush: ## Delete log files and recreate logs directory
	@echo "$(BLUE)→ Flushing logs...$(RESET)"
	@rm -rf $(LOG_DIR)
	@mkdir -p $(LOG_DIR)
	@echo "$(GREEN)✓ Logs flushed$(RESET)"

logs: ## Follow the application logs in real-time
	@echo "$(BLUE)→ Showing logs:$(RESET)"
	@cat $(LOG_FILE)

tail-log: ## Follow the application logs in real-time
	@echo "$(BLUE)→ Following logs:$(RESET)"
	@tail -f $(LOG_FILE)

###################
# Frontend Logs #
###################

frontend-logs: ## Show the frontend logs
	@echo "$(BLUE)→ Showing frontend logs:$(RESET)"
	@cat $(FRONTEND_LOG_FILE)

frontend-tail-log: ## Follow the frontend logs in real-time
	@echo "$(BLUE)→ Following frontend logs:$(RESET)"
	@tail -f $(FRONTEND_LOG_FILE)

###################
# API Documentation #
###################

openapi-spec: ## Generate OpenAPI spec and split it into multiple files
	@echo "$(BLUE)→ Checking if backend server is running...$(RESET)"
	@if ! pgrep -f "$(PYTHON_SERVER)" > /dev/null; then \
		echo "$(RED)✗ Backend server is not running! Start it first with 'make backend-start'$(RESET)"; \
		exit 1; \
	fi
	@echo "$(BLUE)→ Generating OpenAPI spec from running server...$(RESET)"
	@curl -s http://localhost:8000/api/v1/openapi.json -o $(OPENAPI_SPEC_FILE) || \
		(echo "$(RED)✗ Failed to fetch OpenAPI spec$(RESET)" && exit 1)
	@echo "$(GREEN)✓ Generated $(OPENAPI_SPEC_FILE)$(RESET)"
	
	@echo "$(BLUE)→ Checking if redocly is installed...$(RESET)"
	@if ! which npx > /dev/null; then \
		echo "$(RED)✗ npx not found. Please install Node.js$(RESET)"; \
		exit 1; \
	fi
	@if ! command -v redocly > /dev/null 2>&1 && ! npx redocly -v > /dev/null 2>&1; then \
		echo "$(YELLOW)! redocly not found. Installing...$(RESET)"; \
		npm install -g @redocly/cli; \
	fi
	
	@echo "$(BLUE)→ Removing existing split files to ensure fresh generation...$(RESET)"
	@rm -rf $(OPENAPI_SPEC_DIR)
	@echo "$(BLUE)→ Splitting spec into multiple files...$(RESET)"
	@mkdir -p $(OPENAPI_SPEC_DIR)
	@npx redocly split $(OPENAPI_SPEC_FILE) --outDir $(OPENAPI_SPEC_DIR) || \
		(echo "$(RED)✗ Failed to split OpenAPI spec$(RESET)" && exit 1)
	@echo "$(GREEN)✓ OpenAPI spec split into $(OPENAPI_SPEC_DIR)/ directory$(RESET)"
	@echo "$(BLUE)→ Completed API documentation generation$(RESET)"

#################
# Clean Target #
################

clean: ## Remove all generated files and directories
	@echo "$(BLUE)→ Cleaning generated files...$(RESET)"
	@rm -rf $(LOG_DIR)
	@rm -rf $(FRONTEND_LOG_DIR)
	@find . -type d -name "__pycache__" -exec rm -rf {} +
	@find . -type f -name "*.pyc" -delete
	@find . -type f -name "*.pyo" -delete
	@find . -type f -name "*.pyd" -delete
	@find . -type f -name ".coverage" -delete
	@find . -type d -name "*.egg-info" -exec rm -rf {} +
	@find . -type d -name "*.egg" -exec rm -rf {} +
	@find . -type d -name ".pytest_cache" -exec rm -rf {} +
	@find . -type d -name ".ruff_cache" -exec rm -rf {} +
	@echo "$(GREEN)✓ Clean complete$(RESET)"
