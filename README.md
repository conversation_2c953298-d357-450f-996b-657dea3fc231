# Claimentine - Elysian Claim Management System


This is a monorepo containing the backend, frontend, and CLI for Claimentine, an Elysian claim management system featuring granular permissions and role-based access control.

## Table of Contents

*   [Overview](#overview)
*   [Technology Stack](#technology-stack)
*   [Directory Structure](#directory-structure)
*   [Getting Started](#getting-started)
    *   [Prerequisites](#prerequisites)
    *   [Configuration](#configuration)
    *   [Installation](#installation)
*   [Running the Application](#running-the-application)
    *   [Backend](#backend)
    *   [Frontend](#frontend)
    *   [CLI](#cli)
*   [Running Tests](#running-tests)
*   [Database Setup](#database-setup)
*   [API Documentation](#api-documentation)
*   [Permissions & Roles](#permissions--roles)
*   [Contribution Guidelines](#contribution-guidelines)
*   [License](#license)

## Overview

Claimentine consists of three main components:

1.  **Backend (`backend/`)**: A Python-based API built with FastAPI, using Poetry for dependency management. Handles business logic, data persistence, and user authentication.
2.  **Frontend (`frontend/`)**: A web interface built with TypeScript, Next.js, and Tailwind CSS. Provides the user interface for interacting with the system.
3.  **CLI (`cli/`)**: A Python-based command-line interface, also using Poetry. Offers administrative and operational functionalities.

## Technology Stack

*   **Backend**:
    *   Python 3.x
    *   FastAPI
    *   SQLAlchemy (with PostgreSQL)
    *   Pydantic
    *   Poetry
    *   Uvicorn
*   **Frontend**:
    *   TypeScript
    *   Next.js
    *   React
    *   Tailwind CSS
    *   npm (or pnpm/yarn)
*   **CLI**:
    *   Python 3.x
    *   Poetry
    *   Typer (or similar CLI framework - *confirm or update*)
    *   Pytest (for integrated tests)
*   **Development**:
    *   Make
    *   Pre-commit (for linting hooks)

## Directory Structure

```
.
├── backend/          # FastAPI backend application
│   ├── src/
│   ├── tests/        # Backend tests (if any)
│   ├── .env.example  # Example environment variables (if exists)
│   ├── pyproject.toml
│   └── poetry.lock
├── cli/              # Command Line Interface application
│   ├── src/
│   ├── tests/        # CLI integration tests
│   ├── .env.example  # Example environment variables (if exists)
│   ├── pyproject.toml
│   └── poetry.lock
├── frontend/         # Next.js frontend application
│   ├── app/
│   ├── components/
│   ├── public/
│   ├── styles/
│   ├── package.json
│   └── ...
├── shared/           # Shared code/types (if applicable)
├── scripts/          # Utility scripts (e.g., DB setup helpers)
├── specs/            # API specifications (e.g., OpenAPI)
├── .env              # Local development environment variables (GITIGNORED!)
├── .gitignore
├── Makefile          # Common development tasks
├── README.md         # This file
├── PERMISSIONS.md    # Detailed permission matrix and role hierarchy
├── CONTRIBUTING.md   # Guidelines for contributing
└── ...               # Other config files (.pre-commit-config.yaml, etc.)
```

## Getting Started

### Prerequisites

*   **Python**: Version 3.x (Check `backend/pyproject.toml` and `cli/pyproject.toml` for specific version).
*   **Node.js**: Version LTS (Check `frontend/package.json` for specific version).
*   **Poetry**: Python dependency manager. Install via `pip install poetry` or [official instructions](https://python-poetry.org/docs/#installation).
*   **npm** (or pnpm/yarn): Node.js package manager (usually comes with Node.js).
*   **Make**: Build automation tool (usually pre-installed on Linux/macOS).
*   **PostgreSQL**: A running PostgreSQL database server.

### Configuration

Configuration for the backend and potentially the CLI is loaded from environment variables, primarily sourced from a `.env` file in the project root.

1.  **Create `.env` file**: Copy an example file if one exists (e.g., `cp backend/.env.example .env`) or create a new `.env` file in the project root.
2.  **Update Variables**: Modify the variables in the `.env` file according to your local setup. Key variables include:
    *   `CLAIMENTINE_POSTGRES_HOST`: Your PostgreSQL host (e.g., `localhost`).
    *   `CLAIMENTINE_POSTGRES_PORT`: Your PostgreSQL port (e.g., `5432`).
    *   `CLAIMENTINE_POSTGRES_USER`: Your PostgreSQL username.
    *   `CLAIMENTINE_POSTGRES_PASSWORD`: Your PostgreSQL password.
    *   `CLAIMENTINE_POSTGRES_DB`: The database name (default: `claimentine`). Create this database in PostgreSQL if it doesn't exist.
    *   `CLAIMENTINE_JWT_SECRET_KEY`: A strong secret key for JWT tokens.
    *   Other variables as defined in `backend/src/claimentine/core/config.py`.

**Important**: Ensure the `.env` file is added to your `.gitignore` to avoid committing secrets.

### Installation

1.  **Backend Dependencies**:
    ```bash
    cd backend
    poetry install
    cd ..
    ```
2.  **CLI Dependencies**:
    ```bash
    cd cli
    poetry install
    cd ..
    ```
3.  **Frontend Dependencies**:
    ```bash
    cd frontend
    npm install # or pnpm install / yarn install
    cd ..
    ```

## Running the Application

Common tasks are managed via the `Makefile` in the root directory. Run `make help` to see all available commands and aliases.

### Backend

*   **Start Development Server**:
    ```bash
    make backend-start
    # Alias: make bs
    ```
    This starts the Uvicorn server with auto-reload on port 8000 (default). Logs are directed to `backend/logs/app.log`.
*   **Stop Server**: `make backend-stop`
*   **Restart Server**: `make backend-restart` (stops, starts, and shows logs)
*   **View Logs**: `make logs`
*   **Tail Logs**: `make tail-log`

### Frontend

*   **Start Development Server**:
    ```bash
    make frontend-dev
    # Alias: make fs
    ```
    This starts the Next.js development server, typically on port 3000.

### CLI

*   Execute CLI commands using `poetry run` from the `cli/` directory.
    ```bash
    cd cli
    poetry run claimentine --help # Replace 'claimentine' with actual entry point if different
    # Example: poetry run python -m claimentine.cli.main user list
    cd ..
    ```
    *(Update the command structure based on the actual CLI entry point defined in `cli/pyproject.toml`)*

## Running Tests

*   **CLI Tests**: The project includes integrated tests for the CLI, run using Pytest. There are no separate backend unit/integration tests mentioned.
    ```bash
    make test-cli
    # Alias: make tc
    ```

## Database Setup

The `Makefile` provides a command to initialize or reset the database schema and populate it with initial data.

*   **Setup Database**:
    ```bash
    make setup-db
    ```
    This command:
    1.  Connects to the PostgreSQL database specified in your `.env` file.
    2.  **Drops and recreates the `public` schema (Warning: Destructive operation!)**.
    3.  Creates database tables based on the SQLAlchemy models (`claimentine.scripts.setup_db`).
    4.  Inserts initial required data like roles and permissions (`claimentine.scripts.insert_initial_data`).
    5.  Creates an initial superuser (`claimentine.scripts.create_superuser`).

    Ensure your database is running and the credentials in `.env` are correct before running this.

## API Documentation

*   **OpenAPI Schema**: The API specification is available in `openapi.json`.
*   **Swagger UI**: When the backend server is running, interactive API documentation is usually available at `/docs` (e.g., `http://localhost:8000/docs`).
*   **ReDoc**: Alternative documentation is often at `/redoc` (e.g., `http://localhost:8000/redoc`).
*   **Markdown**: Detailed API documentation might also be present in `API_Documentation.md`.

### Use redocly for spliting openapi.json

## Permissions & Roles

For a detailed breakdown of user roles and their associated permissions across different system operations, please refer to the [PERMISSIONS.md](./PERMISSIONS.md) file.

## Contribution Guidelines

Please read the [CONTRIBUTING.md](./CONTRIBUTING.md) file for details on our code of conduct, and the process for submitting pull requests.

## License

*(Add license information here, e.g., MIT License)*
