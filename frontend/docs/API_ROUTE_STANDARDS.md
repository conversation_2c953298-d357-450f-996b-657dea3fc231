# API Route Implementation Standards

This document outlines the standards and best practices for implementing API routes in the Claimentine frontend application.

## Overview

The Claimentine frontend uses Next.js API routes as proxies to the backend services. These routes handle:

1. Authentication and authorization
2. Error normalization and formatting
3. Request/response logging
4. Content-type validation
5. CORS remediation

## Implementation Guidelines

### Directory Structure

- All API routes should be placed in the `/app/api/v1/` directory
- Each resource should have its own subdirectory (e.g., `/app/api/v1/claims/`)
- Dynamic routes should use Next.js parameter syntax (e.g., `/app/api/v1/claims/[id]/`)

### Standard Route Implementation

All API routes should:

1. Use the utilities from `lib/api-proxy-utils.ts` for handling requests
2. Implement consistent error handling with proper status codes
3. Include detailed logging using `lib/api-logging.ts`
4. Validate content types for incoming requests
5. Follow the template in `app/api/template/route.ts`

### Example Implementation

```typescript
import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest, ContentType, ErrorCode } from '@/lib/api-proxy-utils'

// GET endpoint
export async function GET(request: NextRequest) {
  return handleGetRequest(request, '/api/v1/resource', {
    errorMessage: 'Error fetching resource',
  })
}

// POST endpoint
export async function POST(request: NextRequest) {
  return handleDataRequest(request, '/api/v1/resource', {
    method: 'POST',
    errorMessage: 'Error creating resource',
    allowedContentTypes: [ContentType.JSON],
  })
}
```

### Error Handling

Always use the standard error codes from `ErrorCode` enum for consistent error handling:

- `AUTH_REQUIRED`: Missing authentication
- `AUTH_INVALID`: Invalid authentication credentials
- `FORBIDDEN`: Authenticated but not authorized
- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Invalid request parameters
- `BACKEND_ERROR`: Error from backend service
- `API_ROUTE_ERROR`: Error in the API route handler
- `JSON_PARSE_ERROR`: Error parsing JSON response
- `CONTENT_TYPE_ERROR`: Unsupported content type

### Logging

All API routes should use structured logging with request tracing:

1. Generate a unique request ID at the beginning of each request
2. Log the start of request processing
3. Log the response (success or error)
4. Include timing information in logs
5. Use appropriate log levels:
   - `DEBUG`: Detailed debugging information
   - `INFO`: Standard request/response information
   - `WARN`: Potential issues that don't affect operation
   - `ERROR`: Errors that prevent normal operation

### Content Type Handling

API routes should validate the content type of incoming requests:

- `application/json` for JSON data
- `application/x-www-form-urlencoded` for form data
- `multipart/form-data` for file uploads

Use the `validateContentType` function to check request content types.

### Response Format

Ensure consistent response format for all API routes:

- Success responses should maintain the structure from the backend
- Arrays should be wrapped in `{ items: [], total: n }`
- Error responses should include:
  ```json
  {
    "detail": "Error message",
    "status": 400,
    "error_code": "VALIDATION_ERROR",
    "request_id": "req_12345"
  }
  ```

## Testing API Routes

When testing API routes:

1. Verify authentication handling
2. Test both success and error paths
3. Ensure proper content-type validation
4. Check that error responses have the correct format and status codes
5. Verify logging is implemented correctly

## Migration Guide

To update existing API routes to the new standards:

1. Replace direct console logging with structured logging
2. Add request ID generation and tracking
3. Implement content-type validation
4. Use standard error codes
5. Add timing information to logs

## References

- [Next.js API Routes Documentation](https://nextjs.org/docs/api-routes/introduction)
- [HTTP Status Codes](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status)
- [Template API Route](../app/api/template/route.ts)
