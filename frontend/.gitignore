# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/

# Next.js build outputs
/.next/
/out/
.next/
next-env.d.ts

# Cache files
.eslintcache
.vercel
.turbo

# Testing
/coverage
/.nyc_output

# Build files
/build
/dist

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE / Editor specific files
/.idea
/.vscode
*.swp
*.swo
.DS_Store
*~

# TypeScript cache
*.tsbuildinfo
