# Claimentine Frontend

This is the frontend application for the Claimentine claims management system.

## Getting Started

### Prerequisites

- Node.js (v16+)
- npm or yarn

### Installation

```bash
npm install
# or
yarn install
```

### Development

```bash
npm run dev
# or
yarn dev
```

## Project Structure

- `app/` - Next.js app router pages and API routes
- `components/` - Reusable UI components
- `hooks/` - Custom React hooks
- `lib/` - Utility functions and API clients
- `styles/` - Global CSS styles
- `public/` - Static assets

## Type Standards

We've implemented strict type standards to ensure consistency and type safety across the codebase.

### Key Features

1. **Consistent Property Naming**

   - Frontend code uses camelCase properties
   - Backend API uses snake_case properties
   - Automatic conversion between formats in the API client layer

2. **Avoiding `any` Type**

   - Specific interfaces for all API responses and requests
   - Proper type definitions for all UI components
   - No `any` type without clear justification

3. **API Client Typing**
   - Type-safe API client methods
   - Automatic request/response transformation
   - Proper error handling with typed error responses

### Documentation

For detailed information about our type standards, see:

- [TypeStandards.md](./TypeStandards.md) - Comprehensive documentation of our type standards

### Using Types in Components

```typescript
// Import types from the camelCase namespace
import { CamelCase } from '@/lib/api';

// Type-safe component props
interface TaskListProps {
  tasks: CamelCase.Task[];
  onTaskSelect: (taskId: string) => void;
}

// Component with proper typing
const TaskList: React.FC<TaskListProps> = ({ tasks, onTaskSelect }) => {
  return (
    <ul>
      {tasks.map((task) => (
        <li key={task.id} onClick={() => onTaskSelect(task.id)}>
          {task.title} - {task.status}
        </li>
      ))}
    </ul>
  );
};
```

## API Documentation

For detailed API documentation, see:

- [API_Documentation.md](./API_Documentation.md)

## Running Tests

```bash
npm run test
# or
yarn test
```
