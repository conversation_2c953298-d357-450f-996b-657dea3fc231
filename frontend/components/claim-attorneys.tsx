'use client'

import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Plus, Pencil, Trash, Loader2, AlertCircle } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog'
import { useAttorneys, useAttorneyMutation } from '@/hooks/useAttorneys'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { AttorneyResponse, AttorneyType } from '@/lib/api/types'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { useToast } from '@/components/ui/use-toast'
import Link from 'next/link'

interface ClaimAttorneysProps {
  claimId: string
}

// Form schema
const attorneyFormSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  attorney_type: z.nativeEnum(AttorneyType),
  firm_name: z.string().nullable().optional(),
  email: z.string().email().nullable().optional(),
  phone: z.string().nullable().optional(),
  address: z.string().nullable().optional(),
  notes: z.string().nullable().optional(),
})

type AttorneyFormValues = z.infer<typeof attorneyFormSchema>

export function ClaimAttorneys({ claimId }: ClaimAttorneysProps) {
  const { toast } = useToast()
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [formState, setFormState] = useState<AttorneyFormValues>({
    name: '',
    attorney_type: AttorneyType.OTHER,
    firm_name: '',
    email: '',
    phone: '',
    address: '',
    notes: '',
  })

  const { data: attorneys, isLoading, error, mutate } = useAttorneys(claimId)
  const { currentAttorney, setCurrentAttorney, createMutation, updateMutation, deleteMutation } =
    useAttorneyMutation(claimId)

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<AttorneyFormValues>({
    resolver: zodResolver(attorneyFormSchema),
    defaultValues: formState,
  })

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormState(prev => ({
      ...prev,
      [name]: value,
    }))
    setValue(name as keyof AttorneyFormValues, value)
  }

  // Handle select changes
  const handleSelectChange = (value: string, name: keyof AttorneyFormValues) => {
    setFormState(prev => ({
      ...prev,
      [name]: value,
    }))
    setValue(name, value as any)
  }

  // Handle opening the edit dialog
  const handleEditAttorney = (attorney: AttorneyResponse) => {
    setIsEditMode(true)
    setCurrentAttorney(attorney)
    setFormState({
      name: attorney.name,
      attorney_type: attorney.attorney_type,
      firm_name: attorney.firm_name || '',
      email: attorney.email || '',
      phone: attorney.phone || '',
      address: attorney.address || '',
      notes: attorney.notes || '',
    })
    reset({
      name: attorney.name,
      attorney_type: attorney.attorney_type,
      firm_name: attorney.firm_name || '',
      email: attorney.email || '',
      phone: attorney.phone || '',
      address: attorney.address || '',
      notes: attorney.notes || '',
    })
    setIsFormDialogOpen(true)
  }

  // Handle opening the delete dialog
  const handleDeleteClick = (attorney: AttorneyResponse) => {
    setCurrentAttorney(attorney)
    setIsDeleteDialogOpen(true)
  }

  // Handle form submission
  const onSubmit = async (data: AttorneyFormValues) => {
    try {
      if (isEditMode && currentAttorney) {
        await updateMutation.mutateAsync(data)
        toast({
          title: 'Success',
          description: 'Attorney updated successfully',
        })
      } else {
        await createMutation.mutateAsync(data)
        toast({
          title: 'Success',
          description: 'Attorney added successfully',
        })
      }
      setIsFormDialogOpen(false)
      mutate() // Refresh the list
    } catch (error) {
      console.error('Error saving attorney:', error)
      toast({
        title: 'Error',
        description: 'Failed to save attorney',
        variant: 'destructive',
      })
    }
  }

  // Handle attorney deletion
  const handleDelete = async () => {
    if (!currentAttorney) return
    try {
      await deleteMutation.mutateAsync(currentAttorney.id)
      toast({
        title: 'Success',
        description: 'Attorney deleted successfully',
      })
      setIsDeleteDialogOpen(false)
      mutate() // Refresh the list
    } catch (error) {
      console.error('Error deleting attorney:', error)
      toast({
        title: 'Error',
        description: 'Failed to delete attorney',
        variant: 'destructive',
      })
    }
  }

  // Reset form on dialog open
  const handleAddNew = () => {
    setIsEditMode(false)
    setCurrentAttorney(null)
    setFormState({
      name: '',
      attorney_type: AttorneyType.OTHER,
      firm_name: '',
      email: '',
      phone: '',
      address: '',
      notes: '',
    })
    reset({
      name: '',
      attorney_type: AttorneyType.OTHER,
      firm_name: '',
      email: '',
      phone: '',
      address: '',
      notes: '',
    })
    setIsFormDialogOpen(true)
  }

  const getAttorneyTypeColor = (type: AttorneyType) => {
    switch (type) {
      case AttorneyType.PLAINTIFF:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case AttorneyType.DEFENSE:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case AttorneyType.COVERAGE:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case AttorneyType.MONITORING:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Render loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Attorneys</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-6">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Loading attorneys...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Render error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Attorneys</CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-6">
          <div className="flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-sm text-muted-foreground">Error loading attorneys</p>
            <Button size="sm" variant="outline" onClick={() => mutate()}>
              Try again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Attorneys</CardTitle>
        <Button className="flex items-center gap-1" onClick={handleAddNew}>
          <Plus className="h-4 w-4 mr-1" />
          Add Attorney
        </Button>
      </CardHeader>
      <CardContent>
        {attorneys && attorneys.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-6">
            <p className="text-sm text-muted-foreground">No attorneys found</p>
            <Button size="sm" variant="outline" className="mt-2" onClick={handleAddNew}>
              Add your first attorney
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Firm</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Notes</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {attorneys?.map(attorney => (
                <TableRow key={attorney.id}>
                  <TableCell className="font-medium">{attorney.name}</TableCell>
                  <TableCell>{attorney.firm_name || '-'}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      {attorney.email && (
                        <a
                          href={`mailto:${attorney.email}`}
                          className="text-sm text-primary hover:underline"
                        >
                          {attorney.email}
                        </a>
                      )}
                      {attorney.phone && (
                        <a
                          href={`tel:${attorney.phone}`}
                          className="text-xs text-muted-foreground hover:underline"
                        >
                          {attorney.phone}
                        </a>
                      )}
                      {!attorney.email && !attorney.phone && '-'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={getAttorneyTypeColor(attorney.attorney_type)}
                    >
                      {attorney.attorney_type}
                    </Badge>
                  </TableCell>
                  <TableCell className="max-w-[200px] truncate" title={attorney.notes || ''}>
                    {attorney.notes || '-'}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        title="Edit"
                        onClick={() => handleEditAttorney(attorney)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        title="Delete"
                        onClick={() => handleDeleteClick(attorney)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}

        {/* Attorney Form Dialog */}
        <Dialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{isEditMode ? 'Edit Attorney' : 'Add Attorney'}</DialogTitle>
              <DialogDescription>
                {isEditMode
                  ? 'Update the attorney details below.'
                  : 'Enter the details of the attorney.'}
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name" className="text-right">
                    Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id="name"
                    {...register('name')}
                    value={formState.name}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                  {errors.name && (
                    <p className="col-span-3 col-start-2 text-sm text-destructive">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="attorney_type" className="text-right">
                    Type <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    value={formState.attorney_type}
                    onValueChange={value => handleSelectChange(value, 'attorney_type')}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select attorney type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={AttorneyType.PLAINTIFF}>Plaintiff</SelectItem>
                      <SelectItem value={AttorneyType.DEFENSE}>Defense</SelectItem>
                      <SelectItem value={AttorneyType.COVERAGE}>Coverage</SelectItem>
                      <SelectItem value={AttorneyType.MONITORING}>Monitoring</SelectItem>
                      <SelectItem value={AttorneyType.OTHER}>Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.attorney_type && (
                    <p className="col-span-3 col-start-2 text-sm text-destructive">
                      {errors.attorney_type.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="firm_name" className="text-right">
                    Firm Name
                  </Label>
                  <Input
                    id="firm_name"
                    {...register('firm_name')}
                    value={formState.firm_name || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    Email
                  </Label>
                  <Input
                    id="email"
                    {...register('email')}
                    type="email"
                    value={formState.email || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                  {errors.email && (
                    <p className="col-span-3 col-start-2 text-sm text-destructive">
                      {errors.email.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="phone" className="text-right">
                    Phone
                  </Label>
                  <Input
                    id="phone"
                    {...register('phone')}
                    value={formState.phone || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="address" className="text-right">
                    Address
                  </Label>
                  <Input
                    id="address"
                    {...register('address')}
                    value={formState.address || ''}
                    onChange={handleInputChange}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="notes" className="text-right">
                    Notes
                  </Label>
                  <Textarea
                    id="notes"
                    {...register('notes')}
                    value={formState.notes || ''}
                    onChange={handleInputChange}
                    className="col-span-3 min-h-[100px]"
                  />
                </div>
              </div>
              <DialogFooter>
                <DialogClose asChild>
                  <Button type="button" variant="outline">
                    Cancel
                  </Button>
                </DialogClose>
                <Button
                  type="submit"
                  disabled={createMutation.isPending || updateMutation.isPending}
                >
                  {(createMutation.isPending || updateMutation.isPending) && (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  )}
                  {isEditMode ? 'Update' : 'Add'} Attorney
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Delete Confirmation Dialog */}
        <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Delete Attorney</DialogTitle>
              <DialogDescription>
                Are you sure you want to delete this attorney? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">
                  Cancel
                </Button>
              </DialogClose>
              <Button
                variant="destructive"
                disabled={deleteMutation.isPending}
                onClick={handleDelete}
              >
                {deleteMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Delete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  )
}
