'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Plus, Pencil, Trash, Loader2, AlertCircle } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from '@/components/ui/dialog'
import { useWitnesses, useWitnessMutation } from '@/hooks/useWitnesses'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { WitnessResponse, WitnessCreate, WitnessUpdate } from '@/lib/api/types'

interface ClaimWitnessesProps {
  claimId: string
}

export function ClaimWitnesses({ claimId }: ClaimWitnessesProps) {
  const { toast } = useToast()
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isFormDialogOpen, setIsFormDialogOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [witnessToDelete, setWitnessToDelete] = useState<WitnessResponse | null>(null)

  // Form state
  const [formState, setFormState] = useState<WitnessCreate>({
    name: '',
    email: '',
    phone: '',
    address: '',
    statement: '',
  })

  // Get witnesses data
  const { data: witnesses, isLoading, error, refetch } = useWitnesses(claimId)

  // Get witness mutations
  const {
    createWitness,
    updateWitness,
    deleteWitness,
    currentWitness,
    setCurrentWitness,
    isCreating,
    isUpdating,
    isDeleting,
  } = useWitnessMutation(claimId)

  // Reset form state
  const resetForm = () => {
    setFormState({
      name: '',
      email: '',
      phone: '',
      address: '',
      statement: '',
    })
    setCurrentWitness(null)
    setIsEditMode(false)
  }

  // Handle opening the edit dialog
  const handleEditWitness = (witness: WitnessResponse) => {
    setIsEditMode(true)
    setCurrentWitness(witness)
    setFormState({
      name: witness.name,
      email: witness.email || '',
      phone: witness.phone || '',
      address: witness.address || '',
      statement: witness.statement || '',
    })
    setIsFormDialogOpen(true)
  }

  // Handle form submit
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      if (isEditMode && currentWitness) {
        // Update existing witness
        await updateWitness({
          witnessId: currentWitness.id,
          data: formState as WitnessUpdate,
        })
        toast({
          title: 'Witness updated',
          description: 'Witness has been updated successfully',
        })
      } else {
        // Create new witness
        await createWitness(formState)
        toast({
          title: 'Witness added',
          description: 'Witness has been added successfully',
        })
      }

      setIsFormDialogOpen(false)
      resetForm()
      refetch()
    } catch (err) {
      toast({
        title: 'Error',
        description: isEditMode
          ? 'Failed to update witness. Please try again.'
          : 'Failed to add witness. Please try again.',
        variant: 'destructive',
      })
      console.error('Witness operation failed:', err)
    }
  }

  // Handle delete
  const handleDeleteWitness = async () => {
    if (!witnessToDelete) return

    try {
      await deleteWitness(witnessToDelete.id)
      toast({
        title: 'Witness deleted',
        description: 'Witness has been deleted successfully',
      })
      setIsDeleteDialogOpen(false)
      setWitnessToDelete(null)
      refetch()
    } catch (err) {
      toast({
        title: 'Error',
        description: 'Failed to delete witness. Please try again.',
        variant: 'destructive',
      })
      console.error('Delete witness failed:', err)
    }
  }

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormState(prev => ({ ...prev, [name]: value }))
  }

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Witnesses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading witnesses...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Witnesses</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-8 text-destructive">
            <AlertCircle className="h-8 w-8 mr-2" />
            <span>Failed to load witnesses. Please try again later.</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Witnesses</CardTitle>
        <Button
          className="flex items-center gap-1"
          onClick={() => {
            resetForm()
            setIsFormDialogOpen(true)
          }}
        >
          <Plus className="h-4 w-4 mr-1" />
          Add Witness
        </Button>
      </CardHeader>
      <CardContent>
        {!witnesses || witnesses.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <p className="text-muted-foreground mb-4">No witnesses have been added yet.</p>
            <Button
              variant="outline"
              className="flex items-center gap-1"
              onClick={() => {
                resetForm()
                setIsFormDialogOpen(true)
              }}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add First Witness
            </Button>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Contact</TableHead>
                <TableHead>Statement</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {witnesses.map(witness => (
                <TableRow key={witness.id}>
                  <TableCell className="font-medium">{witness.name}</TableCell>
                  <TableCell>
                    <div className="flex flex-col">
                      {witness.email && (
                        <a
                          href={`mailto:${witness.email}`}
                          className="text-sm text-primary hover:underline"
                        >
                          {witness.email}
                        </a>
                      )}
                      {witness.phone && (
                        <a
                          href={`tel:${witness.phone}`}
                          className="text-xs text-muted-foreground hover:underline"
                        >
                          {witness.phone}
                        </a>
                      )}
                      {!witness.email && !witness.phone && (
                        <span className="text-xs text-muted-foreground">No contact info</span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="max-w-[300px] truncate" title={witness.statement || ''}>
                    {witness.statement || (
                      <span className="text-muted-foreground italic">No statement</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        title="Edit"
                        onClick={() => handleEditWitness(witness)}
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8"
                        title="Delete"
                        onClick={() => {
                          setWitnessToDelete(witness)
                          setIsDeleteDialogOpen(true)
                        }}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>

      {/* Witness Form Dialog */}
      <Dialog open={isFormDialogOpen} onOpenChange={setIsFormDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{isEditMode ? 'Edit Witness' : 'Add Witness'}</DialogTitle>
            <DialogDescription>
              {isEditMode
                ? 'Update witness information for this claim.'
                : 'Add a new witness to this claim.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name <span className="text-destructive">*</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formState.name}
                  onChange={handleInputChange}
                  className="col-span-3"
                  required
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formState.email || ''}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="phone" className="text-right">
                  Phone
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formState.phone || ''}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="address" className="text-right">
                  Address
                </Label>
                <Input
                  id="address"
                  name="address"
                  value={formState.address || ''}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="statement" className="text-right pt-2">
                  Statement
                </Label>
                <Textarea
                  id="statement"
                  name="statement"
                  value={formState.statement || ''}
                  onChange={handleInputChange}
                  className="col-span-3"
                  rows={4}
                />
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsFormDialogOpen(false)
                  resetForm()
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isCreating || isUpdating || !formState.name.trim()}>
                {(isCreating || isUpdating) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditMode ? 'Update Witness' : 'Add Witness'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Witness</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this witness? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsDeleteDialogOpen(false)
                setWitnessToDelete(null)
              }}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteWitness} disabled={isDeleting}>
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
}
