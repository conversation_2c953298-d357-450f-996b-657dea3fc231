'use client'

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  AlertCircle,
  Loader2,
  UserCircle,
  FileText,
  CheckSquare,
  DollarSign,
  Briefcase,
  Anchor,
  Edit3,
  PlusCircle,
  ListChecks,
} from 'lucide-react'
import { useState, useEffect } from 'react'
import { useApi } from '@/hooks/useApi'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { api } from '@/lib/api'
import {
  Claim,
  StatusHistorySchema,
  NoteSchema,
  Task,
  Document,
  ReserveResponse,
  PaymentResponse,
} from '@/lib/api/types'
import { useUserResolver } from '@/hooks/useUserResolver'

interface TimelineEvent {
  id: string
  timestamp: Date
  type: string // e.g., 'CLAIM_CREATED', 'STATUS_CHANGED', 'NOTE_ADDED'
  title: string
  description?: string
  user?: string // Name or identifier of the user associated with the event
  icon?: React.ReactNode // Optional: Specific icon for the event type
  data?: any // Optional: Original data object for more details if needed
}

interface ClaimTimelineProps {
  claimId: string
}

const TIMELINE_INCLUDES = [
  'customer',
  'status_history',
  'notes',
  'tasks',
  'documents',
  'witnesses',
  'attorneys',
  'financials',
]

export function ClaimTimeline({ claimId }: ClaimTimelineProps) {
  const { formatDateTime } = useDateFormatter()
  const [claimUuid, setClaimUuid] = useState<string | null>(null)
  const [processedTimelineEvents, setProcessedTimelineEvents] = useState<TimelineEvent[]>([])

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // Helper function to get user display name or fallback
  const getUserDisplayForTimeline = (
    userId?: string | null,
    fallback: string = 'System'
  ): string => {
    if (!userId) return fallback
    return getUserDisplayName(userId)
  }

  const {
    data: claimDataFull,
    isLoading: isLoadingClaim,
    error: claimError,
    refetch: refetchClaimData,
  } = useApi(() => api.claims.getClaimByNumber(claimId, TIMELINE_INCLUDES), [claimId])

  useEffect(() => {
    if (claimDataFull && claimDataFull.id) {
      setClaimUuid(claimDataFull.id)
    } else {
      setClaimUuid(null)
    }
  }, [claimDataFull])

  const {
    data: reserveHistory,
    isLoading: isLoadingReserveHistory,
    error: reserveHistoryError,
    refetch: refetchReserveHistory,
  } = useApi<any>(async () => {
    if (!claimUuid) return null
    return api.financials.getClaimReserveHistory(claimUuid)
  }, [claimUuid])

  const {
    data: payments,
    isLoading: isLoadingPayments,
    error: paymentsError,
    refetch: refetchPayments,
  } = useApi<any>(async () => {
    if (!claimUuid) return null
    return api.financials.listClaimPayments(claimUuid)
  }, [claimUuid])

  const isLoadingFinancials = claimUuid && (isLoadingReserveHistory || isLoadingPayments)
  const isLoading = isLoadingClaim || isLoadingFinancials

  const anyError = claimError || (claimUuid && (reserveHistoryError || paymentsError))

  // Extract and resolve all user IDs from timeline data
  useEffect(() => {
    const allUserIds: string[] = []

    if (claimDataFull) {
      // Collect user IDs from various sources
      if (claimDataFull.created_by_id) allUserIds.push(claimDataFull.created_by_id)

      if (claimDataFull.status_history) {
        claimDataFull.status_history.forEach((item: StatusHistorySchema) => {
          if (item.changed_by_id) allUserIds.push(item.changed_by_id)
        })
      }

      if (claimDataFull.tasks) {
        claimDataFull.tasks.forEach((task: any) => {
          if (task.created_by_id) allUserIds.push(task.created_by_id)
          if (task.assignee_id) allUserIds.push(task.assignee_id)
        })
      }

      if (claimDataFull.documents) {
        claimDataFull.documents.forEach((doc: any) => {
          if (doc.created_by_id) allUserIds.push(doc.created_by_id)
        })
      }

      if (claimDataFull.witnesses) {
        claimDataFull.witnesses.forEach((witness: any) => {
          if (witness.created_by_id) allUserIds.push(witness.created_by_id)
        })
      }

      if (claimDataFull.attorneys) {
        claimDataFull.attorneys.forEach((attorney: any) => {
          if (attorney.created_by_id) allUserIds.push(attorney.created_by_id)
        })
      }
    }

    // Handle reserve history - check if it's an array or paginated object
    if (reserveHistory) {
      const reserveItems = Array.isArray(reserveHistory)
        ? reserveHistory
        : reserveHistory.items || []
      reserveItems.forEach((item: any) => {
        if (item.changed_by_id) allUserIds.push(item.changed_by_id)
      })
    }

    // Handle payments - check if it's an array or paginated object
    if (payments) {
      const paymentItems = Array.isArray(payments) ? payments : payments.items || []
      paymentItems.forEach((item: any) => {
        if (item.created_by_id) allUserIds.push(item.created_by_id)
      })
    }

    // Remove duplicates and resolve users
    const uniqueUserIds = [...new Set(allUserIds)].filter(Boolean)
    if (uniqueUserIds.length > 0) {
      resolveUsers(uniqueUserIds)
    }
  }, [claimDataFull, reserveHistory, payments, resolveUsers])

  useEffect(() => {
    const events: TimelineEvent[] = []

    // Helper function to safely create a Date object
    const createSafeDate = (dateString?: string | null): Date | null => {
      if (!dateString) return null
      try {
        const date = new Date(dateString)
        // Check if the date is valid
        if (isNaN(date.getTime())) return null
        return date
      } catch (error) {
        console.warn('Invalid date string:', dateString, error)
        return null
      }
    }

    if (claimDataFull) {
      // 1. Claim Creation Event
      if (claimDataFull.created_at) {
        const createdDate = createSafeDate(claimDataFull.created_at)
        if (createdDate) {
          events.push({
            id: `claim-${claimDataFull.id}-created`,
            timestamp: createdDate,
            type: 'CLAIM_CREATED',
            title: 'Claim Created',
            description: `Claim ${claimDataFull.claim_number} was initiated.`,
            user: getUserDisplayForTimeline(claimDataFull.created_by_id),
            icon: <PlusCircle className="h-5 w-5" />,
          })
        }
      }

      // 2. Status Change Events
      if (claimDataFull.status_history && Array.isArray(claimDataFull.status_history)) {
        claimDataFull.status_history.forEach((historyItem: StatusHistorySchema) => {
          const changeDate = createSafeDate(historyItem.changed_at)
          if (changeDate) {
            events.push({
              id: `status-${historyItem.id}`,
              timestamp: changeDate,
              type: 'STATUS_CHANGED',
              title: `Status changed to ${historyItem.new_status}`,
              description: `From ${historyItem.previous_status || 'Initial'} to ${historyItem.new_status}. ${historyItem.notes ? `Reason: ${historyItem.notes}` : ''}`,
              user: getUserDisplayForTimeline(historyItem.changed_by_id),
              icon: <ListChecks className="h-5 w-5" />,
              data: historyItem,
            })
          }
        })
      }

      // 3. Note Added Events
      if (claimDataFull.notes && Array.isArray(claimDataFull.notes)) {
        claimDataFull.notes.forEach((note: NoteSchema) => {
          const noteDate = createSafeDate(note.created_at)
          if (noteDate) {
            events.push({
              id: `note-${note.id}`,
              timestamp: noteDate,
              type: 'NOTE_ADDED',
              title: 'Note Added',
              description: note.content,
              user: note.author || 'System',
              icon: <FileText className="h-5 w-5" />,
              data: note,
            })
          }
        })
      }

      // 4. Task Created Events
      if (claimDataFull.tasks && Array.isArray(claimDataFull.tasks)) {
        claimDataFull.tasks.forEach(
          (task: {
            id: string
            created_at: string
            title: string
            priority?: string
            due_date?: string
            status?: string
            created_by_id?: string
            completed_at?: string
            assignee_id?: string
            [key: string]: any
          }) => {
            const taskCreatedDate = createSafeDate(task.created_at)
            if (taskCreatedDate) {
              let description = `Status: ${task.status || 'N/A'}`
              if (task.priority) description += `, Priority: ${task.priority}`
              if (task.due_date) {
                const dueDate = createSafeDate(task.due_date)
                if (dueDate) {
                  description += `, Due: ${formatDateTime(dueDate.toISOString())}`
                }
              }

              events.push({
                id: `task-created-${task.id}`,
                timestamp: taskCreatedDate,
                type: 'TASK_CREATED',
                title: `Task Created: ${task.title}`,
                description: description,
                user: getUserDisplayForTimeline(task.created_by_id),
                icon: <CheckSquare className="h-5 w-5" />,
                data: task,
              })

              // Check for Task Completed event
              if (task.completed_at) {
                const completedDate = createSafeDate(task.completed_at)
                if (completedDate) {
                  events.push({
                    id: `task-completed-${task.id}`,
                    timestamp: completedDate,
                    type: 'TASK_COMPLETED',
                    title: `Task Completed: ${task.title}`,
                    description: `Status: ${task.status || 'Completed'}`,
                    user: task.assignee_id
                      ? getUserDisplayForTimeline(task.assignee_id, 'Assignee')
                      : getUserDisplayForTimeline(task.created_by_id, 'Creator'),
                    icon: (
                      <span>
                        <CheckSquare className="h-5 w-5 text-green-600" />
                      </span>
                    ),
                    data: task,
                  })
                }
              }
            }
          }
        )
      }

      // 5. Document Added Events
      if (claimDataFull.documents && Array.isArray(claimDataFull.documents)) {
        claimDataFull.documents.forEach(
          (doc: {
            id: string
            created_at: string
            name: string
            type: string
            created_by_id?: string
            [key: string]: any
          }) => {
            const docCreatedDate = createSafeDate(doc.created_at)
            if (docCreatedDate) {
              events.push({
                id: `document-${doc.id}`,
                timestamp: docCreatedDate,
                type: 'DOCUMENT_ADDED',
                title: `Document Added: ${doc.name}`,
                description: `Type: ${doc.type}`,
                user: getUserDisplayForTimeline(doc.created_by_id),
                icon: <FileText className="h-5 w-5" />,
                data: doc,
              })
            }
          }
        )
      }

      // 6. Witness Added Events
      if (claimDataFull.witnesses && Array.isArray(claimDataFull.witnesses)) {
        claimDataFull.witnesses.forEach(
          (witness: {
            id: string
            created_at: string
            name: string
            created_by_id?: string
            [key: string]: any
          }) => {
            const witnessDate = createSafeDate(witness.created_at)
            if (witnessDate) {
              events.push({
                id: `witness-${witness.id}`,
                timestamp: witnessDate,
                type: 'WITNESS_ADDED',
                title: `Witness Added: ${witness.name}`,
                user: getUserDisplayForTimeline(witness.created_by_id),
                icon: <UserCircle className="h-5 w-5" />,
                data: witness,
              })
            }
          }
        )
      }

      // 7. Attorney Added Events
      if (claimDataFull.attorneys && Array.isArray(claimDataFull.attorneys)) {
        claimDataFull.attorneys.forEach(
          (attorney: {
            id: string
            created_at: string
            name: string
            attorney_type: string
            created_by_id?: string
            [key: string]: any
          }) => {
            const attorneyDate = createSafeDate(attorney.created_at)
            if (attorneyDate) {
              events.push({
                id: `attorney-${attorney.id}`,
                timestamp: attorneyDate,
                type: 'ATTORNEY_ADDED',
                title: `Attorney Added: ${attorney.name}`,
                description: `Type: ${attorney.attorney_type}`,
                user: getUserDisplayForTimeline(attorney.created_by_id),
                icon: <Briefcase className="h-5 w-5" />,
                data: attorney,
              })
            }
          }
        )
      }

      // 8. Reserve Change Events (from detailed reserveHistory fetch)
      if (reserveHistory) {
        const reserveItems = Array.isArray(reserveHistory)
          ? reserveHistory
          : reserveHistory.items || []
        reserveItems.forEach((item: any) => {
          const reserveChangeDate = createSafeDate(item.changed_at)
          if (reserveChangeDate) {
            // Convert string amounts to numbers if needed
            const prevAmount =
              typeof item.previous_amount === 'string'
                ? parseFloat(item.previous_amount)
                : item.previous_amount
            const newAmount =
              typeof item.new_amount === 'string' ? parseFloat(item.new_amount) : item.new_amount

            const formatCurrency = (amount: number) =>
              new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount)
            events.push({
              id: `reserve-${item.id}`,
              timestamp: reserveChangeDate,
              type: 'RESERVE_CHANGED',
              title: `Reserve Changed: ${item.reserve_type}`,
              description: `From ${formatCurrency(prevAmount)} to ${formatCurrency(newAmount)}.`,
              user: getUserDisplayForTimeline(item.changed_by_id),
              icon: <DollarSign className="h-5 w-5" />,
              data: item,
            })
          }
        })
      }

      // 9. Payment Made Events (from detailed payments fetch)
      if (payments) {
        const paymentItems = Array.isArray(payments) ? payments : payments.items || []
        paymentItems.forEach(
          (item: {
            id: string
            payment_date?: string
            created_at: string
            payment_type: string
            amount: number
            payee: string
            created_by_id?: string
            [key: string]: any
          }) => {
            const formatCurrency = (amount: number) =>
              new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount)
            const paymentTimestamp = createSafeDate(item.payment_date || item.created_at)
            if (paymentTimestamp) {
              events.push({
                id: `payment-${item.id}`,
                timestamp: paymentTimestamp,
                type: 'PAYMENT_MADE',
                title: `Payment: ${item.payment_type}`,
                description: `${formatCurrency(item.amount)} to ${item.payee}`,
                user: getUserDisplayForTimeline(item.created_by_id),
                icon: <DollarSign className="h-5 w-5" />,
                data: item,
              })
            }
          }
        )
      }

      // 10. Financial Summary Events (from claimDataFull.financials)
      if (claimDataFull.financials && typeof claimDataFull.financials === 'object') {
        const financialsSummary = claimDataFull.financials as {
          last_reserve_change_date?: string | Date
          last_payment_date?: string | Date
          [key: string]: any
        }

        // Check for Last Reserve Change Date on the summary object
        if (financialsSummary.last_reserve_change_date) {
          const reserveChangeDate = createSafeDate(
            typeof financialsSummary.last_reserve_change_date === 'string'
              ? financialsSummary.last_reserve_change_date
              : financialsSummary.last_reserve_change_date.toString()
          )
          if (reserveChangeDate) {
            events.push({
              id: `summary-last-reserve-change-${claimDataFull.id}`,
              timestamp: reserveChangeDate,
              type: 'FINANCIAL_SUMMARY_EVENT',
              title: 'Last Reserve Change Recorded',
              description: 'A reserve amount was last changed on this date.',
              user: 'System',
              icon: <DollarSign className="h-5 w-5" />,
              data: { date: financialsSummary.last_reserve_change_date },
            })
          }
        }

        // Check for Last Payment Date on the summary object
        if (financialsSummary.last_payment_date) {
          const paymentDate = createSafeDate(
            typeof financialsSummary.last_payment_date === 'string'
              ? financialsSummary.last_payment_date
              : financialsSummary.last_payment_date.toString()
          )
          if (paymentDate) {
            events.push({
              id: `summary-last-payment-${claimDataFull.id}`,
              timestamp: paymentDate,
              type: 'FINANCIAL_SUMMARY_EVENT',
              title: 'Last Payment Recorded',
              description: 'A payment was last made or recorded on this date.',
              user: 'System',
              icon: <DollarSign className="h-5 w-5" />,
              data: { date: financialsSummary.last_payment_date },
            })
          }
        }
      }
    }

    // Sort events in reverse chronological order
    events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    setProcessedTimelineEvents(events)
  }, [claimDataFull, reserveHistory, payments, formatDateTime]) // Dependencies for reprocessing timeline

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Claim Timeline</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {isLoadingClaim ? 'Loading claim details...' : 'Loading financial details...'}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (anyError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Claim Timeline</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-sm text-destructive">
              {claimError || reserveHistoryError || paymentsError || 'Failed to load timeline data'}
            </p>
            <button
              onClick={() => {
                if (claimError) refetchClaimData()
                if (reserveHistoryError) refetchReserveHistory()
                if (paymentsError) refetchPayments()
              }}
              className="mt-2 px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!claimDataFull && !isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Claim Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Claim not found or no timeline data available.</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Claim Timeline</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {processedTimelineEvents.length > 0 ? (
          <div className="relative pl-6 border-l border-gray-200 dark:border-gray-700">
            {processedTimelineEvents.map((event, index) => (
              <div key={event.id} className="mb-8 relative">
                <div className="absolute -left-[32px] top-0 flex items-center justify-center h-[28px] w-[28px] rounded-full bg-primary border-2 border-background">
                  {event.icon ? (
                    <span className="text-primary-foreground">{event.icon}</span>
                  ) : (
                    <Edit3 className="h-4 w-4 text-primary-foreground" />
                  )}
                </div>
                <div className="ml-6">
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-semibold text-sm text-foreground">{event.title}</span>
                    <time className="text-xs text-muted-foreground">
                      {formatDateTime(event.timestamp.toISOString())}
                    </time>
                  </div>
                  {event.user && (
                    <p className="text-xs text-muted-foreground mb-1">By: {event.user}</p>
                  )}
                  {event.description && (
                    <p className="text-sm text-foreground">{event.description}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p>No timeline events available for this claim.</p>
        )}
      </CardContent>
    </Card>
  )
}
