'use client'

import { useState, useEffect, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { SortableHeader, type SortDirection } from '@/components/ui/sortable-header'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api' // Import the API object instead of just tasksApi
import {
  Task,
  TaskPriority,
  TaskStatus,
  UserReadBasic,
  BackendPaginatedResponse,
} from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'
import { cn } from '@/lib/utils'

export interface TasksTableProps {
  searchQuery?: string
  statusFilter?: string
  priorityFilter?: string
  assigneeFilter?: string
}

export function TasksTable({
  searchQuery = '',
  statusFilter = '',
  priorityFilter = '',
  assigneeFilter = '',
}: TasksTableProps) {
  const { formatDate } = useDateFormatter()
  const [sortColumn, setSortColumn] = useState<string | null>('due_date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // Fetch tasks with filters and pagination
  const getTasks = async () => {
    try {
      console.log('🔍 Fetching tasks with filters:', {
        searchQuery,
        statusFilter,
        priorityFilter,
        assigneeFilter,
        currentPage,
        pageSize,
      })

      const params: any = {
        skip: (currentPage - 1) * pageSize,
        limit: pageSize,
      }

      // Add filters if they're set
      if (searchQuery) params.title = searchQuery
      if (statusFilter && statusFilter !== 'all') params.status = statusFilter
      if (priorityFilter && priorityFilter !== 'all') params.priority = priorityFilter
      if (assigneeFilter && assigneeFilter !== 'all') params.assignee = assigneeFilter

      console.log('📄 Request params:', params)

      try {
        const response = await api.tasks.getTasks(params)
        console.log('✅ Tasks response received:', response)

        // The API now returns BackendPaginatedResponse<Task> directly
        return response
      } catch (apiError) {
        console.error('❌ API call failed:', apiError)
        throw apiError
      }
    } catch (error) {
      console.error('❌ Error in getTasks function:', error)
      throw error instanceof Error ? error : new Error('Failed to fetch tasks: Unknown error')
    }
  }

  // Use the API hook to fetch data
  const {
    data: tasksData,
    isLoading,
    error,
    refetch,
  } = useApi<BackendPaginatedResponse<Task>>(getTasks, [
    searchQuery,
    statusFilter,
    priorityFilter,
    assigneeFilter,
    currentPage,
    pageSize,
  ])

  // Transform API data to match expected format
  const { tasks, totalItems, totalPages } = useMemo(() => {
    if (!tasksData) {
      return {
        tasks: [],
        totalItems: 0,
        totalPages: 0,
      }
    }

    const tasksArray = tasksData.items || []
    const total = tasksData.total || 0
    const pages = Math.ceil(total / pageSize)

    return {
      tasks: tasksArray,
      totalItems: total,
      totalPages: pages,
    }
  }, [tasksData, pageSize])

  // Collect and resolve assignee IDs from tasks
  useEffect(() => {
    if (tasks && tasks.length > 0) {
      const assigneeIds: string[] = []

      tasks.forEach(task => {
        if (typeof task.assignee === 'string') {
          assigneeIds.push(task.assignee)
        } else if (task.assignee && typeof task.assignee === 'object' && task.assignee.id) {
          assigneeIds.push(task.assignee.id)
        }
      })

      const uniqueAssigneeIds = [...new Set(assigneeIds)].filter(Boolean)
      if (uniqueAssigneeIds.length > 0) {
        resolveUsers(uniqueAssigneeIds)
      }
    }
  }, [tasks, resolveUsers])

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc')
      if (sortDirection === null) {
        setSortColumn(null)
      }
    } else {
      // Set new column and default to ascending
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  // Pagination handlers
  const handlePageChange = (page: number) => {
    // Ensure page is always at least 1
    setCurrentPage(Math.max(1, page))
  }

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(parseInt(newPageSize))
    setCurrentPage(1) // Reset to first page when changing page size
  }

  const sortedTasks = tasks
    ? [...tasks].sort((a, b) => {
        if (!sortColumn || sortDirection === null) return 0

        const direction = sortDirection === 'asc' ? 1 : -1

        switch (sortColumn) {
          case 'id':
            return direction * a.id.localeCompare(b.id)
          case 'title':
            return direction * a.title.localeCompare(b.title)
          case 'due_date':
            // Handle null values
            if (!a.due_date) return direction * 1
            if (!b.due_date) return direction * -1
            return direction * (new Date(a.due_date).getTime() - new Date(b.due_date).getTime())
          case 'claim_id':
            // Handle null values and choose claim_number if available
            if (!a.claim_id && !a.claim_number) return direction * 1
            if (!b.claim_id && !b.claim_number) return direction * -1
            const claimValueA = a.claim_number || a.claim_id || ''
            const claimValueB = b.claim_number || b.claim_id || ''
            return direction * claimValueA.localeCompare(claimValueB)
          case 'priority':
            const priorityOrder = {
              [TaskPriority.URGENT]: 0,
              [TaskPriority.HIGH]: 1,
              [TaskPriority.MEDIUM]: 2,
              [TaskPriority.LOW]: 3,
            }
            return (
              direction *
              (priorityOrder[a.priority as keyof typeof priorityOrder] -
                priorityOrder[b.priority as keyof typeof priorityOrder])
            )
          case 'status':
            return direction * a.status.localeCompare(b.status)
          case 'assignee':
            // Handle null values
            if (!a.assignee) return direction * 1
            if (!b.assignee) return direction * -1

            // Handle different assignee types - with safer type checking
            const aValue =
              typeof a.assignee === 'string'
                ? a.assignee
                : (a.assignee as UserReadBasic)?.email || ''
            const bValue =
              typeof b.assignee === 'string'
                ? b.assignee
                : (b.assignee as UserReadBasic)?.email || ''

            return direction * aValue.localeCompare(bValue)

          default:
            return 0
        }
      })
    : []

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case TaskPriority.HIGH:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case TaskStatus.BLOCKED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Helper to format display status
  const formatStatus = (status: string) => {
    return status.replace('_', ' ')
  }

  // Helper to get a task identifier for display
  const getTaskDisplayId = (task: Task) => {
    return task.hr_id || task.id.substring(0, 8)
  }

  // Helper to get a task identifier for linking
  const getTaskLinkId = (task: Task) => {
    // Prefer hr_id for routing if available
    return task.hr_id || task.id
  }

  // Helper to get assignee display name
  const getAssigneeName = (assignee: string | UserReadBasic | null): string => {
    if (!assignee) return 'Unknown'

    if (typeof assignee === 'string') {
      // If it's a UUID string, resolve it
      return getUserDisplayName(assignee)
    }

    // If it's a user object, try to resolve by ID first
    const resolvedName = getUserDisplayName(assignee.id)
    if (resolvedName !== 'Unknown User') {
      return resolvedName
    }

    // Fallback to email
    return assignee.email || 'Unknown'
  }

  // Helper to get assignee initials for avatar
  const getAssigneeInitials = (assignee: string | UserReadBasic | null): string => {
    if (!assignee) return 'UN'

    const name = getAssigneeName(assignee)

    // Generate initials from resolved name
    if (name && name !== 'Unknown' && name !== 'Unknown User') {
      return (
        name
          .split(' ')
          .map(n => n.charAt(0))
          .join('')
          .slice(0, 2)
          .toUpperCase() || 'UN'
      )
    }

    return 'UN'
  }

  // Helper to get assignee ID for link
  const getAssigneeId = (assignee: string | UserReadBasic | null): string => {
    if (!assignee) return '0'
    if (typeof assignee === 'string') return assignee
    return assignee.id || '0'
  }

  // Display loading or error states
  if (isLoading) {
    return <div className="flex justify-center p-8">Loading tasks...</div>
  }

  if (error) {
    return (
      <div className="rounded-md border p-6 text-center">
        <div className="text-red-500 mb-4">Error loading tasks: {error}</div>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Page size selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Show</span>
          <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">tasks per page</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {totalItems > 0 ? (currentPage - 1) * pageSize + 1 : 0} to{' '}
          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} tasks
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-10">
                <Checkbox id="select-all" />
              </TableHead>
              <SortableHeader
                column="id"
                label="Task #"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="title"
                label="Task"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="due_date"
                label="Due Date"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="claim_id"
                label="Claim"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="priority"
                label="Priority"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="status"
                label="Status"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="assignee"
                label="Assignee"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <TableHead className="w-10"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedTasks.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10 text-muted-foreground">
                  No tasks found
                </TableCell>
              </TableRow>
            ) : (
              sortedTasks.map(task => (
                <TableRow key={task.id}>
                  <TableCell>
                    <Checkbox id={`select-${task.id}`} />
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/tasks/${getTaskLinkId(task)}`}
                      className="font-medium hover:underline"
                    >
                      {getTaskDisplayId(task)}
                    </Link>
                  </TableCell>
                  <TableCell>{task.title}</TableCell>
                  <TableCell>{formatDate(task.due_date)}</TableCell>
                  <TableCell>
                    {task.claim_id ? (
                      <Link href={`/claims/${task.claim_id}`} className="hover:underline">
                        {task.claim_number || task.claim_id}
                      </Link>
                    ) : (
                      <span className="text-gray-400">No claim</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(task.status)}>
                      {formatStatus(task.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {task.assignee ? (
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="/placeholder-user.jpg" alt="User" />
                          <AvatarFallback>{getAssigneeInitials(task.assignee)}</AvatarFallback>
                        </Avatar>
                        <Link
                          href={`/users/${getAssigneeId(task.assignee)}`}
                          className="text-sm hover:underline"
                        >
                          {getAssigneeName(task.assignee)}
                        </Link>
                      </div>
                    ) : (
                      <span className="text-gray-400">Unassigned</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Link href={`/tasks/${task.id}`}>
                      <Button size="icon" variant="ghost" className="h-8 w-8">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Pagination>
            <PaginationContent>
              {/* First Page Button */}
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage > 1) handlePageChange(1)
                  }}
                  className={cn(
                    'gap-1 pl-2.5',
                    currentPage <= 1 ? 'pointer-events-none opacity-50' : ''
                  )}
                  aria-label="Go to first page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                  <span>First</span>
                </PaginationLink>
              </PaginationItem>

              {/* Previous Page Button */}
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage > 1) handlePageChange(currentPage - 1)
                  }}
                  className={currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>

              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum: number
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      href="#"
                      onClick={e => {
                        e.preventDefault()
                        handlePageChange(pageNum)
                      }}
                      isActive={currentPage === pageNum}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                )
              })}

              {/* Next Page Button */}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage < totalPages) handlePageChange(currentPage + 1)
                  }}
                  className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>

              {/* Last Page Button */}
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage < totalPages) handlePageChange(totalPages)
                  }}
                  className={cn(
                    'gap-1 pr-2.5',
                    currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''
                  )}
                  aria-label="Go to last page"
                >
                  <span>Last</span>
                  <ChevronsRight className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  )
}
