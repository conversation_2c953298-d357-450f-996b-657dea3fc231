'use client'

import { useSearchPara<PERSON>, useRouter, usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { ClaimDocuments } from '@/components/claim-documents'
import { ClaimTasks } from '@/components/claim-tasks'
import { ClaimWitnesses } from '@/components/claim-witnesses'
import { ClaimAttorneys } from '@/components/claim-attorneys'
import { ClaimAudit } from '@/components/claim-audit'
import { ClaimFinancials } from '@/components/claim-financials'
import { ClaimRecovery } from '@/components/claim-recovery'
import { ClaimBodilyInjury } from '@/components/claim-bodily-injury'
import { ClaimPropertyDamage } from '@/components/claim-property-damage'
import { ClaimNotes } from '@/components/claim-notes'
import { ClaimTimeline } from '@/components/claim-timeline'

interface ClaimTabsProps {
  claimNumber: string
}

export function ClaimTabs({ claimNumber }: ClaimTabsProps) {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const [activeTab, setActiveTab] = useState<string>('timeline')

  // Convert URL parameter format (underscore) to tab value format (hyphen)
  const convertUrlParamToTabValue = (urlParam: string): string => {
    return urlParam.replace(/_/g, '-')
  }

  // Convert tab value format (hyphen) to URL parameter format (underscore)
  const convertTabValueToUrlParam = (tabValue: string): string => {
    return tabValue.replace(/-/g, '_')
  }

  // Initialize active tab from URL parameter or default
  useEffect(() => {
    const tabParam = searchParams.get('tab')
    if (tabParam) {
      const tabValue = convertUrlParamToTabValue(tabParam)
      setActiveTab(tabValue)
    } else {
      setActiveTab('timeline')
    }
  }, [searchParams])

  // Handle tab change and update URL
  const handleTabChange = (value: string) => {
    setActiveTab(value)

    // Update URL with new tab parameter
    const newSearchParams = new URLSearchParams(searchParams)
    newSearchParams.set('tab', convertTabValueToUrlParam(value))

    // Use replace to avoid adding to browser history for tab changes
    router.replace(`${pathname}?${newSearchParams.toString()}`)
  }

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
      <TabsList className="w-full">
        <TabsTrigger
          value="timeline"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Timeline
        </TabsTrigger>

        <TabsTrigger
          value="notes"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Notes
        </TabsTrigger>

        <TabsTrigger
          value="tasks"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Tasks
        </TabsTrigger>

        <TabsTrigger
          value="documents"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Documents
        </TabsTrigger>

        <TabsTrigger
          value="bodily-injury"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Bodily Injury
        </TabsTrigger>

        <TabsTrigger
          value="property-damage"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Property Damage
        </TabsTrigger>

        <TabsTrigger
          value="financials"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Financials
        </TabsTrigger>

        <TabsTrigger
          value="witnesses"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Witnesses
        </TabsTrigger>

        <TabsTrigger
          value="attorneys"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Attorneys
        </TabsTrigger>

        <TabsTrigger
          value="recovery"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Recovery
        </TabsTrigger>

        <TabsTrigger
          value="audit"
          className="data-[state=active]:bg-tangerine-100 data-[state=active]:text-tangerine-900 dark:data-[state=active]:bg-tangerine-900/30 dark:data-[state=active]:text-tangerine-100"
        >
          Audit
        </TabsTrigger>
      </TabsList>

      <TabsContent value="notes" className="mt-6 space-y-4">
        <ClaimNotes claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="documents" className="mt-6">
        <ClaimDocuments claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="tasks" className="mt-6 space-y-4">
        <ClaimTasks claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="financials" className="mt-6">
        <ClaimFinancials claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="witnesses" className="mt-6">
        <ClaimWitnesses claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="attorneys" className="mt-6">
        <ClaimAttorneys claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="recovery" className="mt-6">
        <ClaimRecovery claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="bodily-injury" className="mt-6">
        <ClaimBodilyInjury claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="property-damage" className="mt-6">
        <ClaimPropertyDamage claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="timeline" className="mt-6">
        <ClaimTimeline claimId={claimNumber} />
      </TabsContent>

      <TabsContent value="audit" className="mt-6">
        <ClaimAudit claimId={claimNumber} />
      </TabsContent>
    </Tabs>
  )
}
