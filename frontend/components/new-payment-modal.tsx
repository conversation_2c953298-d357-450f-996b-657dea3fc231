'use client'

import React, { useEffect, useState, useMemo } from 'react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Claim, PaymentType, PaymentCreate } from '@/lib/api/types'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { AlertCircle, Loader2 } from 'lucide-react'
import { api } from '@/lib/api'

interface NewPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  claims: Claim[] // Receive claims for selection
}

// Define form schema with Zod
const paymentSchema = z.object({
  claimId: z.string({ required_error: 'Claim is required' }),
  payment_type: z.nativeEnum(PaymentType, { required_error: 'Payment type is required' }),
  payee: z.string().min(1, 'Payee is required'),
  amount: z
    .string()
    .min(1, 'Amount is required')
    .refine(val => !isNaN(parseFloat(val)), {
      message: 'Amount must be a valid number',
    }),
  payment_date: z.string().min(1, 'Payment date is required'),
  notes: z.string().optional(),
})

// Create type from schema
type PaymentFormData = z.infer<typeof paymentSchema>

export default function NewPaymentModal({ isOpen, onClose, claims }: NewPaymentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)

  // Use the shared financialsApi from the api object
  const financialsApi = api.financials

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      claimId: claims.length === 1 ? claims[0]?.claim_number : '',
      payment_type: undefined,
      payee: '',
      amount: '',
      payment_date: new Date().toISOString().split('T')[0], // Default to today
      notes: '',
    },
  })

  // Pre-select the claim if there's only one
  useEffect(() => {
    if (claims.length === 1 && claims[0]?.claim_number) {
      setValue('claimId', claims[0].claim_number)
    }
  }, [claims, setValue])

  const onSubmit = async (data: PaymentFormData) => {
    setIsSubmitting(true)
    setApiError(null)

    try {
      // Try parsing the amount as a number first, to ensure it's valid
      const amountValue = parseFloat(data.amount)
      if (isNaN(amountValue)) {
        throw new Error('Amount must be a valid number')
      }

      const paymentData: PaymentCreate = {
        payment_type: data.payment_type,
        payee: data.payee,
        amount: data.amount, // API expects a string or number
        payment_date: data.payment_date,
        notes: data.notes || '',
      }

      console.log('Submitting payment data for claim:', data.claimId, 'Payload:', paymentData)

      // Use claim number for API calls, not UUID - Now using financialsApi
      await financialsApi.addClaimPayment(data.claimId, paymentData)
      // If addClaimPayment throws, it will be caught by the catch block below
      // ApiClient handles parsing the error and throwing a structured error.

      console.log('Payment added successfully for claim:', data.claimId)
      reset() // Reset form
      onClose() // Close modal
    } catch (err) {
      // err should be an instance of ApiError or a standard Error
      // if coming from financialsApi / ApiClient
      let errorMessage = 'Failed to add payment. Please try again.'
      if (err instanceof Error) {
        errorMessage = err.message
      }
      // For more specific error messages, you might inspect `err.status` if it's an ApiError
      // Example: if (err instanceof ApiError && err.status === 400) { /* specific message */ }

      setApiError(errorMessage)
      console.error('Payment submission error:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset()
      setApiError(null)
    }
  }, [isOpen, reset])

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add New Payment</DialogTitle>
          <DialogDescription>Enter the details for the new payment.</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="claimId" className="text-right">
              Claim
            </Label>
            <div className="col-span-3">
              <Controller
                name="claimId"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={claims.length === 1} // Disable if only one claim
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a claim" />
                    </SelectTrigger>
                    <SelectContent>
                      {claims.map(claim => (
                        <SelectItem key={claim.claim_number} value={claim.claim_number}>
                          {claim.claim_number} - {claim.claimant_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.claimId && (
                <p className="text-sm text-destructive mt-1">{errors.claimId.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="payment_type" className="text-right">
              Type
            </Label>
            <div className="col-span-3">
              <Controller
                name="payment_type"
                control={control}
                render={({ field }) => (
                  <Select onValueChange={field.onChange} value={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(PaymentType).map(type => (
                        <SelectItem key={type} value={type}>
                          {type.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.payment_type && (
                <p className="text-sm text-destructive mt-1">{errors.payment_type.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="payee" className="text-right">
              Payee
            </Label>
            <div className="col-span-3">
              <Input id="payee" {...register('payee')} />
              {errors.payee && (
                <p className="text-sm text-destructive mt-1">{errors.payee.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right">
              Amount
            </Label>
            <div className="col-span-3">
              <Input id="amount" type="number" step="0.01" {...register('amount')} />
              {errors.amount && (
                <p className="text-sm text-destructive mt-1">{errors.amount.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="payment_date" className="text-right">
              Date
            </Label>
            <div className="col-span-3">
              <Input id="payment_date" type="date" {...register('payment_date')} />
              {errors.payment_date && (
                <p className="text-sm text-destructive mt-1">{errors.payment_date.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes
            </Label>
            <div className="col-span-3">
              <Textarea id="notes" {...register('notes')} />
              {errors.notes && (
                <p className="text-sm text-destructive mt-1">{errors.notes.message}</p>
              )}
            </div>
          </div>

          {apiError && (
            <div className="flex items-center gap-2 text-destructive text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{apiError}</span>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Adding...
              </>
            ) : (
              'Add Payment'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
