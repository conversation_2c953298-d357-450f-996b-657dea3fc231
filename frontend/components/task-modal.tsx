'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { api } from '@/lib/api'
import { TaskPriority, TaskStatus, ClaimTaskCreateRequestBody, UserSummary } from '@/lib/api/types'
import { toast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'

const UNASSIGNED_SELECT_VALUE = '__UNASSIGNED__'

interface TaskModalProps {
  isOpen: boolean
  onClose: () => void
  onTaskCreated: () => void
  claimNumber?: string
}

export function TaskModal({ isOpen, onClose, onTaskCreated, claimNumber }: TaskModalProps) {
  const [title, setTitle] = useState('')
  const [description, setDescription] = useState('')
  const [priority, setPriority] = useState<string>(TaskPriority.MEDIUM)
  const [dueDate, setDueDate] = useState('')
  const [selectedAssigneeId, setSelectedAssigneeId] = useState<string | null>(null)

  const [users, setUsers] = useState<UserSummary[]>([])
  const [isLoadingUsers, setIsLoadingUsers] = useState(false)

  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<{ [key: string]: string }>({})

  useEffect(() => {
    if (isOpen) {
      const fetchUsers = async () => {
        setIsLoadingUsers(true)
        try {
          const fetchedUsers = await api.users.getUsersForDropdown()
          setUsers(fetchedUsers || [])
        } catch (error) {
          console.error('Error fetching users:', error)
          toast({
            title: 'Error',
            description: 'Failed to load users for assignee dropdown.',
            variant: 'destructive',
          })
          setUsers([])
        } finally {
          setIsLoadingUsers(false)
        }
      }
      fetchUsers()
    }
  }, [isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors({})
    let isValid = true
    const newErrors: { [key: string]: string } = {}

    if (!title.trim()) {
      newErrors.title = 'Title is required'
      isValid = false
    }
    if (!claimNumber) {
      newErrors.claimNumber = 'Claim number is missing'
      isValid = false
    }

    if (!isValid) {
      setErrors(newErrors)
      return
    }

    setIsLoading(true)

    const taskPayload: ClaimTaskCreateRequestBody = {
      title,
      description: description || undefined,
      priority: priority as TaskPriority,
      due_date: dueDate || undefined,
      assigned_to: selectedAssigneeId || undefined,
      status: TaskStatus.PENDING,
    }

    try {
      let newTask
      if (claimNumber) {
        newTask = await api.tasks.createClaimTask(claimNumber, taskPayload)
      } else {
        // Convert the task payload to a format compatible with createTask API
        newTask = await api.tasks.createTask({
          title: taskPayload.title,
          description: taskPayload.description || undefined,
          priority: taskPayload.priority as TaskPriority,
          status: taskPayload.status as TaskStatus,
          due_date: taskPayload.due_date || undefined,
          // Create a UserReadBasic object if we have an assignee ID
          assignee: taskPayload.assigned_to
            ? {
                id: taskPayload.assigned_to,
                email: taskPayload.assigned_to,
              }
            : undefined,
        })
      }

      toast({
        title: 'Success',
        description: claimNumber
          ? `Task ${newTask.hr_id || newTask.id} created successfully for claim ${claimNumber}`
          : `Task ${newTask.hr_id || newTask.id} created successfully`,
      })

      setTitle('')
      setDescription('')
      setPriority(TaskPriority.MEDIUM)
      setDueDate('')
      setSelectedAssigneeId(null)

      onTaskCreated()
      onClose()
    } catch (error) {
      console.error('Error creating task:', error)
      let errorMessage = 'Failed to create task. Please try again.'
      if (error instanceof Error && (error as any).data?.rawResponse) {
        errorMessage += ` Server: ${(error as any).data.rawResponse}`
      } else if (error instanceof Error && (error as any).data?.message) {
        errorMessage += ` Server: ${(error as any).data.message}`
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>Create New Task</DialogTitle>
            <DialogDescription>
              Add a new task for claim {claimNumber}. Fill out the required fields below.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {claimNumber ? (
              <div className="grid gap-2">
                <Label htmlFor="claimNumberDisplay">Claim Number</Label>
                <Input id="claimNumberDisplay" value={claimNumber} disabled />
                {errors.claimNumber && <p className="text-sm text-red-500">{errors.claimNumber}</p>}
              </div>
            ) : null}

            <div className="grid gap-2">
              <Label htmlFor="title" className={errors.title ? 'text-destructive' : ''}>
                Title <span className="text-destructive">*</span>
              </Label>
              <Input
                id="title"
                placeholder="Enter task title"
                value={title}
                onChange={e => setTitle(e.target.value)}
                className={errors.title ? 'border-destructive' : ''}
              />
              {errors.title && <p className="text-sm text-destructive">{errors.title}</p>}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter task description"
                value={description}
                onChange={e => setDescription(e.target.value)}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="priority">Priority</Label>
                <Select value={priority} onValueChange={setPriority}>
                  <SelectTrigger id="priority">
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                    <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                    <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                    <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid gap-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={dueDate}
                  onChange={e => setDueDate(e.target.value)}
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="assignee">Assignee (Optional)</Label>
              <Select
                value={selectedAssigneeId === null ? UNASSIGNED_SELECT_VALUE : selectedAssigneeId}
                onValueChange={value =>
                  setSelectedAssigneeId(value === UNASSIGNED_SELECT_VALUE ? null : value)
                }
                disabled={isLoadingUsers}
              >
                <SelectTrigger id="assignee">
                  <SelectValue
                    placeholder={isLoadingUsers ? 'Loading users...' : 'Select assignee'}
                  />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingUsers ? (
                    <div className="flex items-center justify-center p-2">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </div>
                  ) : (
                    <>
                      <SelectItem value={UNASSIGNED_SELECT_VALUE}>Unassigned</SelectItem>
                      {users.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.fullName} ({user.email})
                        </SelectItem>
                      ))}
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              type="button"
              onClick={onClose}
              disabled={isLoading || isLoadingUsers}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || isLoadingUsers}>
              {isLoading ? 'Creating...' : 'Create Task'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
