'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Calendar } from '@/components/ui/calendar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import { CalendarIcon, Clock, Loader2 } from 'lucide-react'
import {
  FNOLResponse,
  FNOLUpdate,
  ReporterRelationship,
  CommunicationPreference,
} from '@/lib/api/types'
import { USState } from '@/lib/api/types/shared-types'
import { api } from '@/lib/api'
import { toast } from '@/components/ui/use-toast'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface FnolEditModalProps {
  isOpen: boolean
  onClose: () => void
  fnol: FNOLResponse
  onSuccess?: () => void
}

interface FieldValidationState {
  // Reporter information
  reported_by?: string
  reporter_phone?: string
  reporter_email?: string
  contact_fields?: string // Combined validation for phone/email
  reporter_relationship?: string
  communication_preference?: string

  // Incident details
  incident_date?: string
  incident_time?: string
  incident_state?: string
  incident_location?: string
  description?: string

  // Policy info
  policy_number?: string
}

export default function FnolEditModal({ isOpen, onClose, fnol, onSuccess }: FnolEditModalProps) {
  const [formData, setFormData] = useState<FNOLUpdate>({
    reported_by: '',
    reporter_phone: '',
    reporter_email: '',
    incident_date: '',
    incident_state: USState.UNKNOWN,
    description: '',
    policy_number: '',
  })

  const [date, setDate] = useState<Date | undefined>(undefined)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<FieldValidationState>({})

  // Validation functions (same as creation form)
  const validatePhoneNumber = (phone: string): string | undefined => {
    if (!phone.trim()) return undefined // Optional field

    const digitsOnly = phone.replace(/[^\d]/g, '')
    if (digitsOnly.length === 10) {
      return undefined // Valid 10-digit US number
    } else if (digitsOnly.length === 11 && digitsOnly.startsWith('1')) {
      return undefined // Valid US number with country code
    } else {
      return 'Please enter a valid US phone number (e.g., ************)'
    }
  }

  const validateEmailAddress = (email: string): string | undefined => {
    if (!email.trim()) return undefined // Optional field

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailPattern.test(email)) {
      return 'Please enter a valid email address'
    }
    return undefined
  }

  const validateContactFields = (phone?: string, email?: string): string | undefined => {
    const hasPhone = phone && phone.trim().length > 0
    const hasEmail = email && email.trim().length > 0

    if (!hasPhone && !hasEmail) {
      return 'At least one contact method (phone or email) is required'
    }
    return undefined
  }

  const validateIncidentState = (state?: USState | null): string | undefined => {
    if (!state) return 'Incident state is required'
    return undefined
  }

  const validateIncidentTime = (time?: string): string | undefined => {
    if (!time || !time.trim()) return undefined // Optional field

    const timePattern = /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i
    if (!timePattern.test(time.trim())) {
      return 'Please enter time in 12-hour format (e.g., 2:30 PM)'
    }
    return undefined
  }

  // Convert 24-hour time from backend to 12-hour format for display
  const convertTo12Hour = (time24h: string): string => {
    if (!time24h || !time24h.includes(':')) return ''

    const [hours, minutes] = time24h.split(':')
    const hour24 = parseInt(hours, 10)
    const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24
    const ampm = hour24 >= 12 ? 'PM' : 'AM'

    return `${hour12}:${minutes} ${ampm}`
  }

  // Convert 12-hour time to 24-hour format for API
  const convertTo24Hour = (time12h: string): string => {
    if (!time12h.trim()) return ''

    const [time, modifier] = time12h.trim().split(/\s+/)
    let [hours, minutes] = time.split(':')

    if (hours === '12') {
      hours = '00'
    }

    if (modifier.toUpperCase() === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString()
    }

    return `${hours.padStart(2, '0')}:${minutes}:00`
  }

  // Initialize form data with FNOL values
  useEffect(() => {
    if (fnol) {
      setFormData({
        reported_by: fnol.reported_by,
        reporter_phone: fnol.reporter_phone || '',
        reporter_email: fnol.reporter_email || '',
        reporter_relationship: fnol.reporter_relationship || undefined,
        communication_preference: fnol.communication_preference || undefined,
        incident_date: fnol.incident_date || '',
        incident_time: fnol.incident_time ? convertTo12Hour(fnol.incident_time) : '',
        incident_state: fnol.incident_state || USState.UNKNOWN,
        incident_location: fnol.incident_location || '',
        description: fnol.description || '',
        policy_number: fnol.policy_number || '',
      })

      if (fnol.incident_date) {
        setDate(new Date(fnol.incident_date))
      }
    }
  }, [fnol])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear validation errors when user types
    setValidationErrors(prev => ({ ...prev, [name]: undefined, contact_fields: undefined }))

    // Real-time validation
    if (name === 'reporter_phone') {
      const phoneError = validatePhoneNumber(value)
      if (phoneError) {
        setValidationErrors(prev => ({ ...prev, reporter_phone: phoneError }))
      }
    }

    if (name === 'reporter_email') {
      const emailError = validateEmailAddress(value)
      if (emailError) {
        setValidationErrors(prev => ({ ...prev, reporter_email: emailError }))
      }
    }

    if (name === 'incident_time') {
      const timeError = validateIncidentTime(value)
      if (timeError) {
        setValidationErrors(prev => ({ ...prev, incident_time: timeError }))
      }
    }
  }

  const handleSelectChange = (name: string, value: string | undefined) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    setValidationErrors(prev => ({ ...prev, [name]: undefined }))
  }

  const handleDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate)
    if (selectedDate) {
      const formattedDate = format(selectedDate, 'yyyy-MM-dd')
      setFormData(prev => ({ ...prev, incident_date: formattedDate }))
    } else {
      setFormData(prev => ({ ...prev, incident_date: '' }))
    }
    setValidationErrors(prev => ({ ...prev, incident_date: undefined }))
  }

  const validateForm = (): boolean => {
    const errors: FieldValidationState = {}
    let isValid = true

    // Validate required fields
    if (!formData.reported_by?.trim()) {
      errors.reported_by = 'Reporter name is required'
      isValid = false
    }

    // Validate contact fields (at least one required)
    const contactError = validateContactFields(
      formData.reporter_phone || undefined,
      formData.reporter_email || undefined
    )
    if (contactError) {
      errors.contact_fields = contactError
      isValid = false
    }

    // Validate individual contact fields if provided
    if (formData.reporter_phone) {
      const phoneError = validatePhoneNumber(formData.reporter_phone)
      if (phoneError) {
        errors.reporter_phone = phoneError
        isValid = false
      }
    }

    if (formData.reporter_email) {
      const emailError = validateEmailAddress(formData.reporter_email)
      if (emailError) {
        errors.reporter_email = emailError
        isValid = false
      }
    }

    // Validate incident details
    if (!formData.description?.trim()) {
      errors.description = 'Incident description is required'
      isValid = false
    }

    const stateError = validateIncidentState(formData.incident_state)
    if (stateError) {
      errors.incident_state = stateError
      isValid = false
    }

    if (formData.incident_time) {
      const timeError = validateIncidentTime(formData.incident_time)
      if (timeError) {
        errors.incident_time = timeError
        isValid = false
      }
    }

    setValidationErrors(errors)
    return isValid
  }

  const handleSubmit = async () => {
    // Validate the form first
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Prepare payload with proper format conversions
      const payload = {
        ...formData,
        incident_time: formData.incident_time ? convertTo24Hour(formData.incident_time) : null,
        reporter_phone: formData.reporter_phone?.trim() || null,
        reporter_email: formData.reporter_email?.trim() || null,
        incident_location: formData.incident_location?.trim() || null,
        policy_number: formData.policy_number?.trim() || null,
      }

      const updatedFnol = await api.fnol.updateFNOL(fnol.id, payload)

      toast({
        title: 'FNOL Updated',
        description: `Successfully updated FNOL ${updatedFnol.fnol_number}`,
      })

      if (onSuccess) {
        onSuccess()
      }

      onClose()
    } catch (error: any) {
      console.error('Error updating FNOL:', error)
      setError(error.message || 'Failed to update FNOL')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Create sorted state options for dropdown
  const stateOptions = [
    ...Object.values(USState)
      .filter(state => state !== USState.UNKNOWN)
      .sort(),
    USState.UNKNOWN,
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit FNOL</DialogTitle>
          <DialogDescription>Update the details for FNOL {fnol?.fnol_number}</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Contact fields validation message */}
          {validationErrors.contact_fields && (
            <Alert variant="destructive">
              <AlertDescription>{validationErrors.contact_fields}</AlertDescription>
            </Alert>
          )}

          {/* Reporter Information Section */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-muted-foreground">Reporter Information</h4>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reported_by" className="text-right">
                Reporter Name
              </Label>
              <div className="col-span-3">
                <Input
                  id="reported_by"
                  name="reported_by"
                  value={formData.reported_by || ''}
                  onChange={handleInputChange}
                  className={validationErrors.reported_by ? 'border-red-500' : ''}
                />
                {validationErrors.reported_by && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.reported_by}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reporter_relationship" className="text-right">
                Relationship
              </Label>
              <div className="col-span-3">
                <Select
                  value={formData.reporter_relationship || undefined}
                  onValueChange={value => handleSelectChange('reporter_relationship', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select relationship (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(ReporterRelationship).map(relationship => (
                      <SelectItem key={relationship} value={relationship}>
                        {relationship.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reporter_phone" className="text-right">
                Phone Number
              </Label>
              <div className="col-span-3">
                <Input
                  id="reporter_phone"
                  name="reporter_phone"
                  value={formData.reporter_phone || ''}
                  onChange={handleInputChange}
                  placeholder="************"
                  className={validationErrors.reporter_phone ? 'border-red-500' : ''}
                />
                {validationErrors.reporter_phone && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_phone}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="reporter_email" className="text-right">
                Email Address
              </Label>
              <div className="col-span-3">
                <Input
                  id="reporter_email"
                  name="reporter_email"
                  type="email"
                  value={formData.reporter_email || ''}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className={validationErrors.reporter_email ? 'border-red-500' : ''}
                />
                {validationErrors.reporter_email && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_email}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="communication_preference" className="text-right">
                Preferred Contact
              </Label>
              <div className="col-span-3">
                <Select
                  value={formData.communication_preference || undefined}
                  onValueChange={value => handleSelectChange('communication_preference', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select communication preference (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(CommunicationPreference).map(preference => (
                      <SelectItem key={preference} value={preference}>
                        {preference.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Incident Details Section */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-muted-foreground">Incident Details</h4>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="incident_date" className="text-right">
                Incident Date
              </Label>
              <div className="col-span-3">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !date && 'text-muted-foreground'
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, 'PPP') : <span>Select a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={handleDateSelect}
                      initialFocus
                      disabled={date => date > new Date()}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="incident_time" className="text-right">
                Incident Time
              </Label>
              <div className="col-span-3">
                <div className="relative">
                  <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="incident_time"
                    name="incident_time"
                    value={formData.incident_time || ''}
                    onChange={handleInputChange}
                    placeholder="2:30 PM"
                    className={cn('pl-10', validationErrors.incident_time ? 'border-red-500' : '')}
                  />
                </div>
                {validationErrors.incident_time && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.incident_time}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="incident_state" className="text-right">
                State
              </Label>
              <div className="col-span-3">
                <Select
                  value={formData.incident_state || undefined}
                  onValueChange={value => handleSelectChange('incident_state', value as USState)}
                >
                  <SelectTrigger
                    className={validationErrors.incident_state ? 'border-red-500' : ''}
                  >
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {stateOptions.map(state => (
                      <SelectItem key={state} value={state}>
                        {state === 'UNKNOWN' ? 'Unknown' : state}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.incident_state && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.incident_state}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="incident_location" className="text-right">
                Location
              </Label>
              <div className="col-span-3">
                <Input
                  id="incident_location"
                  name="incident_location"
                  value={formData.incident_location || ''}
                  onChange={handleInputChange}
                  placeholder="123 Main St, City, State (optional)"
                />
              </div>
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Description
              </Label>
              <div className="col-span-3">
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description || ''}
                  onChange={handleInputChange}
                  rows={4}
                  className={validationErrors.description ? 'border-red-500' : ''}
                />
                {validationErrors.description && (
                  <p className="text-sm text-red-500 mt-1">{validationErrors.description}</p>
                )}
              </div>
            </div>
          </div>

          {/* Policy Information Section */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-muted-foreground">Policy Information</h4>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="policy_number" className="text-right">
                Policy Number
              </Label>
              <div className="col-span-3">
                <Input
                  id="policy_number"
                  name="policy_number"
                  value={formData.policy_number || ''}
                  onChange={handleInputChange}
                  placeholder="Enter policy number (optional)"
                />
              </div>
            </div>
          </div>

          {error && <div className="text-sm text-destructive mt-2 text-center">{error}</div>}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
