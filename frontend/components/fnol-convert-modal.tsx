'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ClaimType, FNOLConversionResponse } from '@/lib/api/types'
import { useRouter } from 'next/navigation'
import { api } from '@/lib/api'
import { toast } from '@/components/ui/use-toast'
import { Loader2 } from 'lucide-react'

interface FnolConvertModalProps {
  isOpen: boolean
  onClose: () => void
  fnolId: string
  onSuccess?: () => void
  autoClose?: boolean
}

// Helper function to convert claim type to a more readable format
const formatClaimType = (claimType: string | undefined): string => {
  if (!claimType) return 'New'

  switch (claimType) {
    case ClaimType.AUTO:
      return 'Auto'
    case ClaimType.PROPERTY:
      return 'Property'
    case ClaimType.GENERAL_LIABILITY:
      return 'General Liability'
    default:
      return claimType
  }
}

export default function FnolConvertModal({
  isOpen,
  onClose,
  fnolId,
  onSuccess,
  autoClose,
}: FnolConvertModalProps) {
  const router = useRouter()
  const [selectedClaimType, setSelectedClaimType] = useState<ClaimType | ''>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [convertedClaim, setConvertedClaim] = useState<FNOLConversionResponse | null>(null)

  // Reset state when modal is opened/closed via isOpen prop changes
  useEffect(() => {
    if (!isOpen) {
      setSelectedClaimType('')
      setError(null)
      setConvertedClaim(null)
    }
  }, [isOpen])

  const handleConvert = async () => {
    if (!selectedClaimType) {
      setError('Please select a claim type')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      // Log the request parameters
      console.log('Converting FNOL to claim:', {
        fnolId,
        claimType: selectedClaimType,
      })

      const result = await api.fnol.convertFNOLToClaim(fnolId, selectedClaimType)

      // Log the response
      console.log('Conversion response:', result)

      // API client now handles unwrapping the response from the items array
      // Use the data directly
      const displayType = formatClaimType(result.claim_type)
      const claimNumber = result.claim_number || 'Unknown'

      toast({
        title: 'FNOL Converted',
        description: `Successfully converted to ${displayType} claim ${claimNumber}`,
      })

      // Store the converted claim data
      setConvertedClaim(result)

      if (onSuccess) {
        onSuccess()
      }

      if (autoClose) {
        handleCloseModal()
      }
    } catch (error: any) {
      console.error('Error converting FNOL to claim:', error)
      setError(error.message || 'Failed to convert FNOL to claim')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCloseModal = () => {
    // Reset all state
    setSelectedClaimType('')
    setError(null)
    setConvertedClaim(null)

    // Call the parent's onClose handler
    onClose()
  }

  const handleViewClaim = () => {
    if (convertedClaim?.claim_number) {
      router.push(`/claims/${convertedClaim.claim_number}`)
    } else {
      console.error('Cannot navigate - missing claim number')
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={() => handleCloseModal()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Convert FNOL to Claim</DialogTitle>
          <DialogDescription>
            {convertedClaim
              ? 'You can convert this FNOL to another claim with a different type if needed.'
              : 'Select the type of claim to create from this FNOL.'}
          </DialogDescription>
        </DialogHeader>

        {!convertedClaim ? (
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <label htmlFor="claim-type" className="text-sm font-medium">
                Claim Type
              </label>
              <Select
                value={selectedClaimType}
                onValueChange={value => {
                  setSelectedClaimType(value as ClaimType)
                  setError(null)
                }}
              >
                <SelectTrigger id="claim-type">
                  <SelectValue placeholder="Select claim type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ClaimType.AUTO}>Auto</SelectItem>
                  <SelectItem value={ClaimType.PROPERTY}>Property</SelectItem>
                  <SelectItem value={ClaimType.GENERAL_LIABILITY}>General Liability</SelectItem>
                </SelectContent>
              </Select>
              {error && <p className="text-sm text-destructive">{error}</p>}
            </div>
          </div>
        ) : (
          <div className="py-4 space-y-4">
            <div className="bg-muted p-4 rounded-md text-center">
              <p className="font-medium">
                Successfully created {formatClaimType(convertedClaim.claim_type)} claim
              </p>
              <p className="text-sm">
                {convertedClaim.claim_number || 'Claim number not available'}
              </p>
            </div>
            <p className="text-sm text-muted-foreground">
              You can create another claim from this FNOL or view the newly created claim.
            </p>
          </div>
        )}

        <DialogFooter className="flex justify-between">
          {convertedClaim ? (
            <>
              <Button variant="outline" onClick={handleCloseModal}>
                Done
              </Button>
              <div className="space-x-2">
                <Button variant="outline" onClick={() => setConvertedClaim(null)}>
                  Create Another Claim
                </Button>
                <Button onClick={handleViewClaim} disabled={!convertedClaim.claim_number}>
                  View Claim
                </Button>
              </div>
            </>
          ) : (
            <>
              <Button variant="outline" onClick={handleCloseModal} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button onClick={handleConvert} disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Convert
              </Button>
            </>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
