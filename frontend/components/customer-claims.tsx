'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { ChevronRight, PlusIcon, Loader2, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Claim, PaginatedResponse } from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'

interface CustomerClaimsProps {
  customerId: string
}

export function CustomerClaims({ customerId }: CustomerClaimsProps) {
  const { formatDate } = useDateFormatter()

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  const fetchClaims = async () => {
    console.log(`[CustomerClaims] Fetching claims for customer ID: ${customerId}`)
    const response = await api.claims.getClaimsByCustomer(customerId, {
      /* add pagination/filters here if needed */
    })
    console.log('[CustomerClaims] Raw API response:', response)
    return response
  }

  const {
    data: claimsData,
    isLoading,
    error,
    refetch,
  } = useApi<PaginatedResponse<Claim>>(fetchClaims, [customerId])

  const claims = claimsData?.items

  // Resolve assignee names when claims data changes
  useEffect(() => {
    const assigneeIds =
      claims?.map(claim => claim.assigned_to_id).filter((id): id is string => Boolean(id)) || []

    if (assigneeIds.length > 0) {
      resolveUsers(assigneeIds)
    }
  }, [claims, resolveUsers])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'INVESTIGATION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'SETTLEMENT':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'LITIGATION':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'RECOVERY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'CLOSED_SETTLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      case 'CLOSED_DENIED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'AUTO':
        return 'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300'
      case 'PROPERTY':
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case 'GENERAL_LIABILITY':
        return 'bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Customer Claims</CardTitle>
        <Link href="/claims/new">
          <Button className="flex items-center gap-1">
            <PlusIcon className="h-4 w-4" />
            New Claim
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="space-y-2">
            <label htmlFor="claim-number" className="text-sm font-medium">
              Claim Number
            </label>
            <Input id="claim-number" placeholder="e.g., ACME-2023-0000123" />
          </div>
          <div className="space-y-2">
            <label htmlFor="claim-status" className="text-sm font-medium">
              Status
            </label>
            <Select>
              <SelectTrigger id="claim-status">
                <SelectValue placeholder="Any Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="investigation">Investigation</SelectItem>
                <SelectItem value="settlement">Settlement</SelectItem>
                <SelectItem value="litigation">Litigation</SelectItem>
                <SelectItem value="recovery">Recovery</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label htmlFor="claim-type" className="text-sm font-medium">
              Type
            </label>
            <Select>
              <SelectTrigger id="claim-type">
                <SelectValue placeholder="Any Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Type</SelectItem>
                <SelectItem value="auto">Auto</SelectItem>
                <SelectItem value="property">Property</SelectItem>
                <SelectItem value="liability">General Liability</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label htmlFor="claimant-name" className="text-sm font-medium">
              Claimant Name
            </label>
            <Input id="claimant-name" placeholder="Claimant name" />
          </div>
        </div>

        {isLoading && (
          <div className="flex justify-center items-center p-10 border rounded-md">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Loading claims...</span>
          </div>
        )}

        {error && (
          <div className="flex flex-col justify-center items-center p-10 border rounded-md text-destructive">
            <AlertCircle className="h-8 w-8 mb-2" />
            <p className="mb-4">Error loading claims: {String(error)}</p>
            <Button onClick={() => refetch()}>Try Again</Button>
          </div>
        )}

        {!isLoading && !error && (!claims || claims.length === 0) && (
          <div className="flex justify-center items-center p-10 border rounded-md">
            <p>No claims found for this customer.</p>
          </div>
        )}

        {!isLoading && !error && claims && claims.length > 0 && (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Claim #</TableHead>
                  <TableHead>Claimant</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Incident Date</TableHead>
                  <TableHead>Assignee</TableHead>
                  <TableHead className="w-10"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {claims.map(claim => (
                  <TableRow key={claim.id}>
                    <TableCell className="font-medium">
                      <Link href={`/claims/${claim.claim_number}`} className="hover:underline">
                        {claim.claim_number}
                      </Link>
                    </TableCell>
                    <TableCell>{claim.claimant_name}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getTypeColor(claim.type)}>
                        {claim.type.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className={getStatusColor(claim.status)}>
                        {claim.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{formatDate(claim.incident_date || undefined)}</TableCell>
                    <TableCell>
                      {claim.assigned_to_id
                        ? getUserDisplayName(claim.assigned_to_id)
                        : 'Unassigned'}
                    </TableCell>
                    <TableCell>
                      <Link href={`/claims/${claim.claim_number}`}>
                        <Button size="icon" variant="ghost" className="h-8 w-8">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
