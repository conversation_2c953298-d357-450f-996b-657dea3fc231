'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useRecovery } from '@/hooks/useRecovery'
import { Loader2, Plus } from 'lucide-react'
import { RecoveryEditModal } from './recovery-edit-modal'

interface ClaimRecoveryProps {
  claimId: string
}

export function ClaimRecovery({ claimId }: ClaimRecoveryProps) {
  const { recoveryData, isLoading, error, refetch } = useRecovery(claimId)

  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editMode, setEditMode] = useState<'status' | 'carrier' | 'create'>('status')

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading recovery details...</span>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center text-red-600">
            <p>Error loading recovery details: {error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const handleEditSuccess = () => {
    refetch()
    setIsEditModalOpen(false)
  }

  const handleCreateRecovery = () => {
    setEditMode('create')
    setIsEditModalOpen(true)
  }

  // Show empty state if no recovery data
  if (!recoveryData) {
    return (
      <>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Recovery</CardTitle>
            <Button onClick={handleCreateRecovery} size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Initiate Recovery
            </Button>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-4">
                No recovery information available for this claim.
              </p>
              <p className="text-sm text-muted-foreground">
                Recovery details will appear here when recovery activities are initiated.
              </p>
            </div>
          </CardContent>
        </Card>

        <RecoveryEditModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          claimId={claimId}
          mode={editMode}
          recoveryData={recoveryData}
          onSuccess={handleEditSuccess}
        />
      </>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NOT_STARTED':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case 'INITIATED':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'IN_PROGRESS':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const formatStatus = (status: string) => {
    return status.replace(/_/g, ' ')
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Recovery</CardTitle>
          <CardDescription>Recovery details and third party carrier information</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-sm font-medium mb-3">Recovery Details</h4>
              <dl className="grid grid-cols-2 gap-2 text-sm">
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Expected Amount</dt>
                  <dd className="font-medium">
                    {recoveryData.expected_amount
                      ? `$${recoveryData.expected_amount}`
                      : 'Not specified'}
                  </dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Received Amount</dt>
                  <dd className="font-medium">
                    {recoveryData.received_amount
                      ? `$${recoveryData.received_amount}`
                      : 'Not specified'}
                  </dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Status</dt>
                  <dd>
                    <Badge
                      variant="outline"
                      className={getStatusColor(recoveryData.recovery_status || 'NOT_STARTED')}
                    >
                      {formatStatus(recoveryData.recovery_status || 'NOT_STARTED')}
                    </Badge>
                  </dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Claim Number</dt>
                  <dd className="font-medium">{recoveryData.claim_number}</dd>
                </div>
              </dl>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-3">Third Party Carrier</h4>
              <dl className="grid grid-cols-2 gap-2 text-sm">
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Carrier Name</dt>
                  <dd className="font-medium">{recoveryData.carrier_name || 'Not specified'}</dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Claim Number</dt>
                  <dd className="font-medium">
                    {recoveryData.carrier_claim_number || 'Not specified'}
                  </dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Adjuster</dt>
                  <dd className="font-medium">
                    {recoveryData.carrier_adjuster || 'Not specified'}
                  </dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Contact</dt>
                  <dd className="font-medium">{recoveryData.carrier_contact || 'Not specified'}</dd>
                </div>
              </dl>
            </div>
          </div>

          <div className="mt-8 flex justify-end">
            <Button
              onClick={() => {
                setEditMode('create')
                setIsEditModalOpen(true)
              }}
            >
              Update Recovery
            </Button>
          </div>
        </CardContent>
      </Card>

      <RecoveryEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        claimId={claimId}
        mode={editMode}
        recoveryData={recoveryData}
        onSuccess={handleEditSuccess}
      />
    </>
  )
}
