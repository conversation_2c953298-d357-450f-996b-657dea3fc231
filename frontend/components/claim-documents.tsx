'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import {
  ChevronRight,
  Download,
  Eye,
  FileText,
  ImageIcon,
  FileArchive,
  File,
  Upload,
  AlertCircle,
  Loader2,
} from 'lucide-react'
import Link from 'next/link'
import { useApi } from '@/hooks/useApi'
import { api } from '../lib/api'
import { Document, DocumentList, DocumentType } from '@/lib/api/types'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'
import { useState, useEffect } from 'react'
import { DocumentUploadModal } from './document-upload-modal'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'

interface ClaimDocumentsProps {
  claimId: string
}

export function ClaimDocuments({ claimId }: ClaimDocumentsProps) {
  const { formatRelativeDate } = useDateFormatter()
  // Add state for the upload modal
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // Directly fetch documents using the claim number/ID
  const {
    data: documentData,
    isLoading: isLoadingDocuments,
    error: documentsError,
    refetch: refetchDocuments,
  } = useApi(() => api.documents.listClaimDocuments(claimId), [claimId])

  // Function to open the upload modal
  const handleOpenUploadModal = () => {
    console.log('Opening upload modal')
    setIsUploadModalOpen(true)
  }

  // Function to close the upload modal
  const handleCloseUploadModal = () => {
    console.log('Closing upload modal')
    setIsUploadModalOpen(false)
  }

  // Function to handle successful upload
  const handleUploadSuccess = () => {
    console.log('Upload successful, refreshing documents')
    refetchDocuments()
    handleCloseUploadModal()
  }

  // Function to handle viewing a document
  const handleViewDocument = async (document: Document) => {
    try {
      console.log('Viewing document:', document.id)
      // Get download URL and open in new tab/window
      const downloadUrlData = await api.documents.getDocumentDownloadUrl(claimId, document.id)
      window.open(downloadUrlData.download_url, '_blank')
    } catch (error) {
      console.error('Error viewing document:', error)
    }
  }

  // Function to handle downloading a document
  const handleDownloadDocument = async (document: Document) => {
    try {
      console.log('Downloading document:', document.id)
      // Get download URL and trigger download
      const downloadUrlData = await api.documents.getDocumentDownloadUrl(claimId, document.id)

      // Create a temporary link and trigger download
      const link = window.document.createElement('a')
      link.href = downloadUrlData.download_url
      link.download = document.name
      window.document.body.appendChild(link)
      link.click()
      window.document.body.removeChild(link)
    } catch (error) {
      console.error('Error downloading document:', error)
    }
  }

  const getDocumentIcon = (type: string, mimeType: string) => {
    if (type === 'PHOTO' || mimeType?.startsWith('image/')) return <ImageIcon className="h-4 w-4" />
    if (mimeType === 'application/pdf') return <FileText className="h-4 w-4" />
    if (mimeType === 'application/zip') return <FileArchive className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  const formatFileSize = (bytes: number) => {
    if (!bytes) return 'Unknown'
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
    else return (bytes / 1048576).toFixed(1) + ' MB'
  }

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'PHOTO':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'REPORT':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'POLICY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'INVOICE':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'STATEMENT':
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case 'CONTRACT':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      case 'CORRESPONDENCE':
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Resolve user names when documents change
  useEffect(() => {
    const userIds =
      documentData?.items.map(doc => doc.uploaded_by).filter((id): id is string => Boolean(id)) ||
      []

    if (userIds.length > 0) {
      resolveUsers(userIds)
    }
  }, [documentData, resolveUsers])

  // Show loading state
  if (isLoadingDocuments) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Claim Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <Loading size="md" text="Loading claim documents..." centered />
        </CardContent>
      </Card>
    )
  }

  // Show error state
  if (documentsError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Claim Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <EmptyState
            variant="error"
            title="Could not load documents"
            description={documentsError || 'Failed to load claim documents'}
            actionLabel="Try Again"
            onAction={refetchDocuments}
          />
        </CardContent>
      </Card>
    )
  }

  const documents = documentData?.items || []

  // Show empty state if no documents
  if (!documents || documents.length === 0) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Claim Documents</CardTitle>
          <Button className="flex items-center gap-1" onClick={handleOpenUploadModal}>
            <Upload className="h-4 w-4 mr-1" />
            Upload Document
          </Button>
        </CardHeader>
        <CardContent>
          <EmptyState
            title="No Documents Yet"
            description="No documents have been uploaded for this claim yet. Upload your first document to get started."
            icon={FileText}
            actionLabel="Upload Document"
            onAction={handleOpenUploadModal}
          />
          <DocumentUploadModal
            isOpen={isUploadModalOpen}
            onClose={handleCloseUploadModal}
            claimId={claimId}
            onSuccess={handleUploadSuccess}
          />
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Claim Documents</CardTitle>
        <Button className="flex items-center gap-1" onClick={handleOpenUploadModal}>
          <Upload className="h-4 w-4 mr-1" />
          Upload Document
        </Button>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Document</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>Uploaded</TableHead>
              <TableHead>Uploaded By</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.map(document => (
              <TableRow key={document.id}>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getDocumentIcon(document.type, document.mime_type)}
                    <div>
                      <div className="font-medium">
                        <Link
                          href={`/documents/${document.id}`}
                          className="text-primary hover:underline"
                        >
                          {document.name}
                        </Link>
                      </div>
                      {document.description && (
                        <div className="text-xs text-muted-foreground">{document.description}</div>
                      )}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Link href={`/documents?type=${document.type}`}>
                    <Badge
                      variant="outline"
                      className={getDocumentTypeColor(document.type as string)}
                    >
                      {document.type}
                    </Badge>
                  </Link>
                </TableCell>
                <TableCell>{formatFileSize(document.file_size)}</TableCell>
                <TableCell>{formatRelativeDate(document.created_at)}</TableCell>
                <TableCell>{getUserDisplayName(document.uploaded_by)}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8"
                      title="View"
                      onClick={() => handleViewDocument(document)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8"
                      title="Download"
                      onClick={() => handleDownloadDocument(document)}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Link href={`/documents/${document.id}`}>
                      <Button size="icon" variant="ghost" className="h-8 w-8" title="Details">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Document Upload Modal */}
        <DocumentUploadModal
          isOpen={isUploadModalOpen}
          onClose={handleCloseUploadModal}
          claimId={claimId}
          onSuccess={handleUploadSuccess}
        />
      </CardContent>
    </Card>
  )
}
