'use client'

import React, { useEffect, useState, useMemo } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ReserveType, ReserveUpdate, ReserveResponse } from '@/lib/api/types'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { AlertCircle, Loader2 } from 'lucide-react'
import { api } from '@/lib/api'

interface ReserveFormModalProps {
  isOpen: boolean
  onClose: () => void
  claimId: string // This should be the claim number, not UUID
  initialData?: ReserveResponse // For edit mode
  mode: 'add' | 'edit' | 'update'
  existingReserves?: ReserveResponse[] // For update mode
  onSuccess?: () => void
}

// Define form schema with Zod
const reserveSchema = z.object({
  reserve_type: z.nativeEnum(ReserveType, { required_error: 'Reserve type is required' }),
  amount: z
    .string()
    .min(1, 'Amount is required')
    .refine(val => !isNaN(parseFloat(val)), {
      message: 'Amount must be a valid number',
    }),
  notes: z.string().optional(),
})

// Create type from schema
type ReserveFormData = z.infer<typeof reserveSchema>

export default function ReserveFormModal({
  isOpen,
  onClose,
  claimId,
  initialData,
  mode,
  existingReserves,
  onSuccess,
}: ReserveFormModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)
  const financialsApi = api.financials

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm<ReserveFormData>({
    resolver: zodResolver(reserveSchema),
    defaultValues: {
      reserve_type: initialData?.reserve_type || undefined,
      amount: initialData?.amount || '',
      notes: '',
    },
  })

  // Update form when initialData changes (for edit mode)
  useEffect(() => {
    if (initialData && mode === 'edit') {
      setValue('reserve_type', initialData.reserve_type)
      setValue('amount', initialData.amount)
      // Notes field might not be part of ReserveResponse
    }
  }, [initialData, setValue, mode])

  const onSubmit = async (data: ReserveFormData) => {
    setIsSubmitting(true)
    setApiError(null)

    try {
      const amountValue = parseFloat(data.amount)
      if (isNaN(amountValue)) {
        throw new Error('Amount must be a valid number')
      }

      const reserveDataForUpdate: ReserveUpdate = {
        reserve_type: data.reserve_type,
        amount: data.amount, // API expects string or number, form gives string
        notes: data.notes || '',
      }

      if (mode === 'add') {
        console.log('Adding reserve for claim:', claimId, 'Data:', reserveDataForUpdate)
        // SCENARIO A/B for 'add' mode:
        // 1. Check if financials exist
        const existingFinancials = await financialsApi.getClaimFinancials(claimId)

        if (existingFinancials === null) {
          // Financials do not exist, create them with this reserve
          console.log('No existing financials. Creating financials with initial reserve.')
          const financialsToCreate: import('@/lib/api/types').ClaimFinancialsCreate = {
            // We need an estimated_value. For now, let's use the first reserve amount.
            // This might need to be a dedicated field in the form or a default.
            estimated_value: data.amount,
            reserves: [reserveDataForUpdate], // ReserveUpdate is compatible with ReserveCreate items here
            // currency: 'USD', // Optional, backend defaults
          }
          await financialsApi.createFinancialsWithInitialReserve(claimId, financialsToCreate)
        } else {
          // Financials exist, update (add) this reserve
          console.log('Existing financials found. Updating reserve.')
          await financialsApi.updateClaimReserve(claimId, reserveDataForUpdate)
        }
      } else if (mode === 'edit' || mode === 'update') {
        // Treat 'update' like 'edit' for now
        console.log('Editing/Updating reserve for claim:', claimId, 'Data:', reserveDataForUpdate)
        // This assumes updateClaimReserve can handle adding a new reserve type
        // or updating an existing one based on the reserve_type in ReserveUpdate.
        // If a specific reserve ID is needed for edits, this part needs more work.
        await financialsApi.updateClaimReserve(claimId, reserveDataForUpdate)
      }

      console.log('Reserve operation successful for claim:', claimId)
      reset()
      if (onSuccess) onSuccess() // Call onSuccess passed from parent
      onClose() // Then close
    } catch (err) {
      let errorMessage = 'Failed to process reserve. Please try again.'
      if (err instanceof Error) {
        errorMessage = err.message
      }
      // TODO: More specific error messages based on err.status if ApiError
      setApiError(errorMessage)
      console.error('Reserve submission error:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset()
      setApiError(null)
    }
  }, [isOpen, reset])

  // Determine text based on mode and existing reserves
  let dialogTitle = 'Add Reserve'
  let dialogDescription = 'Enter the details for the new reserve'
  let submitButtonText = 'Add Reserve'
  let loadingText = 'Adding...'

  if (mode === 'edit') {
    dialogTitle = 'Edit Reserve'
    dialogDescription = 'Update the reserve details'
    submitButtonText = 'Update Reserve'
    loadingText = 'Updating...'
  } else if (mode === 'update' && existingReserves && existingReserves.length > 0) {
    dialogTitle = 'Update Reserve'
    dialogDescription = 'Update the reserve details'
    submitButtonText = 'Update Reserve'
    loadingText = 'Updating...'
  }

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{dialogTitle}</DialogTitle>
          <DialogDescription>{dialogDescription}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="reserve_type" className="text-right">
              Type
            </Label>
            <div className="col-span-3">
              <Controller
                name="reserve_type"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={field.onChange}
                    value={field.value}
                    disabled={mode === 'edit'} // Only disable in edit mode
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* In update mode with existing reserves, show only those types */}
                      {mode === 'update' && existingReserves && existingReserves.length > 0
                        ? existingReserves.map(reserve => (
                            <SelectItem key={reserve.reserve_type} value={reserve.reserve_type}>
                              {reserve.reserve_type.replace(/_/g, ' ')}
                            </SelectItem>
                          ))
                        : Object.values(ReserveType).map(type => (
                            <SelectItem key={type} value={type}>
                              {type.replace(/_/g, ' ')}
                            </SelectItem>
                          ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.reserve_type && (
                <p className="text-sm text-destructive mt-1">{errors.reserve_type.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right">
              Amount
            </Label>
            <div className="col-span-3">
              <Input id="amount" type="number" step="0.01" {...register('amount')} />
              {errors.amount && (
                <p className="text-sm text-destructive mt-1">{errors.amount.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes
            </Label>
            <div className="col-span-3">
              <Textarea id="notes" {...register('notes')} />
              {errors.notes && (
                <p className="text-sm text-destructive mt-1">{errors.notes.message}</p>
              )}
            </div>
          </div>

          {apiError && (
            <div className="flex items-center gap-2 text-destructive text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{apiError}</span>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {loadingText}
              </>
            ) : (
              submitButtonText
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
