'use client'

import React from 'react'
import { useAuth } from '@/components/auth-provider'
import SideNav from '@/components/side-nav'
import Header from '@/components/header'
import { usePathname } from 'next/navigation'

export function AuthenticatedLayout({ children }: { children: React.ReactNode }) {
  const { isAuthenticated } = useAuth()
  const pathname = usePathname()
  const isLoginPage = pathname === '/login'

  // If on login page, don't render header/sidebar regardless of auth state
  if (isLoginPage) {
    return <div className="min-h-screen">{children}</div>
  }

  return (
    <div className="flex min-h-screen flex-col">
      {isAuthenticated && <Header />}
      <div className="flex flex-1">
        {isAuthenticated && <SideNav />}
        <main className={`flex-1 ${isAuthenticated ? 'px-6 py-6' : ''}`}>{children}</main>
      </div>
    </div>
  )
}
