'use client'

import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
  DialogClose,
} from '@/components/ui/dialog'
import {
  Injury,
  InjuryCreate,
  InjuryUpdate,
  MedicalTreatmentRequirements,
  InsuranceBillingStatus,
} from '@/lib/api/types' // Assuming enums are available if select is used
import { useEffect } from 'react'
import { Loader2 } from 'lucide-react'

// Zod schema for Injury form validation
const injuryFormSchema = z.object({
  injury_type: z.string().min(1, 'Injury type is required').max(100).nullable().optional(),
  injury_severity: z.string().min(1, 'Severity is required').max(100).nullable().optional(),
  injury_description: z.string().nullable().optional(),
  medical_treatment_requirements: z.nativeEnum(MedicalTreatmentRequirements).nullable().optional(),
  estimated_cost: z.number().positive('Must be positive').nullable().optional(),
  insurance_billing_status: z.nativeEnum(InsuranceBillingStatus).nullable().optional(),
  treatment_nature: z.string().max(500).nullable().optional(),
  medical_provider_name: z.string().max(200).nullable().optional(),
  medical_provider_address: z.string().max(500).nullable().optional(),
  equipment_involved: z.boolean().nullable().optional(),
  equipment_details: z.string().max(500).nullable().optional(),
})

export type InjuryFormValues = z.infer<typeof injuryFormSchema>

interface InjuryFormProps {
  isOpen: boolean
  onClose: () => void
  claimId: string
  personId: string
  injuryData?: Injury | null // For pre-filling in edit mode
  onSave: (data: InjuryFormValues, injuryId?: string) => Promise<void>
  // No isSaving prop here, the dialog will be controlled by isOpen
}

export function InjuryForm({
  isOpen,
  onClose,
  claimId,
  personId,
  injuryData,
  onSave,
}: InjuryFormProps) {
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty, isSubmitting }, // Added isSubmitting
  } = useForm<InjuryFormValues>({
    resolver: zodResolver(injuryFormSchema),
    defaultValues: {
      injury_type: '',
      injury_severity: '',
      injury_description: '',
      medical_treatment_requirements: null,
      estimated_cost: null,
      insurance_billing_status: null,
      treatment_nature: '',
      medical_provider_name: '',
      medical_provider_address: '',
      equipment_involved: false,
      equipment_details: '',
    },
  })

  // Uncommented and fixed useEffect for proper form initialization
  useEffect(() => {
    if (isOpen) {
      if (injuryData) {
        // Explicitly define and sanitize all fields for reset
        const processedDataForReset = {
          injury_type: injuryData.injury_type || '',
          injury_severity: injuryData.injury_severity || '',
          injury_description: injuryData.injury_description || '',
          medical_treatment_requirements: injuryData.medical_treatment_requirements || null,
          estimated_cost:
            injuryData.estimated_cost !== null && injuryData.estimated_cost !== undefined
              ? parseFloat(String(injuryData.estimated_cost))
              : null,
          insurance_billing_status: injuryData.insurance_billing_status || null,
          treatment_nature: injuryData.treatment_nature || '',
          medical_provider_name: injuryData.medical_provider_name || '',
          medical_provider_address: injuryData.medical_provider_address || '',
          equipment_involved:
            injuryData.equipment_involved !== null && injuryData.equipment_involved !== undefined
              ? Boolean(injuryData.equipment_involved)
              : false,
          equipment_details: injuryData.equipment_details || '',
        }

        // Reset synchronously without setTimeout to avoid timing issues
        reset(processedDataForReset)
      } else {
        reset({
          injury_type: '',
          injury_severity: '',
          injury_description: '',
          medical_treatment_requirements: null,
          estimated_cost: null,
          insurance_billing_status: null,
          treatment_nature: '',
          medical_provider_name: '',
          medical_provider_address: '',
          equipment_involved: false,
          equipment_details: '',
        })
      }
    }
  }, [isOpen, injuryData, reset])

  const processSubmit = async (data: InjuryFormValues) => {
    try {
      await onSave(data, injuryData?.id) // This is handleSaveInjury from InjuryList
    } catch (error) {
      // This catch block might be redundant if onSave (handleSaveInjury) already catches/toasts.
      // Adding for comprehensive logging during this debug phase.
    }
  }

  // Handle form submission with special protections against bubbling and default behavior
  const handleFormSubmit = (e: React.FormEvent) => {
    // Stop the event from propagating up to parent forms
    e.stopPropagation()

    // Prevent default form submission which would cause page refresh
    e.preventDefault()

    // Manually trigger react-hook-form submission handler
    handleSubmit(processSubmit)(e)
  }

  const formatEnumForDisplay = (enumValue: string) => {
    try {
      if (!enumValue) return ''
      return enumValue
        .split('_')
        .map(word => word.charAt(0) + word.slice(1).toLowerCase())
        .join(' ')
    } catch (e) {
      return 'Error Displaying Value' // Fallback display
    }
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={open => {
        // If trying to close the dialog, call the onClose handler from props
        if (!open && isOpen) {
          if (isSubmitting) {
            return
          }
          onClose()
        }
      }}
    >
      <DialogContent
        className="sm:max-w-lg max-h-[90vh] overflow-y-auto"
        onEscapeKeyDown={e => {
          e.preventDefault()
        }}
        onInteractOutside={e => {
          e.preventDefault()
        }}
        forceMount={true}
      >
        <DialogHeader>
          <DialogTitle>{injuryData ? 'Edit Injury' : 'Add New Injury'}</DialogTitle>
          <DialogDescription>Provide the details for the injury.</DialogDescription>
        </DialogHeader>
        <form
          onSubmit={handleFormSubmit}
          onClick={e => e.stopPropagation()}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              e.stopPropagation()
            }
          }}
        >
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="injury_type">Injury Type / Body Part</Label>
                <Input
                  id="injury_type"
                  {...register('injury_type')}
                  onKeyDown={e => {
                    if (e.key === 'Enter') {
                      e.stopPropagation()
                    }
                  }}
                />
                {errors.injury_type && (
                  <p className="text-sm text-destructive">{errors.injury_type.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="injury_severity">Severity</Label>
                <Input id="injury_severity" {...register('injury_severity')} />
                {errors.injury_severity && (
                  <p className="text-sm text-destructive">{errors.injury_severity.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-1">
              <Label htmlFor="injury_description">Description</Label>
              <Textarea id="injury_description" {...register('injury_description')} />
              {errors.injury_description && (
                <p className="text-sm text-destructive">{errors.injury_description.message}</p>
              )}
            </div>

            {/* Optional: More detailed medical fields */}
            <h4 className="text-sm font-medium pt-2">Medical Details (Optional)</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="medical_treatment_requirements">Treatment Requirements</Label>
                <Controller
                  control={control}
                  name="medical_treatment_requirements"
                  render={({ field }) => (
                    <Select value={field.value || ''} onValueChange={field.onChange}>
                      <SelectTrigger id="medical_treatment_requirements">
                        <SelectValue placeholder="Select requirement" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(MedicalTreatmentRequirements).map(req => (
                          <SelectItem key={req} value={req}>
                            {formatEnumForDisplay(req)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
              <div className="space-y-1">
                <Label htmlFor="estimated_cost">Estimated Cost</Label>
                <Controller
                  control={control}
                  name="estimated_cost"
                  render={({ field }) => (
                    <Input
                      id="estimated_cost"
                      type="number"
                      step="0.01"
                      onChange={e =>
                        field.onChange(e.target.value ? parseFloat(e.target.value) : null)
                      }
                      value={field.value ?? ''}
                    />
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="treatment_nature">Nature of Treatment</Label>
                <Textarea id="treatment_nature" {...register('treatment_nature')} />
                {errors.treatment_nature && (
                  <p className="text-sm text-destructive">{errors.treatment_nature.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="insurance_billing_status">Insurance Billing</Label>
                <Controller
                  control={control}
                  name="insurance_billing_status"
                  render={({ field }) => (
                    <Select value={field.value || ''} onValueChange={field.onChange}>
                      <SelectTrigger id="insurance_billing_status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(InsuranceBillingStatus).map(status => (
                          <SelectItem key={status} value={status}>
                            {formatEnumForDisplay(status)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="medical_provider_name">Medical Provider Name</Label>
                <Input id="medical_provider_name" {...register('medical_provider_name')} />
                {errors.medical_provider_name && (
                  <p className="text-sm text-destructive">{errors.medical_provider_name.message}</p>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="medical_provider_address">Medical Provider Address</Label>
              <Textarea id="medical_provider_address" {...register('medical_provider_address')} />
              {errors.medical_provider_address && (
                <p className="text-sm text-destructive">
                  {errors.medical_provider_address.message}
                </p>
              )}
            </div>

            <h4 className="text-sm font-medium pt-2">Equipment (Optional)</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-center">
              <div className="space-y-1">
                <Label htmlFor="equipment_details">Equipment Details</Label>
                <Input id="equipment_details" {...register('equipment_details')} />
                {errors.equipment_details && (
                  <p className="text-sm text-destructive">{errors.equipment_details.message}</p>
                )}
              </div>
            </div>
          </div>
          <DialogFooter className="pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                onClose()
              }}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
