'use client'

import React, { useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Claim, ClaimStatus, ClaimType, UserResponse, PaginatedResponse } from '@/lib/api/types'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { useApiMutation, useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Loader2 } from 'lucide-react'
import { format } from 'date-fns'
import { formatDate } from '@/lib/utils'

// Common schema fields for all claim types (without validation)
const baseSchemaFields = z.object({
  status: z.nativeEnum(ClaimStatus, {
    required_error: 'Please select a claim status',
  }),
  assigned_to_id: z.string().uuid().nullable().optional(),
  claimant_name: z.string().optional().nullable(),
  claimant_email: z
    .string()
    .optional()
    .nullable()
    .refine(
      value => {
        if (!value || value.trim() === '') return true // Allow empty/null values
        return z.string().email().safeParse(value).success // Only validate email format if value exists
      },
      {
        message: 'Invalid email address',
      }
    ),
  claimant_phone: z.string().optional().nullable(),
  insured_name: z.string().optional().nullable(),
  insured_email: z
    .string()
    .optional()
    .nullable()
    .refine(
      value => {
        if (!value || value.trim() === '') return true // Allow empty/null values
        return z.string().email().safeParse(value).success // Only validate email format if value exists
      },
      {
        message: 'Invalid email address',
      }
    ),
  insured_phone: z.string().optional().nullable(),
  description: z.string().optional().nullable(),
  incident_date: z.string().optional().nullable(),
  incident_location: z.string().optional().nullable(),
  policy_number: z
    .string()
    .max(100, 'Policy number cannot exceed 100 characters')
    .optional()
    .nullable(),
})

// Add contact person validation to base schema
const baseSchema = baseSchemaFields.refine(
  data => {
    // Ensure at least one contact person is provided
    const hasClaimant = data.claimant_name && data.claimant_name.trim().length > 0
    const hasInsured = data.insured_name && data.insured_name.trim().length > 0
    return hasClaimant || hasInsured
  },
  {
    message: 'At least one contact person (claimant or insured) must be provided',
    path: ['claimant_name'], // This will show the error on the claimant_name field
  }
)

// Auto claim specific fields
const autoSchema = baseSchemaFields
  .extend({
    auto_details: z
      .object({
        vehicle_year: z.string().optional().nullable(),
        vehicle_make: z.string().optional().nullable(),
        vehicle_model: z.string().optional().nullable(),
        vehicle_vin: z
          .string()
          .refine(val => !val || val.length === 17, {
            message: 'Vehicle VIN must be exactly 17 characters',
          })
          .optional()
          .nullable(),
      })
      .optional()
      .nullable(),
  })
  .refine(
    data => {
      // Ensure at least one contact person is provided
      const hasClaimant = data.claimant_name && data.claimant_name.trim().length > 0
      const hasInsured = data.insured_name && data.insured_name.trim().length > 0
      return hasClaimant || hasInsured
    },
    {
      message: 'At least one contact person (claimant or insured) must be provided',
      path: ['claimant_name'],
    }
  )

// Property claim specific fields
const propertySchema = baseSchemaFields
  .extend({
    property_details: z
      .object({
        property_type: z.string().optional().nullable(),
        damage_description: z.string().optional().nullable(),
      })
      .optional()
      .nullable(),
  })
  .refine(
    data => {
      // Ensure at least one contact person is provided
      const hasClaimant = data.claimant_name && data.claimant_name.trim().length > 0
      const hasInsured = data.insured_name && data.insured_name.trim().length > 0
      return hasClaimant || hasInsured
    },
    {
      message: 'At least one contact person (claimant or insured) must be provided',
      path: ['claimant_name'],
    }
  )

// General liability claim specific fields
const generalLiabilitySchema = baseSchemaFields
  .extend({
    gl_details: z
      .object({
        incident_type: z.string().optional().nullable(),
        liability_description: z.string().optional().nullable(),
      })
      .optional()
      .nullable(),
  })
  .refine(
    data => {
      // Ensure at least one contact person is provided
      const hasClaimant = data.claimant_name && data.claimant_name.trim().length > 0
      const hasInsured = data.insured_name && data.insured_name.trim().length > 0
      return hasClaimant || hasInsured
    },
    {
      message: 'At least one contact person (claimant or insured) must be provided',
      path: ['claimant_name'],
    }
  )

// Create a type for the form data based on these schemas
type ClaimFormData = z.infer<typeof baseSchema> & {
  auto_details?: {
    vehicle_year?: string | null
    vehicle_make?: string | null
    vehicle_model?: string | null
    vehicle_vin?: string | null
  } | null
  property_details?: {
    property_type?: string | null
    damage_description?: string | null
  } | null
  gl_details?: {
    incident_type?: string | null
    liability_description?: string | null
  } | null
  assigned_to_id?: string | null
}

interface UpdateClaimModalProps {
  isOpen: boolean
  onClose: () => void
  claim: Claim | null
  onSuccess?: () => void
}

// Helper function to format date for input field
const formatDateForInput = (dateString?: string | null): string => {
  if (!dateString) return ''
  try {
    return dateString.substring(0, 10) // Extract YYYY-MM-DD from ISO string
  } catch (error) {
    console.error('Error formatting date for input:', error)
    return ''
  }
}

export default function UpdateClaimModal({
  isOpen,
  onClose,
  claim,
  onSuccess,
}: UpdateClaimModalProps) {
  const getSchemaForClaimType = (type: ClaimType | string | undefined) => {
    switch (type) {
      case 'AUTO':
        return autoSchema
      case 'PROPERTY':
        return propertySchema
      case 'GENERAL_LIABILITY':
        return generalLiabilitySchema
      default:
        return baseSchema
    }
  }

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ClaimFormData>({
    resolver: zodResolver(claim ? getSchemaForClaimType(claim.type) : baseSchema),
    defaultValues: {
      status: claim?.status,
      claimant_name: claim?.claimant_name || '',
      claimant_email: claim?.claimant_email || '',
      claimant_phone: claim?.claimant_phone || '',
      insured_name: claim?.insured_name || '',
      insured_email: claim?.insured_email || '',
      insured_phone: claim?.insured_phone || '',
      description: claim?.description || '',
      incident_date: claim?.incident_date
        ? format(new Date(claim.incident_date), 'yyyy-MM-dd')
        : '',
      incident_location: claim?.incident_location || '',
      policy_number: claim?.policy_number || '',
      assigned_to_id: claim?.assigned_to_id || null,
      auto_details: claim?.auto_details
        ? {
            vehicle_year: claim.auto_details.vehicle_year?.toString() || null,
            vehicle_make: claim.auto_details.vehicle_make || null,
            vehicle_model: claim.auto_details.vehicle_model || null,
            vehicle_vin: claim.auto_details.vehicle_vin || null,
          }
        : undefined,
      property_details: claim?.type === 'PROPERTY' ? {} : undefined,
      gl_details: claim?.type === 'GENERAL_LIABILITY' ? {} : undefined,
    },
  })

  // Fetch users for adjuster assignment
  const {
    data: usersResponse,
    isLoading: isLoadingUsers,
    error: usersError,
  } = useApi<PaginatedResponse<UserResponse>>(
    () => api.users.getUsers({ limit: 100, role: 'ADJUSTER' }),
    []
  )

  // Extract users array from paginated response
  const users = usersResponse?.items || []

  useEffect(() => {
    console.log('[UpdateClaimModal] isLoadingUsers:', isLoadingUsers)
    console.log('[UpdateClaimModal] usersResponse (raw):', usersResponse)
    console.log('[UpdateClaimModal] users (extracted items):', users)
    console.log('[UpdateClaimModal] Is users an array?:', Array.isArray(users))
    console.log('[UpdateClaimModal] usersError:', usersError)
  }, [users, isLoadingUsers, usersError])

  // Update form when claim changes
  useEffect(() => {
    if (claim) {
      reset({
        status: claim.status,
        claimant_name: claim.claimant_name || '',
        claimant_email: claim.claimant_email || '',
        claimant_phone: claim.claimant_phone || '',
        insured_name: claim.insured_name || '',
        insured_email: claim.insured_email || '',
        insured_phone: claim.insured_phone || '',
        description: claim.description || '',
        incident_date: claim.incident_date
          ? format(new Date(claim.incident_date), 'yyyy-MM-dd')
          : '',
        incident_location: claim.incident_location || '',
        policy_number: claim.policy_number || '',
        assigned_to_id: claim.assigned_to_id || null,
        auto_details: claim.auto_details
          ? {
              vehicle_year: claim.auto_details.vehicle_year?.toString() || null,
              vehicle_make: claim.auto_details.vehicle_make || null,
              vehicle_model: claim.auto_details.vehicle_model || null,
              vehicle_vin: claim.auto_details.vehicle_vin || null,
            }
          : undefined,
        property_details: claim.type === 'PROPERTY' ? {} : undefined,
        gl_details: claim.type === 'GENERAL_LIABILITY' ? {} : undefined,
      })
    }
  }, [claim, reset])

  // Customize mutation hook with extended callbacks
  const mutation = useApiMutation((data: any) => {
    console.log('Updating claim with data:', JSON.stringify(data, null, 2))
    return api.claims.updateClaim(claim?.id || '', data)
  })

  // Use local callback to ensure modal closes
  const handleUpdateClaim = async (data: any) => {
    if (!claim) return

    try {
      // Log the incident_date to help debug
      console.log('[UpdateClaimModal] incident_date in handleUpdateClaim data:', data.incident_date)
      console.log(
        '[UpdateClaimModal] Full data in handleUpdateClaim:',
        JSON.stringify(data, null, 2)
      )

      // Convert empty strings to null for phone fields to match backend expectations
      const cleanedData = {
        ...data,
        claimant_phone: data.claimant_phone === '' ? null : data.claimant_phone,
        claimant_email: data.claimant_email === '' ? null : data.claimant_email,
        insured_phone: data.insured_phone === '' ? null : data.insured_phone,
        insured_email: data.insured_email === '' ? null : data.insured_email,
      }

      // Set the claim type in the request data
      const requestData = {
        type: claim.type,
        ...cleanedData,
      }

      console.log('Updating claim with data:', JSON.stringify(requestData, null, 2))

      await mutation.mutate(requestData)
      console.log('Claim updated successfully')
      onClose()
      if (onSuccess) {
        onSuccess()
      }
    } catch (error) {
      console.error('Failed to update claim:', error)
      // Error is handled by the mutation's onError callback
    }
  }

  const onSubmit = (data: ClaimFormData) => {
    handleUpdateClaim(data)
  }

  // Use the local error state from mutation
  const { isLoading, error, lastError } = mutation

  if (!isOpen || !claim) {
    return null
  }

  return (
    <Dialog
      open={isOpen}
      onOpenChange={open => {
        if (!open) {
          console.log('Dialog closing via onOpenChange')
          onClose()
        }
      }}
    >
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Update Claim {claim.claim_number}</DialogTitle>
          <DialogDescription>
            Update the claim details. Fields marked with * are required.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4 py-4">
          {/* Common Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status" className="font-medium">
                Status *
              </Label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(ClaimStatus).map(status => (
                        <SelectItem key={status} value={status}>
                          {status.replace('_', ' ')}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.status && (
                <p className="text-sm text-red-500">{errors.status.message?.toString()}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="assigned_to_id" className="font-medium">
                Adjuster
              </Label>
              <Controller
                name="assigned_to_id"
                control={control}
                render={({ field }) => (
                  <Select
                    onValueChange={value => field.onChange(value === 'unassigned' ? null : value)}
                    defaultValue={field.value || 'unassigned'}
                    disabled={isLoadingUsers}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={isLoadingUsers ? 'Loading users...' : 'Select adjuster'}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unassigned">Unassigned</SelectItem>
                      {isLoadingUsers && (
                        <SelectItem value="loading-users" disabled>
                          Loading users...
                        </SelectItem>
                      )}
                      {usersError && (
                        <SelectItem value="error-loading-users" disabled>
                          Error loading users
                        </SelectItem>
                      )}
                      {!isLoadingUsers && !usersError && users && users.length === 0 && (
                        <SelectItem value="no-users-found" disabled>
                          No adjusters found
                        </SelectItem>
                      )}
                      {users?.map(user => (
                        <SelectItem key={user.id} value={user.id}>
                          {user.first_name} {user.last_name} {user.email ? `(${user.email})` : ''}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.assigned_to_id && (
                <p className="text-sm text-red-500">{errors.assigned_to_id.message?.toString()}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="claimant_name" className="font-medium">
                Claimant Name *
              </Label>
              <Controller
                name="claimant_name"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.claimant_name && (
                <p className="text-sm text-red-500">{errors.claimant_name.message?.toString()}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="claimant_email" className="font-medium">
                Claimant Email
              </Label>
              <Controller
                name="claimant_email"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.claimant_email && (
                <p className="text-sm text-red-500">{errors.claimant_email.message?.toString()}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="claimant_phone" className="font-medium">
                Claimant Phone
              </Label>
              <Controller
                name="claimant_phone"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.claimant_phone && (
                <p className="text-sm text-red-500">{errors.claimant_phone.message?.toString()}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="insured_name" className="font-medium">
                Insured Name
              </Label>
              <Controller
                name="insured_name"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.insured_name && (
                <p className="text-sm text-red-500">{errors.insured_name.message?.toString()}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="insured_email" className="font-medium">
                Insured Email
              </Label>
              <Controller
                name="insured_email"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.insured_email && (
                <p className="text-sm text-red-500">{errors.insured_email.message?.toString()}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="insured_phone" className="font-medium">
                Insured Phone
              </Label>
              <Controller
                name="insured_phone"
                control={control}
                render={({ field }) => <Input {...field} value={field.value || ''} />}
              />
              {errors.insured_phone && (
                <p className="text-sm text-red-500">{errors.insured_phone.message?.toString()}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident_date" className="font-medium">
                Incident Date
              </Label>
              <Controller
                name="incident_date"
                control={control}
                render={({ field }) => {
                  // Log the field value on each render/change for incident_date
                  console.log('[UpdateClaimModal] incident_date field.value:', field.value)
                  return <Input type="date" {...field} value={formatDateForInput(field.value)} />
                }}
              />
              {errors.incident_date && (
                <p className="text-sm text-red-500">{errors.incident_date.message?.toString()}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="incident_location" className="font-medium">
              Incident Location
            </Label>
            <Controller
              name="incident_location"
              control={control}
              render={({ field }) => <Input {...field} value={field.value || ''} />}
            />
            {errors.incident_location && (
              <p className="text-sm text-red-500">{errors.incident_location.message?.toString()}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="font-medium">
              Description
            </Label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => <Textarea {...field} value={field.value || ''} />}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message?.toString()}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="policy_number" className="font-medium">
              Policy Number
            </Label>
            <Controller
              name="policy_number"
              control={control}
              render={({ field }) => (
                <Input {...field} placeholder="Enter policy number" value={field.value || ''} />
              )}
            />
            {errors.policy_number && (
              <p className="text-sm text-red-500">{errors.policy_number.message?.toString()}</p>
            )}
          </div>

          {/* Auto Claim Specific Fields */}
          {claim.type === 'AUTO' && (
            <div className="space-y-4 mt-2">
              <h3 className="font-semibold">Vehicle Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="auto_details.vehicle_year" className="font-medium">
                    Vehicle Year
                  </Label>
                  <Controller
                    name="auto_details.vehicle_year"
                    control={control}
                    render={({ field }) => <Input {...field} value={field.value || ''} />}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="auto_details.vehicle_make" className="font-medium">
                    Vehicle Make
                  </Label>
                  <Controller
                    name="auto_details.vehicle_make"
                    control={control}
                    render={({ field }) => <Input {...field} value={field.value || ''} />}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="auto_details.vehicle_model" className="font-medium">
                    Vehicle Model
                  </Label>
                  <Controller
                    name="auto_details.vehicle_model"
                    control={control}
                    render={({ field }) => <Input {...field} value={field.value || ''} />}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="auto_details.vehicle_vin" className="font-medium">
                    Vehicle VIN
                  </Label>
                  <Controller
                    name="auto_details.vehicle_vin"
                    control={control}
                    render={({ field }) => <Input {...field} value={field.value || ''} />}
                  />
                  <p className="text-xs text-muted-foreground">
                    Vehicle Identification Number must be exactly 17 characters
                  </p>
                  {errors.auto_details?.vehicle_vin && (
                    <p className="text-sm text-red-500">
                      {errors.auto_details.vehicle_vin.message?.toString()}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Property Claim Specific Fields */}
          {claim.type === 'PROPERTY' && (
            <div className="space-y-4 mt-2">
              <h3 className="font-semibold">Property Information</h3>
              <div className="space-y-2">
                <Label htmlFor="property_details.property_type" className="font-medium">
                  Property Type
                </Label>
                <Controller
                  name="property_details.property_type"
                  control={control}
                  render={({ field }) => <Input {...field} value={field.value || ''} />}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="property_details.damage_description" className="font-medium">
                  Damage Description
                </Label>
                <Controller
                  name="property_details.damage_description"
                  control={control}
                  render={({ field }) => <Textarea {...field} value={field.value || ''} />}
                />
              </div>
            </div>
          )}

          {/* General Liability Claim Specific Fields */}
          {claim.type === 'GENERAL_LIABILITY' && (
            <div className="space-y-4 mt-2">
              <h3 className="font-semibold">Liability Information</h3>
              <div className="space-y-2">
                <Label htmlFor="gl_details.incident_type" className="font-medium">
                  Incident Type
                </Label>
                <Controller
                  name="gl_details.incident_type"
                  control={control}
                  render={({ field }) => <Input {...field} value={field.value || ''} />}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gl_details.liability_description" className="font-medium">
                  Liability Description
                </Label>
                <Controller
                  name="gl_details.liability_description"
                  control={control}
                  render={({ field }) => <Textarea {...field} value={field.value || ''} />}
                />
              </div>
            </div>
          )}

          {error && (
            <div className="space-y-2">
              <p className="text-sm font-medium text-red-600">Error updating claim:</p>
              {(() => {
                // Try to parse detailed validation errors
                try {
                  console.log('Raw error object:', lastError)
                  console.log('Error data:', lastError ? (lastError as any).data : undefined)

                  // Handle preserved Error object (ApiError with .data property)
                  if (lastError && lastError instanceof Error) {
                    const apiError = lastError as any

                    // Check for ApiError with data property containing backend error details
                    if (apiError.data) {
                      // Check for backend message field first, then detail
                      const errorMessage = apiError.data.message || apiError.data.detail
                      if (errorMessage) {
                        return <p className="text-sm text-red-500">{errorMessage}</p>
                      }

                      // Handle FastAPI validation errors (array format)
                      if (Array.isArray(apiError.data.detail)) {
                        return (
                          <ul className="text-sm text-red-500 space-y-1">
                            {apiError.data.detail.map((validationError: any, index: number) => {
                              const field = validationError.loc
                                ? validationError.loc.join(' -> ')
                                : 'unknown field'
                              return (
                                <li key={index} className="flex items-start gap-2">
                                  <span className="font-medium">{field}:</span>
                                  <span>{validationError.msg}</span>
                                </li>
                              )
                            })}
                          </ul>
                        )
                      }
                    }

                    // Fallback to error message property
                    return (
                      <p className="text-sm text-red-500">
                        {apiError.message || 'An unexpected error occurred'}
                      </p>
                    )
                  }

                  // Handle string error (fallback) - use the string error from mutation
                  if (typeof error === 'string') {
                    return <p className="text-sm text-red-500">{error}</p>
                  }

                  // Final fallback
                  return <p className="text-sm text-red-500">An unexpected error occurred</p>
                } catch (e) {
                  console.error('Error parsing error object:', e)
                  return <p className="text-sm text-red-500">An unexpected error occurred</p>
                }
              })()}
            </div>
          )}
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => {
              console.log('Cancel button clicked')
              onClose()
            }}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            onClick={handleSubmit(onSubmit)}
            disabled={isLoading}
            className="min-w-24"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Claim'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
