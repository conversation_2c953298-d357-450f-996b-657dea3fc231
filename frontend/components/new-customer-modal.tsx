'use client'

import React, { useEffect, useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { <PERSON>ton } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { CustomerCreate } from '@/lib/api/types'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { AlertCircle, Loader2 } from 'lucide-react'
import { api } from '@/lib/api'
import { toast } from '@/components/ui/use-toast'

interface NewCustomerModalProps {
  isOpen: boolean
  onClose: () => void
  onCustomerCreated: () => void
}

// Define form schema with Zod
const customerSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  prefix: z
    .string()
    .min(1, 'Prefix is required')
    .length(4, 'Prefix must be exactly 4 characters')
    .refine(value => /^[A-Z0-9]{4}$/.test(value), {
      message: 'Prefix must be 4 uppercase letters or numbers',
    }),
  description: z.string().optional(),
  active: z.boolean().default(true),
})

// Create type from schema
type CustomerFormData = z.infer<typeof customerSchema>

export default function NewCustomerModal({
  isOpen,
  onClose,
  onCustomerCreated,
}: NewCustomerModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [apiError, setApiError] = useState<string | null>(null)

  // Use the shared customersApi from the api object
  const customersApi = api.customers

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: '',
      prefix: '',
      description: '',
      active: true,
    },
  })

  const onSubmit = async (data: CustomerFormData) => {
    setIsSubmitting(true)
    setApiError(null)

    try {
      const customerData: CustomerCreate = {
        name: data.name,
        prefix: data.prefix.toUpperCase(),
        description: data.description || null,
        active: data.active,
      }

      console.log('Submitting customer data:', customerData)

      const newCustomer = await customersApi.createCustomer(customerData)
      console.log('Customer created successfully:', newCustomer)

      toast({
        title: 'Success',
        description: `Customer "${newCustomer.name}" created successfully.`,
      })

      reset() // Reset form
      onCustomerCreated() // Notify parent that a customer was created
      onClose() // Close modal
    } catch (err) {
      let errorMessage = 'Failed to create customer. Please try again.'
      if (err instanceof Error) {
        errorMessage = err.message
      }

      setApiError(errorMessage)
      console.error('Customer creation error:', err)

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset()
      setApiError(null)
    }
  }, [isOpen, reset])

  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Customer</DialogTitle>
          <DialogDescription>
            Add a new insurance carrier or self-insured organization to the system.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name <span className="text-destructive">*</span>
            </Label>
            <div className="col-span-3">
              <Input
                id="name"
                placeholder="Enter customer name"
                {...register('name')}
                className={errors.name ? 'border-destructive' : ''}
              />
              {errors.name && (
                <p className="text-sm text-destructive mt-1">{errors.name.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="prefix" className="text-right">
              Prefix <span className="text-destructive">*</span>
            </Label>
            <div className="col-span-3">
              <Input
                id="prefix"
                placeholder="4-character prefix (e.g. ACME)"
                maxLength={4}
                {...register('prefix')}
                className={errors.prefix ? 'border-destructive' : ''}
                onChange={e => {
                  e.target.value = e.target.value.toUpperCase()
                  register('prefix').onChange(e)
                }}
              />
              {errors.prefix && (
                <p className="text-sm text-destructive mt-1">{errors.prefix.message}</p>
              )}
              <p className="text-xs text-muted-foreground mt-1">
                Used for claim numbering. Must be 4 uppercase letters or numbers.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <div className="col-span-3">
              <Textarea
                id="description"
                placeholder="Enter description (optional)"
                {...register('description')}
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-destructive mt-1">{errors.description.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="active" className="text-right">
              Active
            </Label>
            <div className="flex items-center space-x-2">
              <Switch id="active" defaultChecked {...register('active')} />
              <Label htmlFor="active" className="text-sm text-muted-foreground">
                Customer is active in the system
              </Label>
            </div>
          </div>

          {apiError && (
            <div className="flex items-center gap-2 text-destructive text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{apiError}</span>
            </div>
          )}
        </form>

        <DialogFooter>
          <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit(onSubmit)} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              'Create Customer'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
