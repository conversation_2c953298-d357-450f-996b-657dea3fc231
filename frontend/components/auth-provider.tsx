'use client'

import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
  useRef,
  useCallback,
} from 'react'
import { useRouter, usePathname } from 'next/navigation'
// import { api } from '../lib/api'; // Keep for now, might be used by other parts of the app or future logic
import { getApiClientForAuthProvider, ApiClientClass } from '../lib/api' // Import the new function and type

// Define User type to match ApiClient.getCurrentUser return type
interface User {
  id: string
  name?: string
  email?: string
  role?: string
}

interface AuthContextType {
  isAuthenticated: boolean
  isLoading: boolean
  user: User | null
  error: string | null
  login: (username: string, password: string, rememberMe?: boolean) => Promise<boolean>
  logout: () => Promise<void>
}

const AuthContext = createContext<AuthContextType>({
  isAuthenticated: false,
  isLoading: true,
  user: null,
  error: null,
  login: async () => false,
  logout: async () => {},
})

export const useAuth = () => useContext(AuthContext)

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [user, setUser] = useState<User | null>(null)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()
  const pathname = usePathname()
  const refreshTimerRef = useRef<NodeJS.Timeout | null>(null)
  const checkAuthStatusRef = useRef<(() => void) | null>(null)

  // Special case for login page
  const isLoginPage = pathname === '/login'

  // Clear any existing refresh timer
  const clearRefreshTimer = useCallback(() => {
    if (refreshTimerRef.current) {
      clearTimeout(refreshTimerRef.current)
      refreshTimerRef.current = null
    }
  }, [])

  // Setup token refresh timer
  const setupRefreshTimer = useCallback(
    (client: ApiClientClass) => {
      clearRefreshTimer()
      if (!client) return

      const token = client.getAuthToken()
      if (!token) return

      try {
        const decoded = client.decodeToken(token)
        const currentTime = Math.floor(Date.now() / 1000)
        const expiresAt = decoded.exp
        const timeToExpiration = expiresAt - currentTime
        const refreshTime = Math.max(timeToExpiration * 0.75, 30) * 1000
        console.log(`Setting up token refresh timer for ${refreshTime / 1000} seconds from now`)

        refreshTimerRef.current = setTimeout(async () => {
          console.log('Token refresh timer triggered')
          if (client.isAuthenticated()) {
            try {
              console.log('Attempting to refresh token...')
              await client.refreshToken()
              console.log('Token refreshed successfully')
              setupRefreshTimer(client)
            } catch (e) {
              console.error('Failed to refresh token:', e)
              clearRefreshTimer()
              // Re-check auth status after failed refresh
              if (checkAuthStatusRef.current) {
                checkAuthStatusRef.current()
              }
            }
          }
        }, refreshTime)
      } catch (e) {
        console.error('Error setting up refresh timer:', e)
      }
    },
    [clearRefreshTimer]
  )

  // Check authentication status and update user info
  const checkAuthStatus = useCallback(() => {
    console.log('Checking authentication status...')

    try {
      // Get client directly, no state dependency
      const client = getApiClientForAuthProvider()
      const authenticated = client.isAuthenticated()
      console.log('Is authenticated:', authenticated)

      setIsAuthenticated(authenticated)

      if (authenticated) {
        const userInfo = client.getCurrentUser()
        console.log('User info from token:', userInfo)
        setUser(userInfo)
        setupRefreshTimer(client)
      } else {
        setUser(null)
        clearRefreshTimer()
      }
    } catch (error) {
      console.error('Error during auth check:', error)
      setIsAuthenticated(false)
      setUser(null)
      clearRefreshTimer()
    } finally {
      // Always set loading to false after we've made a determination
      setIsLoading(false)
    }
  }, [setupRefreshTimer, clearRefreshTimer])

  // Update the ref whenever checkAuthStatus changes
  useEffect(() => {
    checkAuthStatusRef.current = checkAuthStatus
  }, [checkAuthStatus])

  // Check auth status on mount and pathname changes
  useEffect(() => {
    console.log('[AuthProvider] Checking auth status for pathname:', pathname)
    checkAuthStatus()

    return () => {
      clearRefreshTimer()
    }
  }, [pathname, checkAuthStatus, clearRefreshTimer])

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !isLoginPage && !pathname.includes('/login')) {
      console.log('Not authenticated, redirecting to login')
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, isLoginPage, pathname, router])

  const login = async (username: string, password: string, rememberMe: boolean = false) => {
    setIsLoading(true)
    setError(null)
    try {
      console.log(`AuthProvider: Attempting login for ${username} (Remember Me: ${rememberMe})`)
      const client = getApiClientForAuthProvider()
      await client.login(username, password, rememberMe)
      console.log('AuthProvider: Login successful')
      checkAuthStatus()
      return true
    } catch (err) {
      console.error('AuthProvider: Login failed', err)
      const errorMessage = err instanceof Error ? err.message : 'Authentication failed'
      console.error('Error message:', errorMessage)
      setError(errorMessage)
      setIsLoading(false)
      return false
    }
  }

  const logout = async () => {
    setIsLoading(true)
    clearRefreshTimer()
    try {
      const client = getApiClientForAuthProvider()
      await client.logout()
    } catch (err) {
      console.error('Logout error:', err)
    } finally {
      setIsAuthenticated(false)
      setUser(null)
      setIsLoading(false)
      router.push('/login')
    }
  }

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        error,
        login,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}
