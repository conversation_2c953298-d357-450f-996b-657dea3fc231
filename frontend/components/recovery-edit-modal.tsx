'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2 } from 'lucide-react'
import { useRecovery } from '@/hooks/useRecovery'
import { RecoveryDetailsResponse, RecoveryStatus } from '@/lib/api/types/claim-base-types'

interface RecoveryEditModalProps {
  isOpen: boolean
  onClose: () => void
  claimId: string
  mode: 'status' | 'carrier' | 'create'
  recoveryData?: RecoveryDetailsResponse | null
  onSuccess?: () => void
}

export function RecoveryEditModal({
  isOpen,
  onClose,
  claimId,
  mode,
  recoveryData,
  onSuccess,
}: RecoveryEditModalProps) {
  const {
    updateRecoveryStatus,
    updateCarrierDetails,
    updateRecoveryAmounts,
    isUpdatingStatus,
    isUpdatingCarrier,
    isUpdatingAmounts,
    updateError,
  } = useRecovery(claimId)

  const [selectedStatus, setSelectedStatus] = useState<RecoveryStatus | ''>('')
  const [carrierName, setCarrierName] = useState('')
  const [carrierContact, setCarrierContact] = useState('')
  const [carrierClaimNumber, setCarrierClaimNumber] = useState('')
  const [carrierAdjuster, setCarrierAdjuster] = useState('')
  const [expectedAmount, setExpectedAmount] = useState('')
  const [receivedAmount, setReceivedAmount] = useState('')
  const [error, setError] = useState<string | null>(null)

  // Initialize form data
  useEffect(() => {
    if (recoveryData) {
      setSelectedStatus(recoveryData.recovery_status || '')
      setCarrierName(recoveryData.carrier_name || '')
      setCarrierContact(recoveryData.carrier_contact || '')
      setCarrierClaimNumber(recoveryData.carrier_claim_number || '')
      setCarrierAdjuster(recoveryData.carrier_adjuster || '')
      setExpectedAmount(recoveryData.expected_amount || '')
      setReceivedAmount(recoveryData.received_amount || '')
    } else {
      // For create mode, set defaults
      setSelectedStatus('')
      setCarrierName('')
      setCarrierContact('')
      setCarrierClaimNumber('')
      setCarrierAdjuster('')
      setExpectedAmount('')
      setReceivedAmount('')
    }
    setError(null)
  }, [recoveryData, isOpen])

  const handleSubmit = async () => {
    setError(null)

    try {
      if (mode === 'status' || mode === 'create') {
        if (!selectedStatus) {
          setError('Please select a recovery status')
          return
        }
        await updateRecoveryStatus(selectedStatus as RecoveryStatus)

        // Also update recovery amounts if they're provided
        if (expectedAmount || receivedAmount) {
          await updateRecoveryAmounts(expectedAmount, receivedAmount)
        }
      }

      if (mode === 'carrier' || mode === 'create') {
        await updateCarrierDetails(carrierName, carrierContact, carrierClaimNumber, carrierAdjuster)
      }

      if (onSuccess) {
        onSuccess()
      }
    } catch (error: any) {
      console.error('Error updating recovery:', error)
      setError(error.message || 'Failed to update recovery information')
    }
  }

  const isLoading = isUpdatingStatus || isUpdatingCarrier || isUpdatingAmounts

  // Determine modal content based on mode
  let title = 'Edit Recovery'
  let description = 'Update recovery information'

  if (mode === 'create') {
    title = 'Initiate Recovery'
    description = 'Set up recovery information for this claim'
  } else if (mode === 'status') {
    title = 'Edit Recovery Status'
    description = 'Update the recovery status'
  } else if (mode === 'carrier') {
    title = 'Edit Carrier Details'
    description = 'Update third party carrier information'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {(mode === 'status' || mode === 'create') && (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="recovery-status" className="text-right">
                  Status
                </Label>
                <div className="col-span-3">
                  <Select
                    value={selectedStatus}
                    onValueChange={value => setSelectedStatus(value as RecoveryStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select recovery status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value={RecoveryStatus.NOT_STARTED}>Not Started</SelectItem>
                      <SelectItem value={RecoveryStatus.INITIATED}>Initiated</SelectItem>
                      <SelectItem value={RecoveryStatus.IN_PROGRESS}>In Progress</SelectItem>
                      <SelectItem value={RecoveryStatus.COMPLETED}>Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="expected-amount" className="text-right">
                  Expected Amount
                </Label>
                <Input
                  id="expected-amount"
                  value={expectedAmount}
                  onChange={e => setExpectedAmount(e.target.value)}
                  placeholder="0.00"
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="received-amount" className="text-right">
                  Received Amount
                </Label>
                <Input
                  id="received-amount"
                  value={receivedAmount}
                  onChange={e => setReceivedAmount(e.target.value)}
                  placeholder="0.00"
                  className="col-span-3"
                />
              </div>
            </>
          )}

          {(mode === 'carrier' || mode === 'create') && (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="carrier-name" className="text-right">
                  Carrier Name
                </Label>
                <Input
                  id="carrier-name"
                  value={carrierName}
                  onChange={e => setCarrierName(e.target.value)}
                  placeholder="Enter carrier name"
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="carrier-claim-number" className="text-right">
                  Claim Number
                </Label>
                <Input
                  id="carrier-claim-number"
                  value={carrierClaimNumber}
                  onChange={e => setCarrierClaimNumber(e.target.value)}
                  placeholder="Enter carrier's claim number"
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="carrier-adjuster" className="text-right">
                  Adjuster
                </Label>
                <Input
                  id="carrier-adjuster"
                  value={carrierAdjuster}
                  onChange={e => setCarrierAdjuster(e.target.value)}
                  placeholder="Enter adjuster name"
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="carrier-contact" className="text-right pt-2">
                  Contact Info
                </Label>
                <Textarea
                  id="carrier-contact"
                  value={carrierContact}
                  onChange={e => setCarrierContact(e.target.value)}
                  placeholder="Enter contact information (phone, email, etc.)"
                  className="col-span-3"
                  rows={3}
                />
              </div>
            </>
          )}

          {(error || updateError) && (
            <div className="col-span-4 text-sm text-destructive text-center">
              {error || updateError}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === 'create' ? 'Initiate Recovery' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
