'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, Car, Home, Building, Loader2 } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'
import { ClaimType, ClaimStatus, Claim } from '@/lib/api/types'
import { useState, useEffect } from 'react'
import UpdateClaimModal from './update-claim-modal'

interface ClaimInfoProps {
  claimNumber: string
  claim: Claim | null
  isLoading: boolean
  error: string | null
  onRefresh: () => Promise<void>
}

export function ClaimInfo({ claimNumber, claim, isLoading, error, onRefresh }: ClaimInfoProps) {
  const { formatDate } = useDateFormatter()
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false)

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // Add logging for state changes
  useEffect(() => {
    console.log('Modal state changed:', isUpdateModalOpen)
  }, [isUpdateModalOpen])

  // Resolve user ID when claim data loads
  useEffect(() => {
    if (claim?.created_by_id && !claim.created_by) {
      resolveUsers([claim.created_by_id])
    }
  }, [claim, resolveUsers])

  // Helper to get creator display name
  const getCreatorDisplayName = () => {
    if (claim?.created_by) {
      return `${claim.created_by.first_name} ${claim.created_by.last_name}`.trim()
    }
    if (claim?.created_by_id) {
      return getUserDisplayName(claim.created_by_id)
    }
    return 'Unknown'
  }

  // Handler for opening modal
  const handleOpenModal = () => {
    console.log('Opening update modal')
    setIsUpdateModalOpen(true)
  }

  // Handler for closing modal
  const handleCloseModal = () => {
    console.log('Closing update modal')
    setIsUpdateModalOpen(false)
  }

  // Handler for successful update
  const handleUpdateSuccess = () => {
    console.log('Update successful, refreshing claim data')
    onRefresh()
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-10">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Loading claim details...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !claim) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-10">
          <div className="flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-sm text-destructive">{error || 'Failed to load claim details'}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const getTypeIcon = () => {
    switch (claim.type) {
      case 'AUTO':
        return <Car className="h-10 w-10 text-sky-600" />
      case 'PROPERTY':
        return <Home className="h-10 w-10 text-rose-600" />
      case 'GENERAL_LIABILITY':
        return <Building className="h-10 w-10 text-violet-600" />
      default:
        return <AlertCircle className="h-10 w-10 text-slate-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'INVESTIGATION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'SETTLEMENT':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'LITIGATION':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'RECOVERY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'DRAFT':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'AUTO':
        return 'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300'
      case 'PROPERTY':
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case 'GENERAL_LIABILITY':
        return 'bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center gap-4">
          {getTypeIcon()}
          <div>
            <CardTitle className="text-xl">
              {claim.claimant_name || claim.insured_name || 'Unnamed Claim'}
            </CardTitle>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="outline" className={getTypeColor(claim.type)}>
                {claim.type.replace('_', ' ')}
              </Badge>
              <Badge variant="outline" className={getStatusColor(claim.status)}>
                {claim.status}
              </Badge>
            </div>
          </div>
        </div>
        <Button onClick={handleOpenModal}>Update Claim</Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Description</h3>
            <p className="text-sm text-muted-foreground">
              {claim.description || 'No description available'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Incident Details</h3>
              <dl className="grid grid-cols-1 gap-2 text-sm">
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Date</dt>
                  <dd>{formatDate(claim.incident_date || claim.date_of_loss || '')}</dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Location</dt>
                  <dd>{claim.incident_location || 'Not specified'}</dd>
                </div>
                <div className="grid grid-cols-2">
                  <dt className="text-muted-foreground">Claim Number</dt>
                  <dd>{claim.claim_number}</dd>
                </div>
                {claim.policy_number && (
                  <div className="grid grid-cols-2">
                    <dt className="text-muted-foreground">Policy Number</dt>
                    <dd>{claim.policy_number}</dd>
                  </div>
                )}
                {claim.jurisdiction && (
                  <div className="grid grid-cols-2">
                    <dt className="text-muted-foreground">Jurisdiction</dt>
                    <dd>{claim.jurisdiction}</dd>
                  </div>
                )}
              </dl>
            </div>

            <div>
              <h3 className="font-medium mb-2">Contact Information</h3>
              <dl className="grid grid-cols-1 gap-2 text-sm">
                {/* Claimant Information - Always show section */}
                <div className="space-y-2">
                  <dt className="text-muted-foreground font-medium">Claimant</dt>
                  <dd className="ml-2 space-y-1">
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Name:</span>
                      <span>{claim.claimant_name || 'Not provided'}</span>
                    </div>
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Email:</span>
                      <span>
                        {claim.claimant_email ? (
                          <a
                            href={`mailto:${claim.claimant_email}`}
                            className="text-primary hover:underline"
                          >
                            {claim.claimant_email}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </span>
                    </div>
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Phone:</span>
                      <span>
                        {claim.claimant_phone ? (
                          <a
                            href={`tel:${claim.claimant_phone}`}
                            className="text-primary hover:underline"
                          >
                            {claim.claimant_phone}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </span>
                    </div>
                  </dd>
                </div>

                {/* Insured Information - Always show section */}
                <div className="space-y-2">
                  <dt className="text-muted-foreground font-medium">Insured</dt>
                  <dd className="ml-2 space-y-1">
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Name:</span>
                      <span>{claim.insured_name || 'Not provided'}</span>
                    </div>
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Email:</span>
                      <span>
                        {claim.insured_email ? (
                          <a
                            href={`mailto:${claim.insured_email}`}
                            className="text-primary hover:underline"
                          >
                            {claim.insured_email}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </span>
                    </div>
                    <div className="grid grid-cols-2">
                      <span className="text-muted-foreground">Phone:</span>
                      <span>
                        {claim.insured_phone ? (
                          <a
                            href={`tel:${claim.insured_phone}`}
                            className="text-primary hover:underline"
                          >
                            {claim.insured_phone}
                          </a>
                        ) : (
                          'Not provided'
                        )}
                      </span>
                    </div>
                  </dd>
                </div>

                {(claim.carrier_name || claim.carrier_contact) && (
                  <>
                    {claim.carrier_name && (
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Carrier</dt>
                        <dd>{claim.carrier_name}</dd>
                      </div>
                    )}
                    {claim.carrier_contact && (
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Carrier Contact</dt>
                        <dd>{claim.carrier_contact}</dd>
                      </div>
                    )}
                  </>
                )}
                {(claim.created_by || claim.created_by_id) && (
                  <div className="grid grid-cols-2">
                    <dt className="text-muted-foreground">Created By</dt>
                    <dd>{getCreatorDisplayName()}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Administrative details for non-auto claims */}
          {(claim.type !== 'AUTO' || !claim.auto_details) &&
            (claim.carrier_name ||
              claim.carrier_contact ||
              claim.created_by ||
              claim.created_by_id) && (
              <div>
                <h3 className="font-medium mb-2">Administrative Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <dl className="grid grid-cols-1 gap-2 text-sm">
                    {claim.carrier_name && (
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Carrier</dt>
                        <dd>{claim.carrier_name}</dd>
                      </div>
                    )}
                    {claim.carrier_contact && (
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Carrier Contact</dt>
                        <dd>{claim.carrier_contact}</dd>
                      </div>
                    )}
                  </dl>
                  <dl className="grid grid-cols-1 gap-2 text-sm">
                    {(claim.created_by || claim.created_by_id) && (
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Created By</dt>
                        <dd>{getCreatorDisplayName()}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>
            )}

          {/* Additional sections for auto claims with vehicle details */}
          {claim.type === 'AUTO' && claim.auto_details && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">Vehicle Information</h3>
                <dl className="grid grid-cols-1 gap-2 text-sm">
                  <div className="grid grid-cols-2">
                    <dt className="text-muted-foreground">Year/Make/Model</dt>
                    <dd>
                      {claim.auto_details.vehicle_year ? `${claim.auto_details.vehicle_year} ` : ''}
                      {claim.auto_details.vehicle_make || 'Unknown'}{' '}
                      {claim.auto_details.vehicle_model || ''}
                    </dd>
                  </div>
                  {claim.auto_details.vehicle_vin && (
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">VIN</dt>
                      <dd className="font-mono text-xs">{claim.auto_details.vehicle_vin}</dd>
                    </div>
                  )}
                  {claim.auto_details.driver_name && (
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">Driver</dt>
                      <dd>{claim.auto_details.driver_name}</dd>
                    </div>
                  )}
                </dl>
              </div>

              <div>
                <h3 className="font-medium mb-2">Administrative Details</h3>
                <dl className="grid grid-cols-1 gap-2 text-sm">
                  {(claim.carrier_name || claim.carrier_contact) && (
                    <>
                      {claim.carrier_name && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Carrier</dt>
                          <dd>{claim.carrier_name}</dd>
                        </div>
                      )}
                      {claim.carrier_contact && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Carrier Contact</dt>
                          <dd>{claim.carrier_contact}</dd>
                        </div>
                      )}
                    </>
                  )}
                  {(claim.created_by || claim.created_by_id) && (
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">Created By</dt>
                      <dd>{getCreatorDisplayName()}</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      <UpdateClaimModal
        isOpen={isUpdateModalOpen}
        onClose={handleCloseModal}
        claim={claim}
        onSuccess={handleUpdateSuccess}
      />
    </Card>
  )
}
