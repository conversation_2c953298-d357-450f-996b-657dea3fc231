'use client'

import * as React from 'react'
import { useState, useEffect, useMemo } from 'react'
import { Check, ChevronsUpDown, Loader2, User } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { api } from '@/lib/api'
import { UserSummary } from '@/lib/api/types'
import { useDebounce } from '@/hooks/useDebounce'

const UNASSIGNED_VALUE = '__UNASSIGNED__'

interface UserAutocompleteProps {
  value?: string | null
  onSelect: (userId: string | null) => void
  placeholder?: string
  role?: string
  allowUnassigned?: boolean
  disabled?: boolean
  className?: string
  // Optional: pass existing user info to display correctly (matches UserReadBasic)
  selectedUserInfo?: {
    id: string
    email: string
  } | null
}

export function UserAutocomplete({
  value,
  onSelect,
  placeholder = 'Search users...',
  role,
  allowUnassigned = true,
  disabled = false,
  className,
  selectedUserInfo,
}: UserAutocompleteProps) {
  const [open, setOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [users, setUsers] = useState<UserSummary[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [hasSearched, setHasSearched] = useState(false)
  const [selectedUserDetails, setSelectedUserDetails] = useState<UserSummary | null>(null)

  // Debounce search input to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300)

  // Get display name for selected user - try multiple sources
  const selectedUser = useMemo(() => {
    if (!value || value === UNASSIGNED_VALUE) return null

    // First try selectedUserDetails (fetched full details)
    if (selectedUserDetails && selectedUserDetails.id === value) {
      return selectedUserDetails
    }

    // Then try finding in users array (from search/dropdown)
    const userFromList = users.find(user => user.id === value)
    if (userFromList) {
      return userFromList
    }

    // Finally, create minimal display from selectedUserInfo
    if (selectedUserInfo && selectedUserInfo.id === value) {
      return {
        id: selectedUserInfo.id,
        firstName: '',
        lastName: '',
        email: selectedUserInfo.email,
        fullName: selectedUserInfo.email.split('@')[0], // Use email prefix as fallback name
        role: undefined,
        department: undefined,
      }
    }

    return null
  }, [value, users, selectedUserDetails, selectedUserInfo])

  // Fetch user details when we have a value but no complete user info
  useEffect(() => {
    if (value && value !== UNASSIGNED_VALUE && !selectedUser && selectedUserInfo) {
      fetchUserDetails(value)
    }
  }, [value, selectedUser, selectedUserInfo])

  // Load initial users when component opens OR when it has a value but no user info
  useEffect(() => {
    if (
      (open && !hasSearched && !debouncedSearch) ||
      (value && !selectedUser && !hasSearched && !selectedUserInfo)
    ) {
      loadInitialUsers()
    }
  }, [open, hasSearched, debouncedSearch, value, selectedUser, selectedUserInfo])

  // Search users when debounced search changes
  useEffect(() => {
    if (debouncedSearch.trim()) {
      searchUsers(debouncedSearch)
    } else if (hasSearched && open) {
      // When clearing search in an open dropdown, reload initial users
      loadInitialUsers()
    }
  }, [debouncedSearch, open])

  const fetchUserDetails = async (userId: string) => {
    try {
      // Note: This would require implementing getUserById in the API client
      // For now, we'll search for the user by email if available
      if (selectedUserInfo?.email) {
        const searchResults = await api.users.searchUsers(selectedUserInfo.email, {
          role,
          status: 'ACTIVE',
          limit: 5,
        })
        const userDetails = searchResults.find(u => u.id === userId)
        if (userDetails) {
          setSelectedUserDetails(userDetails)
        }
      }
    } catch (error) {
      console.error('Error fetching user details:', error)
    }
  }

  const loadInitialUsers = async () => {
    setIsLoading(true)
    try {
      console.log('🔍 UserAutocomplete: Loading initial users with role:', role)
      const fetchedUsers = await api.users.getUsersForDropdown({
        role,
        status: 'ACTIVE',
        limit: 20,
      })
      console.log('✅ UserAutocomplete: Loaded users:', fetchedUsers.length)
      setUsers(fetchedUsers)
      setHasSearched(true)
    } catch (error) {
      console.error('❌ UserAutocomplete: Error loading initial users:', error)
      setUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  const searchUsers = async (query: string) => {
    setIsLoading(true)
    try {
      console.log('🔍 UserAutocomplete: Searching users with query:', query, 'role:', role)
      const fetchedUsers = await api.users.searchUsers(query, {
        role,
        status: 'ACTIVE',
        limit: 20,
      })
      console.log('✅ UserAutocomplete: Found users:', fetchedUsers.length)
      setUsers(fetchedUsers)
      setHasSearched(true)
    } catch (error) {
      console.error('❌ UserAutocomplete: Error searching users:', error)
      setUsers([])
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelect = (selectedValue: string) => {
    console.log('🎯 UserAutocomplete: handleSelect called with:', selectedValue)
    const userId = selectedValue === UNASSIGNED_VALUE ? null : selectedValue
    console.log('🎯 UserAutocomplete: Calling onSelect with userId:', userId)
    onSelect(userId)
    setOpen(false)
    setSearch('')
  }

  const getDisplayText = () => {
    if (value === null || value === UNASSIGNED_VALUE) {
      return allowUnassigned ? 'Unassigned' : placeholder
    }
    if (selectedUser) {
      const displayName =
        selectedUser.fullName || `${selectedUser.firstName} ${selectedUser.lastName}`.trim()
      const email = selectedUser.email
      if (displayName && displayName !== email) {
        return email ? `${displayName} (${email})` : displayName
      } else {
        return email || displayName || 'Unknown User'
      }
    }
    // If we have a value but no user info yet, show loading
    return 'Loading user...'
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn('justify-between', className)}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 truncate">
            <User className="h-4 w-4 shrink-0" />
            <span className="truncate">{getDisplayText()}</span>
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0" align="start">
        <Command shouldFilter={false}>
          <CommandInput placeholder={placeholder} value={search} onValueChange={setSearch} />
          <CommandList>
            {isLoading && (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span className="text-sm text-muted-foreground">Searching...</span>
              </div>
            )}

            {!isLoading && hasSearched && users.length === 0 && (
              <CommandEmpty>
                {search.trim() ? 'No users found.' : 'No users available.'}
              </CommandEmpty>
            )}

            {!isLoading && (hasSearched || users.length > 0) && (
              <CommandGroup>
                {/* Show "Unassigned" only when no search query OR when search has no results */}
                {allowUnassigned && (!search.trim() || users.length === 0) && (
                  <CommandItem
                    value={UNASSIGNED_VALUE}
                    onSelect={handleSelect}
                    onClick={() => handleSelect(UNASSIGNED_VALUE)}
                    className="flex items-center gap-2 cursor-pointer hover:bg-accent hover:text-accent-foreground"
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === null || value === UNASSIGNED_VALUE ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <span className="text-muted-foreground">Unassigned</span>
                    </div>
                  </CommandItem>
                )}

                {users.map(user => (
                  <CommandItem
                    key={user.id}
                    value={user.id}
                    onSelect={handleSelect}
                    onClick={() => handleSelect(user.id)}
                    className="flex items-center gap-2 cursor-pointer hover:bg-accent hover:text-accent-foreground"
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === user.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex flex-col">
                      <span className="font-medium text-foreground">{user.fullName}</span>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>{user.email}</span>
                        {user.role && (
                          <>
                            <span>•</span>
                            <span>{user.role}</span>
                          </>
                        )}
                        {user.department && (
                          <>
                            <span>•</span>
                            <span>{user.department}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}
