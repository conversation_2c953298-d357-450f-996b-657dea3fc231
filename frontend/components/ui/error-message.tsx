import React from 'react'
import { AlertCircle, AlertTriangle, XCircle, Ban } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from './button'
import { Alert, AlertDescription, AlertTitle } from './alert'

export type ErrorSeverity = 'info' | 'warning' | 'error' | 'fatal'

interface ErrorMessageProps {
  /**
   * Title of the error
   */
  title?: string

  /**
   * Error message to display
   */
  message: string

  /**
   * Severity level of the error
   * @default "error"
   */
  severity?: ErrorSeverity

  /**
   * Action button label
   */
  actionLabel?: string

  /**
   * Action button click handler
   */
  onAction?: () => void

  /**
   * Whether to show a retry button
   * @default false
   */
  showRetry?: boolean

  /**
   * Retry button click handler
   */
  onRetry?: () => void

  /**
   * Additional CSS classes to apply
   */
  className?: string
}

export function ErrorMessage({
  title,
  message,
  severity = 'error',
  actionLabel,
  onAction,
  showRetry = false,
  onRetry,
  className,
}: ErrorMessageProps) {
  // Determine alert variant based on severity
  const getVariant = () => {
    switch (severity) {
      case 'info':
        return 'default'
      case 'warning':
        return 'warning'
      case 'fatal':
      case 'error':
      default:
        return 'destructive'
    }
  }

  // Determine icon based on severity
  const getIcon = () => {
    switch (severity) {
      case 'info':
        return AlertCircle
      case 'warning':
        return AlertTriangle
      case 'fatal':
        return Ban
      case 'error':
      default:
        return XCircle
    }
  }

  const Icon = getIcon()
  const variant = getVariant()

  // Generate a default title if none provided
  const defaultTitle = () => {
    switch (severity) {
      case 'info':
        return 'Information'
      case 'warning':
        return 'Warning'
      case 'fatal':
        return 'Critical Error'
      case 'error':
      default:
        return 'Error'
    }
  }

  const errorTitle = title || defaultTitle()

  return (
    <Alert variant={variant} className={cn(className)}>
      <Icon className="h-4 w-4" />
      <AlertTitle>{errorTitle}</AlertTitle>
      <AlertDescription>
        <div className="flex flex-col gap-3">
          <p>{message}</p>

          {(actionLabel || showRetry) && (
            <div className="flex gap-2 mt-2">
              {actionLabel && onAction && (
                <Button variant="outline" size="sm" onClick={onAction}>
                  {actionLabel}
                </Button>
              )}

              {showRetry && onRetry && (
                <Button variant="outline" size="sm" onClick={onRetry}>
                  Try again
                </Button>
              )}
            </div>
          )}
        </div>
      </AlertDescription>
    </Alert>
  )
}
