import React from 'react'
import { Loader2 } from 'lucide-react'
import { cn } from '@/lib/utils'

export type LoadingVariant = 'spinner' | 'skeleton' | 'text'
export type LoadingSize = 'sm' | 'md' | 'lg'

interface LoadingProps {
  /**
   * Loading component variant
   * @default "spinner"
   */
  variant?: LoadingVariant

  /**
   * Size of the loading indicator
   * @default "md"
   */
  size?: LoadingSize

  /**
   * Custom text to display alongside or instead of the loading indicator
   * @default "Loading..."
   */
  text?: string

  /**
   * Center the loading indicator in its container
   * @default true
   */
  centered?: boolean

  /**
   * Whether to show text next to spinner
   * @default true
   */
  showText?: boolean

  /**
   * Additional CSS classes to apply
   */
  className?: string

  /**
   * Number of skeleton items to show when variant is "skeleton"
   * @default 5
   */
  skeletonCount?: number

  /**
   * Height of each skeleton item in pixels
   * @default 24
   */
  skeletonHeight?: number
}

export function Loading({
  variant = 'spinner',
  size = 'md',
  text = 'Loading...',
  centered = true,
  showText = true,
  className,
  skeletonCount = 5,
  skeletonHeight = 24,
}: LoadingProps) {
  // Size mappings
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  }

  // Text size mappings
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
  }

  // Container classes
  const containerClasses = cn(
    'flex items-center',
    centered && 'justify-center',
    variant === 'skeleton' ? 'flex-col w-full' : 'gap-2',
    className
  )

  // Render loading component based on variant
  switch (variant) {
    case 'spinner':
      return (
        <div className={containerClasses}>
          <Loader2 className={cn(sizeClasses[size], 'animate-spin text-muted-foreground')} />
          {showText && (
            <span className={cn(textSizeClasses[size], 'text-muted-foreground')}>{text}</span>
          )}
        </div>
      )

    case 'skeleton':
      return (
        <div className={containerClasses}>
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <div
              key={index}
              className="w-full animate-pulse rounded bg-muted mb-2"
              style={{ height: `${skeletonHeight}px` }}
            />
          ))}
        </div>
      )

    case 'text':
    default:
      return (
        <div className={containerClasses}>
          <span className={cn(textSizeClasses[size], 'text-muted-foreground')}>{text}</span>
        </div>
      )
  }
}
