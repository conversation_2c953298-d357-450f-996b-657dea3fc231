import React from 'react'
import { LucideIcon, FileQuestion, Search, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from './button'

export type EmptyStateVariant = 'default' | 'search' | 'error' | 'filtered'

interface EmptyStateProps {
  /**
   * Title to display in the empty state
   */
  title: string

  /**
   * Optional description text
   */
  description?: string

  /**
   * Empty state variant
   * @default "default"
   */
  variant?: EmptyStateVariant

  /**
   * Optional custom icon to show
   */
  icon?: LucideIcon

  /**
   * Optional action button text
   */
  actionLabel?: string

  /**
   * Optional action button click handler
   */
  onAction?: () => void

  /**
   * Additional CSS classes to apply
   */
  className?: string
}

export function EmptyState({
  title,
  description,
  variant = 'default',
  icon: Icon,
  actionLabel,
  onAction,
  className,
}: EmptyStateProps) {
  // Get the appropriate icon based on variant
  const getIcon = () => {
    if (Icon) return Icon

    switch (variant) {
      case 'search':
        return Search
      case 'error':
        return AlertCircle
      case 'filtered':
      case 'default':
      default:
        return FileQuestion
    }
  }

  const IconComponent = getIcon()

  // Get appropriate text color based on variant
  const getTextColorClass = () => {
    switch (variant) {
      case 'error':
        return 'text-destructive'
      default:
        return 'text-muted-foreground'
    }
  }

  // Get appropriate button variant based on the empty state variant
  const getButtonVariant = () => {
    switch (variant) {
      case 'error':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  return (
    <div
      className={cn('flex flex-col items-center justify-center py-8 px-4 text-center', className)}
    >
      <div className="rounded-full bg-muted p-3 mb-4">
        <IconComponent className="h-6 w-6 text-muted-foreground" />
      </div>

      <h3 className="text-lg font-medium">{title}</h3>

      {description && (
        <p className={cn('mt-2 max-w-md text-sm', getTextColorClass())}>{description}</p>
      )}

      {actionLabel && onAction && (
        <Button variant={getButtonVariant()} onClick={onAction} className="mt-4">
          {actionLabel}
        </Button>
      )}
    </div>
  )
}
