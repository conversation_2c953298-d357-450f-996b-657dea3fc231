'use client'

import type React from 'react'

import { ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react'
import { TableHead } from '@/components/ui/table'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

export type SortDirection = 'asc' | 'desc' | null

interface SortableHeaderProps extends React.HTMLAttributes<HTMLTableCellElement> {
  column: string
  label: React.ReactNode
  sortColumn: string | null
  sortDirection: SortDirection
  onSort: (column: string) => void
  className?: string
}

export function SortableHeader({
  column,
  label,
  sortColumn,
  sortDirection,
  onSort,
  className,
  ...props
}: SortableHeaderProps) {
  const isSorted = sortColumn === column

  return (
    <TableHead className={cn('cursor-pointer select-none', className)} {...props}>
      <Button
        variant="ghost"
        onClick={() => onSort(column)}
        className="h-auto p-0 font-medium hover:bg-transparent hover:text-primary"
      >
        <span className="flex items-center gap-1">
          {label}
          {isSorted ? (
            sortDirection === 'asc' ? (
              <ArrowUp className="h-4 w-4 text-primary" />
            ) : (
              <ArrowDown className="h-4 w-4 text-primary" />
            )
          ) : (
            <ArrowUpDown className="h-4 w-4 opacity-50" />
          )}
        </span>
      </Button>
    </TableHead>
  )
}
