'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Plus, AlertCircle, Loader2 } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { formatDistanceToNow } from 'date-fns'
import { useState, useEffect } from 'react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { useAuditEntries, useAuditSummary } from '@/hooks/useAudit'
import {
  AuditTrailResponse,
  ChangeType,
  EntityType,
  PaginatedAuditTrailResponse,
} from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'
import { Calendar, User, Clock } from 'lucide-react'

interface ClaimAuditProps {
  claimId: string
}

export function ClaimAudit({ claimId }: ClaimAuditProps) {
  const [claimUuid, setClaimUuid] = useState<string | null>(null)
  const { formatDateTime, formatRelativeDate } = useDateFormatter()

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // First, fetch the claim to get its UUID if we were passed a claim number
  const {
    data: claim,
    isLoading: isLoadingClaim,
    error: claimError,
  } = useApi(() => api.claims.getClaimByNumber(claimId), [claimId])

  useEffect(() => {
    if (claim && claim.id) {
      setClaimUuid(claim.id)
    }
  }, [claim])

  // Fetch audit entries when we have the claim UUID
  const {
    data: auditData,
    isLoading: isLoadingAudit,
    error: auditError,
  } = useAuditEntries(claimUuid || claimId)

  // Also fetch the audit summary
  const {
    data: auditSummary,
    isLoading: isLoadingSummary,
    error: summaryError,
  } = useAuditSummary(claimUuid || claimId)

  // Extract the audit entries from the paginated response
  const auditEntries = auditData?.items || []

  // Collect and resolve user IDs from audit entries
  useEffect(() => {
    if (auditEntries && auditEntries.length > 0) {
      const userIds = auditEntries
        .map((entry: AuditTrailResponse) => entry.changed_by_id)
        .filter(Boolean) as string[]

      const uniqueUserIds = [...new Set(userIds)]
      if (uniqueUserIds.length > 0) {
        resolveUsers(uniqueUserIds)
      }
    }
  }, [auditEntries, resolveUsers])

  // Helper to format user display from changed_by_id
  const formatUserDisplay = (userId?: string | null) => {
    if (!userId) return { name: 'System', initials: 'SYS' }

    const userName = getUserDisplayName(userId)

    // Simple initials - just use first 2 chars or default
    const initials = userName === 'System' ? 'SYS' : userName.substring(0, 2).toUpperCase() || 'US'

    return {
      name: userName, // This shows the real name instead of UUID
      initials, // Simple fallback for Avatar component
    }
  }

  const getActionColor = (entityType: EntityType, changeType: ChangeType) => {
    // Colors based on entity type
    const entityColors: Record<string, string> = {
      CLAIM: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      DOCUMENT: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      RESERVE: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      PAYMENT: 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300',
      TASK: 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300',
      WITNESS: 'bg-cyan-100 text-cyan-800 dark:bg-cyan-900 dark:text-cyan-300',
      ATTORNEY: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300',
      FINANCIAL: 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300',
    }

    // Default color
    return (
      entityColors[entityType] ||
      'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    )
  }

  // Format action display
  const formatActionDisplay = (entry: AuditTrailResponse) => {
    return `${entry.entity_type}_${entry.change_type}`
  }

  // Loading state
  if (isLoadingClaim || isLoadingAudit) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Audit Trail</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Loading audit data...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (claimError || auditError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Audit Trail</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-sm text-destructive">
              {claimError || auditError || 'Failed to load audit data'}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Empty state
  if (!auditEntries || auditEntries.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Audit Trail</CardTitle>
          <CardDescription>No audit records found for this claim</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <p className="text-sm text-muted-foreground">No activity has been recorded yet</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Data state
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Audit Trail</CardTitle>
          {auditSummary && (
            <CardDescription>{auditSummary.total_entries} total entries</CardDescription>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Action</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Timestamp</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {auditEntries.map((entry: AuditTrailResponse) => {
              const userInfo = formatUserDisplay(entry.changed_by_id)
              const timestamp = new Date(entry.changed_at)
              return (
                <TableRow key={entry.id}>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={getActionColor(entry.entity_type, entry.change_type)}
                    >
                      {formatActionDisplay(entry).replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {entry.description || `${entry.field_name || 'Field'} was changed`}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={'/placeholder.svg'} alt={userInfo.name} />
                        <AvatarFallback>{userInfo.initials}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{userInfo.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <span>{formatDateTime(timestamp.toISOString())}</span>
                      <span>{formatRelativeDate(timestamp.toISOString())}</span>
                    </div>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
