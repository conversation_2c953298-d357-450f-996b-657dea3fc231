'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card'
import { Calendar, Hourglass, AlertCircle, Loader2 } from 'lucide-react'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { Claim } from '@/lib/api/types'

interface ClaimDateInfoProps {
  claimNumber: string
  claim: Claim | null
  isLoading: boolean
  error: string | null
}

export function ClaimDateInfo({ claimNumber, claim, isLoading, error }: ClaimDateInfoProps) {
  const { formatDate } = useDateFormatter()

  if (isLoading) {
    return (
      <div className="bg-muted rounded-lg p-4 flex items-center justify-center">
        <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
      </div>
    )
  }

  if (error || !claim) {
    return (
      <div className="bg-muted rounded-lg p-4 flex items-center justify-center">
        <div className="flex flex-col items-center gap-2">
          <AlertCircle className="h-5 w-5 text-destructive" />
          <p className="text-xs text-destructive">Failed to load date information</p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
      <div className="font-medium flex items-center gap-2">
        <Calendar className="h-4 w-4 text-muted-foreground" />
        Key Dates
      </div>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div className="text-muted-foreground">Created</div>
        <div>{formatDate(claim.created_at)}</div>
        <div className="text-muted-foreground">Last Updated</div>
        <div>{formatDate(claim.updated_at)}</div>
        <div className="text-muted-foreground">Incident Date</div>
        <div>{formatDate(claim.incident_date || claim.date_of_loss || '')}</div>
        {claim.closed_at && (
          <>
            <div className="text-muted-foreground">Closed</div>
            <div>{formatDate(claim.closed_at)}</div>
          </>
        )}
      </div>
    </div>
  )
}
