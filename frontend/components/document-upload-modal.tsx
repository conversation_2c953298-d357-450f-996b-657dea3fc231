'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { DocumentType } from '@/lib/api/types'
import { X, FileText, Upload } from 'lucide-react'
import { Card } from '@/components/ui/card'
import { api } from '@/lib/api'

interface DocumentUploadModalProps {
  isOpen: boolean
  onClose: () => void
  claimId: string
  onSuccess?: () => void
}

export function DocumentUploadModal({
  isOpen,
  onClose,
  claimId,
  onSuccess,
}: DocumentUploadModalProps) {
  const [files, setFiles] = useState<File[]>([])
  const [documentType, setDocumentType] = useState<DocumentType | ''>('')
  const [description, setDescription] = useState<string>('')
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)

  // Max file size in bytes (100MB)
  const MAX_FILE_SIZE = 100 * 1024 * 1024

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      console.log(
        `🔍 [DocumentUploadModal] File input change, files selected: ${e.target.files.length}`
      )

      // Filter files by size and valid types
      const validFiles: File[] = []
      const invalidFiles: { name: string; reason: string }[] = []

      Array.from(e.target.files).forEach(file => {
        // Check file size
        if (file.size > MAX_FILE_SIZE) {
          invalidFiles.push({
            name: file.name,
            reason: `File exceeds maximum size of ${MAX_FILE_SIZE / (1024 * 1024)}MB`,
          })
          return
        }

        // Check file type (optional validation)
        const validTypes = [
          'image/jpeg',
          'image/png',
          'image/gif',
          'application/pdf',
          'application/zip',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ]

        if (!validTypes.includes(file.type)) {
          invalidFiles.push({
            name: file.name,
            reason: `File type ${file.type || 'unknown'} is not supported`,
          })
          return
        }

        validFiles.push(file)
      })

      if (invalidFiles.length > 0) {
        const errorMessages = invalidFiles.map(f => `${f.name}: ${f.reason}`).join('\n')
        setUploadError(`Some files couldn't be added:\n${errorMessages}`)
      }

      if (validFiles.length > 0) {
        setFiles(prev => [...prev, ...validFiles])
        console.log(`🔍 [DocumentUploadModal] Added ${validFiles.length} valid files`)
      }
    }
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const resetForm = () => {
    setFiles([])
    setDocumentType('')
    setDescription('')
    setUploadError(null)
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('🔍 [DocumentUploadModal] Submit started')

    if (!documentType || files.length === 0) {
      setUploadError('Please select a document type and at least one file')
      console.log('🔍 [DocumentUploadModal] Validation failed')
      return
    }

    setUploading(true)
    setUploadError(null)
    console.log(`🔍 [DocumentUploadModal] Starting upload of ${files.length} files`)

    try {
      // Upload files one at a time instead of in parallel
      console.log('🔍 [DocumentUploadModal] Starting sequential upload of files')
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        console.log(
          `🔍 [DocumentUploadModal] Uploading file ${i + 1}/${files.length}: ${file.name}`
        )

        await api.documents.uploadDocumentDirect(
          claimId,
          file,
          documentType as DocumentType,
          file.name,
          description || undefined
        )

        console.log(
          `✅ [DocumentUploadModal] Completed upload of file ${i + 1}/${files.length}: ${file.name}`
        )
      }

      console.log('✅ [DocumentUploadModal] All uploads completed successfully')

      // Reset form on success
      console.log('🔍 [DocumentUploadModal] Resetting form')
      resetForm()

      // Close the modal first
      console.log('🔍 [DocumentUploadModal] Closing modal')
      onClose()

      // Notify parent about successful upload - no delay needed
      if (onSuccess) {
        console.log('🔍 [DocumentUploadModal] Calling success callback')
        onSuccess()
      }
      console.log('🔍 [DocumentUploadModal] Upload process completed')
    } catch (error: any) {
      console.error('❌ [DocumentUploadModal] Error uploading documents:', error)
      setUploadError(error.message || 'Failed to upload documents. Please try again.')
    } finally {
      console.log('🔍 [DocumentUploadModal] Setting uploading state to false')
      setUploading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Upload Document</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="document-type">Document Type</Label>
              <Select
                value={documentType}
                onValueChange={value => setDocumentType(value as DocumentType)}
              >
                <SelectTrigger id="document-type">
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={DocumentType.PHOTO}>Photo</SelectItem>
                  <SelectItem value={DocumentType.REPORT}>Report</SelectItem>
                  <SelectItem value={DocumentType.INVOICE}>Invoice</SelectItem>
                  <SelectItem value={DocumentType.STATEMENT}>Statement</SelectItem>
                  <SelectItem value={DocumentType.CONTRACT}>Contract</SelectItem>
                  <SelectItem value={DocumentType.POLICY}>Policy</SelectItem>
                  <SelectItem value={DocumentType.CORRESPONDENCE}>Correspondence</SelectItem>
                  <SelectItem value={DocumentType.OTHER}>Other</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter document description"
                value={description}
                onChange={e => setDescription(e.target.value)}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="file-upload">Upload Files</Label>
              <div className="flex items-center justify-center w-full">
                <label
                  htmlFor="file-upload"
                  className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/20 hover:bg-muted/30"
                >
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                    <p className="mb-2 text-sm text-muted-foreground">
                      <span className="font-semibold">Click to upload</span> or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground">
                      PDF, JPG, PNG, or ZIP (MAX. 100MB)
                    </p>
                  </div>
                  <Input
                    id="file-upload"
                    type="file"
                    multiple
                    className="hidden"
                    onChange={handleFileChange}
                  />
                </label>
              </div>
            </div>

            {files.length > 0 && (
              <div className="grid gap-2">
                <Label>Selected Files</Label>
                <div className="space-y-2 max-h-[150px] overflow-y-auto">
                  {files.map((file, index) => (
                    <Card key={index} className="p-2 flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="text-sm truncate max-w-[300px]">{file.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7"
                        onClick={() => removeFile(index)}
                        type="button"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {uploadError && <div className="text-sm text-red-500 mt-2">{uploadError}</div>}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={handleClose} type="button">
              Cancel
            </Button>
            <Button type="submit" disabled={uploading}>
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
