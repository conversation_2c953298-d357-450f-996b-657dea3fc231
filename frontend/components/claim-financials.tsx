'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import Link from 'next/link'
import { useState, useEffect, useMemo } from 'react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import {
  ClaimFinancialsInDB,
  PaymentList,
  ReserveResponse,
  ReserveType,
  PaymentType,
} from '@/lib/api/types'
import { format } from 'date-fns'
import { Loader2, AlertCircle } from 'lucide-react'
import { FinancialsApi } from '@/lib/api/financials.api'
import NewPaymentModal from '@/components/new-payment-modal'
import ReserveFormModal from '@/components/reserve-form-modal'
import { useDateFormatter } from '@/hooks/useDateFormatter'

interface ClaimFinancialsProps {
  claimId: string
}

export function ClaimFinancials({ claimId }: ClaimFinancialsProps) {
  const { formatDateShort } = useDateFormatter()
  const [claimUuid, setClaimUuid] = useState<string | null>(null)
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [isAddReserveModalOpen, setIsAddReserveModalOpen] = useState(false)
  const [isEditReserveModalOpen, setIsEditReserveModalOpen] = useState(false)
  const [isUpdateReserveModalOpen, setIsUpdateReserveModalOpen] = useState(false)
  const [selectedReserve, setSelectedReserve] = useState<ReserveResponse | null>(null)
  const [activeTab, setActiveTab] = useState<string>('summary')

  // Robust currency formatting function
  const formatDisplayCurrency = (amount?: string | null | number): string => {
    if (
      amount === null ||
      amount === undefined ||
      (typeof amount === 'string' && amount.trim() === '')
    ) {
      return '$0.00'
    }
    let numAmount: number
    if (typeof amount === 'string') {
      const cleanedAmount = amount.replace(/[$,]/g, '') // Remove $ and commas
      numAmount = parseFloat(cleanedAmount)
    } else {
      // it's a number
      numAmount = amount
    }
    if (isNaN(numAmount)) {
      console.error(`Invalid amount for currency formatting: Value was '${amount}'`)
      return '$0.00' // Or perhaps a more indicative error string like '$---.--'
    }
    return numAmount.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    })
  }

  // Use useMemo to create a stable instance of FinancialsApi
  const financialsApi = useMemo(() => new FinancialsApi(api.client), [])

  // First, fetch the claim to get its UUID if we were passed a claim number
  const {
    data: claim,
    isLoading: isLoadingClaim,
    error: claimError,
  } = useApi(() => api.claims.getClaimByNumber(claimId), [claimId])

  useEffect(() => {
    if (claim && claim.id) {
      setClaimUuid(claim.id)
    }
  }, [claim])

  // Fetch financial details
  const {
    data: financials,
    isLoading: isLoadingFinancials,
    error: financialsError,
    refetch: refetchFinancials,
  } = useApi(() => {
    if (claimId) {
      console.log(`Fetching financials for claim: ${claimId}`)
      return financialsApi.getClaimFinancials(claimId)
    }
    return Promise.resolve(null)
  }, [claimId, financialsApi])

  // Log for debugging
  useEffect(() => {
    if (financials === null && !isLoadingFinancials) {
      console.log(`No financials found for claim ${claimId} - this is normal for new claims`)
    }
  }, [financials, isLoadingFinancials, claimId])

  // Fetch payments
  const {
    data: paymentsData,
    isLoading: isLoadingPayments,
    error: paymentsError,
    refetch: refetchPayments,
  } = useApi<PaymentList>(() => {
    if (claimId) {
      return financialsApi.listClaimPayments(claimId)
    }
    return Promise.resolve({ items: [], total: 0 })
  }, [claimId, financialsApi])

  // Get payments array from API data
  const payments = paymentsData?.items || []

  // Add useEffect to fetch payments when tab changes to 'payments'
  useEffect(() => {
    if (activeTab === 'payments' && claimId) {
      console.log('Payments tab selected, fetching payment data')
      refetchPayments()
    }
  }, [activeTab, claimId, refetchPayments])

  const handleNewPaymentClick = () => {
    setIsPaymentModalOpen(true)
  }

  const handleClosePaymentModal = () => {
    setIsPaymentModalOpen(false)
    // Refetch payments data after modal closes to update the list
    refetchPayments()
    // Also refetch financials to update summary totals
    refetchFinancials()
  }

  const handleAddReserveClick = () => {
    setIsAddReserveModalOpen(true)
  }

  const handleUpdateReserveClick = () => {
    setIsUpdateReserveModalOpen(true)
  }

  const handleEditReserveClick = (reserve: ReserveResponse) => {
    setSelectedReserve(reserve)
    setIsEditReserveModalOpen(true)
  }

  const handleCloseReserveModal = () => {
    setIsAddReserveModalOpen(false)
    setIsEditReserveModalOpen(false)
    setIsUpdateReserveModalOpen(false)
    setSelectedReserve(null)
    // Refetch financial data after modal closes to update reserves
    refetchFinancials()
  }

  const getReserveTypeColor = (type: string) => {
    switch (type) {
      case 'PROPERTY_DAMAGE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'BODILY_INJURY':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'MEDICAL_PAYMENTS':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'DEFENSE_COST':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'LOSS_OF_USE':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getPaymentTypeColor = (type: string) => {
    switch (type) {
      case 'INDEMNITY':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'EXPENSE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'DEFENSE':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Loading state
  const isLoading = isLoadingClaim || isLoadingFinancials || isLoadingPayments
  const error = claimError || financialsError || paymentsError

  // Helper functions for payment button state
  const hasReserves = financials && financials.reserves && financials.reserves.length > 0
  const canAddPayments = hasReserves
  const paymentDisabledReason = !hasReserves
    ? 'You must set up reserves before adding payments'
    : null

  // Component for Add Payment button with conditional tooltip
  const AddPaymentButton = ({
    variant = 'default' as const,
    size = 'default' as const,
    className = '',
  }: {
    variant?:
      | 'default'
      | 'destructive'
      | 'outline'
      | 'secondary'
      | 'ghost'
      | 'link'
      | null
      | undefined
    size?: 'default' | 'sm' | 'lg' | 'icon' | null | undefined
    className?: string
  }) => {
    const buttonClassName = !canAddPayments
      ? `${className} opacity-50 cursor-not-allowed`
      : className

    const button = (
      <Button
        variant={variant}
        size={size}
        className={buttonClassName}
        onClick={handleNewPaymentClick}
        disabled={!canAddPayments}
      >
        Add Payment
      </Button>
    )

    if (!canAddPayments && paymentDisabledReason) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>{button}</TooltipTrigger>
            <TooltipContent>
              <p>{paymentDisabledReason}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )
    }

    return button
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Financial Details</CardTitle>
          <CardDescription>Loading...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Loading financial details...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Financial Details</CardTitle>
          <CardDescription>Error</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2">
            <AlertCircle className="h-8 w-8 text-destructive" />
            <p className="text-sm text-destructive">
              {error ? String(error) : 'Failed to load financial details'}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If no financials data and we're not loading, show empty state
  if (financials === null && !isLoadingFinancials) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Financial Details</CardTitle>
          <CardDescription>No financials found</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex flex-col items-center gap-2 text-center">
            <p className="text-muted-foreground">No financial records exist for this claim.</p>
            <div className="flex gap-2 mt-4">
              <Button variant="outline" onClick={handleAddReserveClick}>
                Add Reserve
              </Button>
              <AddPaymentButton variant="outline" />
            </div>
          </div>
        </CardContent>

        {/* Render the modals even when no financials exist */}
        {isAddReserveModalOpen && (
          <ReserveFormModal
            isOpen={isAddReserveModalOpen}
            onClose={() => setIsAddReserveModalOpen(false)}
            claimId={claimId}
            mode="add"
            onSuccess={() => {
              refetchFinancials()
            }}
          />
        )}

        {isPaymentModalOpen && (
          <NewPaymentModal
            isOpen={isPaymentModalOpen}
            onClose={handleClosePaymentModal}
            claims={claim ? [claim] : []}
          />
        )}
      </Card>
    )
  }

  // If financials data loaded successfully
  if (financials) {
    const reserves = financials.reserves || []
    const totalAmount = reserves.reduce((total, reserve) => {
      try {
        // Handle currency strings or numbers
        const cleanedAmount = String(reserve.amount).replace(/[$,]/g, '')
        return parseFloat(cleanedAmount) + total
      } catch (error) {
        console.error(`Invalid amount format: ${reserve.amount}`, error)
        return total
      }
    }, 0)

    console.log('Financials loaded successfully:', financials)

    return (
      <Card>
        <CardHeader>
          <CardTitle>Financial Details</CardTitle>
          <CardDescription>Reserves, payments, and financial summary</CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full md:w-auto grid-cols-3 md:flex">
              <TabsTrigger value="summary">Summary</TabsTrigger>
              <TabsTrigger value="reserves">Reserves</TabsTrigger>
              <TabsTrigger value="payments">Payments</TabsTrigger>
            </TabsList>

            <TabsContent value="summary" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium mb-3">Claim Value</h4>
                  <dl className="grid grid-cols-2 gap-2 text-sm">
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">Estimated Value</dt>
                      <dd className="font-medium">
                        {formatDisplayCurrency(financials.estimated_value)}
                      </dd>
                    </div>
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">Deductible</dt>
                      <dd className="font-medium">
                        {formatDisplayCurrency(financials.deductible_amount)}
                      </dd>
                    </div>
                    <div className="grid grid-cols-2">
                      <dt className="text-muted-foreground">Payments Made</dt>
                      <dd className="font-medium">
                        {formatDisplayCurrency(
                          Number(financials.indemnity_paid || 0) +
                            Number(financials.expense_paid || 0) +
                            Number(financials.defense_paid || 0)
                        )}
                      </dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-3">Reserves</h4>
                  <div className="space-y-2">
                    {financials.reserves.map(reserve => (
                      <div
                        key={reserve.id}
                        className="flex justify-between items-center py-1 border-b"
                      >
                        <span className="text-sm">{reserve.reserve_type.replace('_', ' ')}</span>
                        <span className="text-sm font-medium">
                          {formatDisplayCurrency(reserve.amount)}
                        </span>
                      </div>
                    ))}
                    <div className="flex justify-between items-center pt-2">
                      <span className="text-sm font-medium">Total Reserves</span>
                      <span className="text-sm font-medium">
                        {formatDisplayCurrency(financials.estimated_value)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-end">
                <Button onClick={handleUpdateReserveClick}>Update Reserves</Button>
              </div>
            </TabsContent>

            <TabsContent value="reserves" className="mt-6">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Reserve Type</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead className="w-[100px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {financials.reserves.map(reserve => (
                      <TableRow key={reserve.id}>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getReserveTypeColor(reserve.reserve_type)}
                          >
                            {reserve.reserve_type.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell className="font-medium">
                          {formatDisplayCurrency(reserve.amount)}
                        </TableCell>
                        <TableCell>{formatDateShort(reserve.created_at)}</TableCell>
                        <TableCell>
                          <Button size="sm" onClick={() => handleEditReserveClick(reserve)}>
                            Edit
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
              <div className="mt-4 flex justify-end">
                <Button onClick={handleAddReserveClick}>Add Reserve</Button>
              </div>
            </TabsContent>

            <TabsContent value="payments" className="mt-6">
              {payments.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8 text-center">
                  <p className="text-muted-foreground mb-4">
                    No payments have been made for this claim yet.
                  </p>
                  <AddPaymentButton />
                </div>
              ) : (
                <>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Payment Type</TableHead>
                          <TableHead>Amount</TableHead>
                          <TableHead>Payee</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Notes</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {payments.map(payment => (
                          <TableRow key={payment.id}>
                            <TableCell>
                              <Badge
                                variant="outline"
                                className={getPaymentTypeColor(payment.payment_type)}
                              >
                                {payment.payment_type}
                              </Badge>
                            </TableCell>
                            <TableCell className="font-medium">
                              {formatDisplayCurrency(payment.amount)}
                            </TableCell>
                            <TableCell>{payment.payee}</TableCell>
                            <TableCell>{formatDateShort(payment.payment_date)}</TableCell>
                            <TableCell
                              className="max-w-[200px] truncate"
                              title={payment.notes || ''}
                            >
                              {payment.notes || '-'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <AddPaymentButton />
                  </div>
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>

        {/* Render Payment Modal */}
        <NewPaymentModal
          isOpen={isPaymentModalOpen}
          onClose={handleClosePaymentModal}
          claims={claim ? [claim] : []}
        />

        {/* Render Add Reserve Modal */}
        <ReserveFormModal
          isOpen={isAddReserveModalOpen}
          onClose={handleCloseReserveModal}
          claimId={claimId}
          mode="add"
          onSuccess={() => {
            refetchFinancials()
          }}
        />

        {/* Render Edit Reserve Modal */}
        <ReserveFormModal
          isOpen={isEditReserveModalOpen}
          onClose={handleCloseReserveModal}
          claimId={claimId}
          initialData={selectedReserve || undefined}
          mode="edit"
          onSuccess={handleCloseReserveModal}
        />

        {/* Render Update Reserve Modal */}
        <ReserveFormModal
          isOpen={isUpdateReserveModalOpen}
          onClose={handleCloseReserveModal}
          claimId={claimId}
          mode="update"
          existingReserves={financials?.reserves}
          onSuccess={handleCloseReserveModal}
        />
      </Card>
    )
  }
}
