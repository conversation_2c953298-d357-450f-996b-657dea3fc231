import Link from 'next/link'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { FileText, AlertCircle, Calendar, User, Building, Briefcase } from 'lucide-react'
import { Claim, FNOLStatus } from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'

interface FnolAssociatedClaimsProps {
  fnolId: string
  claims: Claim[]
  fnolStatus?: FNOLStatus
}

export function FnolAssociatedClaims({ fnolId, claims, fnolStatus }: FnolAssociatedClaimsProps) {
  const { formatDateShort } = useDateFormatter()

  // Function to determine badge color based on claim status
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'DRAFT':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case 'INVESTIGATION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'SETTLEMENT':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'LITIGATION':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'RECOVERY':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300'
      case 'CLOSED_SETTLED':
      case 'CLOSED_DENIED':
      case 'CLOSED_WITHDRAWN':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Function to determine badge color based on claim type
  const getTypeColor = (type: string) => {
    switch (type.toUpperCase()) {
      case 'AUTO':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'PROPERTY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'GENERAL_LIABILITY':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Format date, handling potentially undefined dates
  const formatDate = (dateString: string | undefined | null) => {
    if (!dateString) return 'N/A'
    try {
      return formatDateShort(dateString)
    } catch (e) {
      return 'Invalid date'
    }
  }

  // Determine appropriate message for empty state
  const getEmptyStateMessage = () => {
    // If FNOL has been converted but no claims are showing yet
    if (fnolStatus === FNOLStatus.CONVERTED && claims.length === 0) {
      return (
        <>
          <h3 className="text-lg font-medium">Refreshing Claims</h3>
          <p className="text-sm text-muted-foreground mt-1 mb-4">
            This FNOL has been converted to one or more claims, but they may not be visible yet.
          </p>
          <p className="text-sm text-muted-foreground">
            Try refreshing the page to see the associated claims.
          </p>
        </>
      )
    }

    // Default no claims message
    return (
      <>
        <h3 className="text-lg font-medium">No Claims Yet</h3>
        <p className="text-sm text-muted-foreground mt-1 mb-4">
          No claims have been created from this FNOL yet.
        </p>
        <p className="text-sm text-muted-foreground">
          Use the "Convert to Claim" button at the top to create a claim from this FNOL.
        </p>
      </>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-xl flex items-center">
          <FileText className="h-5 w-5 mr-2 text-muted-foreground" />
          Associated Claims
        </CardTitle>
      </CardHeader>
      <CardContent>
        {claims.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
            {getEmptyStateMessage()}
          </div>
        ) : (
          <div className="space-y-2">
            <div className="grid grid-cols-5 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground">
              <div>Claim</div>
              <div>Customer</div>
              <div>Claimant</div>
              <div>Incident Date</div>
              <div>Assigned To</div>
            </div>

            {claims.map(claim => (
              <div
                key={claim.id}
                className="grid grid-cols-5 gap-4 border rounded-lg p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="flex flex-col">
                  <Link
                    href={`/claims/${claim.claim_number}`}
                    className="font-medium text-primary hover:underline"
                  >
                    {claim.claim_number}
                  </Link>
                  <div className="flex gap-1 mt-1">
                    <Badge variant="outline" className={getStatusColor(claim.status)}>
                      {claim.status}
                    </Badge>
                    <Badge variant="outline" className={getTypeColor(claim.type)}>
                      {claim.type}
                    </Badge>
                  </div>
                </div>

                <div className="flex items-center">
                  <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{claim.customer?.name || 'Unknown'}</span>
                </div>

                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{claim.claimant_name || 'Not specified'}</span>
                </div>

                <div className="flex items-center">
                  <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>{formatDate(claim.incident_date || claim.date_of_loss)}</span>
                </div>

                <div className="flex items-center">
                  <Briefcase className="h-4 w-4 mr-2 text-muted-foreground" />
                  <span>
                    {claim.assigned_to_id
                      ? 'Assigned' // Ideally, we would display the assigned user's name here
                      : 'Unassigned'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
