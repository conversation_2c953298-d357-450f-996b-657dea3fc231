'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { ChevronRight, AlertCircle, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { Card, CardContent } from '@/components/ui/card'
import { SortableHeader } from '@/components/ui/sortable-header'
import { useSorting } from '@/hooks/use-sorting'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Task, TaskPriority, TaskStatus } from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useEffect, useMemo } from 'react'

export function TasksList() {
  const { formatDate } = useDateFormatter()

  // Fetch tasks that are assigned to the current user
  const {
    data: tasksData,
    isLoading,
    error,
    refetch,
  } = useApi(
    () =>
      api.tasks.getTasks({
        page: 0,
        size: 10,
        // Add any filters you want here, e.g. status: TaskStatus.PENDING
      }),
    []
  )

  // Extract tasks from the wrapped response with useMemo to avoid dependency issues
  const tasks = useMemo(() => tasksData?.items || [], [tasksData])

  // Log data state for debugging
  useEffect(() => {
    if (tasksData) {
      console.log('TasksList - Tasks data loaded:', tasksData)
      console.log('TasksList - Extracted tasks:', tasks)
    }
    if (error) {
      console.error('TasksList - Error loading tasks:', error)
    }
  }, [tasksData, tasks, error])

  // Priority order for sorting
  const priorityOrder = {
    [TaskPriority.URGENT]: 0,
    [TaskPriority.HIGH]: 1,
    [TaskPriority.MEDIUM]: 2,
    [TaskPriority.LOW]: 3,
  }

  // Define sort functions for each column
  const sortFunctions = {
    hr_id: (a: Task, b: Task, direction: number) =>
      (a.hr_id || '').localeCompare(b.hr_id || '') * direction,
    title: (a: Task, b: Task, direction: number) =>
      (a.title || '').localeCompare(b.title || '') * direction,
    due_date: (a: Task, b: Task, direction: number) => {
      // Convert date strings to Date objects for comparison
      const dateA = a.due_date ? new Date(a.due_date).getTime() : 0
      const dateB = b.due_date ? new Date(b.due_date).getTime() : 0
      return (dateA - dateB) * direction
    },
    claim_id: (a: Task, b: Task, direction: number) =>
      (a.claim_number || '').localeCompare(b.claim_number || '') * direction,
    priority: (a: Task, b: Task, direction: number) => {
      // Sort by priority order
      return (
        (priorityOrder[a.priority as TaskPriority] - priorityOrder[b.priority as TaskPriority]) *
        direction
      )
    },
    status: (a: Task, b: Task, direction: number) =>
      (a.status || '').localeCompare(b.status || '') * direction,
  }

  // Use the sorting hook
  const { sortedItems, sortColumn, sortDirection, handleSort } = useSorting(
    tasks,
    'due_date', // Default sort by due date
    'asc', // Default sort direction
    sortFunctions
  )

  const getPriorityColor = (priority: TaskPriority) => {
    switch (priority) {
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case TaskPriority.HIGH:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case TaskStatus.BLOCKED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Format status for display
  const formatStatus = (status: TaskStatus) => {
    return status.replace('_', ' ')
  }

  // Display loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6 flex justify-center items-center min-h-[200px]">
          <div className="flex items-center">
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            <span>Loading tasks...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Display error state
  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>Failed to load tasks: {error}</span>
            </div>
            <Button onClick={() => refetch()} variant="outline" size="sm">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Display empty state
  if (sortedItems.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6 text-center py-8">
          <p className="text-muted-foreground">No tasks found</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="pt-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-10">
                <Checkbox id="select-all" />
              </TableHead>
              <SortableHeader
                column="hr_id"
                label="Task #"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="title"
                label="Task"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="due_date"
                label="Due Date"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="claim_id"
                label="Claim"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="priority"
                label="Priority"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="status"
                label="Status"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <TableHead className="w-10"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedItems.map(task => (
              <TableRow key={task.id}>
                <TableCell>
                  <Checkbox id={`task-${task.id}`} />
                </TableCell>
                <TableCell>
                  <Link href={`/tasks/${task.id}`} className="text-primary hover:underline">
                    {task.hr_id}
                  </Link>
                </TableCell>
                <TableCell className="font-medium">{task.title}</TableCell>
                <TableCell>{task.due_date ? formatDate(task.due_date) : 'No date'}</TableCell>
                <TableCell>
                  {task.claim_id ? (
                    <Link href={`/claims/${task.claim_id}`} className="hover:underline">
                      {task.claim_number || task.claim_id}
                    </Link>
                  ) : (
                    <span className="text-muted-foreground">No claim</span>
                  )}
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getPriorityColor(task.priority)}>
                    {task.priority}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(task.status)}>
                    {formatStatus(task.status)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Link href={`/tasks/${task.id}`}>
                    <Button size="icon" variant="ghost" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
