'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { ChevronRight, Plus, Loader2, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useState, useEffect } from 'react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Task, BackendPaginatedResponse } from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useUserResolver } from '@/hooks/useUserResolver'
import { TaskModal } from '@/components/task-modal'
import { TaskStatus } from '@/lib/api/types'

interface ClaimTasksProps {
  claimId: string
}

export function ClaimTasks({ claimId }: ClaimTasksProps) {
  const { formatDate } = useDateFormatter()
  const [claimUuid, setClaimUuid] = useState<string | null>(null)
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false)

  // User resolver for converting UUIDs to names
  const { resolveUsers, getUserDisplayName } = useUserResolver()

  // First, fetch the claim to get its UUID if we were passed a claim number
  const {
    data: claim,
    isLoading: isLoadingClaim,
    error: claimError,
  } = useApi(() => api.claims.getClaimByNumber(claimId), [claimId])

  useEffect(() => {
    if (claim && claim.id) {
      setClaimUuid(claim.id)
    }
  }, [claim])

  // Fetch tasks for this claim
  const {
    data: tasksData,
    isLoading: isLoadingTasks,
    error: tasksError,
    refetch: refetchTasks,
  } = useApi<BackendPaginatedResponse<Task>>(() => {
    if (!claimUuid) return Promise.resolve({ items: [], total: 0, skip: 0, limit: 10 })
    return api.tasks.getTasksByClaim(claimUuid)
  }, [claimUuid])

  const tasks = tasksData?.items || []

  // Collect and resolve assignee IDs from tasks
  useEffect(() => {
    if (tasks && tasks.length > 0) {
      const assigneeIds: string[] = []

      tasks.forEach(task => {
        // Check if assignee is a UUID string instead of object
        if (typeof task.assignee === 'string') {
          assigneeIds.push(task.assignee)
        }
        // Check if assignee is an object with id
        else if (task.assignee && typeof task.assignee === 'object' && task.assignee.id) {
          assigneeIds.push(task.assignee.id)
        }
      })

      const uniqueAssigneeIds = [...new Set(assigneeIds)].filter(Boolean)
      if (uniqueAssigneeIds.length > 0) {
        resolveUsers(uniqueAssigneeIds)
      }
    }
  }, [tasks, resolveUsers])

  // Helper to get assignee display info
  const getAssigneeDisplayInfo = (task: Task) => {
    // If assignee is a proper user object
    if (task.assignee && typeof task.assignee === 'object') {
      // Try to get resolved user name using the assignee ID
      const resolvedName = getUserDisplayName(task.assignee.id)
      if (resolvedName !== 'Unknown User') {
        return {
          name: resolvedName,
          initials:
            resolvedName
              .split(' ')
              .map(n => n.charAt(0))
              .join('')
              .slice(0, 2)
              .toUpperCase() || 'UN',
        }
      }

      // Fallback to email if available
      return {
        name: task.assignee.email || 'Unknown User',
        initials: task.assignee.email ? task.assignee.email.substring(0, 2).toUpperCase() : 'UN',
      }
    }

    // If assignee is a UUID string, use resolver
    if (typeof task.assignee === 'string') {
      const resolvedName = getUserDisplayName(task.assignee)
      return {
        name: resolvedName,
        initials:
          resolvedName
            .split(' ')
            .map(n => n.charAt(0))
            .join('')
            .slice(0, 2)
            .toUpperCase() || 'UN',
      }
    }

    return { name: 'Unassigned', initials: 'UN' }
  }

  const handleAddTaskClick = () => {
    setIsTaskModalOpen(true)
  }

  const handleTaskCreated = () => {
    refetchTasks()
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'URGENT':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'HIGH':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'MEDIUM':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'LOW':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case TaskStatus.BLOCKED:
      case TaskStatus.CANCELLED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Empty state for when there are no tasks
  const renderEmptyState = () => (
    <div className="flex flex-col items-center justify-center py-10 text-center">
      <h3 className="text-lg font-medium">No Tasks Yet</h3>
      <p className="text-sm text-muted-foreground max-w-sm mt-1 mb-4">
        No tasks have been assigned for this claim yet.
      </p>
    </div>
  )

  // Loading state
  const isLoading = isLoadingClaim || isLoadingTasks
  const error = claimError || tasksError

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-sm text-muted-foreground">Loading tasks...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="flex flex-col items-center gap-2">
          <AlertCircle className="h-8 w-8 text-destructive" />
          <p className="text-sm text-destructive">{error || 'Failed to load tasks'}</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Claim Tasks</h3>
        <Button className="flex items-center gap-1" onClick={handleAddTaskClick}>
          <Plus className="h-4 w-4 mr-1" />
          Add Task
        </Button>
      </div>

      {tasks.length === 0 ? (
        renderEmptyState()
      ) : (
        <div className="rounded-md border mt-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-10">
                  <Checkbox id="select-all" />
                </TableHead>
                <TableHead>Task #</TableHead>
                <TableHead>Task</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead>Priority</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Assignee</TableHead>
                <TableHead className="w-10"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tasks.map(task => (
                <TableRow key={task.id}>
                  <TableCell>
                    <Checkbox id={`task-${task.id}`} />
                  </TableCell>
                  <TableCell>
                    <Link
                      href={`/tasks/${task.hr_id || task.id}`}
                      className="text-primary hover:underline"
                    >
                      {task.hr_id || `TASK-${task.id.slice(0, 8)}`}
                    </Link>
                  </TableCell>
                  <TableCell className="font-medium">
                    <Link href={`/tasks/${task.hr_id || task.id}`} className="hover:underline">
                      {task.title}
                    </Link>
                  </TableCell>
                  <TableCell>{formatDate(task.due_date)}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(task.status)}>
                      {task.status.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>{getAssigneeDisplayInfo(task).initials}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{getAssigneeDisplayInfo(task).name}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Link href={`/tasks/${task.hr_id || task.id}`}>
                      <Button size="icon" variant="ghost" className="h-8 w-8">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Task Modal */}
      {isTaskModalOpen && (
        <TaskModal
          isOpen={isTaskModalOpen}
          onClose={() => setIsTaskModalOpen(false)}
          onTaskCreated={handleTaskCreated}
          claimNumber={claimId}
        />
      )}
    </>
  )
}
