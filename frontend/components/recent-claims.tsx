'use client'

import { useState, useEffect, useMemo } from 'react'
import { AlertCircle, CalendarIcon, ChevronDown, Loader2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { SortableHeader } from '@/components/ui/sortable-header'
import { useSorting } from '@/hooks/use-sorting'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { Claim, ClaimQueryParams, ClaimStatus, ClaimType } from '@/lib/api/types'
import { api } from '@/lib/api'
import { useApi } from '@/hooks/useApi'
import { formatCurrency } from '@/lib/utils'
import Link from 'next/link'

// Time period mapping to generate date filters for API calls
const timePeriodMap: Record<string, { days?: number; period?: string }> = {
  last_7_days: { days: 7 },
  last_30_days: { days: 30 },
  last_90_days: { days: 90 },
  last_6_months: { days: 180 },
  last_1_year: { days: 365 },
}

// Time period options for UI
const timePeriods = [
  { label: 'Last 7 days', value: 'last_7_days' },
  { label: 'Last 30 days', value: 'last_30_days' },
  { label: 'Last 90 days', value: 'last_90_days' },
  { label: 'Last 6 months', value: 'last_6_months' },
  { label: 'Last year', value: 'last_1_year' },
]

export function RecentClaims() {
  const { formatDate } = useDateFormatter()
  const [selectedPeriod, setSelectedPeriod] = useState({
    label: 'Last 30 days',
    value: 'last_30_days',
  })

  // Fetch claims based on the selected time period
  const {
    data: claimsData,
    isLoading,
    error,
    refetch,
  } = useApi(() => {
    // Calculate date range for filtering
    const periodConfig = timePeriodMap[selectedPeriod.value] || { days: 30 }
    const days = periodConfig.days || 30
    const fromDate = new Date()
    fromDate.setDate(fromDate.getDate() - days)

    // Build query params with a date filter
    const params: ClaimQueryParams = {
      page: 0,
      size: 10,
      // Only show most recent claims
      // Additional filters could be added here
    }

    // The API expects these parameters but they're not in the TypeScript interface
    const extendedParams = {
      ...params,
      include: ['customer'],
    }

    return api.claims.getClaims(extendedParams as any)
  }, [selectedPeriod.value])

  // Extract claims from the wrapped response with useMemo to avoid dependency issues
  const claims = useMemo(() => claimsData?.items || [], [claimsData])

  // Log data state for debugging
  useEffect(() => {
    if (claimsData) {
      console.log('RecentClaims - Claims data loaded:', claimsData)
      console.log('RecentClaims - Extracted claims:', claims)
    }
    if (error) {
      console.error('RecentClaims - Error loading claims:', error)
    }
  }, [claimsData, claims, error])

  // Define sort functions for each column
  const sortFunctions = {
    claim_number: (a: Claim, b: Claim, direction: number) =>
      (a.claim_number || '').localeCompare(b.claim_number || '') * direction,
    claimant_name: (a: Claim, b: Claim, direction: number) =>
      (a.claimant_name || '').localeCompare(b.claimant_name || '') * direction,
    type: (a: Claim, b: Claim, direction: number) =>
      (a.type || '').localeCompare(b.type || '') * direction,
    status: (a: Claim, b: Claim, direction: number) =>
      (a.status || '').localeCompare(b.status || '') * direction,
    date_of_loss: (a: Claim, b: Claim, direction: number) => {
      const dateA = a.date_of_loss ? new Date(a.date_of_loss).getTime() : 0
      const dateB = b.date_of_loss ? new Date(b.date_of_loss).getTime() : 0
      return (dateA - dateB) * direction
    },
    total_amount: (a: Claim, b: Claim, direction: number) => {
      const amountA = a.total_amount ? Number(a.total_amount) : 0
      const amountB = b.total_amount ? Number(b.total_amount) : 0
      return (amountA - amountB) * direction
    },
  }

  // Use the sorting hook
  const { sortedItems, sortColumn, sortDirection, handleSort } = useSorting(
    claims,
    'date_of_loss', // Default sort by date of loss
    'desc', // Default sort direction
    sortFunctions
  )

  // Helper function to get status badge variant
  const getStatusBadgeVariant = (status?: ClaimStatus) => {
    if (!status) return 'outline'

    // Type-safe comparison using string value
    switch (status) {
      case ClaimStatus.INVESTIGATION:
      case ClaimStatus.SETTLEMENT:
      case ClaimStatus.LITIGATION:
      case ClaimStatus.RECOVERY:
        return 'default'
      case ClaimStatus.DRAFT:
        return 'secondary'
      case ClaimStatus.CLOSED_SETTLED:
      case ClaimStatus.CLOSED_DENIED:
        return 'outline'
      default:
        return 'outline'
    }
  }

  // Format claim type for display
  const formatClaimType = (type?: ClaimType) => {
    if (!type) return 'Unknown'
    return type.replace('_', ' ')
  }

  // Display loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Claims</CardTitle>
          <CardDescription>Loading claims data...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <div className="flex items-center">
            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
            <span>Loading claims...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Display error state
  if (error) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Recent Claims</CardTitle>
            <CardDescription>Error loading claims</CardDescription>
          </div>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            Retry
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex items-center text-red-600 py-4">
            <AlertCircle className="h-5 w-5 mr-2" />
            <span>Failed to load claims: {error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Recent Claims</CardTitle>
          <CardDescription>Overview of recently filed claims</CardDescription>
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-8 gap-1 hover:border-tangerine-500 hover:text-tangerine-600"
            >
              <CalendarIcon className="h-3.5 w-3.5" />
              <span>{selectedPeriod.label}</span>
              <ChevronDown className="h-3.5 w-3.5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {timePeriods.map(period => (
              <DropdownMenuItem
                key={period.value}
                className={selectedPeriod.value === period.value ? 'bg-muted' : ''}
                onClick={() => setSelectedPeriod(period)}
              >
                {period.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>
      <CardContent>
        {sortedItems.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No claims found for the selected period
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <SortableHeader
                  column="claim_number"
                  label="Claim ID"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
                <SortableHeader
                  column="claimant_name"
                  label="Claimant"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
                <SortableHeader
                  column="type"
                  label="Type"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
                <SortableHeader
                  column="status"
                  label="Status"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
                <SortableHeader
                  column="date_of_loss"
                  label="Date of Loss"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                />
                <SortableHeader
                  column="total_amount"
                  label="Amount"
                  sortColumn={sortColumn}
                  sortDirection={sortDirection}
                  onSort={handleSort}
                  className="text-right"
                />
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedItems.map(claim => (
                <TableRow key={claim.id} className="hover:bg-muted/50">
                  <TableCell className="font-medium">
                    <Link
                      href={`/claims/${claim.claim_number}`}
                      className="hover:text-tangerine-600 hover:underline"
                    >
                      {claim.claim_number}
                    </Link>
                  </TableCell>
                  <TableCell>{claim.claimant_name || 'N/A'}</TableCell>
                  <TableCell>{formatClaimType(claim.type)}</TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(claim.status)}>
                      {claim.status?.replace('_', ' ') || 'Unknown'}
                    </Badge>
                  </TableCell>
                  <TableCell>{formatDate(claim.date_of_loss || '')}</TableCell>
                  <TableCell className="text-right">
                    {claim.total_amount ? formatCurrency(Number(claim.total_amount)) : 'N/A'}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
