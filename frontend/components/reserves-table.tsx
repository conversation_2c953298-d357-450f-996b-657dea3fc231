import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { Claim, ReserveResponse, ReserveType } from '@/lib/api/types'
import { Skeleton } from '@/components/ui/skeleton'
import { useDateFormatter } from '@/hooks/useDateFormatter'

interface AggregatedReserve extends ReserveResponse {
  claim_id: string
  claim_number: string
  claimant_name: string | null | undefined
  claim_type: string
}

interface ReservesTableProps {
  claims: Claim[]
}

export function ReservesTable({ claims }: ReservesTableProps) {
  const { formatDateShort } = useDateFormatter()

  const aggregatedReserves = React.useMemo(() => {
    return claims.reduce((acc, claim) => {
      if (claim.financials?.reserves) {
        const claimReserves = claim.financials.reserves.map(reserve => ({
          ...reserve,
          claim_id: claim.id,
          claim_number: claim.claim_number,
          claimant_name: claim.claimant_name,
          claim_type: claim.type,
        }))
        acc.push(...claimReserves)
      }
      return acc
    }, [] as AggregatedReserve[])
  }, [claims])

  const formatCurrency = (value: string | number | null | undefined) => {
    const num = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (num === null || num === undefined || isNaN(num)) {
      return '$0.00'
    }
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(num)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'AUTO':
        return 'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300'
      case 'PROPERTY':
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case 'GENERAL_LIABILITY':
        return 'bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getReserveTypeColor = (type: ReserveType) => {
    switch (type) {
      case ReserveType.PROPERTY_DAMAGE:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case ReserveType.BODILY_INJURY:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case ReserveType.MEDICAL_PAYMENTS:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case ReserveType.DEFENSE_COST:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case ReserveType.LOSS_OF_USE:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  if (!claims || claims.length === 0) {
    return (
      <div className="rounded-md border p-4 text-center text-muted-foreground">
        No reserve data available.
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Claim #</TableHead>
            <TableHead>Claimant</TableHead>
            <TableHead>Claim Type</TableHead>
            <TableHead>Reserve Type</TableHead>
            <TableHead className="text-right">Amount</TableHead>
            <TableHead>Last Updated</TableHead>
            <TableHead className="w-10"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {aggregatedReserves.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                No reserves found for the current claims.
              </TableCell>
            </TableRow>
          ) : (
            aggregatedReserves.map(reserve => (
              <TableRow key={`${reserve.claim_id}-${reserve.id}`}>
                <TableCell className="font-medium">
                  <Link href={`/claims/${reserve.claim_number}`} className="hover:underline">
                    {reserve.claim_number}
                  </Link>
                </TableCell>
                <TableCell>{reserve.claimant_name}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getTypeColor(reserve.claim_type)}>
                    {reserve.claim_type.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getReserveTypeColor(reserve.reserve_type)}>
                    {reserve.reserve_type.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Link
                    href={`/claims/${reserve.claim_number}/financials#reserve-${reserve.id}`}
                    className="hover:underline"
                  >
                    {formatCurrency(reserve.amount)}
                  </Link>
                </TableCell>
                <TableCell>{formatDateShort(reserve.updated_at)}</TableCell>
                <TableCell>
                  <Link href={`/claims/${reserve.claim_number}/financials`}>
                    <Button size="icon" variant="ghost" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
