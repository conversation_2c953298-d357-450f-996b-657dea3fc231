'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Injury, InjuryCreate, InjuryUpdate } from '@/lib/api/types'
import { useToast } from '@/components/ui/use-toast'
import { Loader2, AlertCircle, PlusCircle, Edit, Trash2 } from 'lucide-react'
import { InjuryForm, InjuryFormValues } from './injury-form' // Import the actual form

interface InjuryListProps {
  claimId: string
  personId: string
}

export function InjuryList({ claimId, personId }: InjuryListProps) {
  const { toast } = useToast()
  const [showInjuryForm, setShowInjuryForm] = useState(false)
  const [editingInjury, setEditingInjury] = useState<Injury | null>(null)
  const [isSavingInjury, setIsSavingInjury] = useState(false) // For form submission state

  const {
    data: injuries,
    isLoading,
    error,
    refetch,
  } = useApi<Injury[]>(
    () => (claimId && personId ? api.claims.getInjuries(claimId, personId) : Promise.resolve([])),
    [claimId, personId]
  )

  const handleAddInjury = () => {
    setEditingInjury(null)
    setShowInjuryForm(true)
  }

  const handleEditInjury = (injury: Injury) => {
    const openInjuryFormWithInjury = () => {
      setEditingInjury(injury)
      setShowInjuryForm(true)
    }
    openInjuryFormWithInjury()
  }

  const handleDeleteInjury = async (injuryId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this injury?')
    if (!confirmed) return

    try {
      await api.claims.deleteInjury(claimId, personId, injuryId)
      toast({ title: 'Success', description: 'Injury deleted successfully.' })
      refetch()
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete injury.',
        variant: 'destructive',
      })
    }
  }

  // This function will be passed to InjuryForm's onSave prop
  const handleSaveInjury = async (data: InjuryFormValues, injuryIdToUpdate?: string) => {
    setIsSavingInjury(true)
    try {
      if (injuryIdToUpdate) {
        await api.claims.updateInjury(claimId, personId, injuryIdToUpdate, data as InjuryUpdate)
        toast({ title: 'Success', description: 'Injury updated successfully.' })
        await refetch()
        setShowInjuryForm(false)
        setEditingInjury(null)
      } else {
        await api.claims.createInjury(claimId, personId, data as InjuryCreate)
        toast({ title: 'Success', description: 'Injury added successfully.' })
        await refetch()
        setShowInjuryForm(false)
        setEditingInjury(null)
      }
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to save injury.',
        variant: 'destructive',
      })
    } finally {
      setIsSavingInjury(false)
    }
  }

  const handleFormCancel = () => {
    setShowInjuryForm(false)
    setEditingInjury(null) // Clear editing state on cancel
  }

  const formatDisplay = (value?: string | null) => {
    if (!value) return 'N/A'
    return value
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  }

  if (isLoading) {
    return (
      <div className="flex items-center">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Loading injuries...
      </div>
    )
  }

  if (error) {
    let errorMessage = 'Could not load injuries.'
    if (typeof error === 'string') errorMessage = error
    else if (error && typeof (error as any).message === 'string')
      errorMessage = (error as any).message
    return (
      <div className="text-red-500 flex items-center">
        <AlertCircle className="mr-2 h-4 w-4" />
        {errorMessage}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Recorded Injuries</h3>
        <Button onClick={handleAddInjury} size="sm" type="button">
          <PlusCircle className="mr-2 h-4 w-4" /> Add Injury
        </Button>
      </div>

      {injuries && injuries.length > 0 ? (
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Injury Type / Body Part</TableHead>
              <TableHead>Severity</TableHead>
              <TableHead>Description</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {injuries.map(injury => (
              <TableRow key={injury.id}>
                <TableCell>{formatDisplay(injury.injury_type)}</TableCell>
                <TableCell>{formatDisplay(injury.injury_severity)}</TableCell>
                <TableCell className="max-w-xs truncate" title={injury.injury_description || ''}>
                  {injury.injury_description || 'N/A'}
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditInjury(injury)}
                    className="mr-1"
                    type="button"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteInjury(injury.id)}
                    className="text-red-600 hover:text-red-700"
                    type="button"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      ) : (
        <p className="text-sm text-muted-foreground">
          No specific injuries recorded for this person.
        </p>
      )}

      {showInjuryForm && (
        <InjuryForm
          isOpen={showInjuryForm}
          onClose={handleFormCancel} // Use handleFormCancel for consistency
          claimId={claimId}
          personId={personId}
          injuryData={editingInjury}
          onSave={handleSaveInjury}
          // isSaving prop is managed by the form itself via isSubmitting from useForm
        />
      )}
    </div>
  )
}
