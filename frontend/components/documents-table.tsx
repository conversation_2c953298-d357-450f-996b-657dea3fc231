'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight, Download, Eye, FileText, ImageIcon, FileArchive, File } from 'lucide-react'
import Link from 'next/link'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { Document, DocumentType } from '@/lib/api/types'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'

interface DocumentsTableProps {
  documentType?: DocumentType
  reloadTrigger?: number
}

export function DocumentsTable({ documentType, reloadTrigger = 0 }: DocumentsTableProps) {
  const { formatRelativeDate } = useDateFormatter()

  const fetchDocuments = async () => {
    const params = documentType ? { document_type: documentType } : {}
    const response = await api.documents.listAllDocuments(params)
    return response.items
  }

  // Use the API hook to fetch data
  const {
    data: documents,
    isLoading,
    error,
    refetch,
  } = useApi<Document[]>(fetchDocuments, [documentType, reloadTrigger])

  const getDocumentIcon = (type: string, mimeType: string) => {
    if (type === 'PHOTO' || mimeType.startsWith('image/')) return <ImageIcon className="h-4 w-4" />
    if (mimeType === 'application/pdf') return <FileText className="h-4 w-4" />
    if (mimeType === 'application/zip') return <FileArchive className="h-4 w-4" />
    return <File className="h-4 w-4" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
    else return (bytes / 1048576).toFixed(1) + ' MB'
  }

  const getDocumentTypeColor = (type: DocumentType) => {
    switch (type) {
      case DocumentType.PHOTO:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case DocumentType.REPORT:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case DocumentType.POLICY:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case DocumentType.INVOICE:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case DocumentType.STATEMENT:
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case DocumentType.CONTRACT:
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      case DocumentType.CORRESPONDENCE:
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const handleDownload = async (document: Document) => {
    try {
      const downloadResponse = await api.documents.getDocumentDownloadUrl(
        document.claim_id,
        document.id
      )

      // Always use the anchor element approach for downloads
      // This preserves the signed URL integrity
      const link = globalThis.document.createElement('a')
      link.href = downloadResponse.download_url
      link.setAttribute('download', document.name) // Set download attribute
      link.setAttribute('target', '_blank')
      globalThis.document.body.appendChild(link)
      link.click()
      globalThis.document.body.removeChild(link)
    } catch (err) {
      console.error('Error downloading document:', err)
      alert('Failed to download document. Please try again.')
    }
  }

  const handleView = async (document: Document) => {
    try {
      const downloadResponse = await api.documents.getDocumentDownloadUrl(
        document.claim_id,
        document.id
      )

      // Open the document in a new tab/window for viewing
      window.open(downloadResponse.download_url, '_blank')
    } catch (err) {
      console.error('Error viewing document:', err)
      alert('Failed to view document. Please try again.')
    }
  }

  // Show standardized loading state
  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <Loading size="md" text="Loading documents..." centered />
      </div>
    )
  }

  // Show standardized error state
  if (error) {
    return (
      <EmptyState
        variant="error"
        title="Failed to load documents"
        description={error}
        actionLabel="Try Again"
        onAction={refetch}
      />
    )
  }

  // Show standardized empty state
  if (!documents || documents.length === 0) {
    return (
      <EmptyState
        variant="filtered"
        title="No documents found"
        description={
          documentType
            ? `No ${documentType.toLowerCase()} documents are available`
            : 'No documents are available'
        }
      />
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Document</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Claim</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Uploaded</TableHead>
            <TableHead className="w-[100px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map(document => (
            <TableRow key={document.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  {getDocumentIcon(document.type, document.mime_type)}
                  <div>
                    <div className="font-medium">
                      <Link href={`/documents/${document.id}`} className="hover:underline">
                        {document.name}
                      </Link>
                    </div>
                    {document.description && (
                      <div className="text-xs text-muted-foreground">{document.description}</div>
                    )}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <Badge variant="outline" className={getDocumentTypeColor(document.type)}>
                  {document.type}
                </Badge>
              </TableCell>
              <TableCell>
                <Link href={`/claims/${document.claim_id}`} className="hover:underline">
                  {document.claim_number || document.claim_id}
                </Link>
              </TableCell>
              <TableCell>{formatFileSize(document.file_size)}</TableCell>
              <TableCell>{formatRelativeDate(document.created_at)}</TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8"
                    title="View"
                    onClick={() => handleView(document)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    className="h-8 w-8"
                    title="Download"
                    onClick={() => handleDownload(document)}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Link href={`/documents/${document.id}`}>
                    <Button size="icon" variant="ghost" className="h-8 w-8" title="Details">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
