'use client'

import {
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Line,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts'

import {
  ClaimsByTypeResponse,
  ClaimsByStatusResponse,
  ClaimsOverTimeResponse,
} from '@/lib/api/types'

// Color palette for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d']

export function BarChart({ data }: { data?: any[] }) {
  console.log('📊 BarChart component received data:', data)

  if (!data || data.length === 0) {
    console.log('⚠️ BarChart: No data available or empty data array')
    return (
      <div className="w-full aspect-[16/9] bg-muted/20 rounded-md flex items-center justify-center">
        <div className="text-muted-foreground">No data available</div>
      </div>
    )
  }

  // Log the structure of the first data item to understand what we're working with
  console.log('📊 BarChart data structure example (first item):', data[0])

  // Check if the data has the expected format (name and value properties)
  const hasExpectedFormat = data.every(item => 'name' in item && 'value' in item)
  console.log('📊 BarChart data has expected format (name/value):', hasExpectedFormat)

  // If data doesn't have expected format, try to transform it
  const chartData = hasExpectedFormat
    ? data
    : data.map((item, index) => {
        // Create a default representation if we can't determine the structure
        const name = item.name || item.label || item.period || item.id || `Item ${index + 1}`
        const value = item.value || item.count || item.amount || 0
        return { name, value }
      })

  console.log('📊 BarChart data after processing:', chartData)

  return (
    <ResponsiveContainer width="100%" height="100%" className="w-full aspect-[16/9]">
      <RechartsBarChart
        data={chartData}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip
          formatter={(value, name, props) => {
            console.log('📊 BarChart tooltip:', { value, name, props })
            return [value, name]
          }}
        />
        <Legend />
        <Bar dataKey="value" fill="#8884d8" />
      </RechartsBarChart>
    </ResponsiveContainer>
  )
}

export function LineChart({ data }: { data?: ClaimsOverTimeResponse }) {
  console.log('📈 LineChart component received data:', data)

  if (!data || !data.data || data.data.length === 0) {
    console.log('⚠️ LineChart: No data available or empty data array')
    return (
      <div className="w-full aspect-[16/9] bg-muted/20 rounded-md flex items-center justify-center">
        <div className="text-muted-foreground">No data available</div>
      </div>
    )
  }

  // Log the structure of the first data item
  console.log('📈 LineChart data structure example (first item):', data.data[0])

  // Check for expected fields
  const hasExpectedFields = data.data.every(
    item => 'period' in item && 'new_claims' in item && 'closed_claims' in item
  )
  console.log('📈 LineChart data has all expected fields:', hasExpectedFields)

  return (
    <ResponsiveContainer width="100%" height="100%" className="w-full aspect-[16/9]">
      <RechartsLineChart
        data={data.data}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="period" />
        <YAxis />
        <Tooltip
          formatter={(value, name, props) => {
            console.log('📈 LineChart tooltip:', { value, name, props })
            return [value, name === 'new_claims' ? 'New Claims' : 'Closed Claims']
          }}
        />
        <Legend />
        <Line
          type="monotone"
          dataKey="new_claims"
          name="New Claims"
          stroke="#8884d8"
          activeDot={{ r: 8 }}
        />
        <Line type="monotone" dataKey="closed_claims" name="Closed Claims" stroke="#82ca9d" />
      </RechartsLineChart>
    </ResponsiveContainer>
  )
}

export function PieChart({ data }: { data?: ClaimsByTypeResponse | ClaimsByStatusResponse }) {
  console.log('📊 PieChart component received data:', data)

  if (!data || !data.data || data.data.length === 0) {
    console.log('⚠️ PieChart: No data available or empty data array')
    return (
      <div className="w-full aspect-square bg-muted/20 rounded-md flex items-center justify-center">
        <div className="text-muted-foreground">No data available</div>
      </div>
    )
  }

  // Log the structure of the first data item to understand what we're working with
  console.log('📊 PieChart data structure example (first item):', data.data[0])

  // Check if we have type or status fields
  const hasTypeField = data.data.some(item => 'type' in item)
  const hasStatusField = data.data.some(item => 'status' in item)

  console.log('📊 PieChart data contains type field:', hasTypeField)
  console.log('📊 PieChart data contains status field:', hasStatusField)

  return (
    <ResponsiveContainer width="100%" height="100%" className="w-full aspect-square">
      <RechartsPieChart>
        <Pie
          data={data.data}
          cx="50%"
          cy="50%"
          labelLine={true}
          outerRadius={80}
          fill="#8884d8"
          dataKey="count"
          nameKey={hasTypeField ? 'type' : hasStatusField ? 'status' : undefined} // Dynamically determine the nameKey
          label={entry => {
            const label = entry.type || entry.status || 'unknown'
            console.log('📊 PieChart entry label:', { entry, resolved: label })
            return `${label}: ${entry.count}`
          }}
        >
          {data.data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip
          formatter={(value, name, props) => {
            console.log('📊 PieChart tooltip:', { value, name, props })
            return [value, name]
          }}
        />
        <Legend />
      </RechartsPieChart>
    </ResponsiveContainer>
  )
}
