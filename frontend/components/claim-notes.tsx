'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Loader2, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { useApi } from '@/hooks/useApi'
import { api } from '../lib/api'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { Note, NoteCreate } from '@/lib/api/types'
import { useToast } from '@/components/ui/use-toast'
import { Textarea } from '@/components/ui/textarea'

interface ClaimNotesProps {
  claimId: string
}

export function ClaimNotes({ claimId }: ClaimNotesProps) {
  const { formatRelativeDate } = useDateFormatter()
  const { toast } = useToast()
  const [newNoteContent, setNewNoteContent] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showAddNote, setShowAddNote] = useState(false)
  const [claimUuid, setClaimUuid] = useState<string | null>(null)

  // First, fetch the claim to get its UUID if we were passed a claim number (needed only for GET)
  const {
    data: claim,
    isLoading: isLoadingClaim,
    error: claimError,
  } = useApi(() => api.claims.getClaimByNumber(claimId), [claimId])

  useEffect(() => {
    if (claim && claim.id) {
      setClaimUuid(claim.id)
    }
  }, [claim])

  // Fetch notes - requires UUID as per API spec
  const {
    data: notes,
    isLoading: isLoadingNotes,
    error: notesError,
    refetch: refreshNotes,
  } = useApi<Note[]>(() => {
    if (!claimUuid) return Promise.resolve([])
    return api.claims.getClaimNotes(claimUuid)
  }, [claimUuid])

  // Function to get author display name from note
  const getAuthorDisplayName = (note: Note): string => {
    // If author is present, use it
    if (note.author) return note.author

    // If recently created and has author_email, extract name from email
    if (note.author_email) {
      // Extract username from email (everything before @)
      const username = note.author_email.split('@')[0]
      // Convert to title case (capitalize first letter of each word)
      return username
        .split(/[._-]/)
        .map(part => part.charAt(0).toUpperCase() + part.slice(1))
        .join(' ')
    }

    // Otherwise, check if recently created
    if (note.created_at && new Date(note.created_at).getTime() > Date.now() - 10000) {
      return 'You'
    }

    // Fallback
    return 'Unknown User'
  }

  // Handler for submitting a new note - use claim number directly for POST
  const handleSubmitNote = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newNoteContent.trim()) return

    try {
      setIsSubmitting(true)
      // Use the claimId (which is either UUID or claim number) directly, as the API supports both
      await api.claims.createClaimNote(claimId, {
        content: newNoteContent,
        claim_id: claimUuid || claimId, // Use UUID if available for data consistency
      })
      setNewNoteContent('')
      setShowAddNote(false)
      toast({
        title: 'Note added',
        description: 'Your note has been added successfully.',
      })
      // Refresh the notes list immediately since API operation completed successfully
      refreshNotes()
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to add note. Please try again.',
        variant: 'destructive',
      })
      console.error('Failed to add note:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const isLoading = isLoadingClaim || isLoadingNotes
  const error = claimError || notesError

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <p className="text-sm text-muted-foreground">Loading notes...</p>
        </div>
      </div>
    )
  }

  if (error) {
    // Ignore certain error messages that might indicate no notes rather than a real error
    if (
      error.includes('invalid response') ||
      error.includes('Failed to parse') ||
      error.includes('404')
    ) {
      // Treat this as "no notes" rather than an error
      return (
        <>
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Case Notes</h3>
            <Button onClick={() => setShowAddNote(true)} disabled={showAddNote}>
              Add Note
            </Button>
          </div>

          {showAddNote && (
            <Card className="mb-4">
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <Textarea
                    placeholder="Enter your note here..."
                    value={newNoteContent}
                    onChange={e => setNewNoteContent(e.target.value)}
                    className="min-h-[100px]"
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setShowAddNote(false)
                        setNewNoteContent('')
                      }}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleSubmitNote}
                      disabled={!newNoteContent.trim() || isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving
                        </>
                      ) : (
                        'Save Note'
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <div className="flex flex-col items-center justify-center py-10 text-center">
            <h3 className="text-lg font-medium">No Notes Yet</h3>
            <p className="text-sm text-muted-foreground max-w-sm mt-1 mb-4">
              No notes have been added for this claim yet. Add your first note to get started.
            </p>
          </div>
        </>
      )
    }

    return (
      <div className="flex items-center justify-center py-10">
        <div className="flex flex-col items-center gap-2">
          <AlertCircle className="h-8 w-8 text-destructive" />
          <p className="text-sm text-destructive">{error || 'Failed to load notes'}</p>
        </div>
      </div>
    )
  }

  // For displaying notes, we still need the UUID (as per API requirement)
  // But we can still show the Add Note UI even if we don't have the UUID yet
  return (
    <>
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Case Notes</h3>
        <Button onClick={() => setShowAddNote(true)} disabled={showAddNote}>
          Add Note
        </Button>
      </div>

      {showAddNote && (
        <Card className="mb-4">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <Textarea
                placeholder="Enter your note here..."
                value={newNoteContent}
                onChange={e => setNewNoteContent(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowAddNote(false)
                    setNewNoteContent('')
                  }}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSubmitNote}
                  disabled={!newNoteContent.trim() || isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving
                    </>
                  ) : (
                    'Save Note'
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {!notes || notes.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-10 text-center">
          <h3 className="text-lg font-medium">No Notes Yet</h3>
          <p className="text-sm text-muted-foreground max-w-sm mt-1 mb-4">
            No notes have been added for this claim yet. Add your first note to get started.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {notes.map(note => (
            <Card key={note.id}>
              <CardContent className="pt-6">
                <div className="flex justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder-user.jpg" alt={getAuthorDisplayName(note)} />
                      <AvatarFallback>
                        {getAuthorDisplayName(note)
                          .split(' ')
                          .map(n => n[0])
                          .join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <Link
                        href={`/users/${note.author_id || '#'}`}
                        className="font-medium text-sm hover:underline"
                      >
                        {getAuthorDisplayName(note)}
                      </Link>
                      <div className="text-xs text-muted-foreground">
                        {formatRelativeDate(note.created_at)}
                      </div>
                    </div>
                  </div>
                </div>
                <p className="text-sm">{note.content}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </>
  )
}
