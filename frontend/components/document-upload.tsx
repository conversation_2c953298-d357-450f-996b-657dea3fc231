'use client'

import type React from 'react'

import { useState, useEffect, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Upload, X, FileText, Check } from 'lucide-react'
import { cn } from '@/lib/utils'
import { DocumentType, Claim } from '@/lib/api/types'
import { api } from '../lib/api'

interface DocumentUploadProps {
  onUploadSuccess?: () => void
}

export function DocumentUpload({ onUploadSuccess }: DocumentUploadProps) {
  const [files, setFiles] = useState<File[]>([])
  const [documentType, setDocumentType] = useState<DocumentType | ''>('')
  const [claimId, setClaimId] = useState<string>('')
  const [description, setDescription] = useState<string>('')
  const [searchValue, setSearchValue] = useState('')
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([])
  const [allClaims, setAllClaims] = useState<Claim[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadError, setUploadError] = useState<string | null>(null)
  const [loadingClaims, setLoadingClaims] = useState(false)
  const suggestionRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Fetch claims from API
  useEffect(() => {
    async function fetchClaims() {
      try {
        setLoadingClaims(true)
        const response = await api.claims.getClaims({ size: 50 })
        setAllClaims(response.items || [])
        setFilteredClaims(response.items?.slice(0, 5) || [])
      } catch (error) {
        console.error('Error fetching claims:', error)
      } finally {
        setLoadingClaims(false)
      }
    }

    fetchClaims()
  }, [])

  // Filter claims based on search input
  useEffect(() => {
    if (searchValue) {
      const filtered = allClaims.filter(
        claim =>
          claim.claim_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          getCustomerName(claim).toLowerCase().includes(searchValue.toLowerCase())
      )
      setFilteredClaims(filtered.slice(0, 10)) // Show top 10 matches
    } else {
      setFilteredClaims(allClaims.slice(0, 5)) // Show first 5 claims when empty
    }
  }, [searchValue, allClaims])

  // Helper function to get customer name from claim
  const getCustomerName = (claim: Claim): string => {
    if (claim.customer?.name) {
      return claim.customer.name
    }
    return 'Unknown Customer'
  }

  // Close suggestions when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        suggestionRef.current &&
        !suggestionRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files)
      setFiles(prev => [...prev, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!claimId || !documentType || files.length === 0) {
      setUploadError('Please select a claim, document type, and at least one file')
      return
    }

    setUploading(true)
    setUploadError(null)

    try {
      // Upload each file using the direct upload endpoint
      const uploadPromises = files.map(file =>
        api.documents.uploadDocumentDirect(
          claimId,
          file,
          documentType as DocumentType,
          file.name,
          description || undefined
        )
      )

      await Promise.all(uploadPromises)

      // Reset form on success
      setFiles([])
      setDocumentType('')
      setClaimId('')
      setDescription('')
      setSearchValue('')

      // Notify parent about successful upload
      if (onUploadSuccess) {
        onUploadSuccess()
      }
    } catch (error: any) {
      console.error('Error uploading documents:', error)
      setUploadError(error.message || 'Failed to upload documents. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  const handleClaimSelect = (claim: Claim) => {
    setClaimId(claim.id)
    setSearchValue(claim.claim_number)
    setShowSuggestions(false)
  }

  // Find the selected claim
  const selectedClaim = allClaims.find(claim => claim.id === claimId)

  return (
    <form onSubmit={handleSubmit}>
      <div className="grid gap-6">
        <div className="grid gap-3">
          <Label htmlFor="claim-search">Claim</Label>
          <div className="relative">
            <Input
              ref={inputRef}
              id="claim-search"
              type="text"
              placeholder={loadingClaims ? 'Loading claims...' : 'Type to search claims...'}
              value={searchValue}
              onChange={e => {
                setSearchValue(e.target.value)
                setShowSuggestions(true)
                // If the input doesn't match any claim, clear the claimId
                if (!allClaims.some(claim => claim.claim_number === e.target.value)) {
                  setClaimId('')
                }
              }}
              onFocus={() => setShowSuggestions(true)}
              className="w-full"
              aria-autocomplete="list"
              aria-controls="claim-suggestions"
              aria-expanded={showSuggestions}
              disabled={loadingClaims}
            />

            {showSuggestions && (
              <div
                ref={suggestionRef}
                id="claim-suggestions"
                className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-md max-h-[300px] overflow-y-auto"
              >
                {filteredClaims.length > 0 ? (
                  <ul className="py-1">
                    {filteredClaims.map(claim => (
                      <li
                        key={claim.id}
                        className={cn(
                          'px-3 py-2 cursor-pointer flex justify-between items-center',
                          'hover:bg-accent hover:text-accent-foreground',
                          claimId === claim.id && 'bg-accent text-accent-foreground'
                        )}
                        onClick={() => handleClaimSelect(claim)}
                      >
                        <div className="flex flex-col">
                          <span className="font-medium">{claim.claim_number}</span>
                          <span className="text-xs text-muted-foreground">
                            {getCustomerName(claim)}
                          </span>
                        </div>
                        {claimId === claim.id && <Check className="h-4 w-4" />}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="px-3 py-4 text-center text-sm text-muted-foreground">
                    No claims found
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid gap-3">
          <Label htmlFor="document-type">Document Type</Label>
          <Select
            value={documentType}
            onValueChange={value => setDocumentType(value as DocumentType)}
          >
            <SelectTrigger id="document-type">
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={DocumentType.PHOTO}>Photo</SelectItem>
              <SelectItem value={DocumentType.REPORT}>Report</SelectItem>
              <SelectItem value={DocumentType.INVOICE}>Invoice</SelectItem>
              <SelectItem value={DocumentType.STATEMENT}>Statement</SelectItem>
              <SelectItem value={DocumentType.CONTRACT}>Contract</SelectItem>
              <SelectItem value={DocumentType.POLICY}>Policy</SelectItem>
              <SelectItem value={DocumentType.CORRESPONDENCE}>Correspondence</SelectItem>
              <SelectItem value={DocumentType.OTHER}>Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid gap-3">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            placeholder="Enter document description"
            value={description}
            onChange={e => setDescription(e.target.value)}
          />
        </div>

        <div className="grid gap-3">
          <Label htmlFor="file-upload">Upload Files</Label>
          <div className="flex items-center justify-center w-full">
            <label
              htmlFor="file-upload"
              className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer bg-muted/20 hover:bg-muted/30"
            >
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="w-8 h-8 mb-2 text-muted-foreground" />
                <p className="mb-2 text-sm text-muted-foreground">
                  <span className="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p className="text-xs text-muted-foreground">PDF, JPG, PNG, or ZIP (MAX. 100MB)</p>
              </div>
              <Input
                id="file-upload"
                type="file"
                multiple
                className="hidden"
                onChange={handleFileChange}
              />
            </label>
          </div>
        </div>

        {files.length > 0 && (
          <div className="grid gap-3">
            <Label>Selected Files</Label>
            <div className="space-y-2">
              {files.map((file, index) => (
                <Card key={index} className="p-2 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    <span className="text-sm truncate max-w-[300px]">{file.name}</span>
                    <span className="text-xs text-muted-foreground">
                      ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-7 w-7"
                    onClick={() => removeFile(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </Card>
              ))}
            </div>
          </div>
        )}

        {uploadError && <div className="text-sm text-red-500 mt-2">{uploadError}</div>}

        <Button type="submit" disabled={uploading}>
          {uploading ? 'Uploading...' : 'Upload Documents'}
        </Button>
      </div>
    </form>
  )
}
