'use client'

import { useState, useEffect } from 'react'
import {
  Activity,
  AlertCircle,
  CalendarIcon,
  ChevronDown,
  FileClock,
  FilePlus2,
  FileQuestion,
  Loader2,
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Button } from '@/components/ui/button'
import { api } from '@/lib/api'
import { useApi } from '@/hooks/useApi'
import { DashboardMetricsResponse } from '@/lib/api/types'

// Period options with their corresponding API values
const timePeriods = [
  { label: 'Last 7 days', value: 'last_7_days' },
  { label: 'Last 30 days', value: 'last_30_days' },
  { label: 'Last 90 days', value: 'last_90_days' },
  { label: 'Last 6 months', value: 'last_6_months' },
  { label: 'Last year', value: 'last_1_year' },
]

export function DashboardMetrics() {
  const [selectedPeriod, setSelectedPeriod] = useState({
    label: 'Last 30 days',
    value: 'last_30_days',
  })

  console.log('DashboardMetrics - Component rendering with period:', selectedPeriod.value)

  // Fetch metrics data using the useApi hook
  const {
    data: wrappedMetrics,
    isLoading,
    error,
    refetch,
  } = useApi<any>(() => {
    console.log('DashboardMetrics - useApi fetcher function called')
    return api.metrics.getDashboardMetrics(selectedPeriod.value, true)
  }, [selectedPeriod.value])

  // Extract the metrics from the wrapped response
  const metrics = wrappedMetrics?.items?.[0] || null

  // Log data state for debugging
  useEffect(() => {
    if (wrappedMetrics) {
      console.log('DashboardMetrics - Metrics data loaded:', wrappedMetrics)
      console.log('DashboardMetrics - Extracted metrics:', metrics)
    }
    if (error) {
      console.error('DashboardMetrics - Error loading metrics:', error)
    }
  }, [wrappedMetrics, metrics, error])

  // Format percentage change for display
  const formatChange = (change?: { percentage: number; direction: string }) => {
    if (!change) return null

    const prefix = change.direction === 'up' ? '+' : change.direction === 'down' ? '-' : ''
    return `${prefix}${Math.abs(change.percentage).toFixed(1)}%`
  }

  // Display loading state
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium">Performance Metrics</h2>
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            <span>Loading metrics...</span>
          </div>
        </div>
      </div>
    )
  }

  // Display error state
  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-medium">Performance Metrics</h2>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            Retry
          </Button>
        </div>
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>Failed to load metrics: {error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Performance Metrics</h2>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-8 gap-1 hover:border-tangerine-500 hover:text-tangerine-600"
            >
              <CalendarIcon className="h-3.5 w-3.5" />
              <span>{selectedPeriod.label}</span>
              <ChevronDown className="h-3.5 w-3.5" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {timePeriods.map(period => (
              <DropdownMenuItem
                key={period.value}
                className={selectedPeriod.value === period.value ? 'bg-muted' : ''}
                onClick={() => setSelectedPeriod(period)}
              >
                {period.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card className="transition-all duration-200 hover:border-tangerine-500 hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Claims</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.open_claims}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.total_claims > 0
                  ? `${((metrics.open_claims / metrics.total_claims) * 100).toFixed(1)}% of total claims`
                  : 'No claims'}
              </p>
            </CardContent>
          </Card>

          <Card className="transition-all duration-200 hover:border-tangerine-500 hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
              <FileClock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.tasks_pending}</div>
              <p className="text-xs text-muted-foreground">
                {metrics.tasks_overdue > 0
                  ? `${metrics.tasks_overdue} require immediate attention`
                  : 'None overdue'}
              </p>
            </CardContent>
          </Card>

          <Card className="transition-all duration-200 hover:border-tangerine-500 hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Claims</CardTitle>
              <FilePlus2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.new_claims_last_period}</div>
              <p className="text-xs text-muted-foreground">
                {formatChange(metrics.total_payments_change) || 'This period'}
              </p>
            </CardContent>
          </Card>

          <Card className="transition-all duration-200 hover:border-tangerine-500 hover:shadow-md">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending FNOLs</CardTitle>
              <FileQuestion className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.fnols_pending}</div>
              <p className="text-xs text-muted-foreground">Awaiting review</p>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
