import React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { Claim } from '@/lib/api/types'
import { Skeleton } from '@/components/ui/skeleton'

interface FinancialsTableProps {
  claims: Claim[]
}

export function FinancialsTable({ claims }: FinancialsTableProps) {
  const formatCurrency = (value: string | number | null | undefined) => {
    const num = typeof value === 'string' ? parseFloat(value.replace(/[^0-9.-]+/g, '')) : value
    if (num === null || num === undefined || isNaN(num)) {
      return '$0.00'
    }
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(num)
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'AUTO':
        return 'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300'
      case 'PROPERTY':
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case 'GENERAL_LIABILITY':
        return 'bg-violet-100 text-violet-800 dark:bg-violet-900 dark:text-violet-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'INVESTIGATION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'SETTLEMENT':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'LITIGATION':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'RECOVERY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'CLOSED_SETTLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      case 'CLOSED_DENIED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  if (!claims || claims.length === 0) {
    return (
      <div className="rounded-md border p-4 text-center text-muted-foreground">
        No financial data available.
      </div>
    )
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Claim #</TableHead>
            <TableHead>Customer</TableHead>
            <TableHead>Claimant</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead className="text-right">Estimated Value</TableHead>
            <TableHead className="text-right">Total Paid</TableHead>
            <TableHead className="text-right">Recovery Received</TableHead>
            <TableHead className="w-10"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {claims.map(claim => {
            const financials = claim.financials
            const indemnityPaid = parseFloat(financials?.indemnity_paid || '0')
            const expensePaid = parseFloat(financials?.expense_paid || '0')
            const defensePaid = parseFloat(financials?.defense_paid || '0')
            const totalPaid = indemnityPaid + expensePaid + defensePaid

            return (
              <TableRow key={claim.id}>
                <TableCell className="font-medium">
                  <Link href={`/claims/${claim.claim_number}`} className="hover:underline">
                    {claim.claim_number}
                  </Link>
                </TableCell>
                <TableCell>
                  <Link
                    href={`/customers/${claim.customer_id}`}
                    className="text-primary hover:underline"
                  >
                    {claim.customer?.name || '-'}
                  </Link>
                </TableCell>
                <TableCell>{claim.claimant_name}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getTypeColor(claim.type)}>
                    {claim.type.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(claim.status)}>
                    {claim.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Link
                    href={`/claims/${claim.claim_number}/financials`}
                    className="hover:underline"
                  >
                    {formatCurrency(financials?.estimated_value)}
                  </Link>
                </TableCell>
                <TableCell className="text-right">{formatCurrency(totalPaid)}</TableCell>
                <TableCell className="text-right">
                  {formatCurrency(financials?.recovery_received)}
                </TableCell>
                <TableCell>
                  <Link href={`/claims/${claim.claim_number}/financials`}>
                    <Button size="icon" variant="ghost" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
