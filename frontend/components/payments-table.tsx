'use client'

import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import Link from 'next/link'
import { SortableHeader, type SortDirection } from '@/components/ui/sortable-header'

export function PaymentsTable() {
  const [sortColumn, setSortColumn] = useState<string | null>('payment_date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  // This would fetch payments data from the API
  // For now, we'll use mock data
  const payments = [
    {
      id: '1',
      claim_id: '5',
      claim_number: 'ACME-2023-0000110',
      claimant: '<PERSON>',
      payment_type: 'INDEMNITY',
      amount: '$20,000.00',
      amountValue: 20000,
      payee: '<PERSON>',
      payment_date: new Date('2023-03-10'),
      notes: 'Settlement payment for property damage',
    },
    {
      id: '2',
      claim_id: '3',
      claim_number: 'ACME-2023-0000119',
      claimant: '<PERSON>',
      payment_type: 'DEF<PERSON>SE_COST',
      amount: '$15,000.00',
      amountValue: 15000,
      payee: 'Johnson & Smith Law Firm',
      payment_date: new Date('2023-03-18'),
      notes: 'Legal representation fees',
    },
    {
      id: '3',
      claim_id: '3',
      claim_number: 'ACME-2023-0000119',
      claimant: 'Robert Johnson',
      payment_type: 'EXPENSE',
      amount: '$3,500.00',
      amountValue: 3500,
      payee: 'Expert Witness Services Inc.',
      payment_date: new Date('2023-03-16'),
      notes: 'Expert witness fees',
    },
    {
      id: '4',
      claim_id: '5',
      claim_number: 'ACME-2023-0000110',
      claimant: 'David Williams',
      payment_type: 'EXPENSE',
      amount: '$1,800.00',
      amountValue: 1800,
      payee: 'Property Damage Assessment Co.',
      payment_date: new Date('2023-03-07'),
      notes: 'Property damage assessment fees',
    },
    {
      id: '5',
      claim_id: '2',
      claim_number: 'ACME-2023-0000125',
      claimant: 'Maria Garcia',
      payment_type: 'EXPENSE',
      amount: '$1,200.00',
      amountValue: 1200,
      payee: 'Water Damage Restoration Inc.',
      payment_date: new Date('2023-03-22'),
      notes: 'Emergency water extraction services',
    },
    {
      id: '6',
      claim_id: '1',
      claim_number: 'ACME-2023-0000127',
      claimant: 'John Smith',
      payment_type: 'EXPENSE',
      amount: '$500.00',
      amountValue: 500,
      payee: 'Auto Towing Services',
      payment_date: new Date('2023-03-25'),
      notes: 'Vehicle towing fees',
    },
    {
      id: '7',
      claim_id: '4',
      claim_number: 'ACME-2023-0000114',
      claimant: 'Emily Chen',
      payment_type: 'EXPENSE',
      amount: '$350.00',
      amountValue: 350,
      payee: 'Accident Scene Investigation LLC',
      payment_date: new Date('2023-03-15'),
      notes: 'Accident scene investigation',
    },
  ]

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc')
      if (sortDirection === null) {
        setSortColumn(null)
      }
    } else {
      // Set new column and default to ascending
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const sortedPayments = [...payments].sort((a, b) => {
    if (!sortColumn || sortDirection === null) return 0

    const direction = sortDirection === 'asc' ? 1 : -1

    switch (sortColumn) {
      case 'payment_date':
        return direction * (a.payment_date.getTime() - b.payment_date.getTime())
      case 'claim_number':
        return direction * a.claim_number.localeCompare(b.claim_number)
      case 'claimant':
        return direction * a.claimant.localeCompare(b.claimant)
      case 'payment_type':
        return direction * a.payment_type.localeCompare(b.payment_type)
      case 'payee':
        return direction * a.payee.localeCompare(b.payee)
      case 'amount':
        return direction * (a.amountValue - b.amountValue)
      case 'notes':
        return direction * a.notes.localeCompare(b.notes)
      default:
        return 0
    }
  })

  const getPaymentTypeColor = (type: string) => {
    switch (type) {
      case 'INDEMNITY':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'EXPENSE':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'DEFENSE_COST':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <SortableHeader
              column="payment_date"
              label="Payment Date"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <SortableHeader
              column="claim_number"
              label="Claim #"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <SortableHeader
              column="claimant"
              label="Claimant"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <SortableHeader
              column="payment_type"
              label="Type"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <SortableHeader
              column="payee"
              label="Payee"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <SortableHeader
              column="amount"
              label="Amount"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
              className="text-right"
            />
            <SortableHeader
              column="notes"
              label="Notes"
              sortColumn={sortColumn}
              sortDirection={sortDirection}
              onSort={handleSort}
            />
            <TableHead className="w-10"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedPayments.map(payment => (
            <TableRow key={payment.id}>
              <TableCell>{payment.payment_date.toLocaleDateString()}</TableCell>
              <TableCell className="font-medium">
                <Link href={`/claims/${payment.claim_id}`} className="hover:underline">
                  {payment.claim_number}
                </Link>
              </TableCell>
              <TableCell>{payment.claimant}</TableCell>
              <TableCell>
                <Badge variant="outline" className={getPaymentTypeColor(payment.payment_type)}>
                  {payment.payment_type.replace('_', ' ')}
                </Badge>
              </TableCell>
              <TableCell>{payment.payee}</TableCell>
              <TableCell className="text-right">
                <Link
                  href={`/claims/${payment.claim_id}/financials#payment-${payment.id}`}
                  className="hover:underline"
                >
                  {payment.amount}
                </Link>
              </TableCell>
              <TableCell className="max-w-[200px] truncate" title={payment.notes}>
                {payment.notes}
              </TableCell>
              <TableCell>
                <Link href={`/claims/${payment.claim_id}/financials`}>
                  <Button size="icon" variant="ghost" className="h-8 w-8">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
