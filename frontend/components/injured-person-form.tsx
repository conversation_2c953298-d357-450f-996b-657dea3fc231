'use client'

import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import {
  InjuredPersonCreate,
  InjuredPersonUpdate,
  InjuredPerson,
  InjuredPersonType,
  IncidentReportStatus,
} from '@/lib/api/types'
import { useEffect } from 'react'
import { InjuryList } from '@/components/injury-list'

// Define the Zod schema for InjuredPerson form validation
// This combines fields from InjuredPersonCreate and common fields
const injuredPersonFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200).nullable().optional(),
  person_type: z.nativeEnum(InjuredPersonType).nullable().optional(),
  contact_info: z.string().max(200).nullable().optional(),
  age: z.number().int().positive().nullable().optional(),
  incident_location: z.string().max(500).nullable().optional(),
  incident_description: z.string().nullable().optional(),
  incident_report_status: z.nativeEnum(IncidentReportStatus).nullable().optional(),
  report_filer_name: z.string().max(200).nullable().optional(),
  report_filer_contact: z.string().max(200).nullable().optional(),
})

export type InjuredPersonFormValues = z.infer<typeof injuredPersonFormSchema>

interface InjuredPersonFormProps {
  claimId: string
  personId?: string // For edit mode
  initialData?: InjuredPerson | null // For pre-filling the form in edit mode
  onSubmit: (data: InjuredPersonFormValues) => Promise<void>
  onCancel: () => void
  isSaving: boolean
}

export function InjuredPersonForm({
  claimId,
  personId,
  initialData,
  onSubmit,
  onCancel,
  isSaving,
}: InjuredPersonFormProps) {
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<InjuredPersonFormValues>({
    resolver: zodResolver(injuredPersonFormSchema),
    defaultValues: initialData
      ? {
          name: initialData.name,
          person_type: initialData.person_type,
          contact_info: initialData.contact_info,
          age: initialData.age,
          incident_location: initialData.incident_location,
          incident_description: initialData.incident_description,
          incident_report_status: initialData.incident_report_status,
          report_filer_name: initialData.report_filer_name,
          report_filer_contact: initialData.report_filer_contact,
        }
      : {
          name: '',
          person_type: null,
          contact_info: '',
          age: null,
          incident_location: '',
          incident_description: '',
          incident_report_status: null,
          report_filer_name: '',
          report_filer_contact: '',
        },
  })

  useEffect(() => {
    if (initialData) {
      reset(initialData as InjuredPersonFormValues) // Type assertion might be needed if fields differ slightly
    }
  }, [initialData, reset])

  // Helper to format enum values for display in Select
  const formatEnumForDisplay = (enumValue: string) => {
    if (!enumValue) return ''
    return enumValue
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ')
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Injured Person Details</CardTitle>
          <CardDescription>
            {personId
              ? 'Update the details of the injured person.'
              : 'Enter the details for the new injured person.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Personal Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Personal Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="name">Full Name</Label>
                <Input id="name" {...register('name')} />
                {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
              </div>
              <div className="space-y-1">
                <Label htmlFor="person_type">Person Type</Label>
                <Controller
                  name="person_type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      defaultValue={field.value || undefined}
                    >
                      <SelectTrigger id="person_type">
                        <SelectValue placeholder="Select person type" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(InjuredPersonType).map(type => (
                          <SelectItem key={type} value={type}>
                            {formatEnumForDisplay(type)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.person_type && (
                  <p className="text-sm text-destructive">{errors.person_type.message}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="contact_info">Contact Info (Phone/Email)</Label>
                <Input id="contact_info" {...register('contact_info')} />
                {errors.contact_info && (
                  <p className="text-sm text-destructive">{errors.contact_info.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="age">Age</Label>
                <Input id="age" type="number" {...register('age', { valueAsNumber: true })} />
                {errors.age && <p className="text-sm text-destructive">{errors.age.message}</p>}
              </div>
            </div>
          </div>

          <Separator />

          {/* Incident Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Incident Information</h4>
            <div className="space-y-1">
              <Label htmlFor="incident_location">Incident Location</Label>
              <Input id="incident_location" {...register('incident_location')} />
              {errors.incident_location && (
                <p className="text-sm text-destructive">{errors.incident_location.message}</p>
              )}
            </div>
            <div className="space-y-1">
              <Label htmlFor="incident_description">Incident Description</Label>
              <Textarea id="incident_description" {...register('incident_description')} />
              {errors.incident_description && (
                <p className="text-sm text-destructive">{errors.incident_description.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Report Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Report Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-1">
                <Label htmlFor="incident_report_status">Incident Report Status</Label>
                <Controller
                  name="incident_report_status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      defaultValue={field.value || undefined}
                    >
                      <SelectTrigger id="incident_report_status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(IncidentReportStatus).map(status => (
                          <SelectItem key={status} value={status}>
                            {formatEnumForDisplay(status)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.incident_report_status && (
                  <p className="text-sm text-destructive">
                    {errors.incident_report_status.message}
                  </p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="report_filer_name">Report Filer Name</Label>
                <Input id="report_filer_name" {...register('report_filer_name')} />
                {errors.report_filer_name && (
                  <p className="text-sm text-destructive">{errors.report_filer_name.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="report_filer_contact">Report Filer Contact</Label>
                <Input id="report_filer_contact" {...register('report_filer_contact')} />
                {errors.report_filer_contact && (
                  <p className="text-sm text-destructive">{errors.report_filer_contact.message}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        {/* Placeholder for Injuries Section */}
        <Separator />
        <CardContent className="mt-6">
          <h4 className="text-md font-medium mb-2">Specific Injuries</h4>
          {/* Wrap InjuryList in a div that prevents event propagation to the parent form */}
          <div
            onClick={e => e.stopPropagation()}
            onSubmit={e => e.stopPropagation()}
            onKeyDown={e => {
              // If Enter is pressed inside this div, don't let it submit parent forms
              if (e.key === 'Enter') {
                e.stopPropagation()
              }
            }}
          >
            {claimId && personId && <InjuryList claimId={claimId} personId={personId} />}
            {!personId && (
              <div className="p-6 border rounded-md bg-muted/20 text-center">
                <p className="text-sm text-muted-foreground">
                  Save the injured person first to add specific injuries.
                </p>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="flex justify-end space-x-2 pt-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSaving}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving || !isDirty}>
            {isSaving ? 'Saving...' : personId ? 'Save Changes' : 'Add Injured Person'}
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}
