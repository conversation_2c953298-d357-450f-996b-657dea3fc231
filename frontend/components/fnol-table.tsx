'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ChevronRight, ChevronLeft } from 'lucide-react'
import Link from 'next/link'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { api } from '@/lib/api'
import { FNOLResponse, FNOLStatus, FNOLQueryParams, PaginatedResponse } from '@/lib/api/types'
import { useApi } from '@/hooks/useApi'
import { useEffect, useState } from 'react'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'

interface FnolTableProps {
  filter?: 'pending'
}

export function FnolTable({ filter }: FnolTableProps) {
  const { formatDateShort, formatRelativeDate } = useDateFormatter()
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 20
  const recordsPerPage = pageSize

  // Convert the current page (1-based) to skip parameter (0-based)
  // The API uses skip/limit instead of page/size
  const skipRecords = (currentPage - 1) * recordsPerPage

  const [queryParams, setQueryParams] = useState<FNOLQueryParams>({
    size: recordsPerPage,
  })

  useEffect(() => {
    let params: FNOLQueryParams = {
      size: recordsPerPage,
    }

    // No need to set status parameter here since we're using a dedicated endpoint for pending conversion

    setQueryParams(params)
  }, [filter, recordsPerPage])

  // Build the API fetch parameters including skip for pagination
  const fetchFnols = async () => {
    // Convert our page/size to the API's skip/limit

    // If we're filtering for pending conversion, use the dedicated method
    if (filter === 'pending') {
      const pendingParams = { page: currentPage, size: recordsPerPage }
      return api.fnol.getPendingConversionFNOLs(pendingParams)
    }

    // Otherwise use the standard FNOLs endpoint
    return api.fnol.getFNOLs({
      ...queryParams,
      page: currentPage,
    })
  }

  const {
    data: fnolData,
    isLoading,
    error,
    refetch,
  } = useApi<PaginatedResponse<FNOLResponse>>(fetchFnols, [queryParams, currentPage])

  // Handle page navigation
  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1)
    }
  }

  const handleNextPage = () => {
    // Check if there might be more pages based on current results
    if (fnolData && fnolData.items && fnolData.items.length === recordsPerPage) {
      setCurrentPage(currentPage + 1)
    }
  }

  // Function to determine FNOL status badge color
  const getStatusColor = (status: FNOLStatus | undefined) => {
    if (!status) return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'

    switch (status) {
      case FNOLStatus.CONVERTED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case FNOLStatus.REJECTED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case FNOLStatus.REVIEWED:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case FNOLStatus.NEW:
      default:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <Loading size="md" text="Loading FNOL reports..." centered />
      </div>
    )
  }

  if (error) {
    return (
      <EmptyState
        variant="error"
        title="Failed to load FNOL reports"
        description={error || 'An unexpected error occurred.'}
        actionLabel="Try Again"
        onAction={refetch}
      />
    )
  }

  const fnols = fnolData?.items ?? []
  const totalCount = fnolData?.total ?? 0
  const totalPages = Math.ceil(totalCount / recordsPerPage)

  if (fnols.length === 0) {
    return (
      <EmptyState
        variant="filtered"
        title="No FNOL reports found"
        description={
          filter === 'pending'
            ? 'No FNOLs are currently pending conversion.'
            : 'There are currently no FNOL reports.'
        }
      />
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-muted-foreground">
          {totalCount > 0 ? (
            <>
              Showing {fnols.length} of {totalCount}{' '}
              {filter === 'pending' ? 'pending conversion' : 'total'} FNOLs
            </>
          ) : (
            <>No FNOLs found</>
          )}
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>FNOL #</TableHead>
              <TableHead>Policy #</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Reported By</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Incident Date</TableHead>
              <TableHead>Reported</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fnols.map((fnol: FNOLResponse) => (
              <TableRow key={fnol.id}>
                <TableCell className="font-medium">
                  <Link href={`/fnol/${fnol.id}`} className="hover:underline">
                    {fnol.fnol_number}
                  </Link>
                </TableCell>
                <TableCell>{fnol.policy_number || '-'}</TableCell>
                <TableCell>
                  {fnol.customer ? (
                    <Link
                      href={`/customers/${fnol.customer_id}`}
                      className="text-primary hover:underline"
                    >
                      {fnol.customer.name}
                    </Link>
                  ) : (
                    <span className="text-muted-foreground">N/A</span>
                  )}
                </TableCell>
                <TableCell>
                  <div>
                    <div>{fnol.reported_by || 'N/A'}</div>
                  </div>
                </TableCell>
                <TableCell>{fnol.description || 'N/A'}</TableCell>
                <TableCell>
                  {fnol.incident_date ? formatDateShort(fnol.incident_date) : 'N/A'}
                </TableCell>
                <TableCell>{formatRelativeDate(fnol.created_at)}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(fnol.status)}>
                    {fnol.status || 'NEW'}
                  </Badge>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination controls */}
      {totalCount > recordsPerPage && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePreviousPage}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleNextPage}
              disabled={currentPage >= totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
