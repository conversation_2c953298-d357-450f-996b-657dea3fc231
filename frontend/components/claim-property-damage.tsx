'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { AlertCircle, Loader2, PlusCircle, Edit, Trash2, ExternalLink } from 'lucide-react'
import { DamagedPropertyAsset, PropertyAssetType } from '@/lib/api/types'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'

interface ClaimPropertyDamageProps {
  claimId: string // claimId is actually the claim number here
}

export function ClaimPropertyDamage({ claimId }: ClaimPropertyDamageProps) {
  const { toast } = useToast()
  const router = useRouter()

  const {
    data: damagedAssets,
    isLoading,
    error,
    refetch,
  } = useApi<DamagedPropertyAsset[]>(() => api.claims.getDamagedPropertyAssets(claimId), [claimId])

  const handleAddNewAsset = () => {
    router.push(`/claims/${claimId}/damaged-property-assets/new`)
  }

  const handleEditAsset = (assetId: string) => {
    router.push(`/claims/${claimId}/damaged-property-assets/${assetId}/edit`)
  }

  const handleViewDamageInstances = (assetId: string) => {
    router.push(`/claims/${claimId}/damaged-property-assets/${assetId}`)
  }

  const handleDeleteAsset = async (assetId: string) => {
    // TODO: Replace window.confirm with a shadcn/ui AlertDialog for better UX
    const confirmed = window.confirm('Are you sure you want to delete this damaged property asset?')
    if (!confirmed) {
      return
    }

    try {
      await api.claims.deleteDamagedPropertyAsset(claimId, assetId)
      toast({
        title: 'Success',
        description: 'Damaged property asset removed successfully.',
      })
      refetch()
    } catch (err) {
      console.error('Error deleting damaged property asset:', err)
      toast({
        title: 'Error',
        description: 'Failed to remove damaged property asset.',
        variant: 'destructive',
      })
    }
  }

  const formatAssetType = (type?: PropertyAssetType | null) => {
    if (!type) return 'Not specified'
    const typeStr = String(type)
    return typeStr
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading property damage information...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage = 'An unknown error occurred.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="flex flex-col items-center justify-center py-12 text-red-600">
        <AlertCircle className="h-8 w-8" />
        <p className="mt-2">Error loading property damage information: {errorMessage}</p>
        <Button onClick={() => refetch()} className="mt-4">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Property Damage</CardTitle>
        <Button onClick={handleAddNewAsset}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add Damaged Property
        </Button>
      </CardHeader>
      <CardContent>
        {damagedAssets && damagedAssets.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Asset Type</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Damage Instances</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {damagedAssets.map(asset => (
                <TableRow key={asset.id}>
                  <TableCell>{asset.name || 'N/A'}</TableCell>
                  <TableCell>{formatAssetType(asset.asset_type)}</TableCell>
                  <TableCell>{asset.location || 'N/A'}</TableCell>
                  <TableCell>{asset.owner_name || 'N/A'}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleViewDamageInstances(asset.id)}
                    >
                      <ExternalLink className="mr-1 h-4 w-4" />
                      {asset.damage_instances?.length || 0} Instances
                    </Button>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditAsset(asset.id)}
                      className="mr-2"
                    >
                      <Edit className="mr-1 h-4 w-4" /> Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteAsset(asset.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="mr-1 h-4 w-4" /> Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            <p>No property damage has been recorded for this claim.</p>
            <p className="mt-2">Click "Add Damaged Property" to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
