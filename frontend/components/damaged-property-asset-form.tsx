'use client'

import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import {
  DamagedPropertyAsset,
  DamagedPropertyAssetCreate,
  DamagedPropertyAssetUpdate,
  PropertyAssetType,
} from '@/lib/api/types'
import { useEffect } from 'react'

// Define the Zod schema for DamagedPropertyAsset form validation
const damagedPropertyAssetFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200).nullable().optional(),
  asset_type: z.nativeEnum(PropertyAssetType).nullable().optional(),
  description: z.string().max(1000).nullable().optional(),
  location: z.string().max(500).nullable().optional(),
  owner_name: z.string().max(200).nullable().optional(),
  estimated_value: z.number().nonnegative().nullable().optional(),
  purchase_date: z.string().nullable().optional(), // Date as ISO string
})

export type DamagedPropertyAssetFormValues = z.infer<typeof damagedPropertyAssetFormSchema>

interface DamagedPropertyAssetFormProps {
  claimId: string
  assetId?: string // For edit mode
  initialData?: DamagedPropertyAsset | null // For pre-filling the form in edit mode
  onSubmit: (data: DamagedPropertyAssetFormValues) => Promise<void>
  onCancel: () => void
  isSaving: boolean
}

export function DamagedPropertyAssetForm({
  claimId,
  assetId,
  initialData,
  onSubmit,
  onCancel,
  isSaving,
}: DamagedPropertyAssetFormProps) {
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<DamagedPropertyAssetFormValues>({
    resolver: zodResolver(damagedPropertyAssetFormSchema),
    defaultValues: initialData
      ? {
          name: initialData.name,
          asset_type: initialData.asset_type,
          description: initialData.description,
          location: initialData.location,
          owner_name: initialData.owner_name,
          estimated_value:
            initialData.estimated_value !== undefined
              ? typeof initialData.estimated_value === 'string'
                ? parseFloat(initialData.estimated_value)
                : initialData.estimated_value
              : null,
          purchase_date: initialData.purchase_date,
        }
      : {
          name: '',
          asset_type: null,
          description: '',
          location: '',
          owner_name: '',
          estimated_value: null,
          purchase_date: null,
        },
  })

  useEffect(() => {
    if (initialData) {
      const formattedData = {
        ...initialData,
        estimated_value:
          initialData.estimated_value !== undefined
            ? typeof initialData.estimated_value === 'string'
              ? parseFloat(initialData.estimated_value)
              : initialData.estimated_value
            : null,
      }
      reset(formattedData as DamagedPropertyAssetFormValues)
    }
  }, [initialData, reset])

  // Helper to format enum values for display in Select
  const formatEnumForDisplay = (enumValue: string) => {
    if (!enumValue) return ''
    return enumValue
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ')
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Damaged Property Asset Details</CardTitle>
          <CardDescription>
            {assetId
              ? 'Update the details of the damaged property asset.'
              : 'Enter the details for the new damaged property asset.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Asset Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Basic Asset Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="name">Asset Name</Label>
                <Input id="name" {...register('name')} />
                {errors.name && <p className="text-sm text-destructive">{errors.name.message}</p>}
              </div>
              <div className="space-y-1">
                <Label htmlFor="asset_type">Asset Type</Label>
                <Controller
                  name="asset_type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      defaultValue={field.value || undefined}
                    >
                      <SelectTrigger id="asset_type">
                        <SelectValue placeholder="Select asset type" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PropertyAssetType).map(type => (
                          <SelectItem key={type} value={type}>
                            {formatEnumForDisplay(type)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.asset_type && (
                  <p className="text-sm text-destructive">{errors.asset_type.message}</p>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="description">Description</Label>
              <Textarea id="description" {...register('description')} />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>
            <div className="space-y-1">
              <Label htmlFor="location">Location</Label>
              <Input id="location" {...register('location')} />
              {errors.location && (
                <p className="text-sm text-destructive">{errors.location.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Owner Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Owner Information</h4>
            <div className="space-y-1">
              <Label htmlFor="owner_name">Owner Name</Label>
              <Input id="owner_name" {...register('owner_name')} />
              {errors.owner_name && (
                <p className="text-sm text-destructive">{errors.owner_name.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Value & Purchase Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Value & Purchase Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="estimated_value">Estimated Value ($)</Label>
                <Input
                  id="estimated_value"
                  type="number"
                  step="0.01"
                  {...register('estimated_value', { valueAsNumber: true })}
                />
                {errors.estimated_value && (
                  <p className="text-sm text-destructive">{errors.estimated_value.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="purchase_date">Purchase Date</Label>
                <Input id="purchase_date" type="date" {...register('purchase_date')} />
                {errors.purchase_date && (
                  <p className="text-sm text-destructive">{errors.purchase_date.message}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onCancel} type="button" disabled={isSaving}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? 'Saving...' : assetId ? 'Update Asset' : 'Create Asset'}
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}
