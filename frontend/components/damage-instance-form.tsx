'use client'

import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { DamageInstance, PropertyDamageType, RepairStatus } from '@/lib/api/types'
import { useEffect } from 'react'

// Helper function to format ISO date to YYYY-MM-DD
const formatDateForInput = (isoDate: string | null | undefined): string | null => {
  if (!isoDate) return null
  try {
    // Extract just the YYYY-MM-DD portion from ISO date string
    return isoDate.substring(0, 10)
  } catch (error) {
    console.error('Error formatting date:', error)
    return null
  }
}

// Define the Zod schema for DamageInstance form validation
const damageInstanceFormSchema = z.object({
  damage_type: z.nativeEnum(PropertyDamageType).nullable().optional(),
  damage_description: z.string().max(1000).nullable().optional(),
  damage_severity: z.string().max(100).nullable().optional(),
  repair_status: z.nativeEnum(RepairStatus).nullable().optional(),
  estimated_repair_cost: z.string().nullable().optional(),
  repair_start_date: z.string().nullable().optional(), // Date as ISO string
  repair_completion_date: z.string().nullable().optional(), // Date as ISO string
  repair_description: z.string().max(1000).nullable().optional(),
  repair_vendor: z.string().max(200).nullable().optional(),
  repairer_contact: z.string().max(200).nullable().optional(),
})

export type DamageInstanceFormValues = z.infer<typeof damageInstanceFormSchema>

interface DamageInstanceFormProps {
  claimId: string
  assetId: string
  instanceId?: string // For edit mode
  initialData?: DamageInstance | null // For pre-filling the form in edit mode
  onSubmit: (data: DamageInstanceFormValues) => Promise<void>
  onCancel: () => void
  isSaving: boolean
}

export function DamageInstanceForm({
  claimId,
  assetId,
  instanceId,
  initialData,
  onSubmit,
  onCancel,
  isSaving,
}: DamageInstanceFormProps) {
  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors, isDirty },
  } = useForm<DamageInstanceFormValues>({
    resolver: zodResolver(damageInstanceFormSchema),
    defaultValues: initialData
      ? {
          damage_type: initialData.damage_type,
          damage_description: initialData.damage_description,
          damage_severity: initialData.damage_severity,
          repair_status: initialData.repair_status,
          estimated_repair_cost: initialData.estimated_repair_cost,
          repair_start_date: formatDateForInput(initialData.repair_start_date),
          repair_completion_date: formatDateForInput(initialData.repair_completion_date),
          repair_description: initialData.repair_description,
          repair_vendor: initialData.repair_vendor,
          repairer_contact: '',
        }
      : {
          damage_type: null,
          damage_description: '',
          damage_severity: '',
          repair_status: null,
          estimated_repair_cost: '',
          repair_start_date: null,
          repair_completion_date: null,
          repair_description: '',
          repair_vendor: '',
          repairer_contact: '',
        },
  })

  useEffect(() => {
    if (initialData) {
      reset({
        damage_type: initialData.damage_type,
        damage_description: initialData.damage_description,
        damage_severity: initialData.damage_severity,
        repair_status: initialData.repair_status,
        estimated_repair_cost: initialData.estimated_repair_cost,
        repair_start_date: formatDateForInput(initialData.repair_start_date),
        repair_completion_date: formatDateForInput(initialData.repair_completion_date),
        repair_description: initialData.repair_description,
        repair_vendor: initialData.repair_vendor,
        repairer_contact: '',
      })
    }
  }, [initialData, reset])

  // Helper to format enum values for display in Select
  const formatEnumForDisplay = (enumValue: string) => {
    if (!enumValue) return ''
    return enumValue
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ')
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Damage Instance Details</CardTitle>
          <CardDescription>
            {instanceId
              ? 'Update the details of the damage instance.'
              : 'Enter the details for the new damage instance.'}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Damage Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Damage Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="damage_type">Damage Type</Label>
                <Controller
                  name="damage_type"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      defaultValue={field.value || undefined}
                    >
                      <SelectTrigger id="damage_type">
                        <SelectValue placeholder="Select damage type" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(PropertyDamageType).map(type => (
                          <SelectItem key={type} value={type}>
                            {formatEnumForDisplay(type)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.damage_type && (
                  <p className="text-sm text-destructive">{errors.damage_type.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="damage_severity">Severity</Label>
                <Input id="damage_severity" {...register('damage_severity')} />
                {errors.damage_severity && (
                  <p className="text-sm text-destructive">{errors.damage_severity.message}</p>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="damage_description">Description</Label>
              <Textarea id="damage_description" {...register('damage_description')} />
              {errors.damage_description && (
                <p className="text-sm text-destructive">{errors.damage_description.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Repair Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Repair Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="repair_status">Repair Status</Label>
                <Controller
                  name="repair_status"
                  control={control}
                  render={({ field }) => (
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ''}
                      defaultValue={field.value || undefined}
                    >
                      <SelectTrigger id="repair_status">
                        <SelectValue placeholder="Select repair status" />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(RepairStatus).map(status => (
                          <SelectItem key={status} value={status}>
                            {formatEnumForDisplay(status)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
                {errors.repair_status && (
                  <p className="text-sm text-destructive">{errors.repair_status.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="estimated_repair_cost">Estimated Repair Cost ($)</Label>
                <Input
                  id="estimated_repair_cost"
                  type="number"
                  step="0.01"
                  {...register('estimated_repair_cost')}
                />
                {errors.estimated_repair_cost && (
                  <p className="text-sm text-destructive">{errors.estimated_repair_cost.message}</p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="repair_start_date">Repair Start Date</Label>
                <Input id="repair_start_date" type="date" {...register('repair_start_date')} />
                {errors.repair_start_date && (
                  <p className="text-sm text-destructive">{errors.repair_start_date.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="repair_completion_date">Repair End Date</Label>
                <Input
                  id="repair_completion_date"
                  type="date"
                  {...register('repair_completion_date')}
                />
                {errors.repair_completion_date && (
                  <p className="text-sm text-destructive">
                    {errors.repair_completion_date.message}
                  </p>
                )}
              </div>
            </div>
            <div className="space-y-1">
              <Label htmlFor="repair_description">Repair Notes</Label>
              <Textarea id="repair_description" {...register('repair_description')} />
              {errors.repair_description && (
                <p className="text-sm text-destructive">{errors.repair_description.message}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Repairer Information Section */}
          <div className="space-y-4">
            <h4 className="text-md font-medium">Repairer Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-1">
                <Label htmlFor="repair_vendor">Repairer Name</Label>
                <Input id="repair_vendor" {...register('repair_vendor')} />
                {errors.repair_vendor && (
                  <p className="text-sm text-destructive">{errors.repair_vendor.message}</p>
                )}
              </div>
              <div className="space-y-1">
                <Label htmlFor="repairer_contact">Repairer Contact</Label>
                <Input id="repairer_contact" {...register('repairer_contact')} />
                {errors.repairer_contact && (
                  <p className="text-sm text-destructive">{errors.repairer_contact.message}</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onCancel} type="button" disabled={isSaving}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving ? 'Saving...' : instanceId ? 'Update' : 'Create'}
          </Button>
        </CardFooter>
      </Card>
    </form>
  )
}
