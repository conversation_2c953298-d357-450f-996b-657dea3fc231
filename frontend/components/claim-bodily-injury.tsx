'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { AlertCircle, Loader2, PlusCircle, Edit, Trash2 } from 'lucide-react'
import { InjuredPerson, InjuredPersonType } from '@/lib/api/types'
import { useToast } from '@/components/ui/use-toast'
import { useRouter } from 'next/navigation'

interface ClaimBodilyInjuryProps {
  claimId: string // claimId is actually the claim number here
}

export function ClaimBodilyInjury({ claimId }: ClaimBodilyInjuryProps) {
  const { toast } = useToast()
  const router = useRouter()

  const {
    data: injuredPersons,
    isLoading,
    error,
    refetch,
  } = useApi<InjuredPerson[]>(() => api.claims.getInjuredPersons(claimId), [claimId])

  const handleAddNewInjuredPerson = () => {
    router.push(`/claims/${claimId}/injured-persons/new`)
  }

  const handleEditInjuredPerson = (personId: string) => {
    router.push(`/claims/${claimId}/injured-persons/${personId}/edit`)
  }

  const handleDeleteInjuredPerson = async (personId: string) => {
    // TODO: Replace window.confirm with a shadcn/ui AlertDialog for better UX
    const confirmed = window.confirm('Are you sure you want to delete this injured person?')
    if (!confirmed) {
      return
    }

    try {
      await api.claims.deleteInjuredPerson(claimId, personId)
      toast({
        title: 'Success',
        description: 'Injured person removed successfully.',
      })
      refetch()
    } catch (err) {
      console.error('Error deleting injured person:', err)
      toast({
        title: 'Error',
        description: 'Failed to remove injured person.',
        variant: 'destructive',
      })
    }
  }

  const formatInjuredPersonType = (type?: InjuredPersonType | null) => {
    if (!type) return 'Not specified'
    const typeStr = String(type)
    return typeStr
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading bodily injury information...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage = 'An unknown error occurred.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="flex flex-col items-center justify-center py-12 text-red-600">
        <AlertCircle className="h-8 w-8" />
        <p className="mt-2">Error loading bodily injury information: {errorMessage}</p>
        <Button onClick={() => refetch()} className="mt-4">
          Try Again
        </Button>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Bodily Injury</CardTitle>
        <Button onClick={handleAddNewInjuredPerson}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add Injured Person
        </Button>
      </CardHeader>
      <CardContent>
        {injuredPersons && injuredPersons.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Person Type</TableHead>
                <TableHead>Number of Injuries</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {injuredPersons.map(person => (
                <TableRow key={person.id}>
                  <TableCell>{person.name || 'N/A'}</TableCell>
                  <TableCell>{formatInjuredPersonType(person.person_type)}</TableCell>
                  <TableCell>{person.injuries?.length || 0}</TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditInjuredPerson(person.id)}
                      className="mr-2"
                    >
                      <Edit className="mr-1 h-4 w-4" /> Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteInjuredPerson(person.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="mr-1 h-4 w-4" /> Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="py-8 text-center text-muted-foreground">
            <p>No bodily injuries have been recorded for this claim.</p>
            <p className="mt-2">Click "Add Injured Person" to get started.</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
