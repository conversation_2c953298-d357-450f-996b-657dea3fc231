'use client'

import { useState } from 'react'
import { useApi, useApiMutation } from './useApi'
import { api } from '@/lib/api'
import { AttorneyC<PERSON>, AttorneyResponse, AttorneyUpdate } from '@/lib/api/types'

/**
 * Hook for fetching attorneys for a specific claim
 */
export function useAttorneys(claimId: string) {
  // Use the generic useApi hook to fetch attorneys
  return useApi(() => api.attorneys.getAttorneys(claimId), [claimId])
}

/**
 * Hook for attorney mutations (create, update, delete)
 */
export function useAttorneyMutation(claimId: string) {
  const [currentAttorney, setCurrentAttorney] = useState<AttorneyResponse | null>(null)

  // Create a new attorney
  const createMutation = useApiMutation<AttorneyResponse, AttorneyCreate>(attorneyData =>
    api.attorneys.createAttorney(claimId, attorneyData)
  )

  // Update an existing attorney
  const updateMutation = useApiMutation<AttorneyResponse, AttorneyUpdate>(attorneyData => {
    if (!currentAttorney?.id) {
      throw new Error('No attorney selected for update')
    }
    return api.attorneys.updateAttorney(claimId, currentAttorney.id, attorneyData)
  })

  // Delete an attorney
  const deleteMutation = useApiMutation<void, string>(attorneyId =>
    api.attorneys.deleteAttorney(claimId, attorneyId)
  )

  return {
    currentAttorney,
    setCurrentAttorney,
    createMutation,
    updateMutation,
    deleteMutation,
  }
}
