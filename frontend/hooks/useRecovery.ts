'use client'

import { useApi, useApiMutation } from './useApi'
import { api } from '@/lib/api'
import { RecoveryDetailsResponse, RecoveryStatus } from '@/lib/api/types/claim-base-types'
import { Claim } from '@/lib/api/types'

export function useRecovery(claimIdentifier: string) {
  const {
    data: recoveryData,
    isLoading,
    error,
    refetch,
  } = useApi<RecoveryDetailsResponse | null>(
    () => api.claims.getRecoveryDetails(claimIdentifier),
    [claimIdentifier]
  )

  const updateRecoveryStatusMutation = useApiMutation<Claim, RecoveryStatus>(
    (recoveryStatus: RecoveryStatus) =>
      api.claims.updateRecoveryStatus(claimIdentifier, recoveryStatus)
  )

  const updateCarrierDetailsMutation = useApiMutation<
    Claim,
    {
      carrierName?: string
      carrierContact?: string
      carrierClaimNumber?: string
      carrierAdjuster?: string
    }
  >(({ carrierName, carrierContact, carrierClaimNumber, carrierAdjuster }) =>
    api.claims.updateCarrierDetails(
      claimIdentifier,
      carrierName,
      carrierContact,
      carrierClaimNumber,
      carrierAdjuster
    )
  )

  const updateRecoveryAmountsMutation = useApiMutation<
    Claim,
    { expectedAmount?: string; receivedAmount?: string }
  >(({ expectedAmount, receivedAmount }) =>
    api.claims.updateRecoveryAmounts(claimIdentifier, expectedAmount, receivedAmount)
  )

  const updateRecoveryStatus = async (recoveryStatus: RecoveryStatus) => {
    const result = await updateRecoveryStatusMutation.mutate(recoveryStatus)
    refetch() // Refresh the recovery data after update
    return result
  }

  const updateCarrierDetails = async (
    carrierName?: string,
    carrierContact?: string,
    carrierClaimNumber?: string,
    carrierAdjuster?: string
  ) => {
    const result = await updateCarrierDetailsMutation.mutate({
      carrierName,
      carrierContact,
      carrierClaimNumber,
      carrierAdjuster,
    })
    refetch() // Refresh the recovery data after update
    return result
  }

  const updateRecoveryAmounts = async (expectedAmount?: string, receivedAmount?: string) => {
    const result = await updateRecoveryAmountsMutation.mutate({ expectedAmount, receivedAmount })
    refetch() // Refresh the recovery data after update
    return result
  }

  return {
    recoveryData,
    isLoading,
    error,
    refetch,
    updateRecoveryStatus,
    updateCarrierDetails,
    updateRecoveryAmounts,
    isUpdatingStatus: updateRecoveryStatusMutation.isLoading,
    isUpdatingCarrier: updateCarrierDetailsMutation.isLoading,
    isUpdatingAmounts: updateRecoveryAmountsMutation.isLoading,
    updateError:
      updateRecoveryStatusMutation.error ||
      updateCarrierDetailsMutation.error ||
      updateRecoveryAmountsMutation.error,
  }
}
