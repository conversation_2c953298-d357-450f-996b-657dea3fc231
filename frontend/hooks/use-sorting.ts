'use client'

import { useState } from 'react'

export type SortDirection = 'asc' | 'desc' | null

export function useSorting<T>(
  items: T[],
  defaultSortColumn: string | null = null,
  defaultSortDirection: SortDirection = 'asc',
  sortFunctions: Record<string, (a: T, b: T, direction: number) => number>
) {
  const [sortColumn, setSortColumn] = useState<string | null>(defaultSortColumn)
  const [sortDirection, setSortDirection] = useState<SortDirection>(defaultSortDirection)

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc')
      if (sortDirection === null) {
        setSortColumn(null)
      }
    } else {
      // Set new column and default to ascending
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const sortedItems = [...items].sort((a, b) => {
    if (!sortColumn || sortDirection === null) return 0

    const direction = sortDirection === 'asc' ? 1 : -1

    if (sortFunctions[sortColumn]) {
      return sortFunctions[sortColumn](a, b, direction)
    }

    return 0
  })

  return {
    sortedItems,
    sortColumn,
    sortDirection,
    handleSort,
  }
}
