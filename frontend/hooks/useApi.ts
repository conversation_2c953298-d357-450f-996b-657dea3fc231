'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

type ApiState<T> = {
  data: T | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
  mutate: () => void
}

/**
 * Generic hook for API data fetching with safety guards
 */
export function useApi<T>(fetchFn: () => Promise<T>, dependencies: any[] = []): ApiState<T> {
  // Start with isLoading false to avoid initial spinner without fetch
  const [data, setData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  // Track if component is mounted
  const isMounted = useRef(true)

  // Skip initial dependency-triggered render
  const skipInitialRender = useRef(true)

  // Store fetchFn in ref to avoid dependency changes
  const fetchFnRef = useRef(fetchFn)

  // Update ref when fetchFn changes
  useEffect(() => {
    fetchFnRef.current = fetchFn
  }, [fetchFn])

  // Set up mount/unmount logic
  useEffect(() => {
    isMounted.current = true
    return () => {
      isMounted.current = false
    }
  }, [])

  // The actual fetch implementation - kept very simple
  const fetchData = useCallback(async () => {
    if (!isMounted.current) return

    // Set loading state
    setIsLoading(true)
    setError(null)

    try {
      // Call the fetch function
      const result = await fetchFnRef.current()

      // Update state if still mounted
      if (isMounted.current) {
        setData(result)
        setIsLoading(false)
      }
    } catch (err) {
      // Handle errors if still mounted
      if (isMounted.current) {
        console.error('Error fetching data:', err)
        setError(String(err))
        setIsLoading(false)
      }
    }
  }, [])

  // Initial fetch on mount
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Fetch when dependencies change
  useEffect(() => {
    // Skip the initial render
    if (skipInitialRender.current) {
      skipInitialRender.current = false
      return
    }

    fetchData()
  }, [fetchData, ...dependencies])

  // Simple mutate function that just triggers a refetch
  const mutate = useCallback(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    isLoading,
    error,
    refetch: fetchData,
    mutate,
  }
}

/**
 * Hook for API mutations (create, update, delete)
 */
export function useApiMutation<T, P>(mutationFn: (params: P) => Promise<T>) {
  const [data, setData] = useState<T | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [isSuccess, setIsSuccess] = useState<boolean>(false)
  const [lastError, setLastError] = useState<Error | null>(null) // Store full error separately

  const mutate = useCallback(
    async (params: P) => {
      setIsLoading(true)
      setError(null)
      setLastError(null)
      setIsSuccess(false)

      try {
        const result = await mutationFn(params)
        setData(result)
        setIsSuccess(true)
        return result
      } catch (err) {
        // Store both the full error object and string message
        if (err instanceof Error) {
          setLastError(err)
          setError(err.message)
        } else {
          setLastError(null)
          setError('An error occurred')
        }
        throw err
      } finally {
        setIsLoading(false)
      }
    },
    [mutationFn]
  )

  // Alias for mutate, for more modern API patterns consistency
  const mutateAsync = mutate

  const reset = useCallback(() => {
    setData(null)
    setIsLoading(false)
    setError(null)
    setLastError(null)
    setIsSuccess(false)
  }, [])

  return {
    mutate,
    mutateAsync,
    data,
    isLoading,
    isPending: isLoading, // Alias for isLoading to match newer React Query patterns
    error,
    lastError, // Expose the full error object
    isSuccess,
    reset,
  }
}
