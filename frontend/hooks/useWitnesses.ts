'use client'

import { useState } from 'react'
import { useApi, useApiMutation } from './useApi'
import { api } from '@/lib/api'
import { WitnessCreate, WitnessResponse, WitnessUpdate } from '@/lib/api/types'

/**
 * Hook for fetching witnesses for a specific claim
 */
export function useWitnesses(claimId: string) {
  // Use the generic useApi hook to fetch witnesses
  return useApi(() => api.witnesses.getWitnesses(claimId), [claimId])
}

/**
 * Hook for witness mutations (create, update, delete)
 */
export function useWitnessMutation(claimId: string) {
  const [currentWitness, setCurrentWitness] = useState<WitnessResponse | null>(null)

  // Create a new witness
  const createMutation = useApiMutation<WitnessResponse, WitnessCreate>(witnessData =>
    api.witnesses.createWitness(claimId, witnessData)
  )

  // Update an existing witness
  const updateMutation = useApiMutation<
    WitnessResponse,
    { witnessId: string; data: WitnessUpdate }
  >(({ witnessId, data }) => api.witnesses.updateWitness(claimId, witnessId, data))

  // Delete a witness
  const deleteMutation = useApiMutation<void, string>(witnessId =>
    api.witnesses.deleteWitness(claimId, witnessId)
  )

  // Get a specific witness
  const getWitness = async (witnessId: string) => {
    try {
      const witness = await api.witnesses.getWitness(claimId, witnessId)
      setCurrentWitness(witness)
      return witness
    } catch (error) {
      console.error('Error fetching witness:', error)
      throw error
    }
  }

  // Helper for optimistic UI updates - you would use this with a state updater
  const removeWitnessFromList = (witnessId: string, witnesses: WitnessResponse[]) => {
    return witnesses.filter(witness => witness.id !== witnessId)
  }

  const addWitnessToList = (newWitness: WitnessResponse, witnesses: WitnessResponse[]) => {
    return [...witnesses, newWitness]
  }

  const updateWitnessInList = (updatedWitness: WitnessResponse, witnesses: WitnessResponse[]) => {
    return witnesses.map(witness => (witness.id === updatedWitness.id ? updatedWitness : witness))
  }

  return {
    // Mutations
    createWitness: createMutation.mutate,
    updateWitness: updateMutation.mutate,
    deleteWitness: deleteMutation.mutate,
    getWitness,

    // Current witness being edited
    currentWitness,
    setCurrentWitness,

    // Mutation states
    isCreating: createMutation.isLoading,
    createError: createMutation.error,
    isCreateSuccess: createMutation.isSuccess,

    isUpdating: updateMutation.isLoading,
    updateError: updateMutation.error,
    isUpdateSuccess: updateMutation.isSuccess,

    isDeleting: deleteMutation.isLoading,
    deleteError: deleteMutation.error,
    isDeleteSuccess: deleteMutation.isSuccess,

    // Helper functions for optimistic updates
    removeWitnessFromList,
    addWitnessToList,
    updateWitnessInList,

    // Reset functions
    resetCreate: createMutation.reset,
    resetUpdate: updateMutation.reset,
    resetDelete: deleteMutation.reset,
  }
}
