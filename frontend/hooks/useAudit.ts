'use client'

import { useState } from 'react'
import { useApi } from './useApi'
import { api } from '@/lib/api'
import {
  AuditSummaryResponse,
  AuditTrailResponse,
  EntityType,
  ChangeType,
  PaginatedAuditTrailResponse,
} from '@/lib/api/types'

/**
 * Hook to fetch audit entries for a claim with optional filtering
 */
export function useAuditEntries(
  claimId: string,
  filters?: {
    entity_type?: EntityType
    change_type?: ChangeType
    from_date?: string
    to_date?: string
    changed_by_id?: string
  }
) {
  const [filterState, setFilterState] = useState(filters || {})

  const apiCall = async () => {
    return api.audit.getAuditEntries(claimId, filterState)
  }

  const result = useApi(apiCall, [claimId, JSON.stringify(filterState)])

  return {
    ...result,
    filters: filterState,
    setFilters: setFilterState,
  }
}

/**
 * Hook to fetch audit summary for a claim
 */
export function useAuditSummary(claimId: string) {
  return useApi(() => api.audit.getAuditSummary(claimId), [claimId])
}
