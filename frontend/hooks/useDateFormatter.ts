import { useMemo } from 'react'
import {
  formatDate as utilFormatDate,
  formatDateTime as utilFormatDateTime,
  formatDateShort as utilFormatDateShort,
  formatRelativeDate as utilFormatRelativeDate,
  getUserTimezone,
} from '@/lib/utils'

/**
 * Hook that provides timezone-aware date formatting functions
 * Automatically detects and uses the user's current timezone
 */
export function useDateFormatter() {
  const userTimezone = useMemo(() => getUserTimezone(), [])

  const formatDate = useMemo(
    () => (dateString?: string) => utilFormatDate(dateString, userTimezone),
    [userTimezone]
  )

  const formatDateTime = useMemo(
    () => (dateString?: string) => utilFormatDateTime(dateString, userTimezone),
    [userTimezone]
  )

  const formatDateShort = useMemo(
    () => (dateString?: string) => utilFormatDateShort(dateString, userTimezone),
    [userTimezone]
  )

  const formatRelativeDate = useMemo(
    () => (dateString?: string) => utilFormatRelativeDate(dateString, userTimezone),
    [userTimezone]
  )

  return {
    formatDate,
    formatDateTime,
    formatDateShort,
    formatRelativeDate,
    userTimezone,
  }
}
