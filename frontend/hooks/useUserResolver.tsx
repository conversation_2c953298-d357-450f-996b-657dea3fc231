'use client'

import { useState, useCallback } from 'react'
import { api } from '@/lib/api'
import { UserResponse } from '@/lib/api/types'

export const useUserResolver = () => {
  const [userCache, setUserCache] = useState<Map<string, UserResponse>>(new Map())
  const [loadingUsers, setLoadingUsers] = useState<Set<string>>(new Set())

  const resolveUser = useCallback(
    async (userId: string): Promise<UserResponse | null> => {
      if (!userId) return null

      // Return cached user if available
      if (userCache.has(userId)) {
        return userCache.get(userId)!
      }

      // Avoid duplicate requests for the same user
      if (loadingUsers.has(userId)) {
        return null
      }

      try {
        setLoadingUsers(prev => new Set(prev).add(userId))
        console.log(`🔍 [useUserResolver] Resolving user: ${userId}`)

        const user = await api.users.getUserById(userId)

        setUserCache(prev => {
          const newCache = new Map(prev)
          newCache.set(userId, user)
          return newCache
        })

        console.log(`✅ [useUserResolver] Resolved user: ${user.first_name} ${user.last_name}`)
        return user
      } catch (error) {
        console.error(`❌ [useUserResolver] Failed to resolve user ${userId}:`, error)
        return null
      } finally {
        setLoadingUsers(prev => {
          const newSet = new Set(prev)
          newSet.delete(userId)
          return newSet
        })
      }
    },
    [userCache, loadingUsers]
  )

  const getUserDisplayName = useCallback(
    (userId?: string | null): string => {
      if (!userId) return 'Unknown'

      const user = userCache.get(userId)
      if (!user) {
        // If user is currently loading, show loading state
        if (loadingUsers.has(userId)) {
          return 'Loading...'
        }
        // Show first 8 characters of UUID as fallback until resolved
        return userId.length > 8 ? `User ${userId.substring(0, 8)}...` : userId
      }

      const fullName = `${user.first_name} ${user.last_name}`.trim()
      return fullName || user.email || 'Unknown User'
    },
    [userCache, loadingUsers]
  )

  const resolveUsers = useCallback(
    async (userIds: string[]): Promise<void> => {
      // Filter out already cached or loading users
      const uniqueIds = [...new Set(userIds)].filter(
        id => id && !userCache.has(id) && !loadingUsers.has(id)
      )

      if (uniqueIds.length === 0) return

      console.log(`🔍 [useUserResolver] Batch resolving ${uniqueIds.length} users`)

      // Resolve users in parallel
      const promises = uniqueIds.map(id => resolveUser(id))
      await Promise.allSettled(promises)

      console.log(`✅ [useUserResolver] Batch resolution complete`)
    },
    [resolveUser, userCache, loadingUsers]
  )

  return {
    resolveUser,
    resolveUsers,
    getUserDisplayName,
    userCache,
    isLoading: (userId: string) => loadingUsers.has(userId),
    cacheSize: userCache.size,
  }
}
