/**
 * HTTP Status Code Constants
 * Standardized status codes to replace hardcoded values across the codebase
 */
export const HTTP_STATUS = {
  // 2xx Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,

  // 3xx Redirection
  NOT_MODIFIED: 304,

  // 4xx Client Errors
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,

  // 5xx Server Errors
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const

/**
 * Mobile Breakpoint Constants
 * Standardized breakpoints to replace hardcoded values
 */
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
} as const

/**
 * API Constants
 * Common API-related constants
 */
export const API_CONSTANTS = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  REQUEST_TIMEOUT: 30000, // 30 seconds
} as const

/**
 * Toast Constants
 * Toast notification settings
 */
export const TOAST_CONSTANTS = {
  LIMIT: 1,
  REMOVE_DELAY: 1000000, // Very long delay for manual dismissal
  AUTO_DISMISS_DELAY: 5000, // 5 seconds for auto-dismiss
} as const

// Type exports for better TypeScript support
export type HttpStatusCode = (typeof HTTP_STATUS)[keyof typeof HTTP_STATUS]
export type Breakpoint = (typeof BREAKPOINTS)[keyof typeof BREAKPOINTS]
