import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Get the user's current timezone
 */
export function getUserTimezone(): string {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone
  } catch {
    return 'UTC' // fallback
  }
}

/**
 * Format a date string to a more readable format in user's timezone
 */
export function formatDate(dateString?: string, userTimezone?: string): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    const timezone = userTimezone || getUserTimezone()

    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      timeZone: timezone,
    }).format(date)
  } catch (e) {
    console.error('Error formatting date:', e)
    return dateString
  }
}

/**
 * Format a date string with time in user's timezone
 */
export function formatDateTime(dateString?: string, userTimezone?: string): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    const timezone = userTimezone || getUserTimezone()

    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: timezone,
    }).format(date)
  } catch (e) {
    console.error('Error formatting datetime:', e)
    return dateString
  }
}

/**
 * Format a date string to short format (MMM dd, yyyy) in user's timezone
 */
export function formatDateShort(dateString?: string, userTimezone?: string): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    const timezone = userTimezone || getUserTimezone()

    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      timeZone: timezone,
    }).format(date)
  } catch (e) {
    console.error('Error formatting date short:', e)
    return dateString
  }
}

/**
 * Format a relative date (e.g., "2 hours ago") in user's timezone
 */
export function formatRelativeDate(dateString?: string, userTimezone?: string): string {
  if (!dateString) return 'N/A'

  try {
    const date = new Date(dateString)
    const timezone = userTimezone || getUserTimezone()

    // Calculate the time difference accounting for timezone
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    // Use Intl.RelativeTimeFormat for proper formatting
    const rtf = new Intl.RelativeTimeFormat('en-US', { numeric: 'auto' })

    if (diffInSeconds < 60) {
      return rtf.format(-diffInSeconds, 'second')
    } else if (diffInSeconds < 3600) {
      return rtf.format(-Math.floor(diffInSeconds / 60), 'minute')
    } else if (diffInSeconds < 86400) {
      return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour')
    } else if (diffInSeconds < 2592000) {
      return rtf.format(-Math.floor(diffInSeconds / 86400), 'day')
    } else if (diffInSeconds < ********) {
      return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month')
    } else {
      return rtf.format(-Math.floor(diffInSeconds / ********), 'year')
    }
  } catch (e) {
    console.error('Error formatting relative date:', e)
    return dateString
  }
}

/**
 * Format a number as currency
 */
export function formatCurrency(amount?: number): string {
  if (amount === undefined || amount === null) return 'N/A'

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}
