/**
 * API Logging Utilities
 * Provides standardized logging for API routes with consistent formatting and log levels
 */

import { NextRequest } from 'next/server'

// Log levels for different types of messages
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

// Interface for log entries
export interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  method?: string
  path?: string
  requestId?: string
  duration?: number
  statusCode?: number
  data?: any
}

/**
 * Generate a unique request ID for tracing
 * @returns Unique ID string
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

/**
 * Format a log entry
 * @param entry Log entry object
 * @returns Formatted log string
 */
function formatLogEntry(entry: LogEntry): string {
  const { timestamp, level, message, method, path, requestId, duration, statusCode } = entry

  let logMessage = `[${timestamp}] [${level}]`

  if (requestId) {
    logMessage += ` [${requestId}]`
  }

  if (method && path) {
    logMessage += ` [${method} ${path}]`
  }

  if (statusCode !== undefined) {
    logMessage += ` [${statusCode}]`
  }

  if (duration !== undefined) {
    logMessage += ` [${duration}ms]`
  }

  logMessage += `: ${message}`

  return logMessage
}

/**
 * Log a message with appropriate formatting
 * @param level Log level
 * @param message Log message
 * @param data Additional data to include
 */
export function log(level: LogLevel, message: string, data?: any): void {
  const timestamp = new Date().toISOString()
  const entry: LogEntry = {
    timestamp,
    level,
    message,
    data,
  }

  const formattedMessage = formatLogEntry(entry)

  switch (level) {
    case LogLevel.DEBUG:
      console.debug(formattedMessage, data ? data : '')
      break
    case LogLevel.INFO:
      console.log(formattedMessage, data ? data : '')
      break
    case LogLevel.WARN:
      console.warn(formattedMessage, data ? data : '')
      break
    case LogLevel.ERROR:
      console.error(formattedMessage, data ? data : '')
      break
  }
}

/**
 * Log an incoming API request
 * @param request NextRequest object
 * @param routePath API route path
 * @returns Request ID for correlation
 */
export function logRequest(request: NextRequest, routePath: string): string {
  const requestId = generateRequestId()
  const method = request.method
  const searchParams = request.nextUrl.searchParams.toString()
  const path = routePath + (searchParams ? `?${searchParams}` : '')

  log(LogLevel.INFO, 'API request received', {
    requestId,
    method,
    path,
    headers: Object.fromEntries(request.headers.entries()),
  })

  return requestId
}

/**
 * Log successful API response
 * @param requestId Request ID for correlation
 * @param method HTTP method
 * @param path API path
 * @param statusCode HTTP status code
 * @param duration Request duration in milliseconds
 */
export function logResponse(
  requestId: string,
  method: string,
  path: string,
  statusCode: number,
  duration: number
): void {
  log(LogLevel.INFO, 'API response sent', {
    requestId,
    method,
    path,
    statusCode,
    duration,
  })
}

/**
 * Log API error
 * @param requestId Request ID for correlation
 * @param method HTTP method
 * @param path API path
 * @param statusCode HTTP status code
 * @param error Error object or message
 * @param duration Request duration in milliseconds
 */
export function logError(
  requestId: string,
  method: string,
  path: string,
  statusCode: number,
  error: any,
  duration: number
): void {
  log(LogLevel.ERROR, 'API error occurred', {
    requestId,
    method,
    path,
    statusCode,
    error: error instanceof Error ? error.message : error,
    stack: error instanceof Error ? error.stack : undefined,
    duration,
  })
}

/**
 * Create a timer function to measure request duration
 * @returns Function that returns elapsed time in ms when called
 */
export function createTimer(): () => number {
  const start = Date.now()
  return () => Date.now() - start
}
