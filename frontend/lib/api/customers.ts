import { ApiClient } from './client'
import { CustomerResponse, CustomerCreate, CustomerUpdate, PaginatedResponse } from './types'

/**
 * Customers API service for handling customer-related endpoints
 */
export class CustomersApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a paginated list of customers with optional filtering
   * @param params - Query parameters for filtering, pagination, and sorting
   */
  async getCustomers(params?: {
    active_only?: boolean
    page?: number
    size?: number
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }): Promise<PaginatedResponse<CustomerResponse>> {
    const queryParams = new URLSearchParams()

    if (params?.active_only !== undefined) {
      queryParams.append('active_only', String(params.active_only))
    }

    if (params?.page !== undefined) {
      queryParams.append('page', String(params.page))
    }

    if (params?.size !== undefined) {
      queryParams.append('size', String(params.size))
    }

    if (params?.sort_by) {
      queryParams.append('sort_by', params.sort_by)
    }

    if (params?.sort_order) {
      queryParams.append('sort_order', params.sort_order)
    }

    const endpoint = `/api/v1/customers${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<PaginatedResponse<CustomerResponse>>(endpoint)
  }

  /**
   * Get a single customer by its UUID
   * @param customerId - The UUID of the customer
   */
  async getCustomerById(customerId: string): Promise<CustomerResponse> {
    const endpoint = `/api/v1/customers/${customerId}`
    return this.client.request<CustomerResponse>(endpoint)
  }

  /**
   * Create a new customer
   * @param customerData - Data for the new customer
   */
  async createCustomer(customerData: CustomerCreate): Promise<CustomerResponse> {
    return this.client.request<CustomerResponse>('/api/v1/customers', {
      method: 'POST',
      body: JSON.stringify(customerData),
    })
  }

  /**
   * Update an existing customer
   * @param customerId - The UUID of the customer to update
   * @param customerData - The fields to update
   */
  async updateCustomer(
    customerId: string,
    customerData: CustomerUpdate
  ): Promise<CustomerResponse> {
    const endpoint = `/api/v1/customers/${customerId}`
    return this.client.request<CustomerResponse>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(customerData),
    })
  }

  /**
   * Delete a customer
   * @param customerId - The UUID of the customer to delete
   */
  async deleteCustomer(customerId: string): Promise<void> {
    const endpoint = `/api/v1/customers/${customerId}`
    await this.client.request<void>(endpoint, {
      method: 'DELETE',
    })
  }
}
