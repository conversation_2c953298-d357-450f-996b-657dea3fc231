'use client'

import { Api<PERSON>lient } from './client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Attorney<PERSON><PERSON>, AttorneyUpdate } from './types'

/**
 * Attorneys API service for handling attorney-related endpoints
 */
export class AttorneysApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get all attorneys for a claim
   * @param claimId UUID or claim number
   */
  async getAttorneys(claimId: string): Promise<AttorneyResponse[]> {
    try {
      const endpoint = `/api/v1/claims/${claimId}/attorneys`
      const attorneys = await this.client.request<AttorneyResponse[]>(endpoint)
      return attorneys
    } catch (error: any) {
      // If 404 or similar, return empty array
      if (error?.status === 404 || error?.message?.includes('404')) {
        return []
      }
      // Re-throw other errors
      throw error
    }
  }

  /**
   * Get a specific attorney by ID
   * @param claimId UUID or claim number
   * @param attorneyId UUID of the attorney
   */
  async getAttorney(claimId: string, attorneyId: string): Promise<AttorneyResponse> {
    const endpoint = `/api/v1/claims/${claimId}/attorneys/${attorneyId}`
    return this.client.request<AttorneyResponse>(endpoint)
  }

  /**
   * Create a new attorney for a claim
   * @param claimId UUID or claim number
   * @param attorneyData Attorney data to create
   */
  async createAttorney(claimId: string, attorneyData: AttorneyCreate): Promise<AttorneyResponse> {
    const endpoint = `/api/v1/claims/${claimId}/attorneys`
    return this.client.request<AttorneyResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify(attorneyData),
    })
  }

  /**
   * Update an existing attorney
   * @param claimId UUID or claim number
   * @param attorneyId UUID of the attorney to update
   * @param attorneyData Updated attorney data
   */
  async updateAttorney(
    claimId: string,
    attorneyId: string,
    attorneyData: AttorneyUpdate
  ): Promise<AttorneyResponse> {
    const endpoint = `/api/v1/claims/${claimId}/attorneys/${attorneyId}`
    return this.client.request<AttorneyResponse>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(attorneyData),
    })
  }

  /**
   * Delete an attorney
   * @param claimId UUID or claim number
   * @param attorneyId UUID of the attorney to delete
   */
  async deleteAttorney(claimId: string, attorneyId: string): Promise<void> {
    const endpoint = `/api/v1/claims/${claimId}/attorneys/${attorneyId}`
    await this.client.request<void>(endpoint, {
      method: 'DELETE',
    })
  }
}
