'use client'

import { TokenResponse, ApiError } from './types'
import { jwtDecode } from 'jwt-decode'
import Cookies from 'js-cookie'

// Constants
const AUTH_TOKEN_KEY = 'claimentine_auth_token'
const REFRESH_TOKEN_KEY = 'claimentine_refresh_token'

// Interface for decoded JWT token payload
interface JWTPayload {
  sub: string
  name?: string
  email?: string
  role?: string
  permissions?: string[]
  exp: number
  iat: number
}

/**
 * Base API client for handling requests, authentication and token management
 */
export class ApiClient {
  /**
   * Get the stored auth token, first checking cookies, then localStorage
   */
  getAuthToken(): string | null {
    // First try to get token from cookie
    if (typeof document !== 'undefined') {
      const cookieToken = Cookies.get('access_token')
      if (cookieToken) {
        return cookieToken
      }
    }

    // Fall back to localStorage for backward compatibility
    if (typeof window !== 'undefined') {
      return localStorage.getItem(AUTH_TOKEN_KEY)
    }
    return null
  }

  /**
   * Get the stored refresh token, first checking cookies, then localStorage
   */
  getRefreshToken(): string | null {
    // First try to get token from cookie
    if (typeof document !== 'undefined') {
      const cookieToken = Cookies.get('refresh_token')
      if (cookieToken) {
        return cookieToken
      }
    }

    // Fall back to localStorage for backward compatibility
    if (typeof window !== 'undefined') {
      return localStorage.getItem(REFRESH_TOKEN_KEY)
    }
    return null
  }

  /**
   * Store authentication tokens in localStorage
   * Note: This is a fallback method as tokens are now primarily stored in HTTP-only cookies
   */
  setTokens(tokenResponse: TokenResponse): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(AUTH_TOKEN_KEY, tokenResponse.access_token)
      localStorage.setItem(REFRESH_TOKEN_KEY, tokenResponse.refresh_token)
    }
  }

  /**
   * Clear stored tokens from both localStorage and cookies
   */
  clearTokens(): void {
    if (typeof window !== 'undefined') {
      // Clear localStorage tokens
      localStorage.removeItem(AUTH_TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)

      // Clear cookies (even though HTTP-only cookies need server to clear them)
      if (typeof document !== 'undefined') {
        Cookies.remove('access_token')
        Cookies.remove('refresh_token')
      }
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getAuthToken()
    if (!token) return false

    // Check if token is valid and not expired
    try {
      const decoded = this.decodeToken(token)
      const currentTime = Math.floor(Date.now() / 1000)

      // If token is expired or about to expire in the next minute
      if (decoded.exp <= currentTime) {
        console.log('Token expired, clearing tokens')
        this.clearTokens()
        return false
      }

      return true
    } catch (error) {
      console.error('Invalid token:', error)
      this.clearTokens()
      return false
    }
  }

  /**
   * Get token expiration time in seconds
   */
  getTokenExpiration(): number | null {
    const token = this.getAuthToken()
    if (!token) return null

    try {
      const decoded = this.decodeToken(token)
      return decoded.exp
    } catch (error) {
      return null
    }
  }

  /**
   * Check if token is about to expire (within next 5 minutes)
   */
  isTokenExpiringSoon(): boolean {
    const exp = this.getTokenExpiration()
    if (!exp) return false

    const currentTime = Math.floor(Date.now() / 1000)
    const fiveMinutesInSeconds = 5 * 60

    return exp - currentTime < fiveMinutesInSeconds
  }

  /**
   * Decode JWT token
   */
  decodeToken(token: string): JWTPayload {
    return jwtDecode<JWTPayload>(token)
  }

  /**
   * Get current user information from token
   */
  getCurrentUser(): { id: string; name?: string; email?: string; role?: string } | null {
    const token = this.getAuthToken()
    if (!token) return null

    try {
      const decoded = this.decodeToken(token)
      return {
        id: decoded.sub,
        name: decoded.name,
        email: decoded.email,
        role: decoded.role,
      }
    } catch (error) {
      console.error('Error getting user from token:', error)
      return null
    }
  }

  /**
   * Login user and store tokens
   */
  async login(
    username: string,
    password: string,
    rememberMe: boolean = false
  ): Promise<TokenResponse> {
    const formData = new URLSearchParams()
    formData.append('grant_type', 'password')
    formData.append('username', username)
    formData.append('password', password)
    formData.append('scope', '')
    formData.append('remember_me', rememberMe ? 'true' : 'false')

    try {
      console.log('Attempting to login via Next.js API route')
      // Use relative URL to Next.js API route instead of directly connecting to backend
      const response = await fetch('/api/v1/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
      })

      let data
      try {
        data = await response.json()
      } catch (e) {
        console.error('Failed to parse JSON response:', e)
        throw new Error(
          'Invalid response from server. Please check if the API is running correctly.'
        )
      }

      if (!response.ok) {
        throw new Error(data.error || 'Authentication failed')
      }

      this.setTokens(data)
      return data
    } catch (error) {
      console.error('Login request failed:', error)
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw new Error('Cannot connect to the authentication service. Please try again later.')
      }
      throw error
    }
  }

  /**
   * Logout user and clear tokens
   */
  async logout(): Promise<void> {
    const refreshToken = this.getRefreshToken()

    try {
      console.log('Attempting to logout via Next.js API route')
      await fetch('/api/v1/auth/logout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: refreshToken ? JSON.stringify({ refresh_token: refreshToken }) : '{}',
      })
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      this.clearTokens()
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(): Promise<TokenResponse> {
    const refreshToken = this.getRefreshToken()

    // We can attempt a refresh without a local token since the cookie might still be valid
    // But log a warning if no token is found
    if (!refreshToken) {
      console.warn(
        'No refresh token found in localStorage or cookies, but attempting refresh anyway'
      )
    }

    try {
      console.log('Attempting to refresh token via Next.js API route')
      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken || '' }),
        credentials: 'include', // Include cookies
      })

      let data
      try {
        // Check if there's content to parse
        const contentType = response.headers.get('content-type')

        // Clone response for checking content length
        const clonedResponse = response.clone()
        const responseText = await clonedResponse.text()

        if (
          contentType &&
          contentType.includes('application/json') &&
          responseText.trim().length > 0
        ) {
          data = JSON.parse(responseText)
        } else {
          // No content or not JSON
          console.log(`Empty or non-JSON response: ${responseText}. Status: ${response.status}`)
          data = { detail: response.status === 404 ? 'Resource not found' : 'No content' }
        }
      } catch (e: any) {
        console.error('Failed to parse JSON response:', e)
        // Don't throw if the response was successful but empty
        if (response.ok) {
          data = {}
        } else if (response.status === 404) {
          // Handle 404 errors specially
          data = { detail: 'Resource not found' }
        } else {
          throw new Error(`Invalid response from server: ${e.message || 'Unknown error'}`)
        }
      }

      if (!response.ok) {
        this.clearTokens()
        throw new Error(data.error || 'Token refresh failed')
      }

      this.setTokens(data)
      return data
    } catch (error) {
      console.error('Token refresh failed:', error)
      this.clearTokens()
      throw error
    }
  }

  /**
   * Make a request to the API
   */
  async request<T>(
    path: string,
    options: RequestInit = {},
    customHeaders: HeadersInit = {}
  ): Promise<T> {
    console.log('🔍 [ApiClient] request - Starting request to:', path)

    // Check auth before request
    const isAuth = path.startsWith('/api/v1/auth') && path !== '/api/v1/auth/refresh'
    const isAuthenticated = this.isAuthenticated()

    console.log('🔍 [ApiClient] request - Auth context:', {
      isAuthEndpoint: isAuth,
      isAuthenticated: isAuthenticated,
      hasToken: !!this.getAuthToken(),
    })

    // Normalize the path to ensure it starts with a slash but doesn't end with one
    const normalizedPath = path.startsWith('/') ? path : `/${path}`
    const finalPath =
      normalizedPath.endsWith('/') && normalizedPath.length > 1
        ? normalizedPath.slice(0, -1)
        : normalizedPath

    // Use the normalized path directly since we're using relative URLs
    const url = finalPath

    // Check if token refresh is needed
    if (this.isAuthenticated() && this.isTokenExpiringSoon()) {
      console.log('🔍 [ApiClient] request - Token is about to expire, attempting to refresh')
      try {
        await this.refreshToken()
      } catch (refreshError) {
        console.error('❌ [ApiClient] request - Token refresh failed:', refreshError)
        // Continue with request using existing token
      }
    }

    // Prepare headers
    const headers = new Headers(customHeaders)

    if (this.isAuthenticated()) {
      const token = this.getAuthToken()
      if (!token) {
        throw new Error('Authentication required')
      }
      headers.set('Authorization', `Bearer ${token}`)
    }

    // Don't set Content-Type if:
    // 1. It's already set in headers, or
    // 2. It's a GET request (doesn't need Content-Type), or
    // 3. There's no body, or
    // 4. The body is FormData (browser will set the correct Content-Type with boundary)
    if (
      !headers.has('Content-Type') &&
      options.method !== 'GET' &&
      options.body &&
      !(options.body instanceof FormData)
    ) {
      headers.set('Content-Type', 'application/json')
    }

    const config: RequestInit = {
      ...options,
      headers,
      credentials: 'include', // Include cookies in all requests
    }

    try {
      console.log(`🔍 [ApiClient] request - Making request to Next.js API route: ${url}`)

      // Log request body for debugging, but be careful not to log sensitive data
      if (options.body && options.method !== 'GET') {
        if (options.body instanceof FormData) {
          console.log('🔍 [ApiClient] request - Request body is FormData, not logging content')
        } else if (typeof options.body === 'string') {
          try {
            // Try to parse and log as JSON if it's a JSON string (hide sensitive data)
            const bodyObj = JSON.parse(options.body)
            const sanitizedBody = { ...bodyObj }
            // Hide sensitive fields
            if (sanitizedBody.password) sanitizedBody.password = '***REDACTED***'
            if (sanitizedBody.token) sanitizedBody.token = '***REDACTED***'
            console.log('🔍 [ApiClient] request - Request body:', sanitizedBody)
          } catch {
            // Not JSON, don't log the body
            console.log('🔍 [ApiClient] request - Request has non-JSON body')
          }
        }
      }

      let response

      try {
        response = await fetch(url, config)
        console.log(`🔍 [ApiClient] request - Response received from ${url}:`, {
          status: response.status,
          ok: response.ok,
          contentType: response.headers.get('content-type'),
        })
      } catch (fetchError) {
        console.error('❌ [ApiClient] request - Fetch operation failed:', fetchError)
        if (fetchError instanceof TypeError && fetchError.message === 'Failed to fetch') {
          throw new Error('Cannot connect to the API. Please check your internet connection.')
        }
        throw fetchError
      }

      // Handle authentication errors
      if (response.status === 401) {
        console.log('Request returned 401 Unauthorized')

        // Only attempt token refresh if this isn't already a refresh token request
        if (options.method !== 'POST' || !path.includes('/api/v1/auth/refresh')) {
          console.log('Attempting to refresh the token and retry the request')
          try {
            await this.refreshToken()

            // Get new auth token
            const newToken = this.getAuthToken()
            if (newToken) {
              // Update Authorization header with new token
              headers.set('Authorization', `Bearer ${newToken}`)
              config.headers = headers

              // Retry the request
              console.log('Retrying request with new token')
              response = await fetch(url, config)
            }
          } catch (refreshError) {
            console.error('Token refresh failed during 401 handling:', refreshError)
            this.clearTokens()
            throw new Error('Session expired. Please login again.')
          }
        } else {
          // If this is a refresh token request or non-auth request that returned 401
          this.clearTokens()
          throw new Error('Authentication failed. Please login again.')
        }
      }

      return this.processResponse<T>(response, url)
    } catch (error) {
      console.error('ApiClient.request - Fetch error:', { path, error })
      throw this.handleError(error, 500, url)
    }
  }

  /**
   * Process a fetch API response
   */
  private async processResponse<T>(response: Response, url: string): Promise<T> {
    if (!response.ok) {
      // Only log non-404 errors to reduce noise for endpoints that intentionally return 404 for missing data
      if (response.status === 404 && (url.includes('/audit') || url.includes('/financials'))) {
        // Silently fail for audit and financials endpoints - they may not exist yet (normal business logic)
      } else {
        console.error(`❌ Error response from ${url}: ${response.status} ${response.statusText}`)
      }

      // Check if the response is from our fallback mechanism
      const dataSource = response.headers.get('x-data-source')
      if (dataSource === 'local-mock' && process.env.NODE_ENV === 'development') {
        console.warn(`⚠️ Using mock data for ${url}. Backend unavailable or not implemented yet.`)

        // Attempt to extract the mock data from the response
        try {
          const responseData = await response.json()
          console.log('Mock data:', responseData)
          return responseData as T
        } catch (e) {
          console.error('Failed to parse mock response', e)
        }
      }

      // Extract detailed error from response if available
      let detailedError = null
      try {
        detailedError = await this.parseErrorResponse(response)
      } catch (e) {
        console.error('Failed to parse error response', e)
      }

      // Create a user-friendly error with status code and include status in the error object
      const error = this.handleError(detailedError, response.status, url)
      ;(error as any).status = response.status // Add status code to error object for API services to check
      throw error
    }

    // For successful responses
    try {
      // Empty response check
      const contentType = response.headers.get('content-type')
      if (
        response.status === 204 ||
        !contentType ||
        contentType.includes('text/plain') ||
        (contentType.includes('application/json') && (await response.clone().text()) === '')
      ) {
        // Return empty object for JSON endpoints expecting a response
        if (contentType && contentType.includes('application/json')) {
          return {} as T
        }
        // Otherwise return null for empty responses
        return null as T
      }

      // JSON response
      if (contentType && contentType.includes('application/json')) {
        return await response.json()
      }

      // Other content types (binary, text, etc.)
      return (await response.text()) as unknown as T
    } catch (error) {
      console.error(`Failed to process response for ${url}`, error)
      throw this.handleError(
        error,
        500,
        url + ' - Error processing response. The server may have returned invalid data.'
      )
    }
  }

  /**
   * Parse an error response
   */
  private async parseErrorResponse(response: Response): Promise<any> {
    try {
      const contentType = response.headers.get('content-type') || ''
      if (contentType.includes('application/json')) {
        const text = await response.text()
        if (text && text.trim() !== '') {
          return JSON.parse(text)
        }
      }
      // If no JSON error or empty, create a simple error object
      return {
        detail: response.statusText || `Error: ${response.status}`,
        status_code: response.status,
      }
    } catch (error) {
      return {
        detail: `Failed to parse error response: ${error}`,
        status_code: response.status,
      }
    }
  }

  /**
   * Handle an error response
   */
  private handleError(error: any, statusCode: number = 500, url: string = ''): Error {
    // Format error message
    let detailedMessage = `Error ${statusCode}`
    if (error && error.detail) {
      if (Array.isArray(error.detail)) {
        // Pydantic validation errors often come as an array of objects in 'detail'
        detailedMessage = error.detail
          .map((err: any) => {
            const field = err.loc ? err.loc.join(' -> ') : 'unknown_field'
            // Ensure err.input is stringified safely
            const inputString =
              typeof err.input === 'object' ? JSON.stringify(err.input) : err.input
            return `${field}: ${err.msg} (input: ${inputString})`
          })
          .join('; ')
      } else if (typeof error.detail === 'object') {
        detailedMessage = JSON.stringify(error.detail)
      } else {
        detailedMessage = error.detail.toString()
      }
    } else if (error && error.message) {
      detailedMessage = error.message
    }

    // Log the structured detail if it's an array of validation errors
    if (error && Array.isArray(error.detail)) {
      console.error(`API Validation Errors (${statusCode}) on ${url}:`)
      error.detail.forEach((validationError: any, index: number) => {
        console.error(`  Error ${index + 1}:`, JSON.parse(JSON.stringify(validationError))) // Log a clean copy
      })
    } else {
      // Don't log 404 errors for endpoints that intentionally return 404 for missing data
      if (statusCode === 404 && (url.includes('/audit') || url.includes('/financials'))) {
        // Silently handle 404s for audit and financials endpoints - they may not exist yet (normal business logic)
      } else {
        console.error(`API Error (${statusCode}) on ${url}:`, detailedMessage, error)
      }
    }

    const apiError = new ApiError(detailedMessage, statusCode)

    // Attach the original error data for debugging
    apiError.data = error

    // Add request URL for debugging
    if (url) {
      apiError.url = url
    }

    return apiError
  }
}
