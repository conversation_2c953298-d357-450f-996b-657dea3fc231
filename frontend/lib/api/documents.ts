import { ApiClient } from './client'
import {
  Document,
  DocumentList,
  DocumentQueryParams,
  DocumentType,
  DocumentUploadUrlRequest,
  DocumentUploadUrlResponse,
  DocumentDownloadUrlResponse,
} from './types'

/**
 * Documents API service for handling document-related endpoints
 */
export class DocumentsApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a list of all documents with optional filters
   */
  async listAllDocuments(params?: DocumentQueryParams): Promise<DocumentList> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/documents?${queryParams.toString()}`
    return this.client.request<DocumentList>(endpoint)
  }

  /**
   * Get a document by ID without knowing its claim ID
   * This is a helper method that fetches all documents and finds the one with the matching ID
   */
  async getDocumentByIdOnly(documentId: string): Promise<Document> {
    // Get a large number of documents to increase the chance of finding the right one
    const allDocsResponse = await this.listAllDocuments({ limit: 100 })
    const targetDocument = allDocsResponse.items.find(doc => doc.id === documentId)

    if (!targetDocument) {
      throw new Error('Document not found')
    }

    // Once we have the document with its claim_id, get the full details
    return this.getDocumentById(targetDocument.claim_id, documentId)
  }

  /**
   * Get documents for a specific claim
   */
  async listClaimDocuments(
    claimId: string,
    params?: Omit<DocumentQueryParams, 'claim_id'>
  ): Promise<DocumentList> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/claims/${claimId}/documents?${queryParams.toString()}`
    return this.client.request<DocumentList>(endpoint)
  }

  /**
   * Get a document by ID for a specific claim
   */
  async getDocumentById(claimId: string, documentId: string): Promise<Document> {
    return this.client.request<Document>(`/api/v1/claims/${claimId}/documents/${documentId}`)
  }

  /**
   * Update document metadata
   */
  async updateDocument(
    claimId: string,
    documentId: string,
    data: {
      name?: string
      type?: DocumentType
      description?: string
    }
  ): Promise<Document> {
    return this.client.request<Document>(`/api/v1/claims/${claimId}/documents/${documentId}`, {
      method: 'PATCH',
      body: JSON.stringify(data),
    })
  }

  /**
   * Delete a document
   */
  async deleteDocument(claimId: string, documentId: string): Promise<void> {
    await this.client.request<void>(`/api/v1/claims/${claimId}/documents/${documentId}`, {
      method: 'DELETE',
    })
  }

  /**
   * Get document download URL
   */
  async getDocumentDownloadUrl(
    claimId: string,
    documentId: string
  ): Promise<DocumentDownloadUrlResponse> {
    return this.client.request<DocumentDownloadUrlResponse>(
      `/api/v1/claims/${claimId}/documents/${documentId}/download-url`
    )
  }

  /**
   * Get a pre-signed URL for uploading a document
   */
  async getDocumentUploadUrl(
    claimId: string,
    request: DocumentUploadUrlRequest
  ): Promise<DocumentUploadUrlResponse> {
    return this.client.request<DocumentUploadUrlResponse>(
      `/api/v1/claims/${claimId}/documents/upload-url`,
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    )
  }

  /**
   * Upload a document directly (multipart form)
   */
  async uploadDocumentDirect(
    claimId: string,
    file: File,
    documentType: DocumentType,
    documentName?: string,
    description?: string
  ): Promise<Document> {
    console.log(`🔍 [DocumentsApi] Starting direct upload for claim ${claimId}, file: ${file.name}`)

    const formData = new FormData()
    formData.append('file', file)
    formData.append('document_type', documentType)

    if (documentName) {
      formData.append('document_name', documentName)
    }

    if (description) {
      formData.append('document_description', description)
    }

    console.log(
      `🔍 [DocumentsApi] Prepared FormData for upload, sending request to /api/v1/claims/${claimId}/documents/direct-upload`
    )

    try {
      // Note: Don't set Content-Type header manually,
      // the browser will automatically set the correct multipart/form-data Content-Type with boundary
      const result = await this.client.request<Document>(
        `/api/v1/claims/${claimId}/documents/direct-upload`,
        {
          method: 'POST',
          body: formData,
          headers: {}, // Empty headers object to ensure we don't override browser-set headers
        }
      )

      console.log(`✅ [DocumentsApi] Upload successful for file: ${file.name}`)
      return result
    } catch (error) {
      console.error(`❌ [DocumentsApi] Upload failed for file: ${file.name}`, error)
      throw error
    }
  }
}
