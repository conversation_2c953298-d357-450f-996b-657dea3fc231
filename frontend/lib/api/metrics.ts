import { ApiClient } from './client'
import { DashboardMetricsResponse } from './types'

/**
 * Metrics API service for handling metrics-related endpoints
 */
export class MetricsApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get dashboard metrics
   * @param period Time period for metrics (e.g., 'last_30_days', 'last_6_months')
   * @param comparePeriod Whether to compare with previous period
   * @param customerId Optional filter by customer ID
   * @param userId Optional filter by user ID
   */
  async getDashboardMetrics(
    period: string = 'last_30_days',
    comparePeriod: boolean = true,
    customerId?: string,
    userId?: string
  ): Promise<DashboardMetricsResponse> {
    // Build query parameters
    const params = new URLSearchParams()
    params.append('period', period)
    params.append('compare_period', comparePeriod.toString())

    if (customerId) {
      params.append('customer_id', customerId)
    }

    if (userId) {
      params.append('user_id', userId)
    }

    const endpoint = `/api/v1/metrics/dashboard?${params.toString()}`
    console.log('Metrics API - Requesting dashboard metrics:', endpoint)

    try {
      const result = await this.client.request<DashboardMetricsResponse>(endpoint)
      console.log('Metrics API - Received dashboard metrics response:', result)
      return result
    } catch (error) {
      console.error('Metrics API - Error fetching dashboard metrics:', error)
      throw error
    }
  }
}
