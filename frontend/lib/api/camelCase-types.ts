/**
 * This file contains camelCase versions of the API types defined in types.ts
 * These types should be used in frontend components for consistent property naming
 */

import { ClaimType, ClaimStatus, TaskStatus, TaskPriority, DocumentType, FNOLStatus } from './types'

// API Authentication Types
export interface TokenResponse {
  accessToken: string
  refreshToken: string
  tokenType: string
}

export interface LoginRequest {
  username: string
  password: string
}

// Bodily Injury Details
export interface BodilyInjuryDetails {
  id: string
  claimId: string
  injuredPartyName?: string | null
  injuredPartyContact?: string | null
  injuryDescription?: string | null
  injuryDate?: string | null
  treatmentStatus?: string | null
  treatmentDetails?: string | null
  medicalExpenses?: number | null
  lastContactDate?: string | null
  attorneyInvolved?: boolean | null
  attorneyName?: string | null
  attorneyContact?: string | null
  lawsuitFiled?: boolean | null
  lawsuitDate?: string | null
  lawsuitJurisdiction?: string | null
  lawsuitStatus?: string | null
  createdAt: string
  updatedAt: string
}

// Auto Details Type
export interface AutoDetails {
  id: string
  claimId: string
  vehicleYear?: string | null
  vehicleMake?: string | null
  vehicleModel?: string | null
  vehicleVin?: string | null
  vehicleLicensePlate?: string | null
  vehicleState?: string | null
  driverName?: string | null
  driverLicenseNumber?: string | null
  driverState?: string | null
  driverRelationship?: string | null
  pointOfImpact?: string | null
  airbagsDeployed?: boolean | null
  vehicleDriveable?: boolean | null
  towed?: boolean | null
  towLocation?: string | null
  incidentType?: string | null
  collisionType?: string | null
  passengerCount?: number | null
  passengerDetails?: string | null
  cargoTheft?: boolean | null
  cargoDescription?: string | null
  hasPropertyDamage?: boolean | null
  propertyDamage?: string | null
  createdAt: string
  updatedAt: string
  bodilyInjuryDetails?: BodilyInjuryDetails
}

// Vehicle Details Type
export interface VehicleDetails {
  year: string
  make: string
  model: string
  vin: string
}

// Customer Type
export interface Customer {
  id: string
  name: string
  prefix: string
  contactName?: string
  contactEmail?: string
  contactPhone?: string
  address?: string
  createdAt: string
  updatedAt: string
}

// Status History Type
export interface StatusHistory {
  id: string
  fromStatus: string | null
  toStatus: string
  reason: string
  changedBy: string
  createdAt: string
}

// Witness Type
export interface Witness {
  id: string
  claimId: string
  name: string
  contact?: string | null
  statement?: string | null
  statementDate?: string | null
  relation?: string | null
  createdAt: string
  updatedAt: string
}

// Attorney Type
export interface Attorney {
  id: string
  claimId: string
  name: string
  attorneyType: string
  firmName?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
  createdAt: string
  updatedAt: string
  claimNumber?: string | null
}

// Claim Type
export interface Claim {
  id: string
  claimNumber: string
  claimantName: string
  claimantEmail?: string | null
  claimantPhone?: string | null
  type: ClaimType
  status: ClaimStatus
  dateOfLoss?: string
  incidentDate?: string
  incidentLocation?: string | null
  jurisdiction?: string | null
  totalAmount?: number
  assignedToId?: string | null
  supervisorId?: string | null
  recoveryStatus?: string
  carrierName?: string | null
  carrierContact?: string | null
  assignee?: string
  customerId: string
  customer?: {
    id: string
    name: string
    prefix: string
    description?: string
    active: boolean
    createdAt: string
    updatedAt: string
  }
  description?: string
  createdAt: string
  updatedAt: string
  closedAt?: string | null
  createdById?: string
  autoDetails?: AutoDetails
  vehicleDetails?: VehicleDetails
  documents?: Document[]
  notes?: Note[]
  tasks?: Task[]
  statusHistory?: StatusHistory[]
  witnesses?: Witness[]
  attorneys?: Attorney[]
}

// Task Type
export interface Task {
  id: string
  hrId: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  dueDate?: string
  assignee?: string
  claimId?: string
  claimNumber?: string
  createdAt: string
  updatedAt: string
}

// Document Type
export interface Document {
  id: string
  name: string
  type: DocumentType
  description?: string | null
  claimId: string
  claimNumber?: string | null
  filePath: string
  fileSize: number
  mimeType: string
  uploadedBy?: string | null
  createdAt: string
  updatedAt: string
}

export interface DocumentList {
  items: Document[]
  total: number
}

export interface DocumentUploadUrlRequest {
  fileName: string
  contentType: string
  documentType: DocumentType
}

export interface DocumentUploadUrlResponse {
  uploadUrl: string
  documentId: string
  expiresAt: string
}

export interface DocumentDownloadUrlResponse {
  downloadUrl: string
  expiresAt: string
  shouldDownload?: boolean
}

// FNOL Type
export interface FNOL {
  id: string
  number: string
  reporterName: string
  reporterContact: string
  incidentDate: string
  incidentDescription: string
  status: FNOLStatus
  customerId: string
  createdAt: string
  updatedAt: string
}

// Note Type
export interface Note {
  id: string
  content: string
  claimId: string
  authorId?: string
  author?: string
  authorEmail?: string
  createdAt: string
  updatedAt: string
}

export interface NoteCreate {
  content: string
  claimId: string
}

export interface NoteUpdate {
  content: string
}

// Common types for pagination
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// Error response
export interface ErrorResponseData {
  detail: string
  statusCode?: number
  errorCode?: string
  additionalInfo?: Record<string, unknown>
  [key: string]: unknown
}
