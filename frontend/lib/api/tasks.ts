'use client'

import { <PERSON>pi<PERSON>lient } from './client'
import {
  Task,
  TaskQueryParams,
  BackendPaginatedResponse,
  ClaimTaskCreateRequestBody,
} from './types'

/**
 * Tasks API service for handling task-related endpoints
 */
export class TasksApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a paginated list of tasks with optional filters
   */
  async getTasks(params?: TaskQueryParams): Promise<BackendPaginatedResponse<Task>> {
    const queryParams = new URLSearchParams()

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<BackendPaginatedResponse<Task>>(endpoint)
  }

  /**
   * Get a task by ID (UUID or hr_id)
   * Prefer using hr_id when available
   */
  async getTaskById(id: string): Promise<Task> {
    console.log(`🔍 Fetching task by ID: ${id}`)

    // Try to use the identifier directly - the backend will determine if it's a UUID or hr_id
    try {
      const task = await this.client.request<Task>(`/api/v1/tasks/${id}`)
      console.log(`✅ Successfully fetched task: ${task.hr_id}`)
      return task
    } catch (error) {
      console.error(`❌ Error fetching task ${id}:`, error)
      throw error
    }
  }

  /**
   * Create a new task
   */
  async createTask(taskData: Partial<Task>): Promise<Task> {
    return this.client.request<Task>('/api/v1/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    })
  }

  /**
   * Create a new task for a specific claim
   */
  async createClaimTask(claimUuid: string, taskData: ClaimTaskCreateRequestBody): Promise<Task> {
    return this.client.request<Task>(`/api/v1/claims/${claimUuid}/tasks`, {
      method: 'POST',
      body: JSON.stringify(taskData),
    })
  }

  /**
   * Update an existing task
   * Always uses hr_id if available for the update
   */
  async updateTask(task: Task | string, taskData: Partial<Task>): Promise<Task> {
    // Get the task identifier - prefer hr_id over id
    const taskId = typeof task === 'string' ? task : task.hr_id || task.id

    console.log(`🔄 Updating task ${taskId}`)
    return this.client.request<Task>(`/api/v1/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(taskData),
    })
  }

  /**
   * Assign or unassign a task to/from a user
   * Always uses hr_id if available for the assignment
   */
  async assignTask(task: Task | string, assigneeId: string | null): Promise<Task> {
    // Get the task identifier - prefer hr_id over id
    const taskId = typeof task === 'string' ? task : task.hr_id || task.id

    console.log(`👤 Assigning task ${taskId} to ${assigneeId || 'unassigned'}`)
    return this.client.request<Task>(`/api/v1/tasks/${taskId}/assign`, {
      method: 'PATCH',
      body: JSON.stringify({ assignee_id: assigneeId }),
    })
  }

  /**
   * Delete a task
   * Always uses hr_id if available for the deletion
   */
  async deleteTask(task: Task | string): Promise<void> {
    // Get the task identifier - prefer hr_id over id
    const taskId = typeof task === 'string' ? task : task.hr_id || task.id

    console.log(`🗑️ Deleting task ${taskId}`)
    await this.client.request<void>(`/api/v1/tasks/${taskId}`, {
      method: 'DELETE',
    })
  }

  /**
   * Get tasks associated with a claim
   */
  async getTasksByClaim(
    claimId: string,
    params?: Omit<TaskQueryParams, 'claim_id'>
  ): Promise<BackendPaginatedResponse<Task>> {
    const queryParams = new URLSearchParams()
    queryParams.append('claim_id', claimId)

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<BackendPaginatedResponse<Task>>(endpoint)
  }

  /**
   * Update task status
   * Always uses hr_id if available for the status update
   */
  async updateTaskStatus(task: Task | string, status: string): Promise<Task> {
    // Get the task identifier - prefer hr_id over id
    const taskId = typeof task === 'string' ? task : task.hr_id || task.id

    console.log(`🔄 Updating status of task ${taskId} to ${status}`)
    return this.client.request<Task>(`/api/v1/tasks/${taskId}/status`, {
      method: 'PATCH',
      body: JSON.stringify({ status }),
    })
  }

  /**
   * Get tasks assigned to a specific user
   */
  async getTasksByAssignee(
    assignee: string,
    params?: Omit<TaskQueryParams, 'assignee'>
  ): Promise<BackendPaginatedResponse<Task>> {
    const queryParams = new URLSearchParams()
    queryParams.append('assignee', assignee)

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/tasks${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<BackendPaginatedResponse<Task>>(endpoint)
  }
}
