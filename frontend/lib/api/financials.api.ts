import { ApiClient } from './client'
import {
  ClaimFinancialsInDB,
  PaymentCreate,
  PaymentList,
  PaymentResponse,
  ReserveHistoryInDB,
  ReserveUpdate,
  ClaimFinancialsCreate,
} from './types' // Corrected path

export class FinancialsApi {
  private client: ApiClient

  public constructor(client: ApiClient) {
    // Added constructor to accept ApiClient
    this.client = client
  }

  public async getClaimFinancials(claimId: string): Promise<ClaimFinancialsInDB | null> {
    try {
      // claimId should be the claim number, not UUID
      console.log(`API: Fetching financials for claim: ${claimId}`)
      const response = await this.client.request<ClaimFinancialsInDB>( // Changed this.request to this.client.request
        `/api/v1/claims/${claimId}/financials`,
        {
          method: 'GET',
        }
      )
      console.log(`API: Successfully retrieved financials for claim: ${claimId}`, response)
      return response
    } catch (error: any) {
      // Check if this is a 404 - which means "no financials exist for this claim"
      if (error.status === 404) {
        console.log(`API: No financials exist for claim ${claimId} (404)`)
        return null
      }

      // For all other errors, log and rethrow
      console.error(`API: Error fetching financials for claim ${claimId}:`, error)
      throw error
    }
  }

  public async updateClaimReserve(
    claimId: string,
    data: ReserveUpdate
  ): Promise<ClaimFinancialsInDB | null> {
    try {
      console.log(`API: Updating reserve for claim ${claimId}`, data)
      const response = await this.client.request<ClaimFinancialsInDB>( // Changed this.request to this.client.request
        `/api/v1/claims/${claimId}/financials/reserve`,
        {
          method: 'PUT',
          body: JSON.stringify(data),
        }
      )
      console.log(`API: Successfully updated reserve for claim: ${claimId}`)
      return response
    } catch (error: any) {
      // Check if this is a 404 - which means "no financials exist for this claim"
      if (error.status === 404) {
        console.error(
          `API: Attempted to update reserve for claim ${claimId}, but no financials record exists (404). This should have been caught earlier.`
        )
        // Throw a specific error because the calling context should have already checked for financials existence
        throw new Error(
          `Cannot update reserve: No financials record found for claim ${claimId}. Please ensure financials are created first.`
        )
      }

      console.error(`API: Error updating reserve for claim ${claimId}:`, error)
      throw error // Re-throw other errors (including potentially ApiError instances)
    }
  }

  public async getClaimReserveHistory(claimId: string): Promise<ReserveHistoryInDB[]> {
    try {
      // claimId should be the claim number, not UUID
      // Assuming the proxy returns the array directly
      return this.client.request<ReserveHistoryInDB[]>( // Changed this.request to this.client.request
        `/api/v1/claims/${claimId}/financials/reserve-history`,
        {
          method: 'GET',
        }
      )
    } catch (error: any) {
      // Check if this is a 404 - which means "no financials exist for this claim"
      if (error.status === 404) {
        console.log(`API: No reserve history for claim ${claimId} (404)`)
        return [] // Return empty array for history if none exists
      }

      console.error(`API: Error fetching reserve history for claim ${claimId}:`, error)
      throw error
    }
  }

  public async listClaimPayments(
    claimId: string,
    params?: { skip?: number; limit?: number }
  ): Promise<PaymentList> {
    try {
      // Build query string
      const queryParams = new URLSearchParams()
      if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString())
      if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString())

      // Get payments
      const url = `/api/v1/claims/${claimId}/financials/payments${
        queryParams.toString() ? `?${queryParams.toString()}` : ''
      }`

      return this.client.request<PaymentList>(url, {
        // Changed this.request to this.client.request
        method: 'GET',
      })
    } catch (error: any) {
      // Check if this is a 404 - which means "no financials exist for this claim"
      if (error.status === 404) {
        console.log(`API: No payments exist for claim ${claimId} (404)`)
        return { items: [], total: 0 } // Return empty payment list
      }

      console.error(`API: Error listing payments for claim ${claimId}:`, error)
      throw error
    }
  }

  public async addClaimPayment(claimId: string, data: PaymentCreate): Promise<PaymentResponse> {
    try {
      return this.client.request<PaymentResponse>(`/api/v1/claims/${claimId}/financials/payments`, {
        // Changed this.request to this.client.request
        method: 'POST',
        body: JSON.stringify(data),
      })
    } catch (error: any) {
      // Check if this is a 404 - which means "no financials exist for this claim"
      if (error.status === 404) {
        console.log(`API: Cannot add payment - no financials exist for claim ${claimId} (404)`)
        throw new Error(
          `Cannot add payment: No financials exist for claim ${claimId}. Please add a reserve first.`
        )
      }

      console.error(`API: Error adding payment for claim ${claimId}:`, error)
      throw error
    }
  }

  public async createFinancialsWithInitialReserve(
    claimId: string,
    data: ClaimFinancialsCreate
  ): Promise<ClaimFinancialsInDB> {
    try {
      console.log(`API: Creating financials for claim ${claimId} with initial reserve`, data)
      const response = await this.client.request<ClaimFinancialsInDB>( // Changed this.request to this.client.request
        `/api/v1/claims/${claimId}/financials`,
        {
          method: 'POST',
          body: JSON.stringify(data),
        }
      )
      console.log(`API: Successfully created financials for claim: ${claimId}`, response)
      return response
    } catch (error: any) {
      console.error(
        `API: Error creating financials with initial reserve for claim ${claimId}:`,
        error
      )
      throw error
    }
  }
}
