'use client'

import { ApiClient } from './client'
import { WitnessR<PERSON>ponse, WitnessCreate, WitnessUpdate } from './types'

/**
 * Witnesses API service for handling witness-related endpoints
 */
export class WitnessesApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get all witnesses for a claim
   * @param claimId UUID or claim number
   */
  async getWitnesses(claimId: string): Promise<WitnessResponse[]> {
    try {
      const endpoint = `/api/v1/claims/${claimId}/witnesses`
      const witnesses = await this.client.request<WitnessResponse[]>(endpoint)
      return witnesses
    } catch (error: any) {
      // If 404 or similar, return empty array
      if (error?.status === 404 || error?.message?.includes('404')) {
        return []
      }
      // Re-throw other errors
      throw error
    }
  }

  /**
   * Get a specific witness by ID
   * @param claimId UUID or claim number
   * @param witnessId UUID of the witness
   */
  async getWitness(claimId: string, witnessId: string): Promise<WitnessResponse> {
    const endpoint = `/api/v1/claims/${claimId}/witnesses/${witnessId}`
    return this.client.request<WitnessResponse>(endpoint)
  }

  /**
   * Create a new witness for a claim
   * @param claimId UUID or claim number
   * @param witnessData Witness data to create
   */
  async createWitness(claimId: string, witnessData: WitnessCreate): Promise<WitnessResponse> {
    const endpoint = `/api/v1/claims/${claimId}/witnesses`
    return this.client.request<WitnessResponse>(endpoint, {
      method: 'POST',
      body: JSON.stringify(witnessData),
    })
  }

  /**
   * Update an existing witness
   * @param claimId UUID or claim number
   * @param witnessId UUID of the witness to update
   * @param witnessData Updated witness data
   */
  async updateWitness(
    claimId: string,
    witnessId: string,
    witnessData: WitnessUpdate
  ): Promise<WitnessResponse> {
    const endpoint = `/api/v1/claims/${claimId}/witnesses/${witnessId}`
    return this.client.request<WitnessResponse>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(witnessData),
    })
  }

  /**
   * Delete a witness
   * @param claimId UUID or claim number
   * @param witnessId UUID of the witness to delete
   */
  async deleteWitness(claimId: string, witnessId: string): Promise<void> {
    const endpoint = `/api/v1/claims/${claimId}/witnesses/${witnessId}`
    await this.client.request<void>(endpoint, {
      method: 'DELETE',
    })
  }
}
