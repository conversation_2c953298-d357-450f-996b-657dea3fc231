// Customer Types

// Renamed from Customer to CustomerResponse to match OpenAPI schema
export interface CustomerResponse {
  id: string
  name: string
  prefix: string
  description?: string | null // Add description field, optional
  active: boolean // Add active field
  created_at: string
  updated_at: string
}

// Schema for creating a customer
export interface CustomerCreate {
  name: string
  prefix: string
  description?: string | null
  active?: boolean // Optional, defaults to true on backend
}

// Schema for updating a customer
export interface CustomerUpdate {
  name?: string | null
  description?: string | null
  active?: boolean | null
}
