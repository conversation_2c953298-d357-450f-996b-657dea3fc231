// Audit Types

export enum EntityType {
  CLAIM = 'CLAIM',
  CLAIM_DETAILS = 'CLAIM_DETAILS',
  AUTO_DETAILS = 'AUTO_DETAILS',
  PROPERTY_DETAILS = 'PROPERTY_DETAILS',
  GL_DETAILS = 'GL_DETAILS',
  WITNESS = 'WITNESS',
  ATTORNEY = 'ATTORNEY',
  FINANCIAL = 'FINANCIAL',
  RESERVE = 'RESERVE',
  PAYMENT = 'PAYMENT',
  DOCUMENT = 'DOCUMENT',
}

export enum ChangeType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export interface AuditTrailResponse {
  id: string
  claim_id: string
  entity_type: EntityType
  entity_id?: string | null
  change_type: ChangeType
  field_name?: string | null
  previous_value?: any | null
  new_value?: any | null
  description?: string | null
  changed_by_id?: string | null
  changed_at: string
}

export interface PaginatedAuditTrailResponse {
  items: AuditTrailResponse[]
  total: number
  skip: number
  limit: number
}

export interface AuditSummaryResponse {
  total_entries: number
  by_entity: Record<string, number>
  by_change_type: Record<string, number>
  by_user: Record<string, number>
  recent_activity?: AuditTrailResponse[]
}
