// Injury Types

export enum InjuredPersonType {
  // General Liability specific
  GUEST = 'GUEST',
  PATRON = 'PATRON',
  EMPLOYEE = 'EMPLOYEE',
  TENANT = 'TENANT',
  CONTRACTOR = 'CONTRACTOR',
  VENDOR = 'VENDOR',
  TRESPASSER = 'TRESPASSER',
  VISITOR = 'VISITOR',

  // Auto specific
  DRIVER = 'DRIVER',
  PASSENGER = 'PASSENGER',
  PEDESTRIAN = 'PEDESTRIAN',
  CYCLIST = 'CYCLIST',
  OTHER_DRIVER = 'OTHER_DRIVER',
  OTHER_PASSENGER = 'OTHER_PASSENGER',

  // General categories
  BYSTANDER = 'BYSTANDER',
  UNKNOWN = 'UNKNOWN',
}

export enum IncidentReportStatus {
  FILED = 'FILED',
  PENDING = 'PENDING',
  NOT_REQUIRED = 'NOT_REQUIRED',
}

export enum MedicalTreatmentRequirements {
  NONE = 'NONE',
  FIRST_AID = 'FIRST_AID',
  OUTPATIENT = 'OUTPATIENT',
  HOSPITALIZATION = 'HOSPITALIZATION',
  SURGERY = 'SURGERY',
  PHYSICAL_THERAPY = 'PHYSICAL_THERAPY',
  ONGOING_CARE = 'ONGOING_CARE',
}

export enum InsuranceBillingStatus {
  NOT_SUBMITTED = 'NOT_SUBMITTED',
  PENDING = 'PENDING',
  PARTIAL_PAYMENT = 'PARTIAL_PAYMENT',
  PAID = 'PAID',
  DENIED = 'DENIED',
}

// Bodily Injury response interface
export interface BodilyInjuryDetails {
  id: string
  gl_details_id?: string
  auto_details_id?: string
  injury_description?: string
  incident_location?: string
  injured_person_type?: InjuredPersonType
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
  created_at: string
  updated_at: string
}

// Bodily Injury update interface for sending data
export interface BodilyInjuryDetailsUpdate {
  injury_description?: string
  incident_location?: string
  injured_person_type?: InjuredPersonType
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

// New interfaces for injured persons and injuries

export interface Injury {
  id: string
  injured_person_id: string
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
  created_at: string
  updated_at: string
}

export interface InjuryCreate {
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

export interface InjuryUpdate {
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

export interface InjuredPerson {
  id: string
  gl_details_id?: string
  auto_details_id?: string
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  created_at: string
  updated_at: string
  injuries?: Injury[]
}

export interface InjuredPersonCreate {
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
}

export interface InjuredPersonUpdate {
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
}
