// Financial Types

export enum ReserveType {
  BODILY_INJURY = 'BODILY_INJURY',
  PROPERTY_DAMAGE = 'PROPERTY_DAMAGE',
  MEDICAL_PAYMENTS = 'MEDICAL_PAYMENTS',
  LOSS_OF_USE = 'LOSS_OF_USE',
  BUSINESS_INTERRUPTION = 'BUSINESS_INTERRUPTION',
  DEFENSE_COST = 'DEFENSE_COST',
  ALLOCATED_EXPENSE = 'ALLOCATED_EXPENSE',
  UNALLOCATED_EXPENSE = 'UNALLOCATED_EXPENSE',
}

export enum PaymentType {
  INDEMNITY = 'INDEMNITY',
  EXPENSE = 'EXPENSE',
  DEFENSE = 'DEFENSE',
}

// --- Response Schemas ---

export interface ReserveResponse {
  reserve_type: ReserveType
  amount: string // Representing Decimal as string
  id: string // uuid
  created_at: string // date-time
  updated_at: string // date-time
}

export interface ClaimFinancialsInDB {
  estimated_value: string // Decimal as string
  indemnity_paid?: string | null // Decimal as string
  expense_paid?: string | null // Decimal as string
  defense_paid?: string | null // Decimal as string
  recovery_expected?: string | null // Decimal as string
  recovery_received?: string | null // Decimal as string
  deductible_amount?: string | null // Decimal as string
  coverage_limit?: string | null // Decimal as string
  currency: string
  id: string // uuid
  claim_id: string // uuid
  claim_number: string
  reserves: ReserveResponse[]
  last_reserve_change?: string | null // date-time
  last_payment_date?: string | null // date-time
  created_at: string // date-time
  updated_at: string // date-time
}

export interface ReserveHistoryInDB {
  id: string // uuid
  claim_id: string // uuid
  claim_number?: string | null
  financials_id: string // uuid
  reserve_type: ReserveType
  previous_amount: string // Decimal as string
  new_amount: string // Decimal as string
  changed_by_id: string // uuid
  changed_at: string // date-time
  notes?: string | null
  // Add changed_by user details if needed/available from backend
  // changed_by?: UserReadBasic | null; // Temporarily commented out
}

export interface PaymentResponse {
  payment_type: PaymentType
  amount: string // Decimal as string
  payee: string
  payment_date: string // date-time
  notes?: string | null
  id: string // uuid
  financials_id: string // uuid
  created_by_id: string // uuid
  created_at: string // date-time
  updated_at: string // date-time
  // Add created_by user details if needed/available from backend
  // created_by?: UserReadBasic | null; // Temporarily commented out
}

export interface PaymentList {
  items: PaymentResponse[]
  total: number
}

// --- Request/Update Schemas ---

export interface ReserveUpdate {
  reserve_type: ReserveType
  amount: number | string // Allow number for input, convert to string if needed
  notes?: string | null
}

export interface PaymentCreate {
  payment_type: PaymentType
  amount: number | string // Allow number for input, convert to string if needed
  payee: string
  payment_date: string // Should be ISO string e.g., Date().toISOString()
  notes?: string | null
}

export interface ReserveCreate {
  reserve_type: ReserveType
  amount: number | string // Representing Decimal as string or number for input
}

export interface ClaimFinancialsCreate {
  estimated_value: number | string // Representing Decimal as string or number for input
  indemnity_paid?: number | string | null
  expense_paid?: number | string | null
  defense_paid?: number | string | null
  recovery_expected?: number | string | null
  recovery_received?: number | string | null
  deductible_amount?: number | string | null
  coverage_limit?: number | string | null
  currency?: string // Defaults to USD on backend
  reserves: ReserveCreate[]
}
