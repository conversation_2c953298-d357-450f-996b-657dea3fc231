// Task Types
import { UserReadBasic } from './user-types'

export interface Task {
  id: string
  hr_id: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  assignee?: UserReadBasic | null
  claim_id?: string
  claim_number?: string
  created_at: string
  updated_at: string
}

export enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  BLOCKED = 'BLOCKED',
  CANCELLED = 'CANCELLED',
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface TaskSchema {
  id: string
  hr_id: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  assignee?: UserReadBasic | null
  claim_id?: string
  claim_number?: string
  created_at: string
  updated_at: string
}

export interface TaskQueryParams {
  page?: number
  size?: number
  status?: TaskStatus
  priority?: TaskPriority
  claim_id?: string
  assignee?: string
}

// Matches openapi.json TaskCreateRequestBody
export interface ClaimTaskCreateRequestBody {
  title: string
  description?: string | null
  priority?: TaskPriority | string
  status?: TaskStatus | string
  due_date?: string | null
  assigned_to?: string | null
}
