// Property Damage Types

export enum PropertyAssetType {
  BUILDING = 'BUILDING',
  STRUCTURE = 'STRUCTURE',
  ROOF = 'ROOF',
  WALL = 'WALL',
  FLOOR = 'FLOOR',
  FOUNDATION = 'FOUNDATION',
  HVAC = 'HVAC',
  PLUMBING = 'PLUMBING',
  ELECTRICAL_SYSTEM = 'ELECTRICAL_SYSTEM',
  FURNITURE = 'FURNITURE',
  APPLIANCE = 'APPLIANCE',
  ELECTRONICS = 'ELECTRONICS',
  MACHINERY = 'MACHINERY',
  EQUIPMENT = 'EQUIPMENT',
  INVENTORY = 'INVENTORY',
  TOOLS = 'TOOLS',
  RAW_MATERIALS = 'RAW_MATERIALS',
  FINISHED_GOODS = 'FINISHED_GOODS',
  OFFICE_EQUIPMENT = 'OFFICE_EQUIPMENT',
  VEHICLE = 'VEHICLE',
  CARGO = 'CARGO',
  TRAILER = 'TRAILER',
  FLEET_VEHICLE = 'FLEET_VEHICLE',
  SPECIALIZED_VEHICLE = 'SPECIALIZED_VEHICLE',
  THIRD_PARTY_STRUCTURE = 'THIRD_PARTY_STRUCTURE',
  THIRD_PARTY_VEHICLE = 'THIRD_PARTY_VEHICLE',
  THIRD_PARTY_PROPERTY = 'THIRD_PARTY_PROPERTY',
  CUSTOMER_PROPERTY = 'CUSTOMER_PROPERTY',
  LANDSCAPING = 'LANDSCAPING',
  FIXTURE = 'FIXTURE',
  SIGNAGE = 'SIGNAGE',
  PARKING_LOT = 'PARKING_LOT',
  FENCING = 'FENCING',
  MANUFACTURING_EQUIPMENT = 'MANUFACTURING_EQUIPMENT',
  RESTAURANT_EQUIPMENT = 'RESTAURANT_EQUIPMENT',
  MEDICAL_EQUIPMENT = 'MEDICAL_EQUIPMENT',
  IT_INFRASTRUCTURE = 'IT_INFRASTRUCTURE',
  DATA = 'DATA',
  OTHER = 'OTHER',
}

export enum PropertyDamageType {
  WATER = 'WATER',
  FIRE = 'FIRE',
  WIND = 'WIND',
  HAIL = 'HAIL',
  THEFT = 'THEFT',
  VANDALISM = 'VANDALISM',
  STRUCTURAL = 'STRUCTURAL',
  OTHER = 'OTHER',
}

export enum RepairStatus {
  NOT_STARTED = 'NOT_STARTED',
  ASSESSMENT_PENDING = 'ASSESSMENT_PENDING',
  ESTIMATED = 'ESTIMATED',
  WAITING_FOR_PARTS = 'WAITING_FOR_PARTS',
  WAITING_FOR_CONTRACTOR = 'WAITING_FOR_CONTRACTOR',
  PERMITS_PENDING = 'PERMITS_PENDING',
  SCHEDULED = 'SCHEDULED',
  IN_PROGRESS = 'IN_PROGRESS',
  PARTIALLY_COMPLETED = 'PARTIALLY_COMPLETED',
  INSPECTION_PENDING = 'INSPECTION_PENDING',
  COMPLETED = 'COMPLETED',
  REPLACED = 'REPLACED',
  NOT_REPAIRABLE = 'NOT_REPAIRABLE',
  REPAIRS_DECLINED = 'REPAIRS_DECLINED',
  ON_HOLD = 'ON_HOLD',
}

export interface DamagedPropertyAsset {
  id: string
  name: string
  asset_type: PropertyAssetType
  description?: string
  location?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  owner_name?: string
  owner_type?: string
  owned_by_insured?: boolean
  estimated_value?: string
  purchase_date?: string
  police_report_filed?: boolean
  police_report_number?: string
  property_details_id?: string
  auto_details_id?: string
  gl_details_id?: string
  created_at: string
  updated_at: string
  damage_instances?: DamageInstance[]
}

export interface DamagedPropertyAssetCreate {
  name: string
  asset_type: PropertyAssetType
  description?: string
  location?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  owner_name?: string
  owner_type?: string
  owned_by_insured?: boolean
  estimated_value?: string
  purchase_date?: string
  police_report_filed?: boolean
  police_report_number?: string
}

export interface DamagedPropertyAssetUpdate {
  name?: string
  asset_type?: PropertyAssetType
  description?: string
  location?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  owner_name?: string
  owner_type?: string
  owned_by_insured?: boolean
  estimated_value?: string
  purchase_date?: string
  police_report_filed?: boolean
  police_report_number?: string
}

export interface DamageInstance {
  id: string
  damaged_property_asset_id: string
  damage_type: PropertyDamageType
  damage_description: string
  damage_severity?: string
  affected_area?: string
  damage_cause?: string
  date_of_damage?: string
  repair_status?: RepairStatus
  repair_description?: string
  repair_vendor?: string
  estimated_repair_cost?: string
  actual_repair_cost?: string
  repair_start_date?: string
  repair_completion_date?: string
  estimated_replacement_cost?: string
  deductible_applied?: boolean
  depreciation_amount?: string
  created_at: string
  updated_at: string
}

export interface DamageInstanceCreate {
  damage_type: PropertyDamageType
  damage_description: string
  damage_severity?: string
  affected_area?: string
  damage_cause?: string
  date_of_damage?: string
  repair_status?: RepairStatus
  repair_description?: string
  repair_vendor?: string
  estimated_repair_cost?: string
  actual_repair_cost?: string
  repair_start_date?: string
  repair_completion_date?: string
  estimated_replacement_cost?: string
  deductible_applied?: boolean
  depreciation_amount?: string
}

export interface DamageInstanceUpdate {
  damage_type?: PropertyDamageType
  damage_description?: string
  damage_severity?: string
  affected_area?: string
  damage_cause?: string
  date_of_damage?: string
  repair_status?: RepairStatus
  repair_description?: string
  repair_vendor?: string
  estimated_repair_cost?: string
  actual_repair_cost?: string
  repair_start_date?: string
  repair_completion_date?: string
  estimated_replacement_cost?: string
  deductible_applied?: boolean
  depreciation_amount?: string
}
