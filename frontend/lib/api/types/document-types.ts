// Document Types

export enum DocumentType {
  PHOTO = 'PHOTO',
  REPORT = 'REPORT',
  INVOICE = 'INVOICE',
  STATEMENT = 'STATEMENT',
  CONTRACT = 'CONTRACT',
  POLICY = 'POLICY',
  CORRESPONDENCE = 'CORRESPONDENCE',
  OTHER = 'OTHER',
}

export interface Document {
  id: string
  name: string
  type: DocumentType
  description?: string | null
  claim_id: string
  claim_number?: string | null
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by?: string | null
  created_at: string
  updated_at: string
}

export interface DocumentSchema {
  id: string
  name: string
  type: DocumentType
  description?: string | null
  claim_id: string
  claim_number?: string | null
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by?: string | null
  created_at: string
  updated_at: string
}

export interface DocumentList {
  items: Document[]
  total: number
}

export interface DocumentUploadUrlRequest {
  file_name: string
  content_type: string
  document_type: DocumentType
}

export interface DocumentUploadUrlResponse {
  upload_url: string
  document_id: string
  expires_at: string
}

export interface DocumentDownloadUrlResponse {
  download_url: string
  expires_at: string
  should_download?: boolean // Optional flag to indicate if browser should force download
}

export interface DocumentQueryParams {
  skip?: number
  limit?: number
  claim_id?: string
  document_type?: DocumentType
}
