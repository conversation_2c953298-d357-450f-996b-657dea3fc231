// Report Types
import { ClaimStatus, ClaimType } from './claim-base-types'

// Report response types
export interface ClaimsKPIMetrics {
  total_claims?: number
  total_claims_change?: number
  open_claims?: number
  open_claims_change?: number
  open_claims_percentage?: number
  open_claims_percentage_change?: number
  closed_claims?: number
  closed_claims_change?: number
  closed_claims_percentage?: number
  closed_claims_percentage_change?: number
  avg_resolution_time?: number
  avg_resolution_time_change?: number
  comparison?: {
    total_claims: number
    open_claims_percentage: number
    closed_claims_percentage: number
    avg_resolution_time: number
  }
  items?: ClaimsKPIMetrics[]
  total?: number
  data?: any
  report_metadata?: any
}

export interface ClaimTypeData {
  type: ClaimType
  count: number
  percentage: number
}

export interface ClaimsByTypeResponse {
  data?: ClaimTypeData[]
  items?: ClaimTypeData[]
  total?: number
}

export interface ClaimStatusData {
  status: ClaimStatus
  count: number
  percentage: number
}

export interface ClaimsByStatusResponse {
  data?: ClaimStatusData[]
  items?: ClaimStatusData[]
  total?: number
}

export interface TimeSeriesPoint {
  period: string
  new_claims: number
  closed_claims: number
}

export interface ClaimsOverTimeResponse {
  data?: TimeSeriesPoint[]
  items?: TimeSeriesPoint[]
  total?: number
}

// Financial tab interfaces
export interface FinancialKPIMetrics {
  total_reserves?: number
  total_reserves_change?: number
  total_payments?: number
  total_payments_change?: number
  avg_claim_value?: number
  avg_claim_value_change?: number
  recovery_amount?: number
  recovery_amount_change?: number
  comparison?: {
    total_reserves: number
    total_payments: number
    avg_claim_value: number
    recovery_amount: number
  }
  items?: FinancialKPIMetrics[]
  total?: number
  data?: any
  report_metadata?: any
}

export interface PaymentReserveTimePoint {
  period: string
  payments: number
  reserves: number
}

export interface PaymentsVsReservesResponse {
  data?: PaymentReserveTimePoint[]
  items?: PaymentReserveTimePoint[]
  total?: number
}

// Performance tab interfaces
export interface AdjusterMetrics {
  adjuster_id: string
  adjuster_name: string
  claims_handled: number
  avg_resolution_time: number
  total_payments: number
  pending_tasks: number
  overdue_tasks: number
}

export interface AdjusterPerformanceResponse {
  data?: AdjusterMetrics[]
  items?: AdjusterMetrics[]
  total?: number
}

// Report parameters
export interface ReportParams {
  period?: string
  start_date?: string
  end_date?: string
  compare_period?: boolean
  customer_id?: string
  claim_type?: string
  user_id?: string
}

// Dashboard Metrics Response
export interface MetricChange {
  value: number
  percentage: number
  direction: 'up' | 'down' | 'no_change'
}

export interface DashboardMetricsResponse {
  total_claims: number
  open_claims: number
  new_claims_last_period: number
  closed_claims_last_period: number
  average_claim_lifecycle_days?: number
  average_claim_lifecycle_days_change?: MetricChange
  total_payments_last_period: number
  total_payments_change?: MetricChange
  total_outstanding_reserves: number
  total_outstanding_reserves_change?: MetricChange
  tasks_pending: number
  tasks_overdue: number
  fnols_pending: number
  reserve_trend?: number
}
