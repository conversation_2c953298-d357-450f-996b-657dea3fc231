// Auto Claim Types

export enum AutoIncidentType {
  COLLISION = 'COLLISION',
  THEFT = 'THEFT',
  VANDALISM = 'VANDALISM',
  WEATHER = 'WEATHER',
  FIRE = 'FIRE',
  MECHANICAL = 'MECHANICAL',
  OTHER = 'OTHER',
}

export enum CollisionType {
  REAR_END = 'REAR_END',
  SIDE_IMPACT = 'SIDE_IMPACT',
  HEAD_ON = 'HEAD_ON',
  ROLLOVER = 'ROLLOVER',
  MULTI_VEHICLE = 'MULTI_VEHICLE',
  SINGLE_VEHICLE = 'SINGLE_VEHICLE',
  HIT_AND_RUN = 'HIT_AND_RUN',
  OTHER = 'OTHER',
}

export enum PointOfImpact {
  FRONT = 'FRONT',
  REAR = 'REAR',
  DRIVER_SIDE = 'DRIVER_SIDE',
  PASSENGER_SIDE = 'PASSENGER_SIDE',
  TOP = 'TOP',
  BOTTOM = 'BOTTOM',
  MULTIPLE = 'MULTIPLE',
  OTHER = 'OTHER',
}

export interface AutoDetailsResponse {
  id: string
  claim_id: string
  incident_type: AutoIncidentType
  vehicle_make?: string | null
  vehicle_model?: string | null
  vehicle_year?: number | null
  vehicle_vin?: string | null
  license_plate?: string | null
  collision_type?: CollisionType | null
  point_of_impact?: PointOfImpact | null
  airbag_deployed?: boolean | null
  has_injuries?: boolean
  has_property_damage?: boolean
  driver_name?: string | null
  driver_license?: string | null
  driver_relationship?: string | null
  created_at: string
  updated_at: string
}

export interface AutoDetailsCreate {
  incident_type: AutoIncidentType
  vehicle_make?: string | null
  vehicle_model?: string | null
  vehicle_year?: number | null
  vehicle_vin?: string | null
  license_plate?: string | null
  collision_type?: CollisionType | null
  point_of_impact?: PointOfImpact | null
  airbag_deployed?: boolean | null
  has_injuries?: boolean
  has_property_damage?: boolean
  driver_name?: string | null
  driver_license?: string | null
  driver_relationship?: string | null
}

export interface AutoDetailsUpdate {
  incident_type?: AutoIncidentType | null
  vehicle_make?: string | null
  vehicle_model?: string | null
  vehicle_year?: number | null
  vehicle_vin?: string | null
  license_plate?: string | null
  collision_type?: CollisionType | null
  point_of_impact?: PointOfImpact | null
  airbag_deployed?: boolean | null
  has_injuries?: boolean | null
  has_property_damage?: boolean | null
  driver_name?: string | null
  driver_license?: string | null
  driver_relationship?: string | null
}

export interface AutoClaimResponse {
  id: string
  claim_number: string
  auto_details: AutoDetailsResponse
  // Other fields from base Claim may be included here
}

export interface AutoClaimCreate {
  claim_number?: string
  customer_id: string
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  auto_details: AutoDetailsCreate
}

export interface AutoClaimUpdate {
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  auto_details?: AutoDetailsUpdate | null
}
