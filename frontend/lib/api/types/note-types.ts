// Note Types

export interface Note {
  id: string
  content: string
  claim_id: string
  author_id?: string
  author?: string
  author_email?: string
  created_at: string
  updated_at: string
}

export interface NoteSchema {
  id: string
  content: string
  claim_id: string
  author_id?: string
  author?: string
  author_email?: string
  created_at: string
  updated_at: string
}

export interface NoteCreate {
  content: string
  claim_id: string
}

export interface NoteUpdate {
  content: string
}
