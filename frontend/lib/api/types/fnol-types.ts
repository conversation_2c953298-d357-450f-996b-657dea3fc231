// FNOL Types
import { Claim, ClaimStatus, ClaimType } from './claim-base-types'
import { CustomerResponse } from './customer-types'
import { USState } from './shared-types'

export enum FNOLStatus {
  NEW = 'NEW',
  REVIEWED = 'REVIEWED',
  CONVERTED = 'CONVERTED',
  REJECTED = 'REJECTED',
}

export enum CommunicationPreference {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  TEXT = 'TEXT',
  MAIL = 'MAIL',
  PORTAL = 'PORTAL',
  NO_PREFERENCE = 'NO_PREFERENCE',
}

export enum ReporterRelationship {
  INSURED = 'INSURED',
  CLAIMANT = 'CLAIMANT',
  ATTORNEY = 'ATTORNEY',
  AGENT = 'AGENT',
  OTHER = 'OTHER',
}

// Renamed from FNOL to FNOLResponse to match OpenAPI schema
export interface FNOLResponse {
  // Core identification
  id: string // uuid
  fnol_number: string
  customer_id: string // uuid

  // Reporter information
  reported_by: string // Name of the person reporting the loss
  reporter_phone?: string | null // Reporter's phone number
  reporter_email?: string | null // Reporter's email address
  reporter_relationship?: ReporterRelationship | null
  communication_preference?: CommunicationPreference | null

  // Incident details
  incident_date?: string | null // ISO 8601 date string
  incident_time?: string | null // ISO time string e.g., "HH:MM:SS"
  incident_location?: string | null // maxLength: 500
  incident_state: USState // US state where the incident occurred
  description?: string | null // Detailed description of the incident

  // Policy/system fields
  policy_number?: string | null
  status?: FNOLStatus
  reported_at: string // date-time, required in OpenAPI FNOLResponse
  created_at: string // date-time
  updated_at: string // date-time

  // Optional included fields based on API 'include' parameter
  customer?: CustomerResponse // Add optional customer
  claims?: Claim[] // Add optional claims array
}

// Response type for FNOL conversion endpoint
export interface FNOLConversionResponse {
  claim_id: string
  claim_number: string
  claim_type: ClaimType
  status: ClaimStatus
}

// Added based on OpenAPI schema for POST /api/v1/fnols
export interface FNOLCreate {
  // Core identification
  customer_id: string // Corresponds to UUID

  // Reporter information
  reported_by: string // Name of the person reporting the loss
  reporter_phone?: string | null // Reporter's phone number
  reporter_email?: string | null // Reporter's email address
  reporter_relationship?: ReporterRelationship | null
  communication_preference?: CommunicationPreference | null

  // Incident details
  incident_date?: string | null // Expects ISO 8601 date string
  incident_time?: string | null // ISO time string e.g., "HH:MM:SS"
  incident_location?: string | null // maxLength: 500
  incident_state: USState // US state where the incident occurred
  description?: string | null // Detailed description of the incident

  // Policy/system fields
  policy_number?: string | null // maxLength: 100
  fnol_number?: string | null // From backend FNOLCreate, optional & auto-generated
}

// Updated based on OpenAPI schema for PATCH /api/v1/fnols/{fnol_id}
export interface FNOLUpdate {
  // Reporter information
  reported_by?: string | null
  reporter_phone?: string | null // Reporter's phone number
  reporter_email?: string | null // Reporter's email address
  reporter_relationship?: ReporterRelationship | null
  communication_preference?: CommunicationPreference | null

  // Incident details
  incident_date?: string | null
  incident_time?: string | null
  incident_location?: string | null
  incident_state: USState // Required field
  description?: string | null

  // Policy/system fields
  policy_number?: string | null
  status?: FNOLStatus | null
}

export interface FNOLQueryParams {
  page?: number
  size?: number
  status?: FNOLStatus
  customer_id?: string
}
