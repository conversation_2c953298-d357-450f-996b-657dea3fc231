// External Party Types (Witnesses, Attorneys)

// Witness types based on OpenAPI schema
export interface WitnessResponse {
  id: string
  claim_id: string
  claim_number?: string | null
  name: string
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
  created_at: string
  updated_at: string
}

export interface WitnessCreate {
  name: string
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
}

export interface WitnessUpdate {
  name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
}

// Attorney types based on OpenAPI schema
export enum AttorneyType {
  PLAINTIFF = 'PLAINTIFF',
  DEFENSE = 'DEFENSE',
  COVERAGE = 'COVERAGE',
  MONITORING = 'MONITORING',
  OTHER = 'OTHER',
}

export interface AttorneyResponse {
  id: string
  claim_id: string
  claim_number?: string | null
  name: string
  attorney_type: AttorneyType
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
  created_at: string
  updated_at: string
}

export interface AttorneyCreate {
  name: string
  attorney_type: AttorneyType
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
}

export interface AttorneyUpdate {
  name?: string | null
  attorney_type?: AttorneyType | null
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
}
