// General Liability Claim Types

export enum GeneralLiabilityIncidentType {
  PREMISES_LIABILITY = 'PREMISES_LIABILITY',
  PRODUCTS_LIABILITY = 'PRODUCTS_LIABILITY',
  COMPLETED_OPERATIONS = 'COMPLETED_OPERATIONS',
  PERSONAL_ADVERTISING_INJURY = 'PERSONAL_ADVERTISING_INJURY',
  THIRD_PARTY_PROPERTY_DAMAGE = 'THIRD_PARTY_PROPERTY_DAMAGE',
  OTHER = 'OTHER',
}

export interface GeneralLiabilityDetailsResponse {
  id: string
  claim_id: string
  incident_type: GeneralLiabilityIncidentType
  has_bodily_injury: boolean
  has_property_damage: boolean
  third_party_property_description?: string | null
  third_party_property_value?: number | null
  premises_address?: string | null
  product_name?: string | null
  product_model?: string | null
  product_description?: string | null
  operation_description?: string | null
  created_at: string
  updated_at: string
}

export interface GeneralLiabilityDetailsCreate {
  incident_type: GeneralLiabilityIncidentType
  has_bodily_injury: boolean
  has_property_damage: boolean
  third_party_property_description?: string | null
  third_party_property_value?: number | null
  premises_address?: string | null
  product_name?: string | null
  product_model?: string | null
  product_description?: string | null
  operation_description?: string | null
}

export interface GeneralLiabilityDetailsUpdate {
  incident_type?: GeneralLiabilityIncidentType | null
  has_bodily_injury?: boolean | null
  has_property_damage?: boolean | null
  third_party_property_description?: string | null
  third_party_property_value?: number | null
  premises_address?: string | null
  product_name?: string | null
  product_model?: string | null
  product_description?: string | null
  operation_description?: string | null
}

export interface PremisesLiabilityDetailsResponse {
  gl_details_id: string
  premises_name?: string | null
  premises_type?: string | null
  unsafe_condition?: string | null
  condition_reported?: boolean | null
  safety_measures?: string | null
  maintenance_history?: string | null
  business_hours?: string | null
  signage_present?: boolean | null
  created_at: string
  updated_at: string
}

export interface ProductsLiabilityDetailsResponse {
  gl_details_id: string
  manufacturer?: string | null
  distributor?: string | null
  purchase_date?: string | null
  sale_date?: string | null
  product_warnings?: string | null
  safety_instructions?: string | null
  defect_description?: string | null
  recall_status?: string | null
  created_at: string
  updated_at: string
}

export interface CompletedOperationsDetailsResponse {
  gl_details_id: string
  operation_type?: string | null
  completion_date?: string | null
  contractor_name?: string | null
  contractor_license?: string | null
  contract_value?: number | null
  work_description?: string | null
  failure_description?: string | null
  warranty_applicable?: boolean | null
  created_at: string
  updated_at: string
}

export interface PersonalAdvertisingInjuryDetailsResponse {
  gl_details_id: string
  injury_type?: string | null
  publication_name?: string | null
  publication_date?: string | null
  content_description?: string | null
  damages_claimed?: string | null
  legal_review_status?: string | null
  created_at: string
  updated_at: string
}

export interface GeneralLiabilityClaimResponse {
  id: string
  claim_number: string
  general_liability_details: GeneralLiabilityDetailsResponse
  premises_liability_details?: PremisesLiabilityDetailsResponse | null
  products_liability_details?: ProductsLiabilityDetailsResponse | null
  completed_operations_details?: CompletedOperationsDetailsResponse | null
  personal_advertising_injury_details?: PersonalAdvertisingInjuryDetailsResponse | null
  // Other fields from base Claim may be included here
}

export interface GeneralLiabilityClaimCreate {
  claim_number?: string
  customer_id: string
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  general_liability_details: GeneralLiabilityDetailsCreate
}

export interface GeneralLiabilityClaimUpdate {
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  general_liability_details?: GeneralLiabilityDetailsUpdate | null
}
