// Core claim types
import { CustomerResponse } from './customer-types'
import { DocumentSchema } from './document-types'
import { ClaimFinancialsInDB } from './financial-types'
import { NoteSchema } from './note-types'
import { TaskSchema } from './task-types'
import { AttorneyResponse, WitnessResponse } from './external-party-types'
import { AutoDetailsResponse } from './auto-claim-types'
import { BackendPaginatedResponse } from './common-types'
import { UserResponse } from './user-types'

export enum ClaimType {
  AUTO = 'AUTO',
  PROPERTY = 'PROPERTY',
  GENERAL_LIABILITY = 'GENERAL_LIABILITY',
}

export enum ClaimStatus {
  INITIAL_REVIEW = 'INITIAL_REVIEW',
  COVERAGE_EVALUATION = 'COVERAGE_EVALUATION',
  INVESTIGATION = 'INVESTIGATION',
  DAMAGES = 'DAMAGES',
  NEGOTIATION_SETTLEMENT = 'NEGOTIATION_SETTLEMENT',
  LITIGATION = 'LITIGATION',
  SUBROGATION = 'SUBROGATION',
  CLOSURE = 'CLOSURE',
  REOPEN = 'REOPEN',
}

export enum RecoveryStatus {
  NOT_STARTED = 'NOT_STARTED',
  INITIATED = 'INITIATED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

export interface StatusHistorySchema {
  id: string
  claim_id: string
  previous_status: ClaimStatus
  new_status: ClaimStatus
  changed_by_id: string
  changed_at: string
  notes?: string | null
}

export interface Claim {
  id: string
  claim_number: string
  customer_id: string
  type: ClaimType
  status: ClaimStatus
  created_at: string
  updated_at: string

  description?: string | null
  claimant_name?: string | null
  claimant_email?: string | null
  claimant_phone?: string | null
  insured_name?: string | null
  insured_email?: string | null
  insured_phone?: string | null
  incident_date?: string | null
  date_of_loss?: string | null // Alias for incident_date for backward compatibility
  jurisdiction?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  recovery_status?: RecoveryStatus | null
  carrier_name?: string | null
  carrier_contact?: string | null
  closed_at?: string | null
  created_by_id?: string | null
  total_amount?: string | null // For backward compatibility

  customer?: CustomerResponse | null
  financials?: ClaimFinancialsInDB | null
  auto_details?: AutoDetailsResponse | null // Added for backward compatibility

  // User relationship objects
  assigned_to?: UserResponse | null
  supervisor?: UserResponse | null
  created_by?: UserResponse | null

  documents?: DocumentSchema[] | null
  notes?: NoteSchema[] | null
  tasks?: TaskSchema[] | null
  status_history?: StatusHistorySchema[] | null
  witnesses?: WitnessResponse[] | null
  attorneys?: AttorneyResponse[] | null
}

export interface ClaimQueryParams {
  page?: number
  size?: number
  claim_number?: string
  status?: ClaimStatus
  type?: ClaimType
  customer_id?: string
  search?: string
}

// Paginated claim response from backend
export type PaginatedClaimResponse = BackendPaginatedResponse<Claim>

export interface RecoveryDetailsResponse {
  recovery_status?: RecoveryStatus | null
  carrier_name?: string | null
  carrier_contact?: string | null
  carrier_claim_number?: string | null
  carrier_adjuster?: string | null
  expected_amount?: string | null
  received_amount?: string | null
  claim_id: string
  claim_number: string
}

export interface RecoveryUpdateRequest {
  recovery_status?: RecoveryStatus
}

export interface CarrierUpdateRequest {
  carrier_name?: string | null
  carrier_contact?: string | null
}
