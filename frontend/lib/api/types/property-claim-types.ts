// Property Claim Types

export enum PropertyType {
  COMMERCIAL_BUILDING = 'COMMERCIAL_BUILDING',
  WAREHOUSE = 'WAREHOUSE',
  OFFICE = 'OFFICE',
  RETAIL = 'RETAIL',
  INDUSTRIAL = 'INDUSTRIAL',
  RESIDENTIAL = 'RESIDENTIAL',
  VACANT_LAND = 'VACANT_LAND',
  OTHER = 'OTHER',
}

export enum DamageType {
  FIRE = 'FIRE',
  WATER = 'WATER',
  WIND = 'WIND',
  HAIL = 'HAIL',
  THEFT = 'THEFT',
  VANDALISM = 'VANDALISM',
  ELECTRICAL = 'ELECTRICAL',
  STRUCTURAL = 'STRUCTURAL',
  GLASS = 'GLASS',
  MACHINERY = 'MACHINERY',
  INVENTORY = 'INVENTORY',
  MULTIPLE = 'MULTIPLE',
  OTHER = 'OTHER',
}

export interface PropertyDetailsResponse {
  id: string
  claim_id: string
  property_type: PropertyType
  property_address?: string | null
  property_owner?: string | null
  property_use?: string | null
  property_size?: number | null
  property_age?: number | null
  damage_type: DamageType
  damage_description?: string | null
  damage_estimate?: number | null
  business_interruption?: boolean
  estimated_interruption_days?: number | null
  created_at: string
  updated_at: string
}

export interface PropertyDetailsCreate {
  property_type: PropertyType
  property_address?: string | null
  property_owner?: string | null
  property_use?: string | null
  property_size?: number | null
  property_age?: number | null
  damage_type: DamageType
  damage_description?: string | null
  damage_estimate?: number | null
  business_interruption?: boolean
  estimated_interruption_days?: number | null
}

export interface PropertyDetailsUpdate {
  property_type?: PropertyType | null
  property_address?: string | null
  property_owner?: string | null
  property_use?: string | null
  property_size?: number | null
  property_age?: number | null
  damage_type?: DamageType | null
  damage_description?: string | null
  damage_estimate?: number | null
  business_interruption?: boolean | null
  estimated_interruption_days?: number | null
}

export interface PropertyClaimResponse {
  id: string
  claim_number: string
  property_details: PropertyDetailsResponse
  // Other fields from base Claim may be included here
}

export interface PropertyClaimCreate {
  claim_number?: string
  customer_id: string
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  property_details: PropertyDetailsCreate
}

export interface PropertyClaimUpdate {
  claimant_name?: string | null
  description?: string | null
  incident_date?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  status?: string | null
  property_details?: PropertyDetailsUpdate | null
}
