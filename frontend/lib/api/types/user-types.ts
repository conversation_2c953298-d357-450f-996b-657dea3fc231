// User related types

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  ADJUSTER = 'ADJUSTER',
  AGENT = 'AGENT',
  CUSTOMER = 'CUSTOMER',
}

export enum AuthorityRole {
  NO_AUTHORITY = 'NO_AUTHORITY',
  BASIC = 'BASIC',
  INTERMEDIATE = 'INTERMEDIATE',
  SENIOR = 'SENIOR',
  SUPERVISOR = 'SUPERVISOR',
  MANAGER = 'MANAGER',
  UNLIMITED = 'UNLIMITED',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

// User summary for dropdowns, etc.
export interface UserSummary {
  id: string // UUID
  firstName: string
  lastName: string
  email?: string | null
  fullName?: string // Optional, can be constructed
  role?: UserRole | string // Role for context/filtering
  department?: string | null // Department for additional context
}

// Based on openapi.json components.schemas.UserResponse
export interface UserResponse {
  id: string // uuid
  email: string // format: email
  first_name: string // minLength: 1, maxLength: 100
  last_name: string // minLength: 1, maxLength: 100
  role: UserRole // Enum, Required in OpenAPI
  authority_role: AuthorityRole // Enum, Default: NO_AUTHORITY in OpenAPI - assuming always present in response
  status: UserStatus // Enum, Default: PENDING in OpenAPI - assuming always present in response
  department?: string | null // maxLength: 100
  job_title?: string | null // maxLength: 100
  phone_number?: string | null // maxLength: 20
  timezone: string // maxLength: 50, Default: "UTC" in OpenAPI - assuming always present
  preferences?: Record<string, any> | null // OpenAPI: additionalProperties: true, type: "object"
  created_at: string // date-time
  updated_at: string // date-time
  last_login_at?: string | null // date-time
  email_verified_at?: string | null // date-time
  last_password_change_at?: string | null // date-time
  force_password_change: boolean // Required in OpenAPI
  locked_until?: string | null // date-time
  failed_login_attempts: number // Integer, Required in OpenAPI
  permissions: string[] // Default: [] in OpenAPI - assuming always present, possibly empty array
}

// Based on openapi.json components.schemas.UserUpdate
export interface UserUpdate {
  first_name?: string | null
  last_name?: string | null
  role?: UserRole | null
  authority_role?: AuthorityRole | null
  status?: UserStatus | null
  department?: string | null
  job_title?: string | null
  phone_number?: string | null
  timezone?: string | null
  preferences?: Record<string, any> | null
  password?: string | null
}

export interface UserReadBasic {
  id: string
  email: string
  // Add other fields like first_name, last_name, fullName if the backend
  // consistently provides them within the UserReadBasic schema embedded in tasks.
}

export interface UserCreate {
  email: string // format: email
  first_name: string // minLength: 1, maxLength: 100
  last_name: string // minLength: 1, maxLength: 100
  role: UserRole // Enum
  password: string // minLength: 8, maxLength: 64
  authority_role?: AuthorityRole | null // Enum, default: NO_AUTHORITY in OpenAPI
  status?: UserStatus | null // Enum, default: PENDING in OpenAPI
  department?: string | null // maxLength: 100
  job_title?: string | null // maxLength: 100
  phone_number?: string | null // maxLength: 20
  timezone?: string | null // maxLength: 50, default: "UTC" in OpenAPI
  preferences?: Record<string, any> | null // OpenAPI: additionalProperties: true, type: "object"
}

// Based on openapi.json components.schemas.PasswordChange
export interface PasswordChange {
  current_password: string
  new_password: string
}
