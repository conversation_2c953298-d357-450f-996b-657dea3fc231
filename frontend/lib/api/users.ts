import { ApiClient } from './client'
import { UserResponse, PaginatedResponse, UserSummary } from './types' // Assuming UserResponse is the detailed type from openapi

/**
 * Users API service for handling user-related endpoints
 */
export class UsersApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a user by ID
   */
  async getUserById(id: string): Promise<UserResponse> {
    const endpoint = `/api/v1/users/${id}`
    return this.client.request<UserResponse>(endpoint)
  }

  /**
   * Search users for autocomplete/dropdown purposes with text search capability.
   * Optimized for typeahead/autocomplete scenarios.
   */
  async searchUsers(
    searchQuery: string,
    options?: {
      role?: string
      status?: string
      limit?: number
    }
  ): Promise<UserSummary[]> {
    const params = new URLSearchParams({
      search: searchQuery.trim(),
      limit: (options?.limit || 20).toString(),
      status: options?.status || 'ACTIVE', // Default to active users
    })

    if (options?.role) {
      params.append('role', options.role)
    }

    const endpoint = `/api/v1/users?${params.toString()}`
    const usersResponse = await this.client.request<PaginatedResponse<UserResponse>>(endpoint)

    // Extract the items array from the paginated response
    const users = usersResponse.items || []

    return users.map(user => ({
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email,
      fullName: `${user.first_name} ${user.last_name}`,
      role: user.role,
      department: user.department,
    }))
  }

  /**
   * Get a list of users, transformed to UserSummary for dropdowns.
   * Now uses better defaults for dropdown scenarios.
   */
  async getUsersForDropdown(options?: {
    role?: string
    status?: string
    limit?: number
  }): Promise<UserSummary[]> {
    const params = new URLSearchParams({
      limit: (options?.limit || 50).toString(),
      status: options?.status || 'ACTIVE', // Default to active users only
    })

    if (options?.role) {
      params.append('role', options.role)
    }

    const endpoint = `/api/v1/users?${params.toString()}`
    const usersResponse = await this.client.request<PaginatedResponse<UserResponse>>(endpoint)

    // Extract the items array from the paginated response
    const users = usersResponse.items || []

    return users.map(user => ({
      id: user.id,
      firstName: user.first_name,
      lastName: user.last_name,
      email: user.email,
      fullName: `${user.first_name} ${user.last_name}`,
      role: user.role,
      department: user.department,
    }))
  }

  /**
   * Get a paginated list of users (raw UserResponse objects)
   */
  async getUsers(params?: any): Promise<PaginatedResponse<UserResponse>> {
    const queryParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value))
        }
      })
    }
    const endpoint = `/api/v1/users${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<PaginatedResponse<UserResponse>>(endpoint)
  }

  // Add other user methods as needed (createUser, updateUser, deleteUser)
}
