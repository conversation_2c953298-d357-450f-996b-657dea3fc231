'use client'

import { ApiClient } from './client'
import {
  AuditSummaryResponse,
  AuditTrailResponse,
  EntityType,
  ChangeType,
  PaginatedAuditTrailResponse,
} from './types'

/**
 * Audit API service for handling claim audit-related endpoints
 */
export class AuditApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get audit entries for a claim with optional filtering
   * @param claimId UUID or claim number
   * @param filters Optional filters for audit entries
   */
  async getAuditEntries(
    claimId: string,
    filters?: {
      entity_type?: string
      change_type?: string
      from_date?: string
      to_date?: string
      changed_by_id?: string
    }
  ): Promise<PaginatedAuditTrailResponse> {
    try {
      const queryParams = new URLSearchParams()

      if (filters) {
        if (filters.entity_type) queryParams.append('entity_type', filters.entity_type)
        if (filters.change_type) queryParams.append('change_type', filters.change_type)
        if (filters.from_date) queryParams.append('from_date', filters.from_date)
        if (filters.to_date) queryParams.append('to_date', filters.to_date)
        if (filters.changed_by_id) queryParams.append('changed_by_id', filters.changed_by_id)
      }

      const queryString = queryParams.toString() ? `?${queryParams.toString()}` : ''
      const endpoint = `/api/v1/claims/${claimId}/audit${queryString}`

      try {
        // In development, try/catch without rethrowing to avoid console errors
        const auditData = await this.client.request<PaginatedAuditTrailResponse>(endpoint)
        return auditData
      } catch (error: any) {
        // In development, return mock data without logging the 404 error
        if (process.env.NODE_ENV === 'development') {
          // Only log a warning if it's not a 404 error
          if (!(error?.status === 404 || error?.message?.includes('404'))) {
            console.warn(`Using mock data for ${endpoint} - ${error.message}`)
          }
          return this.getMockAuditEntries()
        }
        // Re-throw if not 404 or not in development
        throw error
      }
    } catch (error: any) {
      // Only log if not a 404 to reduce console noise
      if (!(error?.status === 404 || error?.message?.includes('404'))) {
        console.error('Error in getAuditEntries:', error)
      }

      // In development environment, return mock data
      if (process.env.NODE_ENV === 'development') {
        return this.getMockAuditEntries()
      }
      throw error
    }
  }

  /**
   * Get audit summary for a claim
   * @param claimId UUID or claim number
   */
  async getAuditSummary(claimId: string): Promise<AuditSummaryResponse> {
    const endpoint = `/api/v1/claims/${claimId}/audit/summary`

    try {
      const summary = await this.client.request<AuditSummaryResponse>(endpoint)
      return summary
    } catch (error: any) {
      // In development, return mock data without logging the 404 error
      if (process.env.NODE_ENV === 'development') {
        // Only log a warning if it's not a 404 error
        if (!(error?.status === 404 || error?.message?.includes('404'))) {
          console.warn(`Using mock data for ${endpoint} - ${error.message}`)
        }
        return this.getMockAuditSummary()
      }
      throw error
    }
  }

  // Mock data for development and testing
  private getMockAuditEntries(): PaginatedAuditTrailResponse {
    const mockEntries = [
      {
        id: '1',
        claim_id: 'claim-123',
        entity_type: EntityType.CLAIM,
        change_type: ChangeType.CREATE,
        description: 'Claim was created',
        changed_by_id: 'user-123',
        changed_at: new Date(Date.now() - 7 * 86400000).toISOString(),
      },
      {
        id: '2',
        claim_id: 'claim-123',
        entity_type: EntityType.DOCUMENT,
        change_type: ChangeType.CREATE,
        description: 'Document "Police Report" was uploaded',
        changed_by_id: 'user-123',
        changed_at: new Date(Date.now() - 6 * 86400000).toISOString(),
      },
      {
        id: '3',
        claim_id: 'claim-123',
        entity_type: EntityType.CLAIM,
        change_type: ChangeType.UPDATE,
        field_name: 'status',
        previous_value: 'DRAFT',
        new_value: 'INVESTIGATION',
        description: 'Status changed from DRAFT to INVESTIGATION',
        changed_by_id: 'user-456',
        changed_at: new Date(Date.now() - 5 * 86400000).toISOString(),
      },
      {
        id: '4',
        claim_id: 'claim-123',
        entity_type: EntityType.RESERVE,
        change_type: ChangeType.CREATE,
        description: 'Reserve of $5,000 was created for PROPERTY_DAMAGE',
        changed_by_id: 'user-456',
        changed_at: new Date(Date.now() - 4 * 86400000).toISOString(),
      },
      {
        id: '5',
        claim_id: 'claim-123',
        entity_type: EntityType.PAYMENT,
        change_type: ChangeType.CREATE,
        description: 'Payment of $2,500 was made to Auto Body Shop',
        changed_by_id: 'user-789',
        changed_at: new Date(Date.now() - 3 * 86400000).toISOString(),
      },
    ]

    return {
      items: mockEntries,
      total: mockEntries.length,
      skip: 0,
      limit: 50,
    }
  }

  private getMockAuditSummary(): AuditSummaryResponse {
    return {
      total_entries: 5,
      by_entity: {
        [EntityType.CLAIM]: 2,
        [EntityType.DOCUMENT]: 1,
        [EntityType.RESERVE]: 1,
        [EntityType.PAYMENT]: 1,
      },
      by_change_type: {
        [ChangeType.CREATE]: 4,
        [ChangeType.UPDATE]: 1,
      },
      by_user: {
        'user-123': 2,
        'user-456': 2,
        'user-789': 1,
      },
      recent_activity: this.getMockAuditEntries().items.slice(0, 3),
    }
  }
}
