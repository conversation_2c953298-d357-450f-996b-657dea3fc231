import { ApiClient } from './client'
import {
  FNOLResponse,
  FNOLQueryParams,
  PaginatedResponse,
  Claim,
  FNOLCreate,
  FNOLUpdate,
  FNOLConversionResponse,
} from './types'

/**
 * FNOL API service for handling First Notice of Loss related endpoints
 */
export class FNOLApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a paginated list of FNOLs with optional filters
   */
  async getFNOLs(params?: FNOLQueryParams): Promise<PaginatedResponse<FNOLResponse>> {
    const queryParams = new URLSearchParams()

    // Map pagination parameters from page/size to skip/limit for the backend API
    // Backend uses skip/limit instead of page/size
    let skip: number | undefined = undefined
    let limit: number | undefined = undefined

    if (params) {
      // Handle pagination parameters conversion
      if (params.page !== undefined && params.size !== undefined) {
        skip = (params.page - 1) * params.size
        limit = params.size

        // Remove page and size from the params object to avoid duplicating them
        const { page, size, ...restParams } = params
        params = restParams
      }

      // Add all other parameters
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    // Add the skip and limit parameters if they are defined
    if (skip !== undefined) {
      queryParams.append('skip', skip.toString())
    }
    if (limit !== undefined) {
      queryParams.append('limit', limit.toString())
    }

    const endpoint = `/api/v1/fnols?${queryParams.toString()}`
    return this.client.request<PaginatedResponse<FNOLResponse>>(endpoint)
  }

  /**
   * Get a FNOL by ID
   */
  async getFNOLById(id: string): Promise<FNOLResponse> {
    return this.client.request<FNOLResponse>(`/api/v1/fnols/${id}`)
  }

  /**
   * Get a FNOL by Number
   */
  async getFNOLByNumber(fnolNumber: string): Promise<FNOLResponse> {
    return this.client.request<FNOLResponse>(`/api/v1/fnols/number/${fnolNumber}`)
  }

  /**
   * Create a new FNOL report
   */
  async createFNOL(fnolData: FNOLCreate): Promise<FNOLResponse> {
    return this.client.request<FNOLResponse>('/api/v1/fnols', {
      method: 'POST',
      body: JSON.stringify(fnolData),
    })
  }

  /**
   * Update an existing FNOL
   */
  async updateFNOL(id: string, fnolData: FNOLUpdate): Promise<FNOLResponse> {
    return this.client.request<FNOLResponse>(`/api/v1/fnols/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(fnolData),
    })
  }

  /**
   * Delete a FNOL
   */
  async deleteFNOL(id: string): Promise<void> {
    await this.client.request<void>(`/api/v1/fnols/${id}`, {
      method: 'DELETE',
    })
  }

  /*
  /**
   * Update FNOL status
   */
  /*
  async updateFNOLStatus(id: string, status: string): Promise<FNOLResponse> {
    return this.client.request<FNOLResponse>(`/api/v1/fnols/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    })
  }
  */

  /**
   * Convert FNOL to Claim
   */
  async convertFNOLToClaim(id: string, claimType: string): Promise<FNOLConversionResponse> {
    const response = await this.client.request<any>(
      `/api/v1/fnols/${id}/convert?claim_type=${claimType}`,
      {
        method: 'POST',
      }
    )

    // Handle the wrapped response from API proxy
    if (response && response.items && response.items.length > 0) {
      return response.items[0] as FNOLConversionResponse
    }

    // If response is not wrapped, return it directly
    return response as FNOLConversionResponse
  }

  /**
   * Get FNOLs for a specific customer
   */
  async getFNOLsByCustomer(
    customerId: string,
    params?: Omit<FNOLQueryParams, 'customer_id'>
  ): Promise<PaginatedResponse<FNOLResponse>> {
    const queryParams = new URLSearchParams()
    queryParams.append('customer_id', customerId)

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/fnols?${queryParams.toString()}`
    return this.client.request<PaginatedResponse<FNOLResponse>>(endpoint)
  }

  /**
   * Get FNOLs that are pending conversion
   * These are FNOLs with status NEW or REVIEWED that have not yet been converted or rejected
   */
  async getPendingConversionFNOLs(
    params?: Omit<FNOLQueryParams, 'status'>
  ): Promise<PaginatedResponse<FNOLResponse>> {
    const queryParams = new URLSearchParams()

    // Map pagination parameters from page/size to skip/limit for the backend API
    let skip: number | undefined = undefined
    let limit: number | undefined = undefined

    if (params) {
      // Handle pagination parameters conversion
      if (params.page !== undefined && params.size !== undefined) {
        skip = (params.page - 1) * params.size
        limit = params.size

        // Remove page and size from the params object to avoid duplicating them
        const { page, size, ...restParams } = params
        params = restParams
      }

      // Add all other parameters
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString())
        }
      })
    }

    // Add the skip and limit parameters if they are defined
    if (skip !== undefined) {
      queryParams.append('skip', skip.toString())
    }
    if (limit !== undefined) {
      queryParams.append('limit', limit.toString())
    }

    const endpoint = `/api/v1/fnols/pending?${queryParams.toString()}`
    return this.client.request<PaginatedResponse<FNOLResponse>>(endpoint)
  }
}
