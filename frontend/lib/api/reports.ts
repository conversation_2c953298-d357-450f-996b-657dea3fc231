import { ApiClient } from './client'
import {
  ClaimsKPIMetrics,
  ClaimsByTypeResponse,
  ClaimsByStatusResponse,
  ClaimsOverTimeResponse,
  FinancialKPIMetrics,
  PaymentsVsReservesResponse,
  AdjusterPerformanceResponse,
  ReportParams,
} from './types'

export class ReportsApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get Claims KPI Metrics for dashboard
   */
  public async getClaimsKpis(params?: ReportParams): Promise<ClaimsKPIMetrics> {
    console.log('📊 ReportsApi.getClaimsKpis called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getClaimsKpis queryParams:', queryParams)
    const endpoint = `/api/v1/reports/claims-kpis${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<ClaimsKPIMetrics>(endpoint, {
      method: 'GET',
    })
  }

  /**
   * Get Claims by Type distribution
   */
  public async getClaimsByType(params?: ReportParams): Promise<ClaimsByTypeResponse> {
    console.log('📊 ReportsApi.getClaimsByType called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getClaimsByType queryParams:', queryParams)
    const endpoint = `/api/v1/reports/claims-by-type${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<ClaimsByTypeResponse>(endpoint, { method: 'GET' })
  }

  /**
   * Get Claims by Status distribution
   */
  public async getClaimsByStatus(params?: ReportParams): Promise<ClaimsByStatusResponse> {
    console.log('📊 ReportsApi.getClaimsByStatus called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getClaimsByStatus queryParams:', queryParams)
    const endpoint = `/api/v1/reports/claims-by-status${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<ClaimsByStatusResponse>(endpoint, { method: 'GET' })
  }

  /**
   * Get Claims Over Time trend
   */
  public async getClaimsOverTime(params?: ReportParams): Promise<ClaimsOverTimeResponse> {
    console.log('📊 ReportsApi.getClaimsOverTime called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getClaimsOverTime queryParams:', queryParams)
    const endpoint = `/api/v1/reports/claims-over-time${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<ClaimsOverTimeResponse>(endpoint, { method: 'GET' })
  }

  /**
   * Get Financial KPI Metrics
   */
  public async getFinancialKpis(params?: ReportParams): Promise<FinancialKPIMetrics> {
    console.log('📊 ReportsApi.getFinancialKpis called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getFinancialKpis queryParams:', queryParams)
    const endpoint = `/api/v1/reports/financial-kpis${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<FinancialKPIMetrics>(endpoint, { method: 'GET' })
  }

  /**
   * Get Payments vs Reserves comparison
   */
  public async getPaymentsVsReserves(params?: ReportParams): Promise<PaymentsVsReservesResponse> {
    console.log('📊 ReportsApi.getPaymentsVsReserves called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getPaymentsVsReserves queryParams:', queryParams)
    const endpoint = `/api/v1/reports/payments-vs-reserves${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<PaymentsVsReservesResponse>(endpoint, { method: 'GET' })
  }

  /**
   * Get Adjuster Performance metrics
   */
  public async getAdjusterPerformance(params?: ReportParams): Promise<AdjusterPerformanceResponse> {
    console.log('📊 ReportsApi.getAdjusterPerformance called with params:', params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getAdjusterPerformance queryParams:', queryParams)
    const endpoint = `/api/v1/reports/adjuster-performance${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<AdjusterPerformanceResponse>(endpoint, { method: 'GET' })
  }

  /**
   * Generic method to get any report by name
   */
  public async getReportByName<T>(reportName: string, params?: ReportParams): Promise<T> {
    console.log(`📊 ReportsApi.getReportByName called with name: ${reportName}, params:`, params)
    const queryParams = this.buildQueryParams(params)
    console.log('📊 ReportsApi.getReportByName queryParams:', queryParams)
    const endpoint = `/api/v1/reports/${reportName}${queryParams}`
    console.log('📊 Making API request to:', endpoint)
    return this.client.request<T>(endpoint, { method: 'GET' })
  }

  /**
   * Helper to build query parameters string
   */
  private buildQueryParams(params?: ReportParams): string {
    if (!params) return ''

    const queryParams = new URLSearchParams()

    // Add parameters according to the OpenAPI spec
    if (params.period !== undefined) {
      // Format period to match backend expectations (e.g., "6months" -> "last_6_months")
      const periodValue = params.period
      // Extract number and unit using regex (e.g., "6months" -> ["6", "months"])
      const periodMatch = periodValue.match(/^(\d+)([a-z]+)$/)

      if (periodMatch) {
        const [_, number, unit] = periodMatch
        const formattedPeriod = `last_${number}_${unit}`
        console.log(`🔄 Transformed period: ${periodValue} → ${formattedPeriod}`)
        queryParams.append('period', formattedPeriod)
      } else {
        // If format doesn't match expected pattern, pass as is (for backward compatibility)
        console.log(`⚠️ Using original period format: ${periodValue}`)
        queryParams.append('period', periodValue)
      }
    }

    if (params.start_date !== undefined) {
      console.log(`🔄 Adding start_date: ${params.start_date}`)
      queryParams.append('start_date', params.start_date)
    }
    if (params.end_date !== undefined) {
      console.log(`🔄 Adding end_date: ${params.end_date}`)
      queryParams.append('end_date', params.end_date)
    }
    if (params.compare_period !== undefined) {
      console.log(`🔄 Adding compare_period: ${params.compare_period}`)
      queryParams.append('compare_period', params.compare_period.toString())
    }
    if (params.customer_id !== undefined) {
      console.log(`🔄 Adding customer_id: ${params.customer_id}`)
      queryParams.append('customer_id', params.customer_id)
    }
    if (params.claim_type !== undefined) {
      console.log(`🔄 Adding claim_type: ${params.claim_type}`)
      queryParams.append('claim_type', params.claim_type)
    }
    if (params.user_id !== undefined) {
      console.log(`🔄 Adding user_id: ${params.user_id}`)
      queryParams.append('user_id', params.user_id)
    }

    const queryString = queryParams.toString()
    console.log(`🔄 Final query string: ${queryString ? `?${queryString}` : '(empty)'}`)
    return queryString ? `?${queryString}` : ''
  }
}
