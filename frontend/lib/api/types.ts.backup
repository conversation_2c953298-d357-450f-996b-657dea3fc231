// API Authentication Types
// Remove the financials type import we added earlier
// import type { ClaimFinancialsInDB } from '../../types/financials';

export interface TokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
}

export interface LoginRequest {
  username: string
  password: string
}

// Claim Types
export interface Claim {
  id: string
  claim_number: string
  customer_id: string
  type: ClaimType
  status: ClaimStatus
  created_at: string
  updated_at: string

  description?: string | null
  claimant_name?: string | null
  claimant_email?: string | null
  claimant_phone?: string | null
  incident_date?: string | null
  jurisdiction?: string | null
  incident_location?: string | null
  policy_number?: string | null
  assigned_to_id?: string | null
  supervisor_id?: string | null
  recovery_status?: RecoveryStatus | null
  carrier_name?: string | null
  carrier_contact?: string | null
  closed_at?: string | null
  created_by_id?: string | null

  customer?: CustomerResponse | null
  financials?: ClaimFinancialsInDB | null

  documents?: DocumentSchema[] | null
  notes?: NoteSchema[] | null
  tasks?: TaskSchema[] | null
  status_history?: StatusHistorySchema[] | null
  witnesses?: WitnessResponse[] | null
  attorneys?: AttorneyResponse[] | null
}

export enum ClaimType {
  AUTO = 'AUTO',
  PROPERTY = 'PROPERTY',
  GENERAL_LIABILITY = 'GENERAL_LIABILITY',
}

export enum ClaimStatus {
  DRAFT = 'DRAFT',
  INVESTIGATION = 'INVESTIGATION',
  SETTLEMENT = 'SETTLEMENT',
  LITIGATION = 'LITIGATION',
  RECOVERY = 'RECOVERY',
  CLOSED_SETTLED = 'CLOSED_SETTLED',
  CLOSED_DENIED = 'CLOSED_DENIED',
  CLOSED_WITHDRAWN = 'CLOSED_WITHDRAWN',
}

// Add RecoveryStatus enum
export enum RecoveryStatus {
  NOT_STARTED = 'NOT_STARTED',
  INITIATED = 'INITIATED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

// Customer Types
// Renamed from Customer to CustomerResponse to match OpenAPI schema
export interface CustomerResponse {
  id: string
  name: string
  prefix: string
  description?: string | null // Add description field, optional
  active: boolean // Add active field
  created_at: string
  updated_at: string
  // Remove fields not in the CustomerResponse schema:
  // contact_name?: string
  // contact_email?: string
  // contact_phone?: string
  // address?: string
}

// Schema for creating a customer
export interface CustomerCreate {
  name: string
  prefix: string
  description?: string | null
  active?: boolean // Optional, defaults to true on backend
}

// Schema for updating a customer
export interface CustomerUpdate {
  name?: string | null
  description?: string | null
  active?: boolean | null
}

// Task Types
export interface Task {
  id: string
  hr_id: string
  title: string
  description?: string
  status: TaskStatus
  priority: TaskPriority
  due_date?: string
  assignee?: UserReadBasic | null
  claim_id?: string
  claim_number?: string
  created_at: string
  updated_at: string
}

export enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  BLOCKED = 'BLOCKED',
  CANCELLED = 'CANCELLED',
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

// Document Types
export enum DocumentType {
  PHOTO = 'PHOTO',
  REPORT = 'REPORT',
  INVOICE = 'INVOICE',
  STATEMENT = 'STATEMENT',
  CONTRACT = 'CONTRACT',
  POLICY = 'POLICY',
  CORRESPONDENCE = 'CORRESPONDENCE',
  OTHER = 'OTHER',
}

export interface Document {
  id: string
  name: string
  type: DocumentType
  description?: string | null
  claim_id: string
  claim_number?: string | null
  file_path: string
  file_size: number
  mime_type: string
  uploaded_by?: string | null
  created_at: string
  updated_at: string
}

export interface DocumentList {
  items: Document[]
  total: number
}

export interface DocumentUploadUrlRequest {
  file_name: string
  content_type: string
  document_type: DocumentType
}

export interface DocumentUploadUrlResponse {
  upload_url: string
  document_id: string
  expires_at: string
}

export interface DocumentDownloadUrlResponse {
  download_url: string
  expires_at: string
  should_download?: boolean // Optional flag to indicate if browser should force download
}

// FNOL Types
// Renamed from FNOL to FNOLResponse to match OpenAPI schema
export interface FNOLResponse {
  id: string // uuid
  fnol_number: string
  reported_by: string // Was reporter_name, matches backend
  // reporter_contact: string; // Removed, not in OpenAPI schema for FNOLResponse
  incident_date?: string | null // ISO 8601 date string, optional in base
  description?: string | null // Was incident_description, optional in base
  // status: FNOLStatus; // Commented out: Not found in openapi-spec/components/schemas/FNOLResponse.json definition
  customer_id: string // uuid
  policy_number?: string | null
  reported_at: string // date-time, required in OpenAPI FNOLResponse
  created_at: string // date-time
  updated_at: string // date-time

  // Optional fields from OpenAPI base schema, now added here explicitly as optional
  incident_location?: string | null // maxLength: 500
  incident_time?: string | null // ISO time string e.g., "HH:MM:SS"
  reporter_relationship?: ReporterRelationship | null // Use enum
  communication_preference?: CommunicationPreference | null // Use enum

  // Optional included fields based on API 'include' parameter
  customer?: CustomerResponse // Add optional customer
  claims?: Claim[] // Add optional claims array
}

// Added based on OpenAPI schema for POST /api/v1/fnols
export interface FNOLCreate {
  customer_id: string // Corresponds to UUID
  reported_by: string // Was reporter_name, matches backend Field(..., max_length=200)
  description?: string | null // Was incident_description, matches backend Optional[str]
  incident_date?: string | null // Expects ISO 8601 date string, matches backend Optional[date]
  incident_location?: string | null // Matches backend Optional[str], maxLength: 500
  policy_number?: string | null // Matches backend Optional[str], maxLength: 100
  incident_time?: string | null // Matches backend Optional[time], ISO time string e.g., "HH:MM:SS"
  reporter_relationship?: ReporterRelationship | null // Use enum
  communication_preference?: CommunicationPreference | null // Use enum
  fnol_number?: string | null // From backend FNOLCreate, optional & auto-generated, pattern: "^[A-Z0-9]{4}-FNOL-\\d{7}$"
  // reporter_contact was removed as it's not in backend FNOLBase/FNOLCreate
  // status is likely set by the backend, not provided on create
}

// Added based on OpenAPI schema for PATCH /api/v1/fnols/{fnol_id}
export interface FNOLUpdate {
  reporter_name?: string
  reporter_contact?: string
  incident_date?: string
  incident_description?: string
  status?: FNOLStatus // Status might be updatable
  policy_number?: string | null
  // customer_id is likely not updatable after creation
}

export enum FNOLStatus {
  NEW = 'NEW',
  REVIEWED = 'REVIEWED',
  CONVERTED = 'CONVERTED',
  REJECTED = 'REJECTED',
}

// Common types for pagination and responses
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

// API Error class for structured error handling
export class ApiError extends Error {
  status_code: number
  data?: any
  url?: string

  constructor(message: string, statusCode: number = 500) {
    super(message)
    this.name = 'ApiError'
    this.status_code = statusCode

    // For better compatibility with Error
    Object.setPrototypeOf(this, ApiError.prototype)
  }
}

// API Filter/Query Parameters
export interface ClaimQueryParams {
  page?: number
  size?: number
  claim_number?: string
  status?: ClaimStatus
  type?: ClaimType
  claimant_name?: string
  customer_id?: string
}

export interface TaskQueryParams {
  page?: number
  size?: number
  status?: TaskStatus
  priority?: TaskPriority
  claim_id?: string
  assignee?: string
}

export interface DocumentQueryParams {
  skip?: number
  limit?: number
  claim_id?: string
  document_type?: DocumentType
}

export interface FNOLQueryParams {
  page?: number
  size?: number
  status?: FNOLStatus
  customer_id?: string
}

// Note Types
export interface Note {
  id: string
  content: string
  claim_id: string
  author_id?: string
  author?: string
  author_email?: string
  created_at: string
  updated_at: string
}

export interface NoteCreate {
  content: string
  claim_id: string
}

export interface NoteUpdate {
  content: string
}

// Remove the financial type re-export block we added earlier
// export type {
//   ClaimFinancialsInDB,
//   PaymentList,
//   PaymentResponse,
//   PaymentCreate,
//   PaymentType,
//   ReserveHistoryInDB,
//   ReserveResponse,
//   ReserveType,
//   ReserveUpdate
// };

// --- Financial Types (Moved from ../../types/financials.ts) ---

// --- Enums ---

export enum ReserveType {
  BODILY_INJURY = 'BODILY_INJURY',
  PROPERTY_DAMAGE = 'PROPERTY_DAMAGE',
  MEDICAL_PAYMENTS = 'MEDICAL_PAYMENTS',
  LOSS_OF_USE = 'LOSS_OF_USE',
  BUSINESS_INTERRUPTION = 'BUSINESS_INTERRUPTION',
  DEFENSE_COST = 'DEFENSE_COST',
  ALLOCATED_EXPENSE = 'ALLOCATED_EXPENSE',
  UNALLOCATED_EXPENSE = 'UNALLOCATED_EXPENSE',
}

export enum PaymentType {
  INDEMNITY = 'INDEMNITY',
  EXPENSE = 'EXPENSE',
  DEFENSE = 'DEFENSE',
}

// --- Response Schemas ---

export interface ReserveResponse {
  reserve_type: ReserveType
  amount: string // Representing Decimal as string
  id: string // uuid
  created_at: string // date-time
  updated_at: string // date-time
}

export interface ClaimFinancialsInDB {
  estimated_value: string // Decimal as string
  indemnity_paid?: string | null // Decimal as string
  expense_paid?: string | null // Decimal as string
  defense_paid?: string | null // Decimal as string
  recovery_expected?: string | null // Decimal as string
  recovery_received?: string | null // Decimal as string
  deductible_amount?: string | null // Decimal as string
  coverage_limit?: string | null // Decimal as string
  currency: string
  id: string // uuid
  claim_id: string // uuid
  claim_number: string
  reserves: ReserveResponse[]
  last_reserve_change?: string | null // date-time
  last_payment_date?: string | null // date-time
  created_at: string // date-time
  updated_at: string // date-time
}

export interface ReserveHistoryInDB {
  id: string // uuid
  claim_id: string // uuid
  claim_number?: string | null
  financials_id: string // uuid
  reserve_type: ReserveType
  previous_amount: string // Decimal as string
  new_amount: string // Decimal as string
  changed_by_id: string // uuid
  changed_at: string // date-time
  notes?: string | null
  // Add changed_by user details if needed/available from backend
  // changed_by?: UserReadBasic | null; // Temporarily commented out
}

export interface PaymentResponse {
  payment_type: PaymentType
  amount: string // Decimal as string
  payee: string
  payment_date: string // date-time
  notes?: string | null
  id: string // uuid
  financials_id: string // uuid
  created_by_id: string // uuid
  created_at: string // date-time
  updated_at: string // date-time
  // Add created_by user details if needed/available from backend
  // created_by?: UserReadBasic | null; // Temporarily commented out
}

export interface PaymentList {
  items: PaymentResponse[]
  total: number
}

// --- Request/Update Schemas ---

export interface ReserveUpdate {
  reserve_type: ReserveType
  amount: number | string // Allow number for input, convert to string if needed
  notes?: string | null
}

export interface PaymentCreate {
  payment_type: PaymentType
  amount: number | string // Allow number for input, convert to string if needed
  payee: string
  payment_date: string // Should be ISO string e.g., Date().toISOString()
  notes?: string | null
}

// Report response types
export interface ClaimsKPIMetrics {
  total_claims?: number
  total_claims_change?: number
  open_claims?: number
  open_claims_change?: number
  open_claims_percentage?: number
  open_claims_percentage_change?: number
  closed_claims?: number
  closed_claims_change?: number
  closed_claims_percentage?: number
  closed_claims_percentage_change?: number
  avg_resolution_time?: number
  avg_resolution_time_change?: number
  comparison?: {
    total_claims: number
    open_claims_percentage: number
    closed_claims_percentage: number
    avg_resolution_time: number
  }
  items?: ClaimsKPIMetrics[]
  total?: number
  data?: any
  report_metadata?: any
}

export interface ClaimTypeData {
  type: ClaimType
  count: number
  percentage: number
}

export interface ClaimsByTypeResponse {
  data?: ClaimTypeData[]
  items?: ClaimTypeData[]
  total?: number
}

export interface ClaimStatusData {
  status: ClaimStatus
  count: number
  percentage: number
}

export interface ClaimsByStatusResponse {
  data?: ClaimStatusData[]
  items?: ClaimStatusData[]
  total?: number
}

export interface TimeSeriesPoint {
  period: string
  new_claims: number
  closed_claims: number
}

export interface ClaimsOverTimeResponse {
  data?: TimeSeriesPoint[]
  items?: TimeSeriesPoint[]
  total?: number
}

// Financial tab interfaces
export interface FinancialKPIMetrics {
  total_reserves?: number
  total_reserves_change?: number
  total_payments?: number
  total_payments_change?: number
  avg_claim_value?: number
  avg_claim_value_change?: number
  recovery_amount?: number
  recovery_amount_change?: number
  comparison?: {
    total_reserves: number
    total_payments: number
    avg_claim_value: number
    recovery_amount: number
  }
  items?: FinancialKPIMetrics[]
  total?: number
  data?: any
  report_metadata?: any
}

export interface PaymentReserveTimePoint {
  period: string
  payments: number
  reserves: number
}

export interface PaymentsVsReservesResponse {
  data?: PaymentReserveTimePoint[]
  items?: PaymentReserveTimePoint[]
  total?: number
}

// Performance tab interfaces
export interface AdjusterMetrics {
  adjuster_id: string
  adjuster_name: string
  claims_handled: number
  avg_resolution_time: number
  total_payments: number
  pending_tasks: number
  overdue_tasks: number
}

export interface AdjusterPerformanceResponse {
  data?: AdjusterMetrics[]
  items?: AdjusterMetrics[]
  total?: number
}

// Report parameters
export interface ReportParams {
  period?: string
  start_date?: string
  end_date?: string
  compare_period?: boolean
  customer_id?: string
  claim_type?: string
  user_id?: string
}

// Dashboard Metrics Response
export interface MetricChange {
  value: number
  percentage: number
  direction: 'up' | 'down' | 'no_change'
}

export interface DashboardMetricsResponse {
  total_claims: number
  open_claims: number
  new_claims_last_period: number
  closed_claims_last_period: number
  average_claim_lifecycle_days?: number
  average_claim_lifecycle_days_change?: MetricChange
  total_payments_last_period: number
  total_payments_change?: MetricChange
  total_outstanding_reserves: number
  total_outstanding_reserves_change?: MetricChange
  tasks_pending: number
  tasks_overdue: number
  fnols_pending: number
  reserve_trend?: number
}

// ADDING NEW TYPES HERE
export interface ReserveCreate {
  reserve_type: ReserveType
  amount: number | string // Representing Decimal as string or number for input
}

export interface ClaimFinancialsCreate {
  estimated_value: number | string // Representing Decimal as string or number for input
  indemnity_paid?: number | string | null
  expense_paid?: number | string | null
  defense_paid?: number | string | null
  recovery_expected?: number | string | null
  recovery_received?: number | string | null
  deductible_amount?: number | string | null
  coverage_limit?: number | string | null
  currency?: string // Defaults to USD on backend
  reserves: ReserveCreate[]
}

// Matches openapi.json TaskCreateRequestBody
export interface ClaimTaskCreateRequestBody {
  title: string
  description?: string | null
  priority?: TaskPriority | string
  status?: TaskStatus | string
  due_date?: string | null
  assigned_to?: string | null
}

// User summary for dropdowns, etc.
export interface UserSummary {
  id: string // UUID
  firstName: string
  lastName: string
  email?: string | null
  fullName?: string // Optional, can be constructed
}

// Based on openapi.json components.schemas.UserResponse
export interface UserResponse {
  id: string // uuid
  email: string // format: email
  first_name: string // minLength: 1, maxLength: 100
  last_name: string // minLength: 1, maxLength: 100
  role: UserRole // Enum, Required in OpenAPI
  authority_role: AuthorityRole // Enum, Default: NO_AUTHORITY in OpenAPI - assuming always present in response
  status: UserStatus // Enum, Default: PENDING in OpenAPI - assuming always present in response
  department?: string | null // maxLength: 100
  job_title?: string | null // maxLength: 100
  phone_number?: string | null // maxLength: 20
  timezone: string // maxLength: 50, Default: "UTC" in OpenAPI - assuming always present
  preferences?: Record<string, any> | null // OpenAPI: additionalProperties: true, type: "object"
  created_at: string // date-time
  updated_at: string // date-time
  last_login_at?: string | null // date-time
  email_verified_at?: string | null // date-time
  last_password_change_at?: string | null // date-time
  force_password_change: boolean // Required in OpenAPI
  locked_until?: string | null // date-time
  failed_login_attempts: number // Integer, Required in OpenAPI
  permissions: string[] // Default: [] in OpenAPI - assuming always present, possibly empty array
  // Deprecated fields or fields not in current UserResponse.json have been removed/aligned.
}

// Enums for UserResponse if not already defined elsewhere centrally
// export enum UserRole { ADMIN = 'ADMIN', ... } // Define based on openapi
// export enum UserStatus { ACTIVE = 'ACTIVE', ... } // Define based on openapi
// export enum AuthorityRole { BASIC = 'BASIC', ... } // Define based on openapi

// END OF ADDING NEW TYPES

// UserReadBasic based on OpenAPI schema for embedded user details
export interface UserReadBasic {
  id: string
  email: string
  // Add other fields like first_name, last_name, fullName if the backend
  // consistently provides them within the UserReadBasic schema embedded in tasks.
  // For now, sticking to what's guaranteed by openapi.json for TaskRead.assignee.
}

// Witness types based on OpenAPI schema
export interface WitnessResponse {
  id: string
  claim_id: string
  claim_number?: string | null
  name: string
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
  created_at: string
  updated_at: string
}

export interface WitnessCreate {
  name: string
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
}

export interface WitnessUpdate {
  name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  statement?: string | null
}

// Attorney types based on OpenAPI schema
export enum AttorneyType {
  PLAINTIFF = 'PLAINTIFF',
  DEFENSE = 'DEFENSE',
  COVERAGE = 'COVERAGE',
  MONITORING = 'MONITORING',
  OTHER = 'OTHER',
}

export interface AttorneyResponse {
  id: string
  claim_id: string
  claim_number?: string | null
  name: string
  attorney_type: AttorneyType
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
  created_at: string
  updated_at: string
}

export interface AttorneyCreate {
  name: string
  attorney_type: AttorneyType
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
}

export interface AttorneyUpdate {
  name?: string | null
  attorney_type?: AttorneyType | null
  firm_name?: string | null
  email?: string | null
  phone?: string | null
  address?: string | null
  notes?: string | null
}

// Audit Types
export enum EntityType {
  CLAIM = 'CLAIM',
  CLAIM_DETAILS = 'CLAIM_DETAILS',
  AUTO_DETAILS = 'AUTO_DETAILS',
  PROPERTY_DETAILS = 'PROPERTY_DETAILS',
  GL_DETAILS = 'GL_DETAILS',
  WITNESS = 'WITNESS',
  ATTORNEY = 'ATTORNEY',
  FINANCIAL = 'FINANCIAL',
  RESERVE = 'RESERVE',
  PAYMENT = 'PAYMENT',
  DOCUMENT = 'DOCUMENT',
}

export enum ChangeType {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export interface AuditTrailResponse {
  id: string
  claim_id: string
  entity_type: EntityType
  entity_id?: string | null
  change_type: ChangeType
  field_name?: string | null
  previous_value?: any | null
  new_value?: any | null
  description?: string | null
  changed_by_id?: string | null
  changed_at: string
}

export interface PaginatedAuditTrailResponse {
  items: AuditTrailResponse[]
  total: number
  skip: number
  limit: number
}

export interface AuditSummaryResponse {
  total_entries: number
  by_entity: Record<string, number>
  by_change_type: Record<string, number>
  by_user: Record<string, number>
  recent_activity?: AuditTrailResponse[]
}

// Bodily Injury related enums
export enum InjuredPersonType {
  // General Liability specific
  GUEST = 'GUEST',
  PATRON = 'PATRON',
  EMPLOYEE = 'EMPLOYEE',
  TENANT = 'TENANT',
  CONTRACTOR = 'CONTRACTOR',
  VENDOR = 'VENDOR',
  TRESPASSER = 'TRESPASSER',
  VISITOR = 'VISITOR',

  // Auto specific
  DRIVER = 'DRIVER',
  PASSENGER = 'PASSENGER',
  PEDESTRIAN = 'PEDESTRIAN',
  CYCLIST = 'CYCLIST',
  OTHER_DRIVER = 'OTHER_DRIVER',
  OTHER_PASSENGER = 'OTHER_PASSENGER',

  // General categories
  BYSTANDER = 'BYSTANDER',
  UNKNOWN = 'UNKNOWN',
}

export enum IncidentReportStatus {
  FILED = 'FILED',
  PENDING = 'PENDING',
  NOT_REQUIRED = 'NOT_REQUIRED',
}

export enum MedicalTreatmentRequirements {
  NONE = 'NONE',
  FIRST_AID = 'FIRST_AID',
  OUTPATIENT = 'OUTPATIENT',
  HOSPITALIZATION = 'HOSPITALIZATION',
  SURGERY = 'SURGERY',
  PHYSICAL_THERAPY = 'PHYSICAL_THERAPY',
  ONGOING_CARE = 'ONGOING_CARE',
}

export enum InsuranceBillingStatus {
  NOT_SUBMITTED = 'NOT_SUBMITTED',
  PENDING = 'PENDING',
  PARTIAL_PAYMENT = 'PARTIAL_PAYMENT',
  PAID = 'PAID',
  DENIED = 'DENIED',
}

// Bodily Injury response interface
export interface BodilyInjuryDetails {
  id: string
  gl_details_id?: string
  auto_details_id?: string
  injury_description?: string
  incident_location?: string
  injured_person_type?: InjuredPersonType
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
  created_at: string
  updated_at: string
}

// Bodily Injury update interface for sending data
export interface BodilyInjuryDetailsUpdate {
  injury_description?: string
  incident_location?: string
  injured_person_type?: InjuredPersonType
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

// New interfaces for injured persons and injuries

export interface Injury {
  id: string
  injured_person_id: string
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
  created_at: string
  updated_at: string
}

export interface InjuryCreate {
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

export interface InjuryUpdate {
  injury_description?: string
  injury_type?: string
  injury_severity?: string
  equipment_involved?: boolean
  equipment_details?: string
  equipment_owned_by_insured?: boolean
  safety_measures_involved?: boolean
  safety_measures_description?: string
  medical_treatment_requirements?: MedicalTreatmentRequirements
  treatment_nature?: string
  medical_provider_name?: string
  medical_provider_address?: string
  estimated_cost?: number
  insurance_billing_status?: InsuranceBillingStatus
}

export interface InjuredPerson {
  id: string
  gl_details_id?: string
  auto_details_id?: string
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
  created_at: string
  updated_at: string
  injuries?: Injury[]
}

export interface InjuredPersonCreate {
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
}

export interface InjuredPersonUpdate {
  name?: string
  person_type?: InjuredPersonType
  contact_info?: string
  age?: number
  incident_location?: string
  incident_description?: string
  incident_report_status?: IncidentReportStatus
  report_filer_name?: string
  report_filer_contact?: string
}

// --- BEGIN ENUM DEFINITIONS (NEW/UPDATED) ---

export enum CommunicationPreference {
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  TEXT = 'TEXT',
  MAIL = 'MAIL',
  PORTAL = 'PORTAL',
  NO_PREFERENCE = 'NO_PREFERENCE',
}

export enum ReporterRelationship {
  CLAIMANT = 'CLAIMANT',
  INSURED = 'INSURED',
  AGENT = 'AGENT',
  THIRD_PARTY = 'THIRD_PARTY',
  FAMILY_MEMBER = 'FAMILY_MEMBER',
  LEGAL_REPRESENTATIVE = 'LEGAL_REPRESENTATIVE',
  OTHER = 'OTHER',
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  ADJUSTER = 'ADJUSTER',
  AGENT = 'AGENT',
  CUSTOMER = 'CUSTOMER',
}

export enum AuthorityRole {
  NO_AUTHORITY = 'NO_AUTHORITY',
  BASIC = 'BASIC',
  INTERMEDIATE = 'INTERMEDIATE',
  SENIOR = 'SENIOR',
  SUPERVISOR = 'SUPERVISOR',
  MANAGER = 'MANAGER',
  UNLIMITED = 'UNLIMITED',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  SUSPENDED = 'SUSPENDED',
}

// --- END ENUM DEFINITIONS ---

// BEGIN USER SCHEMAS //

export interface UserCreate {
  email: string // format: email
  first_name: string // minLength: 1, maxLength: 100
  last_name: string // minLength: 1, maxLength: 100
  role: UserRole // Enum
  password: string // minLength: 8, maxLength: 64
  authority_role?: AuthorityRole | null // Enum, default: NO_AUTHORITY in OpenAPI
  status?: UserStatus | null // Enum, default: PENDING in OpenAPI
  department?: string | null // maxLength: 100
  job_title?: string | null // maxLength: 100
  phone_number?: string | null // maxLength: 20
  timezone?: string | null // maxLength: 50, default: "UTC" in OpenAPI
  preferences?: Record<string, any> | null // OpenAPI: additionalProperties: true, type: "object"
}

// Placeholder for UserResponse if we need to re-declare it or it's far below
// We will update UserResponse interface next, based on its existing location or add it here if not found conveniently.

// END USER SCHEMAS //
