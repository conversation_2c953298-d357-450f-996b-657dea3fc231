/**
 * Utilities for transforming between camelCase and snake_case
 * These utilities help ensure consistent property naming between frontend and API
 */

/**
 * Converts a string from snake_case to camelCase
 */
export function snakeToCamel(str: string): string {
  return str.replace(/_([a-z])/g, (match, char) => char.toUpperCase())
}

/**
 * Converts a string from camelCase to snake_case
 */
export function camelToSnake(str: string): string {
  return str.replace(/[A-Z]/g, match => `_${match.toLowerCase()}`)
}

/**
 * Recursively transforms object keys from snake_case to camelCase
 */
export function transformSnakeToCamel<T>(obj: any): T {
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(transformSnakeToCamel) as unknown as T
  }

  return Object.keys(obj).reduce((result, key) => {
    const camelKey = snakeToCamel(key)
    const value = obj[key]
    const transformedValue = transformSnakeToCamel(value)

    result[camelKey] = transformedValue
    return result
  }, {} as any) as T
}

/**
 * Recursively transforms object keys from camelCase to snake_case
 */
export function transformCamelToSnake<T>(obj: any): T {
  if (obj === null || obj === undefined || typeof obj !== 'object') {
    return obj
  }

  if (Array.isArray(obj)) {
    return obj.map(transformCamelToSnake) as unknown as T
  }

  return Object.keys(obj).reduce((result, key) => {
    const snakeKey = camelToSnake(key)
    const value = obj[key]
    const transformedValue = transformCamelToSnake(value)

    result[snakeKey] = transformedValue
    return result
  }, {} as any) as T
}
