/**
 * Development-only logging utility
 * Disables console.log statements in production for better performance
 */

const isDevelopment = process.env.NODE_ENV === 'development'
const isTest = process.env.NODE_ENV === 'test'

/**
 * Logger interface for consistent logging across the application
 */
export interface Logger {
  debug: (message: string, ...args: any[]) => void
  info: (message: string, ...args: any[]) => void
  warn: (message: string, ...args: any[]) => void
  error: (message: string, ...args: any[]) => void
  log: (message: string, ...args: any[]) => void
}

/**
 * Development logger - logs everything to console
 */
const developmentLogger: Logger = {
  debug: (message: string, ...args: any[]) => console.debug(`🐛 ${message}`, ...args),
  info: (message: string, ...args: any[]) => console.info(`ℹ️ ${message}`, ...args),
  warn: (message: string, ...args: any[]) => console.warn(`⚠️ ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`❌ ${message}`, ...args),
  log: (message: string, ...args: any[]) => console.log(`📝 ${message}`, ...args),
}

/**
 * Production logger - only logs errors and warnings
 */
const productionLogger: Logger = {
  debug: () => {}, // No-op in production
  info: () => {}, // No-op in production
  warn: (message: string, ...args: any[]) => console.warn(`⚠️ ${message}`, ...args),
  error: (message: string, ...args: any[]) => console.error(`❌ ${message}`, ...args),
  log: () => {}, // No-op in production
}

/**
 * Test logger - minimal logging for tests
 */
const testLogger: Logger = {
  debug: () => {}, // No-op in tests
  info: () => {}, // No-op in tests
  warn: () => {}, // No-op in tests
  error: (message: string, ...args: any[]) => console.error(`❌ ${message}`, ...args),
  log: () => {}, // No-op in tests
}

/**
 * Main logger instance - automatically selects appropriate logger based on environment
 */
export const logger: Logger = isTest
  ? testLogger
  : isDevelopment
    ? developmentLogger
    : productionLogger

/**
 * API-specific logger for request/response logging
 */
export const apiLogger = {
  request: (method: string, url: string, requestId?: string) => {
    if (isDevelopment) {
      const id = requestId ? `[${requestId}]` : ''
      logger.info(`${id} 🚀 ${method} ${url}`)
    }
  },
  response: (method: string, url: string, status: number, duration: number, requestId?: string) => {
    if (isDevelopment) {
      const id = requestId ? `[${requestId}]` : ''
      const statusEmoji = status >= 400 ? '❌' : status >= 300 ? '⚠️' : '✅'
      logger.info(`${id} ${statusEmoji} ${method} ${url} - ${status} (${duration}ms)`)
    }
  },
  error: (method: string, url: string, error: any, requestId?: string) => {
    const id = requestId ? `[${requestId}]` : ''
    logger.error(`${id} 💥 ${method} ${url}`, error)
  },
}

/**
 * Component-specific logger for React component debugging
 */
export const componentLogger = {
  mount: (componentName: string) => {
    logger.debug(`🔧 ${componentName} mounted`)
  },
  unmount: (componentName: string) => {
    logger.debug(`🗑️ ${componentName} unmounted`)
  },
  render: (componentName: string, props?: any) => {
    if (isDevelopment && props) {
      logger.debug(`🎨 ${componentName} rendering`, props)
    } else {
      logger.debug(`🎨 ${componentName} rendering`)
    }
  },
  effect: (componentName: string, effectName: string, dependencies?: any[]) => {
    if (isDevelopment) {
      logger.debug(`⚡ ${componentName} effect: ${effectName}`, dependencies)
    }
  },
}

/**
 * Performance logger for timing operations
 */
export const performanceLogger = {
  start: (operation: string): (() => void) => {
    if (!isDevelopment) return () => {}

    const startTime = performance.now()
    logger.debug(`⏱️ Starting: ${operation}`)

    return () => {
      const duration = performance.now() - startTime
      logger.debug(`⏱️ Completed: ${operation} (${duration.toFixed(2)}ms)`)
    }
  },
  mark: (label: string) => {
    if (isDevelopment) {
      logger.debug(`📍 Performance mark: ${label}`)
    }
  },
}

// Legacy console replacement for gradual migration
export const console = {
  log: logger.log,
  info: logger.info,
  warn: logger.warn,
  error: logger.error,
  debug: logger.debug,
}

export default logger
