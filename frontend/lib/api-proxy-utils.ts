/**
 * Utility functions for API proxy routes
 * Standardizes request handling, error handling, and response formatting across all proxy routes
 */
import { NextRequest, NextResponse } from 'next/server'
import { LogLevel, logRequest, logResponse, logError, createTimer } from './api-logging'
import { HTTP_STATUS } from './constants'
import { logger } from './logger'

// Backend API base URL
export const BACKEND_URL = process.env.BACKEND_API_URL || 'http://localhost:8000'

// Error response types
export interface ErrorResponse {
  detail: string
  status: number
  error_code?: string
  additional_info?: any
  request_id?: string
}

// Supported content types for requests
export enum ContentType {
  JSON = 'application/json',
  FORM_DATA = 'application/x-www-form-urlencoded',
  MULTIPART = 'multipart/form-data',
}

// Standard error codes
export enum ErrorCode {
  AUTH_REQUIRED = 'AUTH_REQUIRED',
  AUTH_INVALID = 'AUTH_INVALID',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BACKEND_ERROR = 'BACKEND_ERROR',
  API_ROUTE_ERROR = 'API_ROUTE_ERROR',
  JSON_PARSE_ERROR = 'JSON_PARSE_ERROR',
  RESPONSE_READ_ERROR = 'RESPONSE_READ_ERROR',
  CONTENT_TYPE_ERROR = 'CONTENT_TYPE_ERROR',
}

/**
 * Validate authentication header from request
 * @param request NextRequest object
 * @param requestId Request ID for correlation
 * @returns Authentication header value or null if not found
 */
export function getAuthHeader(request: NextRequest, requestId?: string): string | null {
  const authHeader = request.headers.get('Authorization')
  if (!authHeader) {
    const method = request.method
    const path = request.nextUrl.pathname

    if (requestId) {
      logError(
        requestId,
        method,
        path,
        HTTP_STATUS.UNAUTHORIZED,
        'No Authorization header found',
        0
      )
    } else {
      logger.error('No Authorization header found')
    }

    return null
  }
  return authHeader
}

/**
 * Validate content type from request
 * @param request NextRequest object
 * @param allowedTypes Array of allowed content types
 * @param requestId Request ID for correlation
 * @returns True if content type is valid, false otherwise
 */
export function validateContentType(
  request: NextRequest,
  allowedTypes: ContentType[] = [ContentType.JSON],
  requestId?: string
): boolean {
  const contentType = request.headers.get('Content-Type')

  if (!contentType) {
    // For GET requests, content-type is often not set and that's acceptable
    if (request.method === 'GET') {
      return true
    }

    const method = request.method
    const path = request.nextUrl.pathname

    if (requestId) {
      logError(requestId, method, path, 400, 'Content-Type header is required', 0)
    } else {
      logger.error('No Content-Type header found')
    }

    return false
  }

  // Check if the content type matches any of the allowed types
  // Only check the main content type (before the ;)
  const mainContentType = contentType.split(';')[0].trim().toLowerCase()

  // Special case for multipart/form-data: it's common to have a boundary parameter
  if (
    contentType.startsWith(ContentType.MULTIPART) &&
    allowedTypes.includes(ContentType.MULTIPART)
  ) {
    return true
  }

  const isValid = allowedTypes.some(type => mainContentType.includes(type.toLowerCase()))

  if (!isValid) {
    const method = request.method
    const path = request.nextUrl.pathname

    if (requestId) {
      logError(
        requestId,
        method,
        path,
        415,
        `Unsupported content type: ${contentType}. Allowed types: ${allowedTypes.join(', ')}`,
        0
      )
    } else {
      logger.error(
        `Unsupported content type: ${contentType}. Allowed types: ${allowedTypes.join(', ')}`
      )
    }
  }

  return isValid
}

/**
 * Create a standard error response
 * @param message Error message
 * @param status HTTP status code
 * @param errorCode Optional error code from ErrorCode enum
 * @param additionalInfo Additional error information
 * @param requestId Request ID for correlation
 * @param method HTTP method
 * @param path API path
 * @returns NextResponse with error details
 */
export function createErrorResponse(
  message: string,
  status: number = HTTP_STATUS.INTERNAL_SERVER_ERROR,
  errorCode?: string,
  additionalInfo?: any,
  requestId?: string,
  method?: string,
  path?: string
): NextResponse {
  const errorResponse: ErrorResponse = {
    detail: message,
    status,
  }

  if (errorCode) {
    errorResponse.error_code = errorCode
  }

  if (additionalInfo) {
    errorResponse.additional_info = additionalInfo
  }

  if (requestId) {
    errorResponse.request_id = requestId
  }

  // Use structured logging if we have enough context
  if (requestId && method && path) {
    const duration = 0 // We don't have timing info in this context
    logError(requestId, method, path, status, message, duration)
  } else {
    // Fallback to simpler logging
    logger.error(
      `Error response: ${status} - ${message}`,
      errorCode ? `Code: ${errorCode}` : '',
      requestId ? `Request ID: ${requestId}` : '',
      additionalInfo ? `Additional info: ${JSON.stringify(additionalInfo)}` : ''
    )
  }

  return NextResponse.json(errorResponse, { status })
}

/**
 * Handle authentication errors
 * @param requestId Request ID for correlation
 * @param method HTTP method
 * @param path API path
 * @returns NextResponse with 401 status
 */
export function handleAuthError(requestId?: string, method?: string, path?: string): NextResponse {
  return createErrorResponse(
    'Authorization header is required',
    HTTP_STATUS.UNAUTHORIZED,
    ErrorCode.AUTH_REQUIRED,
    undefined,
    requestId,
    method,
    path
  )
}

/**
 * Process backend response
 * Handles error cases, JSON parsing, and standardizes response format
 * @param response Fetch API Response object
 * @param fallbackErrorMessage Message to use if error details can't be extracted
 * @param requestId Request ID for correlation
 * @param method HTTP method
 * @param path API path
 * @returns NextResponse with processed data
 */
export async function processBackendResponse(
  response: Response,
  fallbackErrorMessage: string = 'Backend error',
  requestId?: string,
  method?: string,
  path?: string
): Promise<NextResponse> {
  // Handle error responses from backend
  if (!response.ok) {
    // Log error with request ID if available
    if (requestId && method && path) {
      logError(
        requestId,
        method,
        path,
        response.status,
        `Backend returned error status: ${response.status}`,
        0
      )
    } else {
      console.error(`❌ Backend returned error status: ${response.status}`)
    }

    try {
      const responseText = await response.text()

      if (requestId) {
        console.log(`[${requestId}] 📄 Error response text:`, responseText.substring(0, 200))
      } else {
        console.log('📄 Error response text:', responseText.substring(0, 200))
      }

      if (responseText && responseText.trim().length > 0) {
        try {
          const errorData = JSON.parse(responseText)

          // Map error code to our standard error codes if possible
          let errorCode = errorData.error_code
          if (!errorCode) {
            // Try to map HTTP status to standard error code
            switch (response.status) {
              case HTTP_STATUS.UNAUTHORIZED:
                errorCode = ErrorCode.AUTH_INVALID
                break
              case HTTP_STATUS.FORBIDDEN:
                errorCode = ErrorCode.FORBIDDEN
                break
              case HTTP_STATUS.NOT_FOUND:
                errorCode = ErrorCode.NOT_FOUND
                break
              case HTTP_STATUS.UNPROCESSABLE_ENTITY:
                errorCode = ErrorCode.VALIDATION_ERROR
                break
              default:
                errorCode = ErrorCode.BACKEND_ERROR
            }
          }

          return createErrorResponse(
            errorData.message || errorData.detail || fallbackErrorMessage,
            response.status,
            errorCode,
            errorData,
            requestId,
            method,
            path
          )
        } catch (e) {
          return createErrorResponse(
            responseText,
            response.status,
            ErrorCode.BACKEND_ERROR,
            undefined,
            requestId,
            method,
            path
          )
        }
      } else {
        return createErrorResponse(
          `${fallbackErrorMessage}: ${response.statusText || `Status ${response.status}`}`,
          response.status,
          ErrorCode.BACKEND_ERROR,
          undefined,
          requestId,
          method,
          path
        )
      }
    } catch (e) {
      return createErrorResponse(
        `${fallbackErrorMessage}: ${response.status}`,
        response.status,
        ErrorCode.BACKEND_ERROR,
        undefined,
        requestId,
        method,
        path
      )
    }
  }

  // Handle successful responses
  const contentType = response.headers.get('content-type')

  // Handle no-content responses
  if (response.status === HTTP_STATUS.NO_CONTENT) {
    console.log('📭 No content response, returning empty body')
    return new NextResponse(null, { status: HTTP_STATUS.NO_CONTENT })
  }

  // Handle responses with no content type
  if (!contentType) {
    console.log('📭 No content type, returning empty object')
    return NextResponse.json({}, { status: response.status })
  }

  // Handle non-JSON responses
  if (!contentType.includes('application/json')) {
    console.log('📄 Non-JSON response, returning as text')
    const text = await response.text()
    return NextResponse.json({ text }, { status: response.status })
  }

  // Parse JSON responses
  try {
    const responseText = await response.text()

    if (!responseText || responseText.trim().length === 0) {
      console.log('📭 Empty response text, returning empty object')
      return NextResponse.json({}, { status: response.status })
    }

    try {
      const data = JSON.parse(responseText)

      // Standardize response format
      // If array, wrap in {items: [], total: n}
      if (Array.isArray(data)) {
        return NextResponse.json({ items: data, total: data.length }, { status: response.status })
      }

      // If object with no items property but should be paginated, wrap the single item
      if (!data.items && !Array.isArray(data) && typeof data === 'object' && data !== null) {
        // Check if this is a single resource that shouldn't be wrapped
        if (data.id || data.uuid) {
          return NextResponse.json(data, { status: response.status })
        } else {
          return NextResponse.json({ items: [data], total: 1 }, { status: response.status })
        }
      }

      // Return as is if it already has the expected format
      return NextResponse.json(data, { status: response.status })
    } catch (parseError) {
      console.error('❌ Error parsing JSON response:', parseError)
      return createErrorResponse(
        'Error parsing response from backend',
        HTTP_STATUS.INTERNAL_SERVER_ERROR,
        'JSON_PARSE_ERROR',
        {
          responsePreview: responseText.substring(0, 200),
        }
      )
    }
  } catch (error) {
    console.error('❌ Error reading response from backend:', error)
    return createErrorResponse(
      'Failed to read response from backend',
      HTTP_STATUS.INTERNAL_SERVER_ERROR,
      'RESPONSE_READ_ERROR'
    )
  }
}

/**
 * Standard handler for GET requests to backend API
 * @param request NextRequest object
 * @param backendPath Path to backend API (without base URL)
 * @param options Additional options
 * @returns NextResponse with backend response data
 */
export async function handleGetRequest(
  request: NextRequest,
  backendPath: string,
  options: {
    requireAuth?: boolean
    errorMessage?: string
    allowedContentTypes?: ContentType[]
  } = {}
): Promise<NextResponse> {
  const {
    requireAuth = true,
    errorMessage = 'Error fetching data',
    allowedContentTypes = [ContentType.JSON],
  } = options

  // Start timing the request
  const getElapsedTime = createTimer()

  // Generate a request ID and log the incoming request
  const requestId = logRequest(request, backendPath)
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Validate content type if specified for GET (typically not needed for GET)
    if (allowedContentTypes.length > 0 && method !== 'GET') {
      if (!validateContentType(request, allowedContentTypes, requestId)) {
        return createErrorResponse(
          `Unsupported content type. Allowed types: ${allowedContentTypes.join(', ')}`,
          415,
          ErrorCode.CONTENT_TYPE_ERROR,
          undefined,
          requestId,
          method,
          path
        )
      }
    }

    // Handle authentication
    if (requireAuth) {
      const authHeader = getAuthHeader(request, requestId)
      if (!authHeader) {
        return handleAuthError(requestId, method, path)
      }

      // Get search params from the request URL
      const searchParams = request.nextUrl.searchParams

      // Construct the backend URL with search params
      const apiUrl = `${BACKEND_URL}${backendPath}${
        searchParams.toString() ? `?${searchParams.toString()}` : ''
      }`

      // Make the request to the backend API
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: authHeader,
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
        },
      })

      // Log the response time
      const elapsedTime = getElapsedTime()
      logResponse(requestId, method, path, response.status, elapsedTime)

      // Process and return the response
      return processBackendResponse(response, errorMessage, requestId, method, path)
    }

    // Non-authenticated request
    const searchParams = request.nextUrl.searchParams
    const apiUrl = `${BACKEND_URL}${backendPath}${
      searchParams.toString() ? `?${searchParams.toString()}` : ''
    }`

    const response = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      },
    })

    // Log the response time
    const elapsedTime = getElapsedTime()
    logResponse(requestId, method, path, response.status, elapsedTime)

    return processBackendResponse(response, errorMessage, requestId, method, path)
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      `${errorMessage}: ${error instanceof Error ? error.message : String(error)}`,
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Standard handler for POST, PUT, PATCH, DELETE requests to backend API
 * @param request NextRequest object
 * @param backendPath Path to backend API (without base URL)
 * @param options Additional options
 * @returns NextResponse with backend response data
 */
export async function handleDataRequest(
  request: NextRequest,
  backendPath: string,
  options: {
    method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE'
    requireAuth?: boolean
    errorMessage?: string
    allowedContentTypes?: ContentType[]
  } = {}
): Promise<NextResponse> {
  const {
    method = 'POST',
    requireAuth = true,
    errorMessage = `Error ${method.toLowerCase()}ing data`,
  } = options

  // Start timing the request
  const getElapsedTime = createTimer()

  // Generate a request ID and log the incoming request
  const requestId = logRequest(request, backendPath)
  const requestMethod = request.method
  const path = request.nextUrl.pathname

  try {
    // Handle authentication
    let authHeader = null
    if (requireAuth) {
      authHeader = getAuthHeader(request, requestId)
      if (!authHeader) {
        return handleAuthError(requestId, requestMethod, path)
      }
    }

    // Get request body if not DELETE
    let body = undefined
    if (method !== 'DELETE') {
      try {
        const contentType = request.headers.get('content-type')
        if (contentType && contentType.includes('application/json')) {
          body = await request.json()
          console.log(`📦 Request body: ${JSON.stringify(body).substring(0, 200)}...`)
        } else if (contentType && contentType.includes('multipart/form-data')) {
          // FormData should use handleFormDataRequest instead
          return createErrorResponse(
            'Use handleFormDataRequest for multipart/form-data uploads',
            400,
            ErrorCode.API_ROUTE_ERROR,
            undefined,
            requestId,
            requestMethod,
            path
          )
        } else {
          // Try to get text
          body = await request.text()
          console.log(`📦 Request body (text): ${body.substring(0, 200)}...`)
        }
      } catch (e) {
        console.log('📦 No body or error parsing body')
      }
    }

    // Get search params from the request URL and construct the backend URL
    const searchParams = request.nextUrl.searchParams
    const apiUrl = `${BACKEND_URL}${backendPath}${
      searchParams.toString() ? `?${searchParams.toString()}` : ''
    }`
    console.log(`🔄 Proxying ${method} request to backend: ${apiUrl}`)

    // Prepare headers
    const headers: Record<string, string> = {
      'X-Request-ID': requestId,
    }
    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    // Set Content-Type for JSON body (simplified since FormData is handled elsewhere)
    if (body && typeof body === 'object') {
      headers['Content-Type'] = 'application/json'
    }

    // Make the request to the backend API
    const response = await fetch(apiUrl, {
      method,
      headers,
      body: typeof body === 'object' ? JSON.stringify(body) : body,
    })

    // Log the response time
    const elapsedTime = getElapsedTime()
    logResponse(requestId, requestMethod, path, response.status, elapsedTime)

    // Process and return the response
    return processBackendResponse(response, errorMessage, requestId, requestMethod, path)
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, requestMethod, path, 500, error, elapsedTime)

    return createErrorResponse(
      `${errorMessage}: ${error instanceof Error ? error.message : String(error)}`,
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      requestMethod,
      path
    )
  }
}

/**
 * Standard handler for POST, PUT, PATCH requests with FormData (file uploads) to backend API
 * @param request NextRequest object
 * @param backendPath Path to backend API (without base URL)
 * @param options Additional options
 * @returns NextResponse with backend response data
 */
export async function handleFormDataRequest(
  request: NextRequest,
  backendPath: string,
  options: {
    method?: 'POST' | 'PUT' | 'PATCH'
    requireAuth?: boolean
    errorMessage?: string
    maxFileSize?: number
    allowedFileTypes?: string[]
  } = {}
): Promise<NextResponse> {
  const {
    method = 'POST',
    requireAuth = true,
    errorMessage = `Error ${method.toLowerCase()}ing file`,
    maxFileSize = 100 * 1024 * 1024, // 100MB default
    allowedFileTypes = [], // Empty array means allow all types
  } = options

  // Start timing the request
  const getElapsedTime = createTimer()

  // Generate a request ID and log the incoming request
  const requestId = logRequest(request, backendPath)
  const requestMethod = request.method
  const path = request.nextUrl.pathname

  try {
    // Validate content type for FormData
    const contentType = request.headers.get('content-type')
    if (!contentType || !contentType.includes('multipart/form-data')) {
      return createErrorResponse(
        'Content-Type must be multipart/form-data for file uploads',
        415,
        ErrorCode.CONTENT_TYPE_ERROR,
        undefined,
        requestId,
        requestMethod,
        path
      )
    }

    // Handle authentication
    let authHeader = null
    if (requireAuth) {
      authHeader = getAuthHeader(request, requestId)
      if (!authHeader) {
        return handleAuthError(requestId, requestMethod, path)
      }
    }

    // Check Content-Length header for file size validation
    const contentLength = request.headers.get('content-length')
    if (contentLength && parseInt(contentLength) > maxFileSize) {
      return createErrorResponse(
        `File size exceeds maximum limit of ${Math.round(maxFileSize / (1024 * 1024))}MB`,
        413,
        ErrorCode.VALIDATION_ERROR,
        {
          max_size_bytes: maxFileSize,
          provided_size_bytes: parseInt(contentLength),
        },
        requestId,
        requestMethod,
        path
      )
    }

    // Get FormData from request
    let formData: FormData
    try {
      formData = await request.formData()
    } catch (error) {
      return createErrorResponse(
        'Invalid FormData in request body',
        400,
        ErrorCode.VALIDATION_ERROR,
        undefined,
        requestId,
        requestMethod,
        path
      )
    }

    // Log FormData metadata (without exposing file content)
    const formDataInfo: Record<string, any> = {}
    for (const [key, value] of formData.entries()) {
      if (value instanceof File) {
        formDataInfo[key] = {
          name: value.name,
          size: value.size,
          type: value.type,
        }

        // Validate file type if specified
        if (allowedFileTypes.length > 0 && !allowedFileTypes.includes(value.type)) {
          return createErrorResponse(
            `File type ${value.type} not allowed. Allowed types: ${allowedFileTypes.join(', ')}`,
            400,
            ErrorCode.VALIDATION_ERROR,
            {
              provided_type: value.type,
              allowed_types: allowedFileTypes,
            },
            requestId,
            requestMethod,
            path
          )
        }

        // Additional file size check from actual file
        if (value.size > maxFileSize) {
          return createErrorResponse(
            `File size exceeds maximum limit of ${Math.round(maxFileSize / (1024 * 1024))}MB`,
            413,
            ErrorCode.VALIDATION_ERROR,
            {
              max_size_bytes: maxFileSize,
              provided_size_bytes: value.size,
              filename: value.name,
            },
            requestId,
            requestMethod,
            path
          )
        }
      } else {
        formDataInfo[key] = typeof value === 'string' ? value.substring(0, 100) : 'non-string'
      }
    }

    console.log(`📦 FormData fields:`, JSON.stringify(formDataInfo, null, 2))

    // Get search params from the request URL and construct the backend URL
    const searchParams = request.nextUrl.searchParams
    const apiUrl = `${BACKEND_URL}${backendPath}${
      searchParams.toString() ? `?${searchParams.toString()}` : ''
    }`

    console.log(`🔄 Proxying ${method} FormData request to backend: ${apiUrl}`)

    // Prepare headers (NOTE: Don't set Content-Type for FormData - browser handles multipart boundaries)
    const headers: Record<string, string> = {
      'X-Request-ID': requestId,
    }
    if (authHeader) {
      headers['Authorization'] = authHeader
    }

    // Make the request to the backend API
    const response = await fetch(apiUrl, {
      method,
      headers,
      body: formData, // Pass FormData directly
    })

    // Log the response time
    const elapsedTime = getElapsedTime()
    logResponse(requestId, requestMethod, path, response.status, elapsedTime)

    // Process and return the response
    return processBackendResponse(response, errorMessage, requestId, requestMethod, path)
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, requestMethod, path, 500, error, elapsedTime)

    return createErrorResponse(
      `${errorMessage}: ${error instanceof Error ? error.message : String(error)}`,
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      requestMethod,
      path
    )
  }
}

/**
 * Standard handler for GET requests to backend API that returns raw data for post-processing
 * @param request NextRequest object
 * @param backendPath Path to backend API (without base URL)
 * @param options Additional options
 * @returns Promise<{data: any, status: number}> Raw response data and status
 * @throws Error if request fails (for proper error handling in calling code)
 */
export async function handleGetRequestRaw(
  request: NextRequest,
  backendPath: string,
  options: {
    requireAuth?: boolean
    errorMessage?: string
  } = {}
): Promise<{ data: any; status: number }> {
  const { requireAuth = true, errorMessage = 'Error fetching data' } = options

  // Start timing the request
  const getElapsedTime = createTimer()

  // Generate a request ID and log the incoming request
  const requestId = logRequest(request, backendPath)
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Handle authentication
    if (requireAuth) {
      const authHeader = getAuthHeader(request, requestId)
      if (!authHeader) {
        const elapsedTime = getElapsedTime()
        logError(requestId, method, path, 401, new Error('Authentication required'), elapsedTime)
        throw new Error('Authentication required')
      }

      // Get search params from the request URL
      const searchParams = request.nextUrl.searchParams

      // Construct the backend URL with search params
      const apiUrl = `${BACKEND_URL}${backendPath}${
        searchParams.toString() ? `?${searchParams.toString()}` : ''
      }`

      // Make the request to the backend API
      const response = await fetch(apiUrl, {
        headers: {
          Authorization: authHeader,
          'Content-Type': 'application/json',
          'X-Request-ID': requestId,
        },
      })

      // Log the response time
      const elapsedTime = getElapsedTime()
      logResponse(requestId, method, path, response.status, elapsedTime)

      // Handle error responses
      if (!response.ok) {
        logError(
          requestId,
          method,
          path,
          response.status,
          `Backend returned error status: ${response.status}`,
          elapsedTime
        )

        let errorData
        try {
          errorData = await response.json()
        } catch (e) {
          errorData = { detail: errorMessage }
        }

        throw new Error(`Backend error ${response.status}: ${errorData.detail || errorMessage}`)
      }

      // Parse and return raw data
      const contentType = response.headers.get('content-type')
      let data

      if (contentType && contentType.includes('application/json')) {
        data = await response.json()
      } else {
        data = await response.text()
      }

      return { data, status: response.status }
    }

    // Non-authenticated request (similar logic)
    const searchParams = request.nextUrl.searchParams
    const apiUrl = `${BACKEND_URL}${backendPath}${
      searchParams.toString() ? `?${searchParams.toString()}` : ''
    }`

    const response = await fetch(apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      },
    })

    // Log the response time
    const elapsedTime = getElapsedTime()
    logResponse(requestId, method, path, response.status, elapsedTime)

    // Handle error responses
    if (!response.ok) {
      logError(
        requestId,
        method,
        path,
        response.status,
        `Backend returned error status: ${response.status}`,
        elapsedTime
      )

      let errorData
      try {
        errorData = await response.json()
      } catch (e) {
        errorData = { detail: errorMessage }
      }

      throw new Error(`Backend error ${response.status}: ${errorData.detail || errorMessage}`)
    }

    // Parse and return raw data
    const contentType = response.headers.get('content-type')
    let data

    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      data = await response.text()
    }

    return { data, status: response.status }
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    // Re-throw the error for proper handling in calling code
    throw error
  }
}
