'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { <PERSON><PERSON>hart, <PERSON><PERSON>hart, PieChart } from '@/components/charts'
import { ReportsApi } from '@/lib/api/reports'
import { ApiClient } from '@/lib/api/client'
import {
  ClaimsKPIMetrics,
  ClaimsByTypeResponse,
  ClaimsByStatusResponse,
  ClaimsOverTimeResponse,
  FinancialKPIMetrics,
  PaymentsVsReservesResponse,
  AdjusterPerformanceResponse,
} from '@/lib/api/types'

// Create a ReportsApi instance
const apiClient = new ApiClient()
const reportsApi = new ReportsApi(apiClient)

export default function ReportsPage() {
  // State for report data
  const [claimsKpis, setClaimsKpis] = useState<ClaimsKPIMetrics | null>(null)
  const [claimsByType, setClaimsByType] = useState<ClaimsByTypeResponse | null>(null)
  const [claimsByStatus, setClaimsByStatus] = useState<ClaimsByStatusResponse | null>(null)
  const [claimsOverTime, setClaimsOverTime] = useState<ClaimsOverTimeResponse | null>(null)
  const [financialKpis, setFinancialKpis] = useState<FinancialKPIMetrics | null>(null)
  const [paymentsVsReserves, setPaymentsVsReserves] = useState<PaymentsVsReservesResponse | null>(
    null
  )
  const [adjusterPerformance, setAdjusterPerformance] =
    useState<AdjusterPerformanceResponse | null>(null)

  // Loading states
  const [loading, setLoading] = useState({
    claimsKpis: false,
    claimsByType: false,
    claimsByStatus: false,
    claimsOverTime: false,
    financialKpis: false,
    paymentsVsReserves: false,
    adjusterPerformance: false,
  })

  // Error states
  const [errors, setErrors] = useState<Record<string, any>>({
    claimsKpis: null,
    claimsByType: null,
    claimsByStatus: null,
    claimsOverTime: null,
    financialKpis: null,
    paymentsVsReserves: null,
    adjusterPerformance: null,
  })

  // Filter state
  const [timeframe, setTimeframe] = useState('6months')

  // Load data based on selected tab
  const [activeTab, setActiveTab] = useState('claims')

  // Load claims data
  useEffect(() => {
    if (activeTab === 'claims') {
      // Load claims KPIs
      setLoading(prev => ({ ...prev, claimsKpis: true }))
      setErrors(prev => ({ ...prev, claimsKpis: null }))

      console.log('🔍 DEBUG: Fetching claims KPIs with timeframe:', timeframe)
      reportsApi
        .getClaimsKpis({ period: timeframe })
        .then((data: ClaimsKPIMetrics) => {
          console.log('✅ Claims KPIs data received:', data)
          // Check if data is in the expected format or has items/total structure
          const kpisData = data.data || (data.items && data.items.length > 0 ? data.items[0] : data)
          console.log('✅ Normalized Claims KPIs data:', kpisData)
          setClaimsKpis(kpisData)
          setLoading(prev => ({ ...prev, claimsKpis: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching claims KPIs:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, claimsKpis: error }))
          setLoading(prev => ({ ...prev, claimsKpis: false }))
        })

      // Load claims by type
      setLoading(prev => ({ ...prev, claimsByType: true }))
      setErrors(prev => ({ ...prev, claimsByType: null }))

      console.log('🔍 DEBUG: Fetching claims by type with timeframe:', timeframe)
      reportsApi
        .getClaimsByType({ period: timeframe })
        .then((data: ClaimsByTypeResponse) => {
          console.log('✅ Claims by type data received:', data)
          // Transform the data structure to what the PieChart component expects
          const transformedData = {
            data: data.items || data.data || [],
          }
          console.log('✅ Transformed Claims by type data:', transformedData)
          setClaimsByType(transformedData)
          setLoading(prev => ({ ...prev, claimsByType: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching claims by type:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, claimsByType: error }))
          setLoading(prev => ({ ...prev, claimsByType: false }))
        })

      // Load claims by status
      setLoading(prev => ({ ...prev, claimsByStatus: true }))
      setErrors(prev => ({ ...prev, claimsByStatus: null }))

      console.log('🔍 DEBUG: Fetching claims by status with timeframe:', timeframe)
      reportsApi
        .getClaimsByStatus({ period: timeframe })
        .then((data: ClaimsByStatusResponse) => {
          console.log('✅ Claims by status data received:', data)
          // Transform the data structure to what the PieChart component expects
          const transformedData = {
            data: data.items || data.data || [],
          }
          console.log('✅ Transformed Claims by status data:', transformedData)
          setClaimsByStatus(transformedData)
          setLoading(prev => ({ ...prev, claimsByStatus: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching claims by status:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, claimsByStatus: error }))
          setLoading(prev => ({ ...prev, claimsByStatus: false }))
        })

      // Load claims over time
      setLoading(prev => ({ ...prev, claimsOverTime: true }))
      setErrors(prev => ({ ...prev, claimsOverTime: null }))

      console.log('🔍 DEBUG: Fetching claims over time with timeframe:', timeframe)
      reportsApi
        .getClaimsOverTime({ period: timeframe })
        .then((data: ClaimsOverTimeResponse) => {
          console.log('✅ Claims over time data received:', data)
          // Transform the data structure to what the LineChart component expects
          const transformedData = {
            data: data.items || data.data || [],
          }
          console.log('✅ Transformed Claims over time data:', transformedData)
          setClaimsOverTime(transformedData)
          setLoading(prev => ({ ...prev, claimsOverTime: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching claims over time:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, claimsOverTime: error }))
          setLoading(prev => ({ ...prev, claimsOverTime: false }))
        })
    }
  }, [activeTab, timeframe])

  // Load financial data
  useEffect(() => {
    if (activeTab === 'financial') {
      // Load financial KPIs
      setLoading(prev => ({ ...prev, financialKpis: true }))
      setErrors(prev => ({ ...prev, financialKpis: null }))

      console.log('🔍 DEBUG: Fetching financial KPIs with timeframe:', timeframe)
      reportsApi
        .getFinancialKpis({ period: timeframe })
        .then((data: FinancialKPIMetrics) => {
          console.log('✅ Financial KPIs data received:', data)
          // Check if data is in the expected format or has items/total structure
          let kpisData = data

          // Try to find the actual KPI data in any location
          if (data.data && typeof data.data === 'object') {
            console.log('💰 Examining nested data structure:', data.data)

            // Check if data.data contains the KPI fields directly
            if ('total_payments' in data.data || 'total_reserves' in data.data) {
              kpisData = data.data
            }
            // Check if data.data is an array with KPI objects
            else if (Array.isArray(data.data) && data.data.length > 0) {
              kpisData = data.data[0]
            }
            // Check if data.data has nested fields that might contain KPIs
            else {
              // Look for objects that might have the KPI data
              for (const key in data.data) {
                const value = data.data[key]
                if (
                  value &&
                  typeof value === 'object' &&
                  ('total_payments' in value || 'total_reserves' in value)
                ) {
                  kpisData = value
                  break
                }
              }
            }
          }
          // Check for items array
          else if (data.items && Array.isArray(data.items) && data.items.length > 0) {
            kpisData = data.items[0]
          }
          // Check for report_metadata structure
          else if (data.report_metadata && data.report_metadata.data) {
            kpisData = data.report_metadata.data
          }

          console.log('✅ Normalized Financial KPIs data:', kpisData)
          setFinancialKpis(kpisData)
          setLoading(prev => ({ ...prev, financialKpis: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching financial KPIs:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, financialKpis: error }))
          setLoading(prev => ({ ...prev, financialKpis: false }))
        })

      // Load payments vs reserves
      setLoading(prev => ({ ...prev, paymentsVsReserves: true }))
      setErrors(prev => ({ ...prev, paymentsVsReserves: null }))

      console.log('🔍 DEBUG: Fetching payments vs reserves with timeframe:', timeframe)
      reportsApi
        .getPaymentsVsReserves({ period: timeframe })
        .then((data: PaymentsVsReservesResponse) => {
          console.log('✅ Payments vs reserves data received:', data)
          // Transform the data structure to what the BarChart component expects
          const transformedData = {
            data: data.items || data.data || [],
          }
          // Ensure we have data even if the structure is unexpected
          if (
            transformedData.data.length === 0 &&
            data.data === undefined &&
            data.items === undefined
          ) {
            // Try to extract data from another location if the expected properties aren't found
            if (typeof data === 'object' && data !== null) {
              // Look for array properties that might contain the data
              const arrayProps = Object.entries(data)
                .filter(([_, val]) => Array.isArray(val) && val.length > 0)
                .map(([key, val]) => ({ key, val }))

              if (arrayProps.length > 0) {
                console.log(
                  '🔍 Found potential data arrays:',
                  arrayProps.map(p => p.key)
                )
                transformedData.data = arrayProps[0].val
              }
            }
          }
          console.log('✅ Transformed Payments vs reserves data:', transformedData)
          setPaymentsVsReserves(transformedData)
          setLoading(prev => ({ ...prev, paymentsVsReserves: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching payments vs reserves:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, paymentsVsReserves: error }))
          setLoading(prev => ({ ...prev, paymentsVsReserves: false }))
        })
    }
  }, [activeTab, timeframe])

  // Load performance data
  useEffect(() => {
    if (activeTab === 'performance') {
      // Load adjuster performance
      setLoading(prev => ({ ...prev, adjusterPerformance: true }))
      setErrors(prev => ({ ...prev, adjusterPerformance: null }))

      console.log('🔍 DEBUG: Fetching adjuster performance with timeframe:', timeframe)
      reportsApi
        .getAdjusterPerformance({ period: timeframe })
        .then((data: AdjusterPerformanceResponse) => {
          console.log('✅ Adjuster performance data received:', data)
          // Transform the data structure to what the BarChart component expects
          const transformedData = {
            data: data.items || data.data || [],
          }
          // Ensure we have data even if the structure is unexpected
          if (
            transformedData.data.length === 0 &&
            data.data === undefined &&
            data.items === undefined
          ) {
            // Try to extract data from another location if the expected properties aren't found
            if (typeof data === 'object' && data !== null) {
              // Look for array properties that might contain the data
              const arrayProps = Object.entries(data)
                .filter(([_, val]) => Array.isArray(val) && val.length > 0)
                .map(([key, val]) => ({ key, val }))

              if (arrayProps.length > 0) {
                console.log(
                  '🔍 Found potential data arrays:',
                  arrayProps.map(p => p.key)
                )
                transformedData.data = arrayProps[0].val
              }
            }
          }
          console.log('✅ Transformed Adjuster performance data:', transformedData)
          setAdjusterPerformance(transformedData)
          setLoading(prev => ({ ...prev, adjusterPerformance: false }))
        })
        .catch((error: any) => {
          console.error('❌ Error fetching adjuster performance:', error)
          console.error('Error details:', {
            message: error.message,
            statusCode: error.status_code,
            data: error.data,
            url: error.url,
          })
          setErrors(prev => ({ ...prev, adjusterPerformance: error }))
          setLoading(prev => ({ ...prev, adjusterPerformance: false }))
        })
    }
  }, [activeTab, timeframe])

  // Debug useEffect for financial data
  useEffect(() => {
    if (paymentsVsReserves) {
      console.log('💰 Financial data for rendering:', {
        paymentsVsReserves,
        dataPresent: !!paymentsVsReserves.data,
        itemCount: paymentsVsReserves.data?.length || 0,
      })
    }
  }, [paymentsVsReserves])

  // Debug useEffect for performance data
  useEffect(() => {
    if (adjusterPerformance) {
      console.log('👷 Performance data for rendering:', {
        adjusterPerformance,
        dataPresent: !!adjusterPerformance.data,
        itemCount: adjusterPerformance.data?.length || 0,
      })
    }
  }, [adjusterPerformance])

  // Debug useEffect specifically for financial KPIs data
  useEffect(() => {
    if (financialKpis) {
      console.log('💰 Financial KPIs data inspection:', {
        financialKpis,
        hasNestedData: !!financialKpis.data,
        hasItems: !!financialKpis.items,
        totalPayments: financialKpis.total_payments,
        totalReserves: financialKpis.total_reserves,
        avgClaimValue: financialKpis.avg_claim_value,
        recoveryAmount: financialKpis.recovery_amount,
        isReportMetadata: !!financialKpis.report_metadata,
        keys: Object.keys(financialKpis),
      })

      // Inspect if data is in a nested structure
      if (financialKpis.report_metadata && financialKpis.data) {
        console.log('💰 Found nested data structure:', {
          reportMetadata: financialKpis.report_metadata,
          dataKeys: Object.keys(financialKpis.data),
        })
      }
    }
  }, [financialKpis])

  // Handler for timeframe selection
  const handleTimeframeChange = (value: string) => {
    setTimeframe(value)
  }

  // Handler for tab selection
  const handleTabChange = (value: string) => {
    console.log(`Tab changed to: ${value}`)
    setActiveTab(value)
  }

  // Helper function to retry a failed API request
  const retryRequest = (reportType: string) => {
    console.log(`Retrying ${reportType} request...`)

    // Reset error state for this report
    setErrors(prev => ({ ...prev, [reportType]: null }))

    // Set loading state
    setLoading(prev => ({ ...prev, [reportType]: true }))

    // Determine which API call to retry based on the report type
    switch (reportType) {
      case 'claimsKpis':
        reportsApi
          .getClaimsKpis({ period: timeframe })
          .then((data: ClaimsKPIMetrics) => {
            // Check if data is in the expected format or has items/total structure
            const kpisData =
              data.data || (data.items && data.items.length > 0 ? data.items[0] : data)
            console.log('✅ Retry success - Claims KPIs data:', kpisData)
            setClaimsKpis(kpisData)
            setLoading(prev => ({ ...prev, claimsKpis: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying claims KPIs:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, claimsKpis: error }))
            setLoading(prev => ({ ...prev, claimsKpis: false }))
          })
        break

      case 'claimsByType':
        reportsApi
          .getClaimsByType({ period: timeframe })
          .then((data: ClaimsByTypeResponse) => {
            // Transform the data structure to what the PieChart component expects
            const transformedData = {
              data: data.items || data.data || [],
            }
            console.log('✅ Retry success - Claims by type data:', transformedData)
            setClaimsByType(transformedData)
            setLoading(prev => ({ ...prev, claimsByType: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying claims by type:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, claimsByType: error }))
            setLoading(prev => ({ ...prev, claimsByType: false }))
          })
        break

      case 'claimsByStatus':
        reportsApi
          .getClaimsByStatus({ period: timeframe })
          .then((data: ClaimsByStatusResponse) => {
            // Transform the data structure to what the PieChart component expects
            const transformedData = {
              data: data.items || data.data || [],
            }
            console.log('✅ Retry success - Claims by status data:', transformedData)
            setClaimsByStatus(transformedData)
            setLoading(prev => ({ ...prev, claimsByStatus: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying claims by status:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, claimsByStatus: error }))
            setLoading(prev => ({ ...prev, claimsByStatus: false }))
          })
        break

      case 'claimsOverTime':
        reportsApi
          .getClaimsOverTime({ period: timeframe })
          .then((data: ClaimsOverTimeResponse) => {
            // Transform the data structure to what the LineChart component expects
            const transformedData = {
              data: data.items || data.data || [],
            }
            console.log('✅ Retry success - Claims over time data:', transformedData)
            setClaimsOverTime(transformedData)
            setLoading(prev => ({ ...prev, claimsOverTime: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying claims over time:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, claimsOverTime: error }))
            setLoading(prev => ({ ...prev, claimsOverTime: false }))
          })
        break

      // Financial KPIs
      case 'financialKpis':
        reportsApi
          .getFinancialKpis({ period: timeframe })
          .then((data: FinancialKPIMetrics) => {
            console.log('✅ Financial KPIs data received:', data)
            // Check if data is in the expected format or has items/total structure
            let kpisData = data

            // Try to find the actual KPI data in any location
            if (data.data && typeof data.data === 'object') {
              console.log('💰 Examining nested data structure:', data.data)

              // Check if data.data contains the KPI fields directly
              if ('total_payments' in data.data || 'total_reserves' in data.data) {
                kpisData = data.data
              }
              // Check if data.data is an array with KPI objects
              else if (Array.isArray(data.data) && data.data.length > 0) {
                kpisData = data.data[0]
              }
              // Check if data.data has nested fields that might contain KPIs
              else {
                // Look for objects that might have the KPI data
                for (const key in data.data) {
                  const value = data.data[key]
                  if (
                    value &&
                    typeof value === 'object' &&
                    ('total_payments' in value || 'total_reserves' in value)
                  ) {
                    kpisData = value
                    break
                  }
                }
              }
            }
            // Check for items array
            else if (data.items && Array.isArray(data.items) && data.items.length > 0) {
              kpisData = data.items[0]
            }
            // Check for report_metadata structure
            else if (data.report_metadata && data.report_metadata.data) {
              kpisData = data.report_metadata.data
            }

            console.log('✅ Normalized Financial KPIs data:', kpisData)
            setFinancialKpis(kpisData)
            setLoading(prev => ({ ...prev, financialKpis: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying financial KPIs:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, financialKpis: error }))
            setLoading(prev => ({ ...prev, financialKpis: false }))
          })
        break

      case 'paymentsVsReserves':
        reportsApi
          .getPaymentsVsReserves({ period: timeframe })
          .then((data: PaymentsVsReservesResponse) => {
            // Transform the data structure to what the BarChart component expects
            const transformedData = {
              data: data.items || data.data || [],
            }
            // Ensure we have data even if the structure is unexpected
            if (
              transformedData.data.length === 0 &&
              data.data === undefined &&
              data.items === undefined
            ) {
              // Try to extract data from another location if the expected properties aren't found
              if (typeof data === 'object' && data !== null) {
                // Look for array properties that might contain the data
                const arrayProps = Object.entries(data)
                  .filter(([_, val]) => Array.isArray(val) && val.length > 0)
                  .map(([key, val]) => ({ key, val }))

                if (arrayProps.length > 0) {
                  console.log(
                    '🔍 Found potential data arrays:',
                    arrayProps.map(p => p.key)
                  )
                  transformedData.data = arrayProps[0].val
                }
              }
            }
            console.log('✅ Transformed Payments vs reserves data:', transformedData)
            setPaymentsVsReserves(transformedData)
            setLoading(prev => ({ ...prev, paymentsVsReserves: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying payments vs reserves:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, paymentsVsReserves: error }))
            setLoading(prev => ({ ...prev, paymentsVsReserves: false }))
          })
        break

      case 'adjusterPerformance':
        reportsApi
          .getAdjusterPerformance({ period: timeframe })
          .then((data: AdjusterPerformanceResponse) => {
            // Transform the data structure to what the BarChart component expects
            const transformedData = {
              data: data.items || data.data || [],
            }
            // Ensure we have data even if the structure is unexpected
            if (
              transformedData.data.length === 0 &&
              data.data === undefined &&
              data.items === undefined
            ) {
              // Try to extract data from another location if the expected properties aren't found
              if (typeof data === 'object' && data !== null) {
                // Look for array properties that might contain the data
                const arrayProps = Object.entries(data)
                  .filter(([_, val]) => Array.isArray(val) && val.length > 0)
                  .map(([key, val]) => ({ key, val }))

                if (arrayProps.length > 0) {
                  console.log(
                    '🔍 Found potential data arrays:',
                    arrayProps.map(p => p.key)
                  )
                  transformedData.data = arrayProps[0].val
                }
              }
            }
            console.log('✅ Transformed Adjuster performance data:', transformedData)
            setAdjusterPerformance(transformedData)
            setLoading(prev => ({ ...prev, adjusterPerformance: false }))
          })
          .catch((error: any) => {
            console.error('Error retrying adjuster performance:', error)
            console.error('Error details:', {
              message: error.message,
              statusCode: error.status_code,
              data: error.data,
              url: error.url,
            })
            setErrors(prev => ({ ...prev, adjusterPerformance: error }))
            setLoading(prev => ({ ...prev, adjusterPerformance: false }))
          })
        break

      // Add other report types as needed

      default:
        console.error(`Unknown report type: ${reportType}`)
    }
  }

  // Log data state when rendering components
  console.log('Current rendering state:', {
    activeTab,
    claimsKpis: claimsKpis ? 'data present' : 'no data',
    claimsByType: claimsByType?.data ? `${claimsByType.data.length} items` : 'no data',
    claimsByStatus: claimsByStatus?.data ? `${claimsByStatus.data.length} items` : 'no data',
    claimsOverTime: claimsOverTime?.data ? `${claimsOverTime.data.length} items` : 'no data',
    financialKpis: financialKpis ? 'data present' : 'no data',
    paymentsVsReserves: paymentsVsReserves?.data
      ? `${paymentsVsReserves.data.length} items`
      : 'no data',
    adjusterPerformance: adjusterPerformance?.data
      ? `${adjusterPerformance.data.length} items`
      : 'no data',
  })

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
      </div>

      <Tabs defaultValue="claims" className="w-full" onValueChange={handleTabChange}>
        <TabsList className="grid w-full md:w-auto grid-cols-4 md:flex">
          <TabsTrigger value="claims">Claims</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="performance" disabled>
            Performance
          </TabsTrigger>
          <TabsTrigger value="custom" disabled>
            Custom
          </TabsTrigger>
        </TabsList>

        <TabsContent value="claims" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            {/* Claims KPIs */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Claims KPIs</CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.claimsKpis && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading claims KPIs...</p>
                  </div>
                )}
                {errors.claimsKpis && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">Error loading claims KPIs. Please try again.</p>
                    <Button variant="outline" size="sm" onClick={() => retryRequest('claimsKpis')}>
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.claimsKpis && !errors.claimsKpis && claimsKpis && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">New Claims</p>
                      <p className="text-2xl font-bold">{claimsKpis.total_claims || 0}</p>
                      {claimsKpis.total_claims_change !== undefined && (
                        <p
                          className={`text-xs ${claimsKpis.total_claims_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {claimsKpis.total_claims_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(claimsKpis.total_claims_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Closed Claims</p>
                      <p className="text-2xl font-bold">{claimsKpis.closed_claims || 0}</p>
                      {claimsKpis.closed_claims_change !== undefined && (
                        <p
                          className={`text-xs ${claimsKpis.closed_claims_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {claimsKpis.closed_claims_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(claimsKpis.closed_claims_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Avg. Resolution Time</p>
                      <p className="text-2xl font-bold">
                        {claimsKpis.avg_resolution_time || 0} days
                      </p>
                      {claimsKpis.avg_resolution_time_change !== undefined && (
                        <p
                          className={`text-xs ${claimsKpis.avg_resolution_time_change <= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {claimsKpis.avg_resolution_time_change <= 0 ? '↓' : '↑'}{' '}
                          {Math.abs(claimsKpis.avg_resolution_time_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Open Claims</p>
                      <p className="text-2xl font-bold">{claimsKpis.open_claims || 0}</p>
                      {claimsKpis.open_claims_change !== undefined && (
                        <p
                          className={`text-xs ${claimsKpis.open_claims_change <= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {claimsKpis.open_claims_change <= 0 ? '↓' : '↑'}{' '}
                          {Math.abs(claimsKpis.open_claims_change)}%
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Claims Over Time */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Claims Over Time</CardTitle>
                <CardDescription>New vs closed claims over time</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.claimsOverTime && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading claims over time data...</p>
                  </div>
                )}
                {errors.claimsOverTime && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading claims over time. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('claimsOverTime')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.claimsOverTime && !errors.claimsOverTime && claimsOverTime && (
                  <div className="h-48">
                    <LineChart data={claimsOverTime} />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            {/* Claims by Type */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Claims by Type</CardTitle>
                <CardDescription>Distribution of claims by type</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.claimsByType && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading claims by type data...</p>
                  </div>
                )}
                {errors.claimsByType && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading claims by type. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('claimsByType')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.claimsByType && !errors.claimsByType && claimsByType && (
                  <div className="h-48">
                    <PieChart data={claimsByType} />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Claims by Status */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Claims by Status</CardTitle>
                <CardDescription>Distribution of claims by status</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.claimsByStatus && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading claims by status data...</p>
                  </div>
                )}
                {errors.claimsByStatus && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading claims by status. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('claimsByStatus')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.claimsByStatus && !errors.claimsByStatus && claimsByStatus && (
                  <div className="h-48">
                    <PieChart data={claimsByStatus} />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="financial" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-2">
            {/* Financial KPIs */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Financial KPIs</CardTitle>
                <CardDescription>Key financial indicators</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.financialKpis && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading financial KPIs...</p>
                  </div>
                )}
                {errors.financialKpis && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading financial KPIs. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('financialKpis')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.financialKpis && !errors.financialKpis && financialKpis && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Total Payments</p>
                      <p className="text-2xl font-bold">${financialKpis.total_payments || 0}</p>
                      {financialKpis.total_payments_change !== undefined && (
                        <p
                          className={`text-xs ${financialKpis.total_payments_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {financialKpis.total_payments_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(financialKpis.total_payments_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Total Reserves</p>
                      <p className="text-2xl font-bold">${financialKpis.total_reserves || 0}</p>
                      {financialKpis.total_reserves_change !== undefined && (
                        <p
                          className={`text-xs ${financialKpis.total_reserves_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {financialKpis.total_reserves_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(financialKpis.total_reserves_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Avg. Claim Value</p>
                      <p className="text-2xl font-bold">${financialKpis.avg_claim_value || 0}</p>
                      {financialKpis.avg_claim_value_change !== undefined && (
                        <p
                          className={`text-xs ${financialKpis.avg_claim_value_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {financialKpis.avg_claim_value_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(financialKpis.avg_claim_value_change)}%
                        </p>
                      )}
                    </div>
                    <div className="space-y-1 border rounded-lg p-3">
                      <p className="text-sm font-medium">Recovery Amount</p>
                      <p className="text-2xl font-bold">${financialKpis.recovery_amount || 0}</p>
                      {financialKpis.recovery_amount_change !== undefined && (
                        <p
                          className={`text-xs ${financialKpis.recovery_amount_change >= 0 ? 'text-green-500' : 'text-red-500'}`}
                        >
                          {financialKpis.recovery_amount_change >= 0 ? '↑' : '↓'}{' '}
                          {Math.abs(financialKpis.recovery_amount_change)}%
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payments vs Reserves */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Payments vs Reserves</CardTitle>
                <CardDescription>Comparison of payments and reserves</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.paymentsVsReserves && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading payments vs reserves data...</p>
                  </div>
                )}
                {errors.paymentsVsReserves && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading payments vs reserves. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('paymentsVsReserves')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.paymentsVsReserves &&
                  !errors.paymentsVsReserves &&
                  paymentsVsReserves && (
                    <div className="h-48">
                      <BarChart
                        data={
                          // Handle both data formats
                          paymentsVsReserves.data?.map(item => ({
                            name: item.period,
                            value: item.payments,
                          })) || []
                        }
                      />
                    </div>
                  )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <div className="grid gap-4">
            {/* Adjuster Performance */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Adjuster Performance</CardTitle>
                <CardDescription>Performance metrics by adjuster</CardDescription>
              </CardHeader>
              <CardContent>
                {loading.adjusterPerformance && (
                  <div className="flex items-center justify-center h-48">
                    <p className="text-muted-foreground">Loading adjuster performance data...</p>
                  </div>
                )}
                {errors.adjusterPerformance && (
                  <div className="flex flex-col items-center justify-center h-48 space-y-4">
                    <p className="text-destructive">
                      Error loading adjuster performance. Please try again.
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => retryRequest('adjusterPerformance')}
                    >
                      Retry
                    </Button>
                  </div>
                )}
                {!loading.adjusterPerformance &&
                  !errors.adjusterPerformance &&
                  adjusterPerformance && (
                    <div className="h-48">
                      <BarChart
                        data={
                          // Handle both data formats
                          adjusterPerformance.data?.map(item => ({
                            name: item.adjuster_name,
                            value: item.claims_handled,
                          })) || []
                        }
                      />
                    </div>
                  )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="custom" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Custom Reports</CardTitle>
              <CardDescription>Create and save custom reports</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center justify-center py-12">
                <h3 className="text-lg font-medium mb-2">No custom reports yet</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Create your first custom report to see it here
                </p>
                <Button>Create Custom Report</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
