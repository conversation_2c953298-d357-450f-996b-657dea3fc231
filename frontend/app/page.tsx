import { DashboardMetrics } from '@/components/dashboard-metrics'
import { RecentClaims } from '@/components/recent-claims'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { TasksList } from '@/components/tasks-list'

export default function Home() {
  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
      </div>

      <DashboardMetrics />

      <Tabs defaultValue="my-claims" className="w-full">
        <TabsList>
          <TabsTrigger value="my-claims">My Claims</TabsTrigger>
          <TabsTrigger value="my-tasks">My Tasks</TabsTrigger>
        </TabsList>
        <TabsContent value="my-claims" className="mt-6">
          <RecentClaims />
        </TabsContent>
        <TabsContent value="my-tasks" className="mt-6">
          <TasksList />
        </TabsContent>
      </Tabs>
    </div>
  )
}
