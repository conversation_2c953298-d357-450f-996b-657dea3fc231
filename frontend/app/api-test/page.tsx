'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export default function ApiTest() {
  const [apiUrl, setApiUrl] = useState('/api/v1/health')
  const [username, setUsername] = useState('<EMAIL>')
  const [password, setPassword] = useState('admin')
  const [response, setResponse] = useState<string>('')
  const [error, setError] = useState<string>('')
  const [loading, setLoading] = useState(false)

  const testFetch = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    try {
      console.log(`Testing URL with Fetch API: ${apiUrl}`)
      const result = await fetch(apiUrl, {
        method: 'GET',
        cache: 'no-cache',
      })

      try {
        const data = await result.json()
        setResponse(`Status: ${result.status}\nBody: ${JSON.stringify(data, null, 2)}`)
      } catch (e) {
        const text = await result.text()
        setResponse(`Status: ${result.status}\nBody (text): ${text}`)
      }
    } catch (err) {
      console.error('Fetch error:', err)
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`)
    } finally {
      setLoading(false)
    }
  }

  const testXhr = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    const xhr = new XMLHttpRequest()
    console.log(`Testing URL with XHR: ${apiUrl}`)
    xhr.open('GET', apiUrl)

    xhr.onload = function () {
      try {
        const data = JSON.parse(xhr.responseText)
        setResponse(`Status: ${xhr.status}\nBody: ${JSON.stringify(data, null, 2)}`)
      } catch (e) {
        setResponse(`Status: ${xhr.status}\nBody (text): ${xhr.responseText}`)
      }
      setLoading(false)
    }

    xhr.onerror = function () {
      setError(`XHR Error: Network error occurred`)
      setLoading(false)
    }

    xhr.send()
  }

  const testHealth = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    try {
      console.log('Testing Next.js health API route...')
      const response = await fetch('/api/v1/health', {
        method: 'GET',
        cache: 'no-cache',
      })

      try {
        const data = await response.json()
        setResponse(`Status: ${response.status}\nBody: ${JSON.stringify(data, null, 2)}`)
      } catch (e) {
        const text = await response.text()
        setResponse(`Status: ${response.status}\nBody (text): ${text}`)
      }
    } catch (err) {
      console.error('Health API test error:', err)
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`)
    } finally {
      setLoading(false)
    }
  }

  const loginTest = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    try {
      const formData = new URLSearchParams()
      formData.append('grant_type', 'password')
      formData.append('username', username)
      formData.append('password', password)
      formData.append('scope', '')

      console.log('Attempting direct login test...')
      const response = await fetch('http://localhost:8000/api/v1/auth/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
        mode: 'cors',
        credentials: 'omit',
      })

      const text = await response.text()
      setResponse(`Status: ${response.status}\nBody: ${text}`)
    } catch (err) {
      console.error('Login test error:', err)
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`)
    } finally {
      setLoading(false)
    }
  }

  const testNextJsApi = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    try {
      const formData = new URLSearchParams()
      formData.append('grant_type', 'password')
      formData.append('username', username)
      formData.append('password', password)
      formData.append('scope', '')

      console.log('Testing Next.js API route with body:', formData.toString())
      const response = await fetch('/api/v1/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData,
      })

      console.log('Response status:', response.status)

      // Get response as text first so we can log it
      const text = await response.text()
      console.log('Response text:', text)

      let jsonData = null
      try {
        // Try to parse as JSON if possible
        if (text) {
          jsonData = JSON.parse(text)
          console.log('Parsed JSON:', jsonData)

          // Store the token in localStorage if login was successful
          if (response.ok && jsonData.access_token) {
            console.log('Storing access token in localStorage')
            localStorage.setItem('claimentine_auth_token', jsonData.access_token)

            // Also store refresh token if available
            if (jsonData.refresh_token) {
              localStorage.setItem('claimentine_refresh_token', jsonData.refresh_token)
            }

            setResponse(
              `Status: ${response.status}\nLogin successful!\nAccess token saved to localStorage.\nBody: ${JSON.stringify(jsonData, null, 2)}`
            )
            return
          }
        }
      } catch (e) {
        console.error('Failed to parse response as JSON:', e)
      }

      if (!response.ok) {
        if (jsonData && jsonData.error) {
          setError(`API Error: ${jsonData.error}`)
        } else {
          setError(`Status ${response.status}: ${text || 'No response'}`)
        }
      }

      setResponse(`Status: ${response.status}\nBody: ${text}`)
    } catch (err) {
      console.error('Next.js API test error:', err)
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`)
    } finally {
      setLoading(false)
    }
  }

  const testClaimsApi = async () => {
    setLoading(true)
    setResponse('')
    setError('')

    // Check if token exists
    const token = localStorage.getItem('claimentine_auth_token')
    if (!token) {
      setError('No access token found. Please log in first using the "Test Login API" button.')
      setLoading(false)
      return
    }

    try {
      console.log('Testing claims API with token:', token.substring(0, 15) + '...')
      const response = await fetch('/api/v1/claims', {
        method: 'GET',
        cache: 'no-cache',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      console.log('Response status:', response.status)

      const text = await response.text()
      console.log('Response text:', text)

      try {
        if (text) {
          const jsonData = JSON.parse(text)
          setResponse(`Status: ${response.status}\nBody: ${JSON.stringify(jsonData, null, 2)}`)
        } else {
          setResponse(`Status: ${response.status}\nEmpty response`)
        }
      } catch (e) {
        setResponse(`Status: ${response.status}\nBody (text): ${text}`)
      }

      if (response.status === 401) {
        setError(
          'Authentication failed. Your token may be expired or invalid. Please log in again using the "Test Login API" button.'
        )
      }
    } catch (err) {
      console.error('Claims API test error:', err)
      setError(`Error: ${err instanceof Error ? err.message : String(err)}`)
    } finally {
      setLoading(false)
    }
  }

  const checkCurrentToken = () => {
    const token = localStorage.getItem('claimentine_auth_token')
    if (token) {
      setResponse(`Current token in localStorage:\n${token}`)
    } else {
      setError('No access token found in localStorage. Please log in first.')
    }
  }

  const clearTokens = () => {
    localStorage.removeItem('claimentine_auth_token')
    localStorage.removeItem('claimentine_refresh_token')
    setResponse('Tokens cleared from localStorage')
  }

  return (
    <div className="max-w-xl mx-auto my-8 px-4">
      <Card>
        <CardHeader>
          <CardTitle>API Connection Test</CardTitle>
          <CardDescription>Test the connection to the backend API</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-blue-50 text-blue-800 rounded-md text-sm">
            <p className="font-medium">Purpose of this Diagnostic Tool:</p>
            <p className="mt-1">
              This page helps diagnose API connectivity issues between the frontend and backend. It
              can identify CORS issues, network problems, and verify that API proxy routes are
              working.
            </p>

            <p className="font-medium mt-3">How to use this page:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Use the input field to test any API endpoint</li>
              <li>
                For backend endpoints, use our API proxy: <code>/api/v1/...</code>
              </li>
              <li>
                Direct connections to <code>http://localhost:8000/...</code> will fail due to CORS
              </li>
            </ul>

            <p className="font-medium mt-3">Button Guide:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>
                <strong>Test Fetch API</strong>: Tests the URL in the input field using the modern
                Fetch API. Use this for general API testing.
              </li>
              <li>
                <strong>Test XHR</strong>: Tests the same URL using the older XMLHttpRequest. Use
                this if Fetch API tests fail to compare behavior.
              </li>
              <li>
                <strong>Test Health API</strong>: Tests the backend health check through our proxy (
                <code>/api/v1/health</code>). Use this to verify backend connectivity.
              </li>
              <li>
                <strong>Test Direct Login</strong>: Attempts to connect directly to the backend auth
                endpoint. This will typically fail with CORS errors, demonstrating why we need the
                proxy approach.
              </li>
              <li>
                <strong>Test Login API</strong>: Tests authentication through our proxy (
                <code>/api/v1/auth</code>). Use this to test login functionality without CORS
                issues.
              </li>
            </ul>

            <p className="font-medium mt-3">When to use:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>When implementing new API integrations to check connectivity</li>
              <li>When troubleshooting authentication or other API issues</li>
              <li>To verify that backend services are running and accessible via the proxy</li>
              <li>To understand the difference between direct API calls and proxied calls</li>
            </ul>
          </div>

          <div className="space-y-2">
            <Label htmlFor="apiUrl">API URL</Label>
            <Input id="apiUrl" value={apiUrl} onChange={e => setApiUrl(e.target.value)} />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input id="username" value={username} onChange={e => setUsername(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={e => setPassword(e.target.value)}
              />
            </div>
          </div>
          <div className="flex space-x-2 flex-wrap gap-2">
            <Button onClick={testFetch} disabled={loading}>
              {loading ? 'Testing...' : 'Test Fetch API'}
            </Button>
            <Button onClick={testXhr} disabled={loading} variant="outline">
              Test XHR
            </Button>
            <Button onClick={testHealth} disabled={loading} variant="secondary">
              Test Health API
            </Button>
            <Button onClick={loginTest} disabled={loading} variant="secondary">
              Test Direct Login
            </Button>
            <Button onClick={testNextJsApi} disabled={loading} variant="default">
              Test Login API
            </Button>
            <Button onClick={testClaimsApi} disabled={loading} variant="default">
              Test Claims API
            </Button>
            <Button onClick={checkCurrentToken} disabled={loading} variant="secondary">
              Check Current Token
            </Button>
            <Button onClick={clearTokens} disabled={loading} variant="destructive">
              Clear Tokens
            </Button>
            <Button
              onClick={() => {
                const encodedUsername = encodeURIComponent(username)
                const encodedPassword = encodeURIComponent(password)

                setResponse(`Test login with curl:
curl -X POST http://localhost:8000/api/v1/auth/token \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -d "grant_type=password&username=${encodedUsername}&password=${encodedPassword}&scope="
                
Test proxy login with curl:
curl -X POST http://localhost:3000/api/v1/auth \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -d "grant_type=password&username=${encodedUsername}&password=${encodedPassword}&scope="
                `)
              }}
              variant="link"
            >
              Show Curl Commands
            </Button>
          </div>

          {response && (
            <div className="mt-4 p-4 bg-muted rounded-md whitespace-pre-wrap text-xs">
              <strong>Response:</strong>
              <pre>{response}</pre>
            </div>
          )}

          {error && (
            <div className="mt-4 p-4 bg-red-50 text-red-900 rounded-md whitespace-pre-wrap text-xs">
              <strong>Error:</strong>
              <pre>{error}</pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
