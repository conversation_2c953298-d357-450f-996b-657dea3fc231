import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { FnolTable } from '@/components/fnol-table'
import { Plus } from 'lucide-react'
import Link from 'next/link'

export default function FnolPage() {
  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">First Notice of Loss</h1>
        <Button asChild>
          <Link href="/fnol/new">
            <Plus className="mr-2 h-4 w-4" /> New FNOL
          </Link>
        </Button>
      </div>

      <Tabs defaultValue="pending" className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-2 md:flex">
          <TabsTrigger value="pending">Pending Conversion</TabsTrigger>
          <TabsTrigger value="all">All FNOLs</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="mt-6">
          <FnolTable filter="pending" />
        </TabsContent>

        <TabsContent value="all" className="mt-6">
          <FnolTable />
        </TabsContent>
      </Tabs>
    </div>
  )
}
