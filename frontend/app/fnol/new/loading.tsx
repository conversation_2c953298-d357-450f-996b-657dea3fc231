import { Skeleton } from '@/components/ui/skeleton'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'

export default function Loading() {
  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <Skeleton className="h-10 w-[300px]" />
      </div>

      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-[250px] mb-2" />
          <Skeleton className="h-5 w-[400px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-8">
            {/* Reporter Information */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-[200px]" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[120px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[150px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[120px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[120px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            </div>

            {/* Incident Details */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-[150px]" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[120px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2 md:col-span-2">
                  <Skeleton className="h-5 w-[150px]" />
                  <Skeleton className="h-32 w-full" />
                </div>
              </div>
            </div>

            {/* Policy Information */}
            <div className="space-y-4">
              <Skeleton className="h-6 w-[180px]" />
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[100px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-5 w-[120px]" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <Skeleton className="h-10 w-[100px]" />
              <Skeleton className="h-10 w-[120px]" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
