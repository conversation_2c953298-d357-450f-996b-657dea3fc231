import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/documents/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Properly await params before using them
    const resolvedParams = await params
    const docId = resolvedParams.id

    return handleGetRequest(request, `/api/v1/documents/${docId}`, {
      errorMessage: 'Error fetching document',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching document',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PATCH requests to /api/v1/documents/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Properly await params before using them
    const resolvedParams = await params
    const docId = resolvedParams.id

    return handleDataRequest(request, `/api/v1/documents/${docId}`, {
      method: 'PATCH',
      errorMessage: 'Error updating document',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while updating document',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles DELETE requests to /api/v1/documents/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Properly await params before using them
    const resolvedParams = await params
    const docId = resolvedParams.id

    return handleDataRequest(request, `/api/v1/documents/${docId}`, {
      method: 'DELETE',
      errorMessage: 'Error deleting document',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while deleting document',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
