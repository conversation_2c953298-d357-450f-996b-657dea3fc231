import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/documents
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleGetRequest(request, '/api/v1/documents', {
      errorMessage: 'Error fetching documents',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching documents',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles POST requests to /api/v1/documents
 * Proxies the request to the backend API and returns the response
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/documents', {
      method: 'POST',
      errorMessage: 'Error creating document',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while creating document',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
