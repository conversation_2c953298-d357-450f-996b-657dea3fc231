import { NextRequest } from 'next/server'
import {
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * <PERSON>les POST requests to /api/v1/documents/upload-url
 * Proxies the request to the backend API for generating document upload URLs
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/documents/upload-url')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/documents/upload-url', {
      method: 'POST',
      errorMessage: 'Error generating document upload URL',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while generating document upload URL',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
