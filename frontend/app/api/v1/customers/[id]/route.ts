import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/customers/[id]
 * Proxies the request to the backend API to get a specific customer
 */
export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { id } = await context.params
  return handleGetRequest(request, `/api/v1/customers/${id}`, {
    errorMessage: `Error fetching customer ${id}`,
  })
}

/**
 * Handles PATCH requests to /api/v1/customers/[id]
 * Proxies the request to the backend API to update a customer
 */
export async function PATCH(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { id } = await context.params
  return handleDataRequest(request, `/api/v1/customers/${id}`, {
    method: 'PATCH',
    errorMessage: `Error updating customer ${id}`,
  })
}

/**
 * <PERSON>les DELETE requests to /api/v1/customers/[id]
 * Proxies the request to the backend API to delete a customer
 */
export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { id } = await context.params
  return handleDataRequest(request, `/api/v1/customers/${id}`, {
    method: 'DELETE',
    errorMessage: `Error deleting customer ${id}`,
  })
}

/**
 * Handles OPTIONS requests to /api/v1/customers/[id]
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
