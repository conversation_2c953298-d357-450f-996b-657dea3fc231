import { NextRequest, NextResponse } from 'next/server'
import { handleDataRequest, BACKEND_URL } from '@/lib/api-proxy-utils'

/**
 * <PERSON>les POST requests to /api/v1/auth/logout
 * Proxies the request to the backend API for logging out
 * Always returns success to ensure client-side token clearing
 */
export async function POST(request: NextRequest) {
  try {
    const data = await request.json().catch(() => ({}))

    // If no refresh token, just return success for client-side logout
    if (!data.refresh_token) {
      console.log('No refresh token provided - performing client-side logout only')
      return NextResponse.json({ success: true, message: 'Logged out successfully' })
    }

    // Try backend logout but handle failures gracefully
    try {
      const response = await handleDataRequest(request, '/api/v1/auth/logout', {
        method: 'POST',
        errorMessage: 'Backend logout failed',
        requireAuth: false, // Don't require auth header for logout
      })

      // Extract Set-Cookie headers for cookie clearing if available
      const setCookieHeaders = response.headers.get('set-cookie')

      // Create success response
      const nextResponse = NextResponse.json({
        success: true,
        message: 'Logged out successfully',
      })

      // Forward Set-Cookie headers for proper cookie clearing
      if (setCookieHeaders) {
        nextResponse.headers.set('set-cookie', setCookieHeaders)
        console.log('🍪 Forwarded Set-Cookie headers to client for logout')
      }

      return nextResponse
    } catch (error) {
      // Even if backend logout fails, return success for client-side logout
      console.warn('Backend logout failed, but proceeding with client-side logout:', error)
      return NextResponse.json({
        success: true,
        message: 'Logout completed, but encountered an error contacting the server',
      })
    }
  } catch (error) {
    console.error('Logout proxy error:', error)
    // Always return success to ensure client clears tokens
    return NextResponse.json({
      success: true,
      message: 'Logout completed, but encountered an error contacting the server',
      error: error instanceof Error ? error.message : 'Unknown error',
    })
  }
}
