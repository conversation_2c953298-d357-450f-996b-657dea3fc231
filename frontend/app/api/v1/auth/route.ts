import { NextRequest, NextResponse } from 'next/server'
import { BACKEND_URL, createErrorResponse, ErrorCode } from '@/lib/api-proxy-utils'
import { logRequest, logResponse, logError, createTimer } from '@/lib/api-logging'

/**
 * <PERSON>les POST requests to /api/v1/auth for token generation
 * Proxies the request to the backend API for authentication, ensuring correct Content-Type
 */
export async function POST(request: NextRequest) {
  const routePath = '/api/v1/auth/token' // Define the route path for logging
  const requestId = logRequest(request, routePath) // Pass request and path
  const timer = createTimer() // Initialize timer
  const backendUrl = `${BACKEND_URL}/api/v1/auth/token`

  console.log(`[${requestId}] 🔑 Authentication token request received`)

  try {
    // Read the raw body (which should be x-www-form-urlencoded)
    const requestBody = await request.text()
    console.log(`[${requestId}] 📦 Request body (text):`, requestBody.substring(0, 500) + '...')

    // Forward the request directly, preserving the original body and setting the correct Content-Type
    const backendResponse = await fetch(backendUrl, {
      method: 'POST',
      headers: {
        // IMPORTANT: Set the correct Content-Type expected by the backend token endpoint
        'Content-Type': 'application/x-www-form-urlencoded',
        // Forward other relevant headers if necessary (e.g., Accept)
        Accept: 'application/json',
      },
      body: requestBody, // Send the raw form data body
      credentials: 'include', // Include cookies in the request
    })

    const duration = timer() // Stop timer and get duration

    // Process the response
    if (!backendResponse.ok) {
      const errorText = await backendResponse.text()
      let errorJson: any = {}
      try {
        errorJson = JSON.parse(errorText)
      } catch (e) {
        console.error(`[${requestId}] ⚠️ Could not parse error response as JSON`)
      }
      logError(
        requestId,
        request.method, // Pass method
        routePath, // Pass path
        backendResponse.status,
        `Backend token endpoint error: ${errorJson.detail || errorText}`,
        duration
        // Removed backendUrl from logError as it's not an expected param
      )
      return createErrorResponse(
        `Authentication failed: ${errorJson.detail || backendResponse.statusText}`,
        backendResponse.status,
        ErrorCode.AUTH_INVALID, // More specific error code
        errorJson,
        requestId,
        request.method,
        request.nextUrl.pathname // Use actual request path for context
      )
    }

    // Success - read the JSON response (access_token, refresh_token)
    const responseData = await backendResponse.json()
    logResponse(
      requestId,
      request.method, // Pass method
      routePath, // Pass path
      backendResponse.status,
      duration
      // Removed request and backendResponse objects as logResponse expects strings/numbers
    )
    console.log(`[${requestId}] ✅ Authentication successful`)

    // Extract Set-Cookie headers from backend response
    const setCookieHeaders = backendResponse.headers.get('set-cookie')
    console.log(`[${requestId}] 🍪 Set-Cookie headers from backend:`, setCookieHeaders)

    // Create response headers object
    const responseHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    // Create the NextResponse first
    const response = NextResponse.json(responseData, {
      status: backendResponse.status,
      headers: responseHeaders,
    })

    // Forward Set-Cookie headers if they exist
    if (setCookieHeaders) {
      // Note: set-cookie header can contain multiple cookies separated by comma
      // We need to handle this properly for the remember me functionality
      response.headers.set('set-cookie', setCookieHeaders)
      console.log(`[${requestId}] 🍪 Forwarded Set-Cookie headers to client`)
    }

    // Return the token response to the client with forwarded cookies
    return response
  } catch (error) {
    const duration = timer() // Stop timer and get duration
    const errorMessage = error instanceof Error ? error.message : 'Unknown auth error'
    logError(
      requestId,
      request.method, // Pass method
      routePath, // Pass path
      500,
      `Internal auth proxy error: ${errorMessage}`,
      duration
      // Removed backendUrl from logError
    )
    console.error('Authentication error in API route:', error)
    return createErrorResponse(
      `Internal Server Error during authentication: ${errorMessage}`,
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      request.method,
      request.nextUrl.pathname // Use actual request path for context
    )
  }
}
