import { NextRequest, NextResponse } from 'next/server'
import { BACKEND_URL, createErrorResponse, getAuthHeader } from '@/lib/api-proxy-utils'
import { logRequest, logError, logResponse, createTimer } from '@/lib/api-logging'

/**
 * <PERSON>les POST requests to /api/v1/auth/refresh
 * Refreshes an access token using a refresh token
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/auth/refresh')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    const data = await request.json().catch(() => ({}))
    const refreshToken = data.refresh_token

    if (!refreshToken) {
      const elapsedTime = getElapsedTime()
      logError(requestId, method, path, 400, 'No refresh token provided', elapsedTime)
      return createErrorResponse(
        'Refresh token is required',
        400,
        'MISSING_REFRESH_TOKEN',
        undefined,
        requestId,
        method,
        path
      )
    }

    // Make request to backend with query parameter
    const apiUrl = `${BACKEND_URL}/api/v1/auth/refresh?refresh_token=${encodeURIComponent(refreshToken)}`

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Request-ID': requestId,
      },
      credentials: 'include',
    })

    // Log response time
    const elapsedTime = getElapsedTime()
    logResponse(requestId, method, path, response.status, elapsedTime)

    // Handle error responses
    if (!response.ok) {
      let errorMessage = 'Token refresh failed'
      let errorCode = 'REFRESH_FAILED'

      try {
        const errorData = await response.json()
        errorMessage = errorData.detail || errorMessage
        errorCode = errorData.error_code || errorCode
      } catch (parseError) {
        logError(
          requestId,
          method,
          path,
          response.status,
          'Failed to parse error response',
          elapsedTime
        )
      }

      logError(requestId, method, path, response.status, errorMessage, elapsedTime)
      return createErrorResponse(
        errorMessage,
        response.status,
        errorCode,
        undefined,
        requestId,
        method,
        path
      )
    }

    // Handle successful response
    try {
      const responseData = await response.json()

      // Extract Set-Cookie headers from backend response for remember me functionality
      const setCookieHeaders = response.headers.get('set-cookie')

      // Create the NextResponse
      const nextResponse = NextResponse.json(responseData)

      // Forward Set-Cookie headers if they exist
      if (setCookieHeaders) {
        nextResponse.headers.set('set-cookie', setCookieHeaders)
        console.log('🍪 Forwarded Set-Cookie headers to client for refresh')
      }

      return nextResponse
    } catch (parseError) {
      logError(requestId, method, path, 500, 'Failed to parse success response', elapsedTime)
      return createErrorResponse(
        'Invalid response from authentication service',
        500,
        'INVALID_RESPONSE',
        undefined,
        requestId,
        method,
        path
      )
    }
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      error instanceof Error ? error.message : 'Token refresh failed',
      500,
      'REFRESH_ERROR',
      undefined,
      requestId,
      method,
      path
    )
  }
}
