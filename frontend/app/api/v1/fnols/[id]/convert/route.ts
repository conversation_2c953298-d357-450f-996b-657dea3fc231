import { NextRequest } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * <PERSON>les POST requests to /api/v1/fnols/{id}/convert
 * Proxies the request to the backend API and returns the response
 */
export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  // Extract the ID from params
  const resolvedParams = await params
  const id = resolvedParams.id

  // Create the backend path - the query parameters will be automatically forwarded by handleDataRequest
  const backendPath = `/api/v1/fnols/${id}/convert`

  return handleDataRequest(request, backendPath, {
    method: 'POST',
    errorMessage: `Error converting FNOL ${id} to claim`,
  })
}
