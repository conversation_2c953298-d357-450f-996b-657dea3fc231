import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * <PERSON>les GET requests to /api/v1/fnols/{id}
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params
  const id = resolvedParams.id
  return handleGetRequest(request, `/api/v1/fnols/${id}`, {
    errorMessage: 'Error fetching FNOL',
  })
}

/**
 * Handles PATCH requests to /api/v1/fnols/{id}
 * Proxies the request to the backend API and returns the response
 */
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params
  const id = resolvedParams.id
  return handleDataRequest(request, `/api/v1/fnols/${id}`, {
    method: 'PATCH',
    errorMessage: `Error updating FNOL ${id}`,
  })
}

/**
 * <PERSON>les DELETE requests to /api/v1/fnols/{id}
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params
  const id = resolvedParams.id
  return handleDataRequest(request, `/api/v1/fnols/${id}`, {
    method: 'DELETE',
    errorMessage: `Error deleting FNOL ${id}`,
  })
}
