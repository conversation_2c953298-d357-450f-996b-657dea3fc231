import { NextRequest } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * Handles PUT requests to /api/v1/fnols/{id}/status
 * Proxies the request to the backend API and returns the response
 */
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  const id = params.id
  return handleDataRequest(request, `/api/v1/fnols/${id}/status`, {
    method: 'PUT',
    errorMessage: `Error updating FNOL ${id} status`,
  })
}
