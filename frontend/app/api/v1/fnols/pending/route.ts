import { NextRequest } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'
import { FNOLStatus } from '@/lib/api/types'

/**
 * Handles GET requests to /api/v1/fnols/pending
 * Fetches FNOLs that are pending conversion (status NEW or REVIEWED, not CONVERTED or REJECTED)
 *
 * Since the backend might not support complex filtering, we use the status=NEW filter
 * and potentially do additional filtering in the frontend
 */
export async function GET(request: NextRequest) {
  // Get the existing URL search params from the request
  const searchParams = request.nextUrl.searchParams

  // Add the status filter for NEW FNOLs (basic pending conversion)
  // In a production environment, you would want to modify this to support
  // filtering for both NEW and REVIEWED statuses if the backend supports it
  searchParams.set('status', FNOLStatus.NEW)

  // Build the backend path with all the original query parameters plus our status filter
  let backendPath = '/api/v1/fnols'
  if (searchParams.toString()) {
    backendPath += `?${searchParams.toString()}`
  }

  return handleGetRequest(request, backendPath, {
    errorMessage: 'Error fetching pending conversion FNOLs',
  })
}
