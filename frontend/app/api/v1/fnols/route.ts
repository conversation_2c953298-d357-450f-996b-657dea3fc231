import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * <PERSON>les GET requests to /api/v1/fnols
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  return handleGetRequest(request, '/api/v1/fnols', {
    errorMessage: 'Error fetching FNOLs',
  })
}

/**
 * Handles POST requests to /api/v1/fnols
 * Proxies the request to the backend API and returns the response
 */
export async function POST(request: NextRequest) {
  return handleDataRequest(request, '/api/v1/fnols', {
    method: 'POST',
    errorMessage: 'Error creating FNOL',
  })
}
