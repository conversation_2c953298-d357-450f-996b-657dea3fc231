import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

export async function GET(request: NextRequest) {
  return handleGetRequest(request, '/api/v1/claims', {
    errorMessage: 'Error fetching claims',
  })
}

export async function POST(request: NextRequest) {
  return handleDataRequest(request, '/api/v1/claims', {
    method: 'POST',
    errorMessage: 'Error creating claim',
  })
}
