import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * GET /api/v1/claims/[number]/attorneys
 * Get attorneys for a claim
 * Special handling: Unwraps array from {items, total} format to direct array
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const response = await handleGetRequest(
    request,
    `/api/v1/claims/${resolvedParams.number}/attorneys`,
    {
      errorMessage: 'Error fetching attorneys',
    }
  )

  // Handle 404 specially to return empty array
  if (response.status === HTTP_STATUS.NOT_FOUND) {
    return NextResponse.json([], {
      status: HTTP_STATUS.OK,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // If response is successful, unwrap the array from {items, total} format
  if (response.ok) {
    const data = await response.json()

    // If the response is wrapped in {items, total} format, unwrap it
    if (data && typeof data === 'object' && Array.isArray(data.items)) {
      return NextResponse.json(data.items, {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // If it's already an array, return as is
    if (Array.isArray(data)) {
      return NextResponse.json(data, {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // If it's neither wrapped nor an array, wrap it in an array
    return NextResponse.json([data], {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // Return the original response for error cases
  return response
}

/**
 * POST /api/v1/claims/[number]/attorneys
 * Create a new attorney for a claim
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  return handleDataRequest(request, `/api/v1/claims/${resolvedParams.number}/attorneys`, {
    method: 'POST',
    errorMessage: 'Error creating attorney',
  })
}
