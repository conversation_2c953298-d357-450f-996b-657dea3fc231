import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/attorneys/[attorneyId]
 * Get a specific attorney by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; attorneyId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, attorneyId } = resolvedParams
  return handleGetRequest(request, `/api/v1/claims/${claimId}/attorneys/${attorneyId}`, {
    errorMessage: 'Error fetching attorney',
  })
}

/**
 * PATCH /api/v1/claims/[number]/attorneys/[attorneyId]
 * Update a specific attorney
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; attorneyId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, attorneyId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimId}/attorneys/${attorneyId}`, {
    method: 'PATCH',
    errorMessage: 'Error updating attorney',
  })
}

/**
 * DELETE /api/v1/claims/[number]/attorneys/[attorneyId]
 * Delete a specific attorney
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; attorneyId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, attorneyId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimId}/attorneys/${attorneyId}`, {
    method: 'DELETE',
    errorMessage: 'Error deleting attorney',
  })
}
