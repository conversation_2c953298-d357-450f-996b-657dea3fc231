import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const claimNumber = resolvedParams.number

  return handleGetRequest(request, `/api/v1/claims/${claimNumber}`, {
    errorMessage: `Error fetching claim ${claimNumber}`,
  })
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const claimNumber = resolvedParams.number

  return handleDataRequest(request, `/api/v1/claims/${claimNumber}`, {
    method: 'PATCH',
    errorMessage: `Error updating claim ${claimNumber}`,
  })
}
