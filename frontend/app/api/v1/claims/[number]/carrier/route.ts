import { NextRequest, NextResponse } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

export async function PATCH(request: NextRequest, { params }: { params: { number: string } }) {
  const number = params.number
  const backendPath = `/api/v1/claims/${number}/carrier`

  return handleDataRequest(request, backendPath, {
    method: 'PATCH',
    errorMessage: `Failed to update carrier details for claim ${number}`,
  })
}
