import { NextRequest } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'

// Define the route segment config to handle params correctly
export const dynamic = 'force-dynamic'

/**
 * GET /api/v1/claims/[number]/audit
 * Get audit entries for a claim with optional filtering
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  return handleGetRequest(request, `/api/v1/claims/${resolvedParams.number}/audit`, {
    errorMessage: 'Error fetching audit entries',
  })
}
