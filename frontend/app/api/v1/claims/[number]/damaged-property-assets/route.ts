import { NextRequest, NextResponse } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/claims/[number]/damaged-property-assets
 * Proxies the request to the backend API and returns the response
 * Special handling: Unwraps array from {items, total} format
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/damaged-property-assets')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract claimNumber after properly awaiting params
    const resolvedParams = await params
    const claimNumber = resolvedParams.number

    const response = await handleGetRequest(
      request,
      `/api/v1/claims/${claimNumber}/damaged-property-assets`,
      {
        errorMessage: 'Error fetching damaged property assets',
      }
    )

    // If response is successful, unwrap the array from {items, total} format
    if (response.ok) {
      const data = await response.json()

      // If the response is wrapped in {items, total} format, unwrap it
      if (data && typeof data === 'object' && Array.isArray(data.items)) {
        return NextResponse.json(data.items, {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      }

      // If it's already an array, return as is
      if (Array.isArray(data)) {
        return NextResponse.json(data, {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
          },
        })
      }

      // If it's neither wrapped nor an array, wrap it in an array
      return NextResponse.json([data], {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // Return the original response for error cases
    return response
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching damaged property assets',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles POST requests to /api/v1/claims/[number]/damaged-property-assets
 * Proxies the request to the backend API and returns the response
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/damaged-property-assets')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract claimNumber after properly awaiting params
    const resolvedParams = await params
    const claimNumber = resolvedParams.number

    return handleDataRequest(request, `/api/v1/claims/${claimNumber}/damaged-property-assets`, {
      method: 'POST',
      errorMessage: 'Error creating damaged property asset',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while creating damaged property asset',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
