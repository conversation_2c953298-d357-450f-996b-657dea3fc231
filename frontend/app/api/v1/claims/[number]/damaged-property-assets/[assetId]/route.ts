import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/claims/[number]/damaged-property-assets/[assetId]
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { number: string; assetId: string } }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/damaged-property-assets/[assetId]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract parameters
    const resolvedParams = await params
    const claimNumber = resolvedParams.number
    const assetId = resolvedParams.assetId

    return handleGetRequest(
      request,
      `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}`,
      {
        errorMessage: 'Error fetching damaged property asset',
      }
    )
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching damaged property asset',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PUT requests to /api/v1/claims/[number]/damaged-property-assets/[assetId]
 * Proxies the request to the backend API and returns the response
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { number: string; assetId: string } }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/damaged-property-assets/[assetId]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract parameters
    const resolvedParams = await params
    const claimNumber = resolvedParams.number
    const assetId = resolvedParams.assetId

    return handleDataRequest(
      request,
      `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}`,
      {
        method: 'PUT',
        errorMessage: 'Error updating damaged property asset',
        allowedContentTypes: [ContentType.JSON],
      }
    )
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while updating damaged property asset',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles DELETE requests to /api/v1/claims/[number]/damaged-property-assets/[assetId]
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { number: string; assetId: string } }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/damaged-property-assets/[assetId]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract parameters
    const resolvedParams = await params
    const claimNumber = resolvedParams.number
    const assetId = resolvedParams.assetId

    return handleDataRequest(
      request,
      `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}`,
      {
        method: 'DELETE',
        errorMessage: 'Error deleting damaged property asset',
      }
    )
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while deleting damaged property asset',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
