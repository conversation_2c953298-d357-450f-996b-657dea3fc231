import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/damaged-property-assets/[assetId]/damage-instances/[instanceId]
 * Get a specific damage instance by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; assetId: string; instanceId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, assetId, instanceId } = resolvedParams
  return handleGetRequest(
    request,
    `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`,
    {
      errorMessage: 'Error fetching damage instance',
    }
  )
}

/**
 * PUT /api/v1/claims/[number]/damaged-property-assets/[assetId]/damage-instances/[instanceId]
 * Update a specific damage instance
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; assetId: string; instanceId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, assetId, instanceId } = resolvedParams
  return handleDataRequest(
    request,
    `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`,
    {
      method: 'PUT',
      errorMessage: 'Error updating damage instance',
    }
  )
}

/**
 * DELETE /api/v1/claims/[number]/damaged-property-assets/[assetId]/damage-instances/[instanceId]
 * Delete a specific damage instance
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; assetId: string; instanceId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, assetId, instanceId } = resolvedParams
  return handleDataRequest(
    request,
    `/api/v1/claims/${claimNumber}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`,
    {
      method: 'DELETE',
      errorMessage: 'Error deleting damage instance',
    }
  )
}
