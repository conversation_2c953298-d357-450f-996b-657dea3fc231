import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/claims/[number]/bodily_injury
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/bodily_injury')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract claimNumber after properly awaiting params
    const resolvedParams = await params
    const claimNumber = resolvedParams.number

    return handleGetRequest(request, `/api/v1/claims/${claimNumber}/bodily_injury`, {
      errorMessage: 'Error fetching bodily injury details',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching bodily injury details',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PUT requests to /api/v1/claims/[number]/bodily_injury
 * Proxies the request to the backend API and returns the response
 */
export async function PUT(request: NextRequest, { params }: { params: { number: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/bodily_injury')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract claimNumber after properly awaiting params
    const resolvedParams = await params
    const claimNumber = resolvedParams.number

    return handleDataRequest(request, `/api/v1/claims/${claimNumber}/bodily_injury`, {
      method: 'PUT',
      errorMessage: 'Error updating bodily injury details',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while updating bodily injury details',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles DELETE requests to /api/v1/claims/[number]/bodily_injury
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(request: NextRequest, { params }: { params: { number: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/bodily_injury')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract claimNumber after properly awaiting params
    const resolvedParams = await params
    const claimNumber = resolvedParams.number

    return handleDataRequest(request, `/api/v1/claims/${claimNumber}/bodily_injury`, {
      method: 'DELETE',
      errorMessage: 'Error deleting bodily injury details',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while deleting bodily injury details',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
