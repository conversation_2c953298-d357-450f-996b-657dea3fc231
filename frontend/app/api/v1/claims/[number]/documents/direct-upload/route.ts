import { NextRequest } from 'next/server'
import { handleFormDataRequest } from '@/lib/api-proxy-utils'

/**
 * POST /api/v1/claims/[number]/documents/direct-upload
 * Upload a file directly and create a document record
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber } = resolvedParams

  return handleFormDataRequest(request, `/api/v1/claims/${claimNumber}/documents/direct-upload`, {
    method: 'POST',
    errorMessage: 'Error uploading document',
    maxFileSize: 100 * 1024 * 1024, // 100MB to match backend
  })
}
