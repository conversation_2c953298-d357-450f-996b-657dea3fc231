import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * Handles GET requests for claim documents
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  return handleGetRequest(request, `/api/v1/claims/${resolvedParams.number}/documents`, {
    errorMessage: 'Error fetching documents',
  })
}

/**
 * Handles POST requests to create a new document for a claim
 * Proxies the request to the backend API and returns the response
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  return handleDataRequest(request, `/api/v1/claims/${resolvedParams.number}/documents`, {
    method: 'POST',
    errorMessage: 'Error creating document',
  })
}
