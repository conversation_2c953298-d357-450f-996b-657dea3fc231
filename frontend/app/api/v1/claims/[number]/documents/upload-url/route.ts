import { NextRequest } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * POST /api/v1/claims/[number]/documents/upload-url
 * Get a pre-signed URL for uploading a document to a claim
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimNumber}/documents/upload-url`, {
    method: 'POST',
    errorMessage: 'Error getting document upload URL',
  })
}
