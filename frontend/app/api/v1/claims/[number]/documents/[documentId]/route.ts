import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * Handles GET requests for a specific document in a claim
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; documentId: string }> }
) {
  const resolvedParams = await params
  return handleGetRequest(
    request,
    `/api/v1/claims/${resolvedParams.number}/documents/${resolvedParams.documentId}`,
    {
      errorMessage: `Error fetching document ${resolvedParams.documentId} for claim ${resolvedParams.number}`,
    }
  )
}

/**
 * Handles PATCH requests to update a document
 * Proxies the request to the backend API and returns the response
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; documentId: string }> }
) {
  const resolvedParams = await params
  return handleDataRequest(
    request,
    `/api/v1/claims/${resolvedParams.number}/documents/${resolvedParams.documentId}`,
    {
      method: 'PATCH',
      errorMessage: `Error updating document ${resolvedParams.documentId} for claim ${resolvedParams.number}`,
    }
  )
}

/**
 * Handles DELETE requests to delete a document
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; documentId: string }> }
) {
  const resolvedParams = await params
  return handleDataRequest(
    request,
    `/api/v1/claims/${resolvedParams.number}/documents/${resolvedParams.documentId}`,
    {
      method: 'DELETE',
      errorMessage: `Error deleting document ${resolvedParams.documentId} for claim ${resolvedParams.number}`,
    }
  )
}
