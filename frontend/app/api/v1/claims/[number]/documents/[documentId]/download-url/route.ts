import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequestRaw, createErrorResponse, ErrorCode } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/documents/[documentId]/download-url
 * Get download URL for a claim document with special download flag processing
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; documentId: string }> }
) {
  try {
    const resolvedParams = await params
    const { number: claimNumber, documentId: docId } = resolvedParams

    // Get raw data from backend
    const { data, status } = await handleGetRequestRaw(
      request,
      `/api/v1/claims/${claimNumber}/documents/${docId}/download-url`,
      {
        errorMessage: 'Error fetching document download URL',
      }
    )

    // Add download flag WITHOUT modifying the URL (preserve special logic)
    if (data && data.download_url) {
      data.should_download = true
    }

    return NextResponse.json(data, { status })
  } catch (error) {
    return createErrorResponse(
      'Failed to get document download URL',
      500,
      ErrorCode.API_ROUTE_ERROR,
      process.env.NODE_ENV === 'development'
        ? { details: error instanceof Error ? error.message : String(error) }
        : undefined
    )
  }
}
