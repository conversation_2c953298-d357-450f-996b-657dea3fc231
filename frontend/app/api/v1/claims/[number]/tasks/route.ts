// frontend/app/api/v1/claims/[number]/tasks/route.ts
// Note: The [number] dynamic segment in this route path will actually receive the claim's UUID.
// This naming is for consistency with other routes under /api/v1/claims/[number]/
import { NextRequest } from 'next/server'
import {
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles POST requests to /api/v1/claims/[number]/tasks
 * Proxies the request to the backend API and returns the response
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { number: string } } // Changed 'claimId' to 'number'
) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/claims/[number]/tasks')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    const actualClaimUuid = params.number // Changed 'params.claimId' to 'params.number'

    return handleDataRequest(request, `/api/v1/claims/${actualClaimUuid}/tasks/`, {
      method: 'POST',
      errorMessage: 'Error creating task',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while creating task',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
