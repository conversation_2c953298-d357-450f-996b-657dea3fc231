import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const number = resolvedParams.number
  const backendPath = `/api/v1/claims/${number}/financials/reserve-history`

  return handleGetRequest(request, backendPath, {
    errorMessage: `Failed to fetch reserve history for claim ${number}`,
    // Add required permissions check if needed
  })
}

// Add OPTIONS handler if needed
// export { OPTIONS } from '@/lib/api-proxy-utils';
