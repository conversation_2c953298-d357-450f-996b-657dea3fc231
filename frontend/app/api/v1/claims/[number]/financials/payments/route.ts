import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest, ContentType } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const number = resolvedParams.number
  const searchParams = request.nextUrl.searchParams.toString()
  const backendPath = `/api/v1/claims/${number}/financials/payments${searchParams ? `?${searchParams}` : ''}`

  return handleGetRequest(request, backendPath, {
    errorMessage: `Failed to fetch payments for claim ${number}`,
    // Add required permissions check if needed
    // forwardSearchParams: true, // Removed this incorrect option
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const number = resolvedParams.number
  const backendPath = `/api/v1/claims/${number}/financials/payments`

  try {
    // Check if we're in a browser environment - we won't be in API routes
    const isClient = typeof window !== 'undefined'

    if (!isClient) {
      // We're in a server environment
      // Check if authorization header exists
      const authHeader = request.headers.get('authorization')
      if (!authHeader) {
        console.error('Missing Authorization header in the payment request to backend')
        return new Response(
          JSON.stringify({
            detail: 'Authorization header is required',
            status: HTTP_STATUS.UNAUTHORIZED,
            error_code: 'AUTH_REQUIRED',
          }),
          {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: { 'Content-Type': 'application/json' },
          }
        )
      }

      console.log('API route: Authorization header is present, forwarding to backend')
    }

    // Forward the request to the backend API
    return handleDataRequest(request, backendPath, {
      method: 'POST',
      errorMessage: `Failed to add payment for claim ${number}`,
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    console.error('Payment API exception:', error)
    return new Response(
      JSON.stringify({
        detail: error instanceof Error ? error.message : 'An unexpected error occurred',
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        error_code: 'INTERNAL_SERVER_ERROR',
      }),
      {
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}

// Add OPTIONS handler if needed
// export { OPTIONS } from '@/lib/api-proxy-utils';
