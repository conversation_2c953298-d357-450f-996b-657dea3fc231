import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'
// import { apiLogger } from '@/lib/api-logging'; // Logging is likely handled by handleGetRequest

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const number = resolvedParams.number
  const backendPath = `/api/v1/claims/${number}/financials`

  // Logging is likely handled internally by handleGetRequest based on standards doc
  // apiLogger.info(`GET Request - Proxying to: ${backendPath}`);

  return handleGetRequest(request, backendPath, {
    errorMessage: `Failed to fetch financials for claim ${number}`,
    // Potentially pass logger options if handleGetRequest accepts them
  })
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string }> }
) {
  const resolvedParams = await params
  const number = resolvedParams.number
  const backendPath = `/api/v1/claims/${number}/financials`

  // Logging is handled by handleDataRequest
  // apiLogger.info(`POST Request - Proxying to: ${backendPath}`);

  return handleDataRequest(request, backendPath, {
    method: 'POST',
    errorMessage: `Failed to create financials for claim ${number}`,
    // allowedContentTypes will default to ['application/json']
    // requireAuth will default to true
  })
}

// Add OPTIONS handler if needed based on project standards
// export { OPTIONS } from '@/lib/api-proxy-utils'; // Or implement manually
