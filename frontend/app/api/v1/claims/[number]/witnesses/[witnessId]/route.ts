import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/witnesses/[witnessId]
 * Get a specific witness by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; witnessId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, witnessId } = resolvedParams
  return handleGetRequest(request, `/api/v1/claims/${claimId}/witnesses/${witnessId}`, {
    errorMessage: 'Error fetching witness',
  })
}

/**
 * PATCH /api/v1/claims/[number]/witnesses/[witnessId]
 * Update a specific witness
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; witnessId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, witnessId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimId}/witnesses/${witnessId}`, {
    method: 'PATCH',
    errorMessage: 'Error updating witness',
  })
}

/**
 * DELETE /api/v1/claims/[number]/witnesses/[witnessId]
 * Delete a specific witness
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; witnessId: string }> }
) {
  const resolvedParams = await params
  const { number: claimId, witnessId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimId}/witnesses/${witnessId}`, {
    method: 'DELETE',
    errorMessage: 'Error deleting witness',
  })
}
