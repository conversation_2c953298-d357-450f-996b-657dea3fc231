import { NextRequest, NextResponse } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

export async function PATCH(request: NextRequest, { params }: { params: { number: string } }) {
  const number = params.number
  const backendPath = `/api/v1/claims/${number}/recovery/status`

  return handleDataRequest(request, backendPath, {
    method: 'PATCH',
    errorMessage: `Failed to update recovery status for claim ${number}`,
  })
}
