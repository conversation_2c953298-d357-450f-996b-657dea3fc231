import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * GET /api/v1/claims/[number]/injured-persons/[personId]/injuries
 * Get all injuries for a specific injured person
 * Special handling: Unwraps array from {items, total} format to direct array
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId } = resolvedParams
  const response = await handleGetRequest(
    request,
    `/api/v1/claims/${claimNumber}/injured-persons/${personId}/injuries`,
    {
      errorMessage: 'Error fetching injuries',
    }
  )

  // Handle 404 specially to return empty array
  if (response.status === HTTP_STATUS.NOT_FOUND) {
    return NextResponse.json([], {
      status: HTTP_STATUS.OK,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // If response is successful, unwrap the array from {items, total} format
  if (response.ok) {
    const data = await response.json()

    // If the response is wrapped in {items, total} format, unwrap it
    if (data && typeof data === 'object' && Array.isArray(data.items)) {
      return NextResponse.json(data.items, {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // If it's already an array, return as is
    if (Array.isArray(data)) {
      return NextResponse.json(data, {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    }

    // If it's neither wrapped nor an array, wrap it in an array
    return NextResponse.json([data], {
      status: response.status,
      headers: {
        'Content-Type': 'application/json',
      },
    })
  }

  // Return the original response for error cases
  return response
}

/**
 * POST /api/v1/claims/[number]/injured-persons/[personId]/injuries
 * Create a new injury for a specific injured person
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId } = resolvedParams
  return handleDataRequest(
    request,
    `/api/v1/claims/${claimNumber}/injured-persons/${personId}/injuries`,
    {
      method: 'POST',
      errorMessage: 'Error creating injury',
    }
  )
}
