import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/injured-persons/[personId]
 * Get a specific injured person by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId } = resolvedParams
  return handleGetRequest(request, `/api/v1/claims/${claimNumber}/injured-persons/${personId}`, {
    errorMessage: 'Error fetching injured person',
  })
}

/**
 * PUT /api/v1/claims/[number]/injured-persons/[personId]
 * Update a specific injured person
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimNumber}/injured-persons/${personId}`, {
    method: 'PUT',
    errorMessage: 'Error updating injured person',
  })
}

/**
 * DELETE /api/v1/claims/[number]/injured-persons/[personId]
 * Delete a specific injured person
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId } = resolvedParams
  return handleDataRequest(request, `/api/v1/claims/${claimNumber}/injured-persons/${personId}`, {
    method: 'DELETE',
    errorMessage: 'Error deleting injured person',
  })
}
