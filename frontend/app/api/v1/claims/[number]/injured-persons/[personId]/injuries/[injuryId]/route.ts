import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * GET /api/v1/claims/[number]/injured-persons/[personId]/injuries/[injuryId]
 * Get a specific injury by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string; injuryId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId, injuryId } = resolvedParams
  return handleGetRequest(
    request,
    `/api/v1/claims/${claimNumber}/injured-persons/${personId}/injuries/${injuryId}`,
    {
      errorMessage: 'Error fetching injury',
    }
  )
}

/**
 * PUT /api/v1/claims/[number]/injured-persons/[personId]/injuries/[injuryId]
 * Update a specific injury
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string; injuryId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId, injuryId } = resolvedParams
  return handleDataRequest(
    request,
    `/api/v1/claims/${claimNumber}/injured-persons/${personId}/injuries/${injuryId}`,
    {
      method: 'PUT',
      errorMessage: 'Error updating injury',
    }
  )
}

/**
 * DELETE /api/v1/claims/[number]/injured-persons/[personId]/injuries/[injuryId]
 * Delete a specific injury
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ number: string; personId: string; injuryId: string }> }
) {
  const resolvedParams = await params
  const { number: claimNumber, personId, injuryId } = resolvedParams
  return handleDataRequest(
    request,
    `/api/v1/claims/${claimNumber}/injured-persons/${personId}/injuries/${injuryId}`,
    {
      method: 'DELETE',
      errorMessage: 'Error deleting injury',
    }
  )
}
