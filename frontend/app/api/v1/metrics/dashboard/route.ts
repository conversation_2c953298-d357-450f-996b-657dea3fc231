import { NextRequest } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'

/**
 * API route handler for GET /api/v1/metrics/dashboard
 */
export async function GET(request: NextRequest) {
  console.log('API Route - Received request for /api/v1/metrics/dashboard', {
    url: request.url,
    auth: !!request.headers.get('Authorization'),
    cookies: Object.fromEntries([...request.cookies.getAll()].map(c => [c.name, 'present'])),
  })

  try {
    const response = await handleGetRequest(request, '/api/v1/metrics/dashboard', {
      errorMessage: 'Error fetching dashboard metrics',
    })

    console.log('API Route - Metrics dashboard response status:', response.status)
    return response
  } catch (error) {
    console.error('API Route - Error in metrics dashboard handler:', error)
    throw error
  }
}
