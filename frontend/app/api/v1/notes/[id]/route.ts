import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ErrorCode,
  createErrorResponse,
  ContentType,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/notes/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/notes/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract noteId after properly awaiting params
    const resolvedParams = await params
    const noteId = resolvedParams.id

    return handleGetRequest(request, `/api/v1/notes/${noteId}`, {
      errorMessage: 'Error fetching note',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching note',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PATCH requests to /api/v1/notes/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/notes/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract noteId after properly awaiting params
    const resolvedParams = await params
    const noteId = resolvedParams.id

    return handleDataRequest(request, `/api/v1/notes/${noteId}`, {
      method: 'PATCH',
      errorMessage: 'Error updating note',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while updating note',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles DELETE requests to /api/v1/notes/[id]
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/notes/[id]')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Extract noteId after properly awaiting params
    const resolvedParams = await params
    const noteId = resolvedParams.id

    return handleDataRequest(request, `/api/v1/notes/${noteId}`, {
      method: 'DELETE',
      errorMessage: 'Error deleting note',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while deleting note',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
