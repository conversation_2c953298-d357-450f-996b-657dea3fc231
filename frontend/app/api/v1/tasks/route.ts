import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ContentType,
  ErrorCode,
  createErrorResponse,
  BACKEND_URL,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/tasks
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/tasks')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleGetRequest(request, '/api/v1/tasks/', {
      errorMessage: 'Error fetching tasks',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching tasks',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles POST requests to /api/v1/tasks
 * Proxies the request to the backend API and returns the response
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/tasks')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/tasks/', {
      method: 'POST',
      errorMessage: 'Error creating task',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while creating task',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
