import { NextRequest, NextResponse } from 'next/server'
import { handleDataRequest, BACKEND_URL } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles PATCH requests to /api/v1/tasks/[task_identifier]/assign
 * Proxies the request to the backend API and returns the response
 */
export async function PATCH(
  request: NextRequest,
  context: { params: Promise<{ task_identifier: string }> }
) {
  try {
    const { task_identifier } = await context.params

    return handleDataRequest(request, `/api/v1/tasks/${task_identifier}/assign`, {
      method: 'PATCH',
      errorMessage: `Error assigning task ${task_identifier}`,
    })
  } catch (error) {
    console.error('Error proxying assign task request:', error)
    return NextResponse.json(
      { detail: 'An error occurred while assigning the task' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles OPTIONS requests to /api/v1/tasks/[task_identifier]/assign
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new Response(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'PATCH, OPTIONS',
      'Access-Control-Allow-Methods': 'PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
