import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest, BACKEND_URL } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/tasks/[task_identifier]
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ task_identifier: string }> }
) {
  try {
    // Log the raw context for debugging
    console.log('Raw context:', context)
    const { task_identifier } = await context.params

    return handleGetRequest(request, `/api/v1/tasks/${task_identifier}`, {
      errorMessage: `Error fetching task ${task_identifier}`,
    })
  } catch (error) {
    console.error(`❌ Error in task detail proxy:`, error)
    return NextResponse.json(
      {
        detail: 'An error occurred while fetching task details',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles PUT requests to /api/v1/tasks/[task_identifier]
 * Proxies the request to the backend API and returns the response
 */
export async function PUT(
  request: NextRequest,
  context: { params: Promise<{ task_identifier: string }> }
) {
  try {
    console.log('PUT /api/v1/tasks/[task_identifier] - Raw context:', context)
    const { task_identifier } = await context.params

    return handleDataRequest(request, `/api/v1/tasks/${task_identifier}`, {
      method: 'PUT',
      errorMessage: `Error updating task ${task_identifier}`,
    })
  } catch (error) {
    console.error('Error proxying update task request:', error)
    return NextResponse.json(
      { detail: 'An error occurred while updating the task' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles DELETE requests to /api/v1/tasks/[task_identifier]
 * Proxies the request to the backend API and returns the response
 */
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ task_identifier: string }> }
) {
  try {
    const { task_identifier } = await context.params

    return handleDataRequest(request, `/api/v1/tasks/${task_identifier}`, {
      method: 'DELETE',
      errorMessage: `Error deleting task ${task_identifier}`,
    })
  } catch (error) {
    console.error('Error proxying delete task request:', error)
    return NextResponse.json(
      { detail: 'An error occurred while deleting the task' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles OPTIONS requests to /api/v1/tasks/[task_identifier]
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
