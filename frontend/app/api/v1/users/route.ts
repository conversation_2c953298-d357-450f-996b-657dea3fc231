import { NextRequest } from 'next/server'
import { handleGetRequest, ErrorCode, createErrorResponse } from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/users
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/users')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleGetRequest(request, '/api/v1/users', {
      errorMessage: 'Error fetching users',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching users',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

// Add POST, PATCH, DELETE handlers for /api/v1/users and /api/v1/users/{user_id} as needed.
