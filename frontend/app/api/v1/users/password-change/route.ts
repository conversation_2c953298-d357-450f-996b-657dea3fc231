import { NextRequest, NextResponse } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'
import { api } from '@/lib/api'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles POST requests to /api/v1/users/password-change
 * This endpoint forwards to the dedicated password change endpoint in the backend
 */
export async function POST(request: NextRequest) {
  try {
    // Forward to the dedicated password change endpoint
    return handleDataRequest(request, `/api/v1/users/password-change`, {
      method: 'POST',
      errorMessage: 'Error changing password',
    })
  } catch (error) {
    console.error('Error in password change endpoint:', error)
    return NextResponse.json(
      { detail: 'Internal server error' },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}
