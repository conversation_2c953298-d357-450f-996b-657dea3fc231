import { NextRequest } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'

/**
 * Handles GET requests to /api/v1/users/{id}
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  return handleGetRequest(request, `/api/v1/users/${id}`, {
    errorMessage: 'Error fetching user',
  })
}
