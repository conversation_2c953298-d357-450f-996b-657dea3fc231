import { NextRequest } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * Handles GET requests to /api/v1/users/me
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  return handleGetRequest(request, '/api/v1/users/me', {
    errorMessage: 'Error fetching current user',
  })
}

/**
 * Handles PATCH requests to /api/v1/users/me
 * Proxies the request to the backend API to update the current user's profile
 */
export async function PATCH(request: NextRequest) {
  return handleDataRequest(request, '/api/v1/users/me', {
    method: 'PATCH',
    errorMessage: 'Error updating user profile',
  })
}
