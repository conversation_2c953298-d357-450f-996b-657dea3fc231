import { NextRequest } from 'next/server'
import { handleDataRequest } from '@/lib/api-proxy-utils'

/**
 * <PERSON>les POST requests to /api/v1/users/me/password
 * Proxies the request to the backend API to change the user's password
 * This is a custom endpoint that works around the limitations in the /api/v1/users/me PATCH endpoint
 */
export async function POST(request: NextRequest) {
  // Extract password data from request
  const { current_password, new_password } = await request.json()

  // Prepare the update data - we include the password in a patch to /users/me and use an admin route
  // This is a workaround since the standard /api/v1/users/me endpoint filters out password fields
  return handleDataRequest(request, '/api/v1/users/password-change', {
    method: 'POST',
    errorMessage: 'Error changing password',
  })
}
