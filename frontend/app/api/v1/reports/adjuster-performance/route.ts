import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/reports/adjuster-performance
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  try {
    return handleGetRequest(request, '/api/v1/reports/adjuster-performance', {
      errorMessage: 'Error fetching adjuster performance report',
    })
  } catch (error) {
    console.error('❌ Error in adjuster performance report proxy:', error)

    // Propagate the error to the frontend
    return NextResponse.json(
      {
        detail: `Error fetching adjuster performance report: ${error instanceof Error ? error.message : String(error)}`,
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        error_code: 'API_ROUTE_ERROR',
      },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles OPTIONS requests to /api/v1/reports/adjuster-performance
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new Response(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
