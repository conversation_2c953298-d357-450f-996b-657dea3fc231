import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequestRaw, createErrorResponse, ErrorCode } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/reports/claims-over-time
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  try {
    const { data, status } = await handleGetRequestRaw(
      request,
      '/api/v1/reports/claims-over-time',
      {
        errorMessage: 'Error fetching claims over time report',
      }
    )

    return NextResponse.json(data, { status })
  } catch (error) {
    return createErrorResponse(
      'Error fetching claims over time report',
      500,
      ErrorCode.API_ROUTE_ERROR,
      process.env.NODE_ENV === 'development'
        ? { details: error instanceof Error ? error.message : String(error) }
        : undefined
    )
  }
}

/**
 * Handles OPTIONS requests to /api/v1/reports/claims-over-time
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new Response(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
