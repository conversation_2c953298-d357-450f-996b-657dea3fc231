import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequestRaw, createErrorResponse, ErrorCode } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * GET /api/v1/reports/claims-kpis
 * Get claims KPI metrics with optional filtering parameters
 */
export async function GET(request: NextRequest) {
  try {
    const { data, status } = await handleGetRequestRaw(request, '/api/v1/reports/claims-kpis', {
      errorMessage: 'Error fetching claims KPI report',
    })

    return NextResponse.json(data, { status })
  } catch (error) {
    return createErrorResponse(
      'Error fetching claims KPI report',
      500,
      ErrorCode.API_ROUTE_ERROR,
      process.env.NODE_ENV === 'development'
        ? { details: error instanceof Error ? error.message : String(error) }
        : undefined
    )
  }
}

/**
 * OPTIONS /api/v1/reports/claims-kpis
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
