import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest } from '@/lib/api-proxy-utils'
import { ClaimType, ClaimStatus } from '@/lib/api/types'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/reports/[report_name]
 * Proxies the request to the backend API and returns the response
 */
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ report_name: string }> }
) {
  try {
    const { report_name } = await context.params

    // Validate report name to prevent potential path traversal attacks
    if (!report_name || report_name.includes('/') || report_name.includes('\\')) {
      return NextResponse.json(
        { detail: 'Invalid report name' },
        { status: HTTP_STATUS.BAD_REQUEST }
      )
    }

    // Extract URL parameters correctly - OpenAPI spec shows the expected parameters
    const url = new URL(request.url)
    const searchParams = url.searchParams

    // Log all parameters to help debug
    const period = searchParams.get('period')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')
    const comparePeriod = searchParams.get('compare_period')
    const customerId = searchParams.get('customer_id')
    const claimType = searchParams.get('claim_type')
    const userId = searchParams.get('user_id')

    console.log(`📊 Fetching ${report_name} report from backend API with parameters:`, {
      period,
      startDate,
      endDate,
      comparePeriod,
      customerId,
      claimType,
      userId,
    })

    return await handleGetRequest(request, `/api/v1/reports/${report_name}`, {
      errorMessage: `Error fetching ${report_name} report`,
      requireAuth: true, // Make sure auth is required according to the OpenAPI spec
    })
  } catch (error) {
    console.error('❌ Error in dynamic report proxy:', error)

    // Log additional details to help debug the issue
    const { report_name } = await context.params
    console.error('Report name:', report_name)
    console.error('Headers:', Object.fromEntries(request.headers.entries()))
    console.error('URL:', request.url)
    console.error('Error details:', error instanceof Error ? error.stack : String(error))

    // Propagate the error to the frontend
    return NextResponse.json(
      {
        detail: `Error fetching ${report_name} report: ${error instanceof Error ? error.message : String(error)}`,
        status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
        error_code: 'API_ROUTE_ERROR',
      },
      { status: HTTP_STATUS.INTERNAL_SERVER_ERROR }
    )
  }
}

/**
 * Handles OPTIONS requests to /api/v1/reports/[report_name]
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS(
  request: NextRequest,
  context: { params: Promise<{ report_name: string }> }
) {
  const { report_name } = await context.params

  return new Response(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
