/**
 * Template API Route
 *
 * This file serves as a template for implementing standardized API routes.
 * Copy this file and adjust as needed for new API routes.
 *
 * Features:
 * - Standard error handling
 * - Request/response logging with tracing
 * - Content type validation
 * - Consistent response format
 */
import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ContentType,
  ErrorCode,
  createErrorResponse,
} from '@/lib/api-proxy-utils'
import { logRequest, logResponse, logError, createTimer, LogLevel, log } from '@/lib/api-logging'

/**
 * Handles GET requests
 *
 * @example
 * // Basic usage
 * export async function GET(request: NextRequest) {
 *   return handleGetRequest(request, '/api/v1/resource', {
 *     errorMessage: 'Error fetching resource',
 *   })
 * }
 */
export async function GET(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/template')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Log additional debug information if needed
    log(LogLevel.DEBUG, 'Processing GET request', {
      requestId,
      headers: Object.fromEntries(request.headers.entries()),
      searchParams: Object.fromEntries(request.nextUrl.searchParams.entries()),
    })

    // Example of using the handleGetRequest utility
    return handleGetRequest(request, '/api/v1/resource', {
      errorMessage: 'Error fetching resource',
      requireAuth: true, // Set to false for public endpoints
    })
  } catch (error) {
    // In case of unexpected errors, log and return standardized error response
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred processing your request',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles POST requests
 *
 * @example
 * // Basic usage
 * export async function POST(request: NextRequest) {
 *   return handleDataRequest(request, '/api/v1/resource', {
 *     method: 'POST',
 *     errorMessage: 'Error creating resource',
 *   })
 * }
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/template')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    // Example of using the handleDataRequest utility
    return handleDataRequest(request, '/api/v1/resource', {
      method: 'POST',
      errorMessage: 'Error creating resource',
      requireAuth: true,
      allowedContentTypes: [ContentType.JSON], // Add more if needed
    })
  } catch (error) {
    // Log error and return standardized response
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred processing your request',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PUT requests
 */
export async function PUT(request: NextRequest) {
  // Similar pattern to POST, but with PUT method
  const requestId = logRequest(request, '/api/v1/template')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/resource', {
      method: 'PUT',
      errorMessage: 'Error updating resource',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred processing your request',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles PATCH requests
 */
export async function PATCH(request: NextRequest) {
  // Similar pattern to POST, but with PATCH method
  const requestId = logRequest(request, '/api/v1/template')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/resource', {
      method: 'PATCH',
      errorMessage: 'Error updating resource',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred processing your request',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles DELETE requests
 */
export async function DELETE(request: NextRequest) {
  // Similar pattern to POST, but with DELETE method
  const requestId = logRequest(request, '/api/v1/template')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/resource', {
      method: 'DELETE',
      errorMessage: 'Error deleting resource',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred processing your request',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
