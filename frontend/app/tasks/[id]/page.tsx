'use client'

import { useState, useEffect } from 'react'
import React, { use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Calendar, MessageSquare, User, Loader2, AlertCircle } from 'lucide-react'
import Link from 'next/link'
import { Separator } from '@/components/ui/separator'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { UserAutocomplete } from '@/components/ui/user-autocomplete'
import { api } from '@/lib/api' // Import the shared API singleton
import { Task, TaskStatus, TaskPriority } from '@/lib/api/types'
import { useApi } from '@/hooks/useApi'
import { toast } from '@/components/ui/use-toast'
import { useDateFormatter } from '@/hooks/useDateFormatter'

const UNASSIGNED_SELECT_VALUE = '__UNASSIGNED__'

interface TaskPageProps {
  params: Promise<{ id: string }>
}

export default function TaskPage({ params }: TaskPageProps) {
  const taskId = use(params).id
  const { formatDate } = useDateFormatter()

  const [newComment, setNewComment] = useState('')
  const [isSubmittingComment, setIsSubmittingComment] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [isUpdatingAssignment, setIsUpdatingAssignment] = useState(false)
  const [newStatus, setNewStatus] = useState<string | null>(null)
  const [selectedAssigneeId, setSelectedAssigneeId] = useState<string | null>(null)

  // Fetch task data
  const getTask = async () => {
    try {
      console.log(`🔍 Fetching task details for ID: ${taskId}`)
      const response = await api.tasks.getTaskById(taskId)
      console.log(`✅ Task response received:`, response)

      // Add more detailed logging
      if (response.hr_id) {
        console.log(`Task has human-readable ID: ${response.hr_id}, UUID: ${response.id}`)
      }

      // Check if we have a valid task object
      if (response && typeof response === 'object') {
        if ('id' in response) {
          return response
        } else if (Object.keys(response).length === 0) {
          throw new Error('Task not found - empty response')
        }
      }

      console.error('❌ Unexpected response format:', response)
      throw new Error('Unexpected response format from server')
    } catch (error) {
      console.error(`❌ Error fetching task ${taskId}:`, error)
      throw error instanceof Error ? error : new Error(String(error))
    }
  }

  const { data: task, isLoading, error, refetch } = useApi<Task>(getTask, [taskId])

  useEffect(() => {
    if (task) {
      setNewStatus(task.status)
      // Set the current assignee ID when task loads
      setSelectedAssigneeId(task.assignee ? task.assignee.id : null)
    }
  }, [task])

  // Display the task ID in a more user-friendly way
  const getDisplayId = (task: Task) => {
    if (task.hr_id) {
      return task.hr_id
    }
    // If only UUID is available, format it to be more readable
    if (task.id) {
      const shortId = task.id.split('-')[0]
      return `Task ${shortId}`
    }
    return 'Unknown Task'
  }

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!task || !newStatus || newStatus === task.status) return

    setIsUpdatingStatus(true)

    try {
      // Use hr_id for the API call if available
      await api.tasks.updateTaskStatus(task, newStatus)
      toast({
        title: 'Status updated',
        description: `Task status has been updated to ${newStatus.replace('_', ' ')}`,
      })
      refetch()
    } catch (error) {
      console.error('Error updating task status:', error)
      toast({
        title: 'Error',
        description: 'Failed to update task status. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Handle assignment update
  const handleAssignmentUpdate = async () => {
    if (!task) return

    setIsUpdatingAssignment(true)

    try {
      console.log('🎯 TaskPage: Updating assignment with selectedAssigneeId:', selectedAssigneeId)

      // Use the dedicated assign endpoint
      await api.tasks.assignTask(task, selectedAssigneeId)

      toast({
        title: 'Task assigned',
        description: selectedAssigneeId ? `Task has been assigned` : 'Task has been unassigned',
      })
      refetch()
    } catch (error) {
      console.error('❌ TaskPage: Error updating task assignment:', error)
      toast({
        title: 'Error',
        description: 'Failed to update task assignment. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingAssignment(false)
    }
  }

  // Handle mark as complete
  const handleMarkComplete = async () => {
    if (!task) return

    setIsUpdatingStatus(true)

    try {
      // Use hr_id for the API call if available
      await api.tasks.updateTaskStatus(task, TaskStatus.COMPLETED)
      toast({
        title: 'Task completed',
        description: 'Task has been marked as completed',
      })
      refetch()
    } catch (error) {
      console.error('Error marking task as complete:', error)
      toast({
        title: 'Error',
        description: 'Failed to mark task as complete. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case TaskPriority.URGENT:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case TaskPriority.HIGH:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case TaskPriority.MEDIUM:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskPriority.LOW:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      case TaskStatus.IN_PROGRESS:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case TaskStatus.BLOCKED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading task details...</p>
      </div>
    )
  }

  // Error state
  if (error || !task) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-destructive mb-4" />
        <p className="text-center mb-4">Failed to load task details: {error || 'Task not found'}</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Link href="/tasks">
          <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">Task Details</h1>
        <Badge variant="outline" className="ml-2">
          {getDisplayId(task)}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-xl">{task.title}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant="outline" className={getPriorityColor(task.priority)}>
                    {task.priority}
                  </Badge>
                  <Badge variant="outline" className={getStatusColor(task.status)}>
                    {task.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => refetch()}>
                  Refresh
                </Button>
                {task.status !== TaskStatus.COMPLETED && (
                  <Button onClick={handleMarkComplete} disabled={isUpdatingStatus}>
                    {isUpdatingStatus ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Updating...
                      </>
                    ) : (
                      'Mark Complete'
                    )}
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-sm text-muted-foreground">
                    {task.description || 'No description provided'}
                  </p>
                </div>

                {task.claim_id && (
                  <div>
                    <h3 className="font-medium mb-2">Related Claim</h3>
                    <div className="flex items-center gap-2">
                      <Link
                        href={`/claims/${task.claim_id}`}
                        className="text-sm text-primary hover:underline"
                      >
                        {task.claim_number || task.claim_id}
                      </Link>
                    </div>
                  </div>
                )}

                <Separator />

                <div>
                  <h3 className="font-medium mb-4 flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    Status
                  </h3>
                  <div className="grid grid-cols-4 gap-4">
                    <div className="col-span-3">
                      <Select value={newStatus || undefined} onValueChange={setNewStatus}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={TaskStatus.PENDING}>Pending</SelectItem>
                          <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                          <SelectItem value={TaskStatus.BLOCKED}>Blocked</SelectItem>
                          <SelectItem value={TaskStatus.COMPLETED}>Completed</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <Button
                      onClick={handleStatusUpdate}
                      disabled={isUpdatingStatus || newStatus === task.status}
                    >
                      {isUpdatingStatus ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Update'}
                    </Button>
                  </div>
                </div>

                {/* Placeholder for comments - can be implemented later */}
                <div>
                  <h3 className="font-medium mb-4 flex items-center gap-2">
                    <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    Comments
                  </h3>
                  <div className="p-4 bg-muted rounded text-center text-sm text-muted-foreground">
                    Comments are not yet available in this version.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
            <div className="font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              Dates
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="text-sm text-muted-foreground">Created</div>
              <div>{formatDate(task.created_at)}</div>
              <div className="text-sm text-muted-foreground">Updated</div>
              <div>{formatDate(task.updated_at)}</div>
              <div className="text-sm text-muted-foreground">Due Date</div>
              <div>{formatDate(task.due_date)}</div>
            </div>
          </div>

          <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
            <div className="font-medium flex items-center gap-2">
              <User className="h-4 w-4 text-muted-foreground" />
              Assignment
            </div>
            <div className="space-y-3">
              <div className="grid grid-cols-4 gap-4">
                <div className="col-span-3">
                  <UserAutocomplete
                    value={selectedAssigneeId}
                    onSelect={setSelectedAssigneeId}
                    placeholder="Search assignees..."
                    role="ADJUSTER"
                    allowUnassigned={true}
                    className="w-full"
                    selectedUserInfo={
                      task.assignee
                        ? {
                            id: task.assignee.id,
                            email: task.assignee.email,
                          }
                        : null
                    }
                  />
                </div>
                <Button
                  onClick={handleAssignmentUpdate}
                  disabled={
                    isUpdatingAssignment || selectedAssigneeId === (task.assignee?.id || null)
                  }
                >
                  {isUpdatingAssignment ? <Loader2 className="h-4 w-4 animate-spin" /> : 'Assign'}
                </Button>
              </div>
              <div className="text-sm text-muted-foreground">
                Current assignee: {task.assignee ? task.assignee.email : 'Unassigned'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
