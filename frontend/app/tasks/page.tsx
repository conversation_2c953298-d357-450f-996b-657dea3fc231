'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { TasksTable } from '@/components/tasks-table'
import { useState, useEffect, useCallback, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { TaskStatus, TaskPriority } from '@/lib/api/types'
import { useDebounce } from '@/hooks/useDebounce'

function TasksContent() {
  const router = useRouter()
  const searchParams = useSearchParams()

  // Get initial values from URL search params
  const initialTitle = searchParams.get('title') || ''
  const initialStatus = searchParams.get('status') || 'all'
  const initialPriority = searchParams.get('priority') || 'all'
  const initialAssignee = searchParams.get('assignee') || 'all'

  // State for search and filters
  const [title, setTitle] = useState(initialTitle)
  const [status, setStatus] = useState(initialStatus)
  const [priority, setPriority] = useState(initialPriority)
  const [assignee, setAssignee] = useState(initialAssignee)

  // Debounce title search to avoid excessive API calls
  const debouncedTitle = useDebounce(title, 500)

  // Function to update URL with current filters
  const updateUrlWithFilters = useCallback(() => {
    const params = new URLSearchParams()

    if (debouncedTitle) params.set('title', debouncedTitle)
    if (status !== 'all') params.set('status', status)
    if (priority !== 'all') params.set('priority', priority)
    if (assignee !== 'all') params.set('assignee', assignee)

    const newUrl = params.toString() ? `?${params.toString()}` : ''
    router.push(`/tasks${newUrl}`, { scroll: false })
  }, [debouncedTitle, status, priority, assignee, router])

  // Update URL when filters change
  useEffect(() => {
    updateUrlWithFilters()
  }, [debouncedTitle, status, priority, assignee, updateUrlWithFilters])

  // Handle search button click
  const handleSearch = () => {
    updateUrlWithFilters()
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Tasks</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search and Filter</CardTitle>
          <CardDescription>Find tasks using various criteria</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label htmlFor="task-title" className="text-sm font-medium">
                Task Title
              </label>
              <Input
                id="task-title"
                placeholder="Search by title"
                value={title}
                onChange={e => setTitle(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="task-status" className="text-sm font-medium">
                Status
              </label>
              <Select value={status} onValueChange={setStatus}>
                <SelectTrigger id="task-status">
                  <SelectValue placeholder="Any Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Statuses</SelectItem>
                  <SelectItem value={TaskStatus.PENDING}>Pending</SelectItem>
                  <SelectItem value={TaskStatus.IN_PROGRESS}>In Progress</SelectItem>
                  <SelectItem value={TaskStatus.COMPLETED}>Completed</SelectItem>
                  <SelectItem value={TaskStatus.BLOCKED}>Blocked</SelectItem>
                  <SelectItem value={TaskStatus.CANCELLED}>Cancelled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="task-priority" className="text-sm font-medium">
                Priority
              </label>
              <Select value={priority} onValueChange={setPriority}>
                <SelectTrigger id="task-priority">
                  <SelectValue placeholder="Any Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Priority</SelectItem>
                  <SelectItem value={TaskPriority.URGENT}>Urgent</SelectItem>
                  <SelectItem value={TaskPriority.HIGH}>High</SelectItem>
                  <SelectItem value={TaskPriority.MEDIUM}>Medium</SelectItem>
                  <SelectItem value={TaskPriority.LOW}>Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="assignee" className="text-sm font-medium">
                Assignee
              </label>
              <Select value={assignee} onValueChange={setAssignee}>
                <SelectTrigger id="assignee">
                  <SelectValue placeholder="Any Assignee" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Assignee</SelectItem>
                  <SelectItem value="me">Assigned to Me</SelectItem>
                  <SelectItem value="unassigned">Unassigned</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button onClick={handleSearch}>Search Tasks</Button>
          </div>
        </CardContent>
      </Card>

      <TasksTable
        searchQuery={debouncedTitle}
        statusFilter={status}
        priorityFilter={priority}
        assigneeFilter={assignee}
      />
    </div>
  )
}

export default function TasksPage() {
  return (
    <Suspense fallback={<div>Loading tasks...</div>}>
      <TasksContent />
    </Suspense>
  )
}
