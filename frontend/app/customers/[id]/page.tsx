'use client'

import { useState, useEffect } from 'react'
import React, { use } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  Building,
  Calendar,
  FileText,
  Settings,
  Loader2,
  AlertCircle,
} from 'lucide-react'
import Link from 'next/link'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CustomerClaims } from '@/components/customer-claims'
import { CustomerFnols } from '@/components/customer-fnols'
import { CustomerUsers } from '@/components/customer-users'
import { CustomerSettings } from '@/components/customer-settings'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { CustomerResponse } from '@/lib/api/types'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { Label } from '@/components/ui/label'

export default function CustomerDetailPage({
  params,
}: {
  params: Promise<{ id: string }> | { id: string }
}) {
  const customerId = 'then' in params ? use(params as Promise<{ id: string }>).id : params.id
  const { formatDate } = useDateFormatter()

  const fetchCustomer = async () => {
    try {
      console.log(`Fetching customer details for ID: ${customerId}`)
      const response = await api.customers.getCustomerById(customerId)
      console.log('Customer response received:', response)
      return response
    } catch (error) {
      console.error(`Error fetching customer ${customerId}:`, error)
      throw error instanceof Error ? error : new Error(String(error))
    }
  }

  const {
    data: customer,
    isLoading,
    error,
    refetch,
  } = useApi<CustomerResponse>(fetchCustomer, [customerId])

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading customer details...</p>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertCircle className="h-8 w-8 text-destructive mb-4" />
        <p className="text-center mb-4">
          Failed to load customer details: {String(error) || 'Customer not found'}
        </p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Link href="/customers">
          <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">Customer Details</h1>
        <Badge variant="outline" className="ml-2">
          {customer.prefix}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-4">
                <Building className="h-10 w-10 text-primary" />
                <div>
                  <CardTitle className="text-xl">{customer.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getStatusColor(customer.active)}>
                      {customer.active ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </div>
              <Button>Edit Customer</Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {customer.description && (
                  <div>
                    <h3 className="font-medium mb-2">Description</h3>
                    <p className="text-sm text-muted-foreground">{customer.description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
            <div className="font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              Key Information
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Created</Label>
                <div>{formatDate(customer.created_at)}</div>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Last Updated</Label>
                <div>{formatDate(customer.updated_at)}</div>
              </div>
              <div className="text-muted-foreground">Prefix</div>
              <div>{customer.prefix}</div>
            </div>
          </div>
        </div>
      </div>

      <Tabs defaultValue="claims" className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-4 md:flex">
          <TabsTrigger value="claims">Claims</TabsTrigger>
          <TabsTrigger value="fnols">FNOLs</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="claims" className="mt-6">
          <CustomerClaims customerId={customer.id} />
        </TabsContent>

        <TabsContent value="fnols" className="mt-6">
          <CustomerFnols customerId={customer.id} />
        </TabsContent>

        <TabsContent value="users" className="mt-6">
          <CustomerUsers customerId={customer.id} />
        </TabsContent>

        <TabsContent value="settings" className="mt-6">
          <CustomerSettings customerId={customer.id} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
