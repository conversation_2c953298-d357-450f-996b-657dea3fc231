'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PlusIcon } from 'lucide-react'
import { CustomersTable } from '@/components/customers-table'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import NewCustomerModal from '@/components/new-customer-modal'

export default function CustomersPage() {
  const router = useRouter()
  const [isCustomerModalOpen, setIsCustomerModalOpen] = useState(false)

  const handleNewCustomerClick = () => {
    setIsCustomerModalOpen(true)
  }

  const handleCustomerCreated = () => {
    setIsCustomerModalOpen(false)
    // Refresh the page to show the new customer
    router.refresh()
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
        <Button className="flex items-center gap-1" onClick={handleNewCustomerClick}>
          <PlusIcon className="h-4 w-4" />
          New Customer
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search and Filter</CardTitle>
          <CardDescription>
            Find insurance carriers and self-insured organizations
            <span className="ml-2 text-sm italic text-muted-foreground">(Coming soon)</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label htmlFor="customer-name" className="text-sm font-medium">
                Name
              </label>
              <Input id="customer-name" placeholder="Search by name" disabled />
            </div>
            <div className="space-y-2">
              <label htmlFor="customer-prefix" className="text-sm font-medium">
                Prefix
              </label>
              <Input id="customer-prefix" placeholder="Search by prefix" disabled />
            </div>
            <div className="space-y-2">
              <label htmlFor="customer-type" className="text-sm font-medium">
                Type
              </label>
              <Select disabled>
                <SelectTrigger id="customer-type">
                  <SelectValue placeholder="Any Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Type</SelectItem>
                  <SelectItem value="insurance">Insurance Carrier</SelectItem>
                  <SelectItem value="self-insured">Self-Insured</SelectItem>
                  <SelectItem value="tpa">TPA Client</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="customer-status" className="text-sm font-medium">
                Status
              </label>
              <Select disabled>
                <SelectTrigger id="customer-status">
                  <SelectValue placeholder="Any Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button disabled>Search Customers</Button>
          </div>
        </CardContent>
      </Card>

      <CustomersTable />

      {/* Customer creation modal */}
      {isCustomerModalOpen && (
        <NewCustomerModal
          isOpen={isCustomerModalOpen}
          onClose={() => setIsCustomerModalOpen(false)}
          onCustomerCreated={handleCustomerCreated}
        />
      )}
    </div>
  )
}
