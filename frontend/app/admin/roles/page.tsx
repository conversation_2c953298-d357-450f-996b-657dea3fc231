import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Edit, MoreHorizontal, Plus, Search, Trash2, Users } from 'lucide-react'

// Sample data for roles
const roles = [
  {
    id: 1,
    name: 'Administrator',
    description: 'Full system access with all permissions',
    userCount: 5,
    keyPermissions: ['Manage Users', 'Manage Roles', 'System Configuration', 'View Audit Logs'],
  },
  {
    id: 2,
    name: 'Claims Manager',
    description: 'Manage and oversee all claims processing',
    userCount: 12,
    keyPermissions: ['Approve Claims', 'Assign Tasks', 'View Reports', 'Manage Reserves'],
  },
  {
    id: 3,
    name: '<PERSON>laims Adjuster',
    description: 'Process and investigate insurance claims',
    userCount: 28,
    keyPermissions: ['Create Claims', 'Update Claims', 'Upload Documents', 'Create Tasks'],
  },
  {
    id: 4,
    name: 'Customer Service',
    description: 'Handle customer inquiries and basic claim information',
    userCount: 15,
    keyPermissions: ['View Claims', 'Update Customer Info', 'Send Messages', 'Create FNOL'],
  },
  {
    id: 5,
    name: 'Finance',
    description: 'Manage financial aspects of claims',
    userCount: 8,
    keyPermissions: ['Process Payments', 'Manage Reserves', 'Financial Reporting', 'Audit Claims'],
  },
  {
    id: 6,
    name: 'Read Only',
    description: 'View-only access to system data',
    userCount: 20,
    keyPermissions: ['View Claims', 'View Customers', 'View Reports', 'View Documents'],
  },
]

// Sample data for permissions
const permissions = [
  {
    category: 'Claims',
    items: [
      { id: 'create_claim', name: 'Create Claims', roleCount: 3 },
      { id: 'view_claim', name: 'View Claims', roleCount: 6 },
      { id: 'update_claim', name: 'Update Claims', roleCount: 3 },
      { id: 'delete_claim', name: 'Delete Claims', roleCount: 1 },
      { id: 'approve_claim', name: 'Approve Claims', roleCount: 2 },
      { id: 'assign_claim', name: 'Assign Claims', roleCount: 2 },
    ],
  },
  {
    category: 'Documents',
    items: [
      { id: 'upload_document', name: 'Upload Documents', roleCount: 4 },
      { id: 'view_document', name: 'View Documents', roleCount: 6 },
      { id: 'delete_document', name: 'Delete Documents', roleCount: 2 },
    ],
  },
  {
    category: 'Users',
    items: [
      { id: 'create_user', name: 'Create Users', roleCount: 1 },
      { id: 'view_user', name: 'View Users', roleCount: 2 },
      { id: 'update_user', name: 'Update Users', roleCount: 1 },
      { id: 'delete_user', name: 'Delete Users', roleCount: 1 },
    ],
  },
  {
    category: 'Financials',
    items: [
      { id: 'process_payment', name: 'Process Payments', roleCount: 2 },
      { id: 'manage_reserves', name: 'Manage Reserves', roleCount: 2 },
      { id: 'view_financials', name: 'View Financials', roleCount: 3 },
    ],
  },
  {
    category: 'System',
    items: [
      { id: 'system_config', name: 'System Configuration', roleCount: 1 },
      { id: 'view_audit', name: 'View Audit Logs', roleCount: 2 },
      { id: 'manage_workflows', name: 'Manage Workflows', roleCount: 1 },
    ],
  },
]

export default function RolesPage() {
  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Roles & Permissions</h1>
          <p className="text-muted-foreground">
            Manage user roles and their associated permissions
          </p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Role
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Create New Role</DialogTitle>
              <DialogDescription>
                Define a new role and assign permissions. Click save when you're done.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Role Name</Label>
                <Input id="name" placeholder="e.g. Claims Supervisor" />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  placeholder="Brief description of this role's responsibilities"
                />
              </div>
              <div className="grid gap-2">
                <Label>Permissions</Label>
                <div className="border rounded-md p-4 max-h-[300px] overflow-y-auto">
                  {permissions.map(category => (
                    <div key={category.category} className="mb-4">
                      <h4 className="font-medium mb-2">{category.category}</h4>
                      <div className="grid grid-cols-2 gap-2">
                        {category.items.map(permission => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox id={permission.id} />
                            <Label htmlFor={permission.id} className="text-sm font-normal">
                              {permission.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline">Cancel</Button>
              <Button>Save Role</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input placeholder="Search roles..." className="max-w-sm" />
      </div>

      <Tabs defaultValue="roles">
        <TabsList>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
        </TabsList>
        <TabsContent value="roles" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roles.map(role => (
              <Card key={role.id}>
                <CardHeader className="pb-3">
                  <div className="flex justify-between items-start">
                    <CardTitle>{role.name}</CardTitle>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">More</span>
                      </Button>
                    </div>
                  </div>
                  <CardDescription>{role.description}</CardDescription>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="flex items-center text-sm text-muted-foreground mb-3">
                    <Users className="mr-1 h-4 w-4" />
                    {role.userCount} users with this role
                  </div>
                  <div className="space-y-1">
                    <h4 className="text-sm font-medium">Key permissions:</h4>
                    <ul className="text-sm text-muted-foreground">
                      {role.keyPermissions.map((permission, i) => (
                        <li key={i} className="flex items-center">
                          <span className="h-1 w-1 rounded-full bg-primary mr-2"></span>
                          {permission}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full">
                    View Details
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </TabsContent>
        <TabsContent value="permissions" className="space-y-4">
          {permissions.map(category => (
            <Card key={category.category}>
              <CardHeader>
                <CardTitle>{category.category}</CardTitle>
                <CardDescription>
                  Permissions related to {category.category.toLowerCase()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {category.items.map(permission => (
                    <div
                      key={permission.id}
                      className="flex justify-between items-center p-3 border rounded-md"
                    >
                      <div>
                        <div className="font-medium">{permission.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {permission.roleCount} {permission.roleCount === 1 ? 'role' : 'roles'}
                        </div>
                      </div>
                      <Button variant="ghost" size="icon">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  )
}
