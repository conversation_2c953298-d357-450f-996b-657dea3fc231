import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
  AlertCircle,
  Users,
  Shield,
  Settings,
  Database,
  Building,
  Plug,
  FileText,
  Server,
} from 'lucide-react'

export default function AdminDashboardPage() {
  return (
    <div className="flex flex-col gap-8 py-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
        <Button>System Status</Button>
      </div>

      <Alert variant="warning">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>System Maintenance</AlertTitle>
        <AlertDescription>
          Scheduled maintenance will occur on Sunday, April 21st from 2:00 AM to 4:00 AM EST.
        </AlertDescription>
      </Alert>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">245</div>
            <p className="text-xs text-muted-foreground">+12 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8</div>
            <p className="text-xs text-muted-foreground">+1 from last month</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Workflow Templates</CardTitle>
            <Settings className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">No change</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Integrations</CardTitle>
            <Plug className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6</div>
            <p className="text-xs text-muted-foreground">+2 from last month</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="recent-activity">
        <TabsList>
          <TabsTrigger value="recent-activity">Recent Activity</TabsTrigger>
          <TabsTrigger value="system-health">System Health</TabsTrigger>
          <TabsTrigger value="quick-actions">Quick Actions</TabsTrigger>
        </TabsList>
        <TabsContent value="recent-activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Admin Activity</CardTitle>
              <CardDescription>
                Recent administrative actions performed in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">User role updated</p>
                    <p className="text-xs text-muted-foreground">
                      Sarah Johnson updated role for Mark Wilson to Senior Adjuster
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">2 hours ago</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New workflow created</p>
                    <p className="text-xs text-muted-foreground">
                      Admin created new workflow template for Auto Claims
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">Yesterday</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">System setting changed</p>
                    <p className="text-xs text-muted-foreground">
                      Admin updated document retention policy to 7 years
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">2 days ago</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New integration added</p>
                    <p className="text-xs text-muted-foreground">
                      Admin configured integration with DocuSign
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">3 days ago</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">User deactivated</p>
                    <p className="text-xs text-muted-foreground">
                      Admin deactivated user account for John Smith
                    </p>
                  </div>
                  <div className="text-xs text-muted-foreground">5 days ago</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="system-health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Health</CardTitle>
              <CardDescription>Current status of system components and services</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">API Services</p>
                    <p className="text-xs text-muted-foreground">All API endpoints operational</p>
                  </div>
                  <div className="text-xs text-green-500 font-medium">Healthy</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Database</p>
                    <p className="text-xs text-muted-foreground">
                      Database connections and queries performing normally
                    </p>
                  </div>
                  <div className="text-xs text-green-500 font-medium">Healthy</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Storage Services</p>
                    <p className="text-xs text-muted-foreground">
                      Document storage and retrieval functioning properly
                    </p>
                  </div>
                  <div className="text-xs text-green-500 font-medium">Healthy</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Email Services</p>
                    <p className="text-xs text-muted-foreground">
                      Email delivery experiencing minor delays
                    </p>
                  </div>
                  <div className="text-xs text-yellow-500 font-medium">Degraded</div>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Authentication Services</p>
                    <p className="text-xs text-muted-foreground">
                      User authentication and authorization working normally
                    </p>
                  </div>
                  <div className="text-xs text-green-500 font-medium">Healthy</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="quick-actions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Frequently used administrative actions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Button className="justify-start" variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  Add New User
                </Button>
                <Button className="justify-start" variant="outline">
                  <Shield className="mr-2 h-4 w-4" />
                  Create New Role
                </Button>
                <Button className="justify-start" variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Configure Workflow
                </Button>
                <Button className="justify-start" variant="outline">
                  <Database className="mr-2 h-4 w-4" />
                  Manage Reference Data
                </Button>
                <Button className="justify-start" variant="outline">
                  <Building className="mr-2 h-4 w-4" />
                  Update Company Info
                </Button>
                <Button className="justify-start" variant="outline">
                  <Plug className="mr-2 h-4 w-4" />
                  Configure Integration
                </Button>
                <Button className="justify-start" variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  View Audit Logs
                </Button>
                <Button className="justify-start" variant="outline">
                  <Server className="mr-2 h-4 w-4" />
                  System Backup
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
