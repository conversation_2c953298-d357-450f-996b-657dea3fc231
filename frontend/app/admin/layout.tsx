import type React from 'react'
import type { Metada<PERSON> } from 'next'
import Link from 'next/link'
import { ScrollArea } from '@/components/ui/scroll-area'

export const metadata: Metadata = {
  title: 'Admin - Claimentine',
  description: 'Administration and system configuration for Claimentine',
}

interface AdminLayoutProps {
  children: React.ReactNode
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  return (
    <div className="container relative">
      <div className="flex-1 items-start md:grid md:grid-cols-[220px_minmax(0,1fr)] md:gap-6 lg:grid-cols-[240px_minmax(0,1fr)] lg:gap-10">
        <aside className="fixed top-14 z-30 -ml-2 hidden h-[calc(100vh-3.5rem)] w-full shrink-0 md:sticky md:block">
          <ScrollArea className="h-full py-6 pr-6 lg:py-8">
            <h2 className="mb-4 px-4 text-lg font-semibold tracking-tight">Administration</h2>
            <nav className="grid gap-1 px-2">
              <Link href="/admin" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Dashboard</span>
                </a>
              </Link>
              <Link href="/admin/users" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>User Management</span>
                </a>
              </Link>
              <Link href="/admin/roles" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Roles & Permissions</span>
                </a>
              </Link>
              <Link href="/admin/workflows" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Workflow Settings</span>
                </a>
              </Link>
              <Link href="/admin/reference-data" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Reference Data</span>
                </a>
              </Link>
              <Link href="/admin/company" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Company Settings</span>
                </a>
              </Link>
              <Link href="/admin/integrations" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Integrations</span>
                </a>
              </Link>
              <Link href="/admin/audit-logs" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>Audit Logs</span>
                </a>
              </Link>
              <Link href="/admin/system" passHref legacyBehavior>
                <a className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary">
                  <span>System Settings</span>
                </a>
              </Link>
            </nav>
          </ScrollArea>
        </aside>
        <main className="flex w-full flex-col overflow-hidden">{children}</main>
      </div>
    </div>
  )
}
