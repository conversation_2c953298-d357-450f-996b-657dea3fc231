@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Update primary to use orange color */
    --primary: 30 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    /* Update accent to use a lighter orange */
    --accent: 30 100% 94%;
    --accent-foreground: 30 80% 30%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;

    /* Update ring to use orange */
    --ring: 30 100% 50%;

    --radius: 0.5rem;

    /* Add a new darker orange for links */
    --link: 25 100% 40%;
    --link-hover: 25 100% 30%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    /* Update primary to use orange color in dark mode */
    --primary: 30 100% 50%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    /* Update accent to use a darker orange in dark mode */
    --accent: 30 70% 25%;
    --accent-foreground: 30 100% 94%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;

    /* Update ring to use orange in dark mode */
    --ring: 30 100% 50%;

    /* Add a new brighter orange for links in dark mode for better visibility */
    --link: 25 100% 60%;
    --link-hover: 25 100% 70%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  /* Add styling for links using the new color variables */
  a {
    color: hsl(var(--link));
    text-decoration: none;
    transition: color 0.2s ease;
  }
  a:hover {
    color: hsl(var(--link-hover));
    text-decoration: underline;
  }

  /* Exception for navigation links */
  .side-nav a,
  nav a {
    color: inherit;
    text-decoration: none;
  }

  .side-nav a:hover,
  nav a:hover {
    text-decoration: none;
  }
}

/* Remove custom classes that use undefined tangerine colors */
