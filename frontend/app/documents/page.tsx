'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DocumentsTable } from '@/components/documents-table'
import { DocumentUpload } from '@/components/document-upload'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import { DocumentType } from '@/lib/api/types'
import { useState, useCallback } from 'react'

export default function DocumentsPage() {
  const [reloadTrigger, setReloadTrigger] = useState(0)
  const [currentTab, setCurrentTab] = useState<string>('all')

  const handleUploadSuccess = useCallback(() => {
    // Increment reload trigger to cause DocumentsTable to refresh
    setReloadTrigger(prev => prev + 1)
  }, [])

  const handleTabChange = useCallback((value: string) => {
    setCurrentTab(value)
  }, [])

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Documents</h1>
      </div>

      <div className="flex w-full items-center space-x-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input type="search" placeholder="Search documents..." className="pl-8" />
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={handleTabChange}>
        <TabsList className="grid w-full md:w-auto grid-cols-4 md:grid-cols-8 md:flex">
          <TabsTrigger value="all">All Documents</TabsTrigger>
          <TabsTrigger value="photos">Photos</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
          <TabsTrigger value="policies">Policies</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="statements">Statements</TabsTrigger>
          <TabsTrigger value="contracts">Contracts</TabsTrigger>
          <TabsTrigger value="correspondence">Correspondence</TabsTrigger>
          <TabsTrigger value="other">Other</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <DocumentsTable reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="photos" className="mt-6">
          <DocumentsTable documentType={DocumentType.PHOTO} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="reports" className="mt-6">
          <DocumentsTable documentType={DocumentType.REPORT} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="policies" className="mt-6">
          <DocumentsTable documentType={DocumentType.POLICY} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="invoices" className="mt-6">
          <DocumentsTable documentType={DocumentType.INVOICE} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="statements" className="mt-6">
          <DocumentsTable documentType={DocumentType.STATEMENT} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="contracts" className="mt-6">
          <DocumentsTable documentType={DocumentType.CONTRACT} reloadTrigger={reloadTrigger} />
        </TabsContent>

        <TabsContent value="correspondence" className="mt-6">
          <DocumentsTable
            documentType={DocumentType.CORRESPONDENCE}
            reloadTrigger={reloadTrigger}
          />
        </TabsContent>

        <TabsContent value="other" className="mt-6">
          <DocumentsTable documentType={DocumentType.OTHER} reloadTrigger={reloadTrigger} />
        </TabsContent>
      </Tabs>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Upload Documents</CardTitle>
          <CardDescription>Upload documents related to claims</CardDescription>
        </CardHeader>
        <CardContent>
          <DocumentUpload onUploadSuccess={handleUploadSuccess} />
        </CardContent>
      </Card>
    </div>
  )
}
