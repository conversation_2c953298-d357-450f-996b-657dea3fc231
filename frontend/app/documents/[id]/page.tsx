'use client'

import { use } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  Calendar,
  Download,
  Eye,
  FileText,
  ImageIcon,
  FileArchive,
  File,
  User,
} from 'lucide-react'
import Link from 'next/link'
import { formatDistanceToNow } from 'date-fns'
import { api } from '../../../lib/api'
import { Document, DocumentType } from '@/lib/api/types'
import { useApi } from '@/hooks/useApi'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'

export default function DocumentDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params)
  const documentId = resolvedParams.id

  // Replace manual fetch with useApi hook
  const {
    data: document,
    isLoading,
    error,
    refetch,
  } = useApi<Document>(() => api.documents.getDocumentByIdOnly(documentId), [documentId])

  const handleDownload = async () => {
    if (!document) return

    try {
      const downloadResponse = await api.documents.getDocumentDownloadUrl(
        document.claim_id,
        document.id
      )

      // Always use the anchor element approach for downloads
      // This preserves the signed URL integrity
      const link = globalThis.document.createElement('a')
      link.href = downloadResponse.download_url
      link.setAttribute('download', document.name) // Set download attribute
      link.setAttribute('target', '_blank')
      globalThis.document.body.appendChild(link)
      link.click()
      globalThis.document.body.removeChild(link)
    } catch (err) {
      console.error('Error downloading document:', err)
      alert('Failed to download document. Please try again.')
    }
  }

  const handleView = async () => {
    if (!document) return

    try {
      const downloadResponse = await api.documents.getDocumentDownloadUrl(
        document.claim_id,
        document.id
      )

      // Open the document in a new tab/window
      window.open(downloadResponse.download_url, '_blank')
    } catch (err) {
      console.error('Error viewing document:', err)
      alert('Failed to view document. Please try again.')
    }
  }

  const getDocumentIcon = (type: DocumentType, mimeType: string) => {
    if (type === DocumentType.PHOTO || mimeType.startsWith('image/'))
      return <ImageIcon className="h-10 w-10 text-blue-600" />
    if (mimeType === 'application/pdf') return <FileText className="h-10 w-10 text-red-600" />
    if (mimeType === 'application/zip') return <FileArchive className="h-10 w-10 text-yellow-600" />
    return <File className="h-10 w-10 text-slate-600" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + ' B'
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB'
    else return (bytes / 1048576).toFixed(1) + ' MB'
  }

  const getDocumentTypeColor = (type: DocumentType) => {
    switch (type) {
      case DocumentType.PHOTO:
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case DocumentType.REPORT:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case DocumentType.POLICY:
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case DocumentType.INVOICE:
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case DocumentType.STATEMENT:
        return 'bg-rose-100 text-rose-800 dark:bg-rose-900 dark:text-rose-300'
      case DocumentType.CONTRACT:
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      case DocumentType.CORRESPONDENCE:
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Page header with back button
  const pageHeader = (
    <div className="flex items-center gap-2">
      <Link href="/documents">
        <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
          <ArrowLeft className="h-4 w-4" />
        </Button>
      </Link>
      <h1 className="text-2xl font-bold tracking-tight">Document Details</h1>
    </div>
  )

  // Show standardized loading state
  if (isLoading) {
    return (
      <div className="flex flex-col gap-6">
        {pageHeader}
        <div className="flex justify-center py-12">
          <Loading variant="spinner" size="lg" text="Loading document details..." />
        </div>
      </div>
    )
  }

  // Show standardized error state
  if (error || !document) {
    return (
      <div className="flex flex-col gap-6">
        {pageHeader}
        <EmptyState
          variant="error"
          title="Could not load document"
          description={error || 'The requested document could not be found'}
          actionLabel="Try Again"
          onAction={refetch}
        />
      </div>
    )
  }

  // Create mock data for UI elements not directly available from the API
  const documentExtras = {
    tags: ['document', document.type.toLowerCase()],
    version: '1.0',
    metadata: {
      dimensions: document.mime_type.startsWith('image/') ? '3024 x 4032' : null,
      pages: document.mime_type === 'application/pdf' ? 5 : null,
    },
    uploaded_by_name: 'System User', // We would get this from the uploaded_by ID in a real app
  }

  return (
    <div className="flex flex-col gap-6">
      {pageHeader}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div className="flex items-center gap-4">
                {getDocumentIcon(document.type, document.mime_type)}
                <div>
                  <CardTitle className="text-xl">{document.name}</CardTitle>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline" className={getDocumentTypeColor(document.type)}>
                      {document.type}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {formatFileSize(document.file_size)}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" className="flex items-center gap-1" onClick={handleView}>
                  <Eye className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button className="flex items-center gap-1" onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-1" />
                  Download
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {document.description && (
                  <div>
                    <h3 className="font-medium mb-2">Description</h3>
                    <p className="text-sm text-muted-foreground">{document.description}</p>
                  </div>
                )}

                <div>
                  <h3 className="font-medium mb-2">Related Claim</h3>
                  <Link
                    href={`/claims/${document.claim_id}`}
                    className="text-sm text-primary hover:underline"
                  >
                    {document.claim_number || document.claim_id}
                  </Link>
                </div>

                <div>
                  <h3 className="font-medium mb-2">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {documentExtras.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                {documentExtras.metadata && (
                  <div>
                    <h3 className="font-medium mb-2">Document Metadata</h3>
                    <dl className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                      {documentExtras.metadata.dimensions && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Dimensions</dt>
                          <dd>{documentExtras.metadata.dimensions}</dd>
                        </div>
                      )}
                    </dl>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
