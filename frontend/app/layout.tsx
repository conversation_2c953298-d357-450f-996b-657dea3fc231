import type React from 'react'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import './globals.css'
import { Inter } from 'next/font/google'
import { AuthProvider } from '@/components/auth-provider'
import { AuthenticatedLayout } from '@/components/authenticated-layout'

const inter = Inter({ subsets: ['latin'] })

export const metadata = {
  title: 'Claimentine - Insurance Claims Management',
  description: 'Insurance claims management system for adjusters',
  generator: 'v0.dev',
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.className} antialiased min-h-screen bg-background`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <AuthenticatedLayout>{children}</AuthenticatedLayout>
            <Toaster />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
