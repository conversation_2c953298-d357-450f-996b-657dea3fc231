'use client'

import React, { Suspense } from 'react'
import { ClaimInfo } from '@/components/claim-info'
import { ClaimTabs } from '@/components/claim-tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { ClaimSkeleton } from '@/components/claim-skeleton'
import { ClaimDateInfo } from '@/components/claim-date-info'
import { ClaimPersonnelInfo } from '@/components/claim-personnel-info'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'

export default function ClaimDetailPage({ params }: { params: Promise<{ number: string }> }) {
  // Since we're now client-side, we need to handle the params differently
  const [claimNumber, setClaimNumber] = React.useState<string>('')

  React.useEffect(() => {
    params.then(resolvedParams => {
      setClaimNumber(resolvedParams.number)
    })
  }, [params])

  // Centralized claim data fetching
  const {
    data: claim,
    isLoading: isClaimLoading,
    error: claimError,
    refetch: refreshClaim,
  } = useApi(() => {
    if (!claimNumber) return Promise.resolve(null)
    return api.claims.getClaimByNumber(claimNumber)
  }, [claimNumber])

  // Don't render anything until we have the claim number
  if (!claimNumber) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Link href="/claims">
          <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">Claim Details</h1>
        <Badge variant="outline" className="ml-2 text-primary">
          {claimNumber}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Suspense fallback={<ClaimSkeleton />}>
            <ClaimInfo
              claimNumber={claimNumber}
              claim={claim}
              isLoading={isClaimLoading}
              error={claimError}
              onRefresh={refreshClaim}
            />
          </Suspense>
        </div>

        <div className="space-y-6">
          <Suspense
            fallback={
              <div className="bg-muted rounded-lg p-4 flex items-center justify-center h-[140px]">
                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
              </div>
            }
          >
            <ClaimDateInfo
              claimNumber={claimNumber}
              claim={claim}
              isLoading={isClaimLoading}
              error={claimError}
            />
          </Suspense>

          <Suspense
            fallback={
              <div className="bg-muted rounded-lg p-4 flex items-center justify-center h-[140px]">
                <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
              </div>
            }
          >
            <ClaimPersonnelInfo
              claimNumber={claimNumber}
              claim={claim}
              isLoading={isClaimLoading}
              error={claimError}
            />
          </Suspense>
        </div>
      </div>

      <ClaimTabs claimNumber={claimNumber} />
    </div>
  )
}
