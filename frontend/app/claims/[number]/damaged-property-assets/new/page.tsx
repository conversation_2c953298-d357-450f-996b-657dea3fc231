'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import {
  DamagedPropertyAssetForm,
  DamagedPropertyAssetFormValues,
} from '@/components/damaged-property-asset-form'
import { api } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'

export default function NewDamagedPropertyAssetPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const [isSaving, setIsSaving] = useState(false)

  const handleBack = () => {
    router.push(`/claims/${claimNumber}?tab=property-damage`)
  }

  const handleSubmitNewAsset = async (data: DamagedPropertyAssetFormValues) => {
    setIsSaving(true)
    try {
      await api.claims.createDamagedPropertyAsset(claimNumber, data as any)
      toast({ title: 'Success', description: 'Damaged property asset added successfully.' })
      router.push(`/claims/${claimNumber}?tab=property-damage`)
    } catch (error: any) {
      console.error('Error creating damaged property asset:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to add damaged property asset. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Claim Details
      </Button>
      <DamagedPropertyAssetForm
        onSubmit={handleSubmitNewAsset}
        claimId={claimNumber}
        onCancel={handleBack}
        isSaving={isSaving}
      />
    </div>
  )
}
