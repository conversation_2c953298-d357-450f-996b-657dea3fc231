'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ArrowLeft, Loader2, AlertCircle, PlusCircle, Edit, Trash2 } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import {
  DamagedPropertyAsset,
  DamageInstance,
  PropertyDamageType,
  RepairStatus,
} from '@/lib/api/types'
import { useToast } from '@/components/ui/use-toast'
import { Separator } from '@/components/ui/separator'

export default function DamagedPropertyAssetDetailPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const assetId = params.assetId as string

  const {
    data: assetData,
    isLoading: isLoadingAsset,
    error: assetError,
    refetch: refetchAsset,
  } = useApi<DamagedPropertyAsset | null>(
    () => api.claims.getDamagedPropertyAsset(claimNumber, assetId),
    [claimNumber, assetId]
  )

  const {
    data: damageInstances,
    isLoading: isLoadingInstances,
    error: instancesError,
    refetch: refetchInstances,
  } = useApi<DamageInstance[]>(
    () => api.claims.getDamageInstances(claimNumber, assetId),
    [claimNumber, assetId]
  )

  const handleBack = () => {
    router.push(`/claims/${claimNumber}?tab=property-damage`)
  }

  const handleEditAsset = () => {
    router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}/edit`)
  }

  const handleAddDamageInstance = () => {
    router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}/damage-instances/new`)
  }

  const handleEditDamageInstance = (instanceId: string) => {
    router.push(
      `/claims/${claimNumber}/damaged-property-assets/${assetId}/damage-instances/${instanceId}/edit`
    )
  }

  const handleDeleteDamageInstance = async (instanceId: string) => {
    const confirmed = window.confirm('Are you sure you want to delete this damage instance?')
    if (!confirmed) {
      return
    }

    try {
      await api.claims.deleteDamageInstance(claimNumber, assetId, instanceId)
      toast({
        title: 'Success',
        description: 'Damage instance removed successfully.',
      })
      refetchInstances()
    } catch (err) {
      console.error('Error deleting damage instance:', err)
      toast({
        title: 'Error',
        description: 'Failed to remove damage instance.',
        variant: 'destructive',
      })
    }
  }

  // Helper for formatting enum values
  const formatEnum = (value?: string | null) => {
    if (!value) return 'Not specified'
    return value
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ')
  }

  // Helper for formatting currency values
  const formatCurrency = (value: any): string => {
    if (value === null || value === undefined) return 'Not specified'

    try {
      // Handle different value types
      let numValue: number
      if (typeof value === 'number') {
        numValue = value
      } else if (typeof value === 'string') {
        numValue = parseFloat(value)
      } else {
        return 'Invalid value'
      }

      // Check if parsing was successful
      if (isNaN(numValue)) return 'Invalid value'

      return `$${numValue.toFixed(2)}`
    } catch (error) {
      console.error('Error formatting currency value:', error)
      return 'Error'
    }
  }

  const isLoading = isLoadingAsset || isLoadingInstances
  const error = assetError || instancesError

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading damaged property asset details...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage = 'An unknown error occurred while fetching the data.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-red-500 mt-8" />
        <p className="mt-2 text-red-500">{errorMessage}</p>
        <Button
          onClick={() => {
            refetchAsset()
            refetchInstances()
          }}
          className="mt-4 mr-2"
        >
          Try Again
        </Button>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  if (!assetData) {
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-orange-500 mt-8" />
        <p className="mt-2 text-orange-500">Damaged property asset not found.</p>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4">
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Claim Details
      </Button>

      {/* Asset Details Card */}
      <Card className="mb-8">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>{assetData.name || 'Damaged Property Asset'}</CardTitle>
            <CardDescription>
              {formatEnum(assetData.asset_type)} • {assetData.location || 'No location specified'}
            </CardDescription>
          </div>
          <Button onClick={handleEditAsset}>
            <Edit className="mr-2 h-4 w-4" /> Edit Asset
          </Button>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Description</h4>
              <p>{assetData.description || 'No description provided.'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Owner</h4>
              <p>{assetData.owner_name || 'No owner specified'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-muted-foreground mb-1">Estimated Value</h4>
              <p>{formatCurrency(assetData.estimated_value)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Damage Instances Card */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Damage Instances</CardTitle>
          <Button onClick={handleAddDamageInstance}>
            <PlusCircle className="mr-2 h-4 w-4" /> Add Damage Instance
          </Button>
        </CardHeader>
        <CardContent>
          {damageInstances && damageInstances.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Damage Type</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Repair Status</TableHead>
                  <TableHead>Repair Cost</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {damageInstances.map(instance => (
                  <TableRow key={instance.id}>
                    <TableCell>{formatEnum(instance.damage_type)}</TableCell>
                    <TableCell>{instance.damage_severity || 'N/A'}</TableCell>
                    <TableCell>{formatEnum(instance.repair_status)}</TableCell>
                    <TableCell>{formatCurrency(instance.estimated_repair_cost)}</TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditDamageInstance(instance.id)}
                        className="mr-2"
                      >
                        <Edit className="mr-1 h-4 w-4" /> Edit
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteDamageInstance(instance.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="mr-1 h-4 w-4" /> Delete
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="py-8 text-center text-muted-foreground">
              <p>No damage instances have been recorded for this asset.</p>
              <p className="mt-2">Click "Add Damage Instance" to get started.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
