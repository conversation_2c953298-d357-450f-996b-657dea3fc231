'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { DamagedPropertyAsset } from '@/lib/api/types'
import {
  DamagedPropertyAssetForm,
  DamagedPropertyAssetFormValues,
} from '@/components/damaged-property-asset-form'
import { useToast } from '@/components/ui/use-toast'

export default function EditDamagedPropertyAssetPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const assetId = params.assetId as string
  const [isSaving, setIsSaving] = useState(false)

  const {
    data: damagedAsset,
    isLoading,
    error,
    refetch,
  } = useApi<DamagedPropertyAsset | null>(
    () =>
      claimNumber && assetId
        ? api.claims.getDamagedPropertyAsset(claimNumber, assetId)
        : Promise.resolve(null),
    [claimNumber, assetId]
  )

  const handleBack = () => {
    router.push(`/claims/${claimNumber}?tab=property-damage`)
  }

  const handleSubmitEditAsset = async (data: DamagedPropertyAssetFormValues) => {
    setIsSaving(true)
    try {
      if (!assetId) throw new Error('Asset ID is missing.')

      await api.claims.updateDamagedPropertyAsset(claimNumber, assetId, data as any)
      toast({ title: 'Success', description: 'Damaged property asset updated successfully.' })
      router.push(`/claims/${claimNumber}?tab=property-damage`)
    } catch (error: any) {
      console.error('Error updating damaged property asset:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to update damaged property asset. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading damaged property asset details...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage =
      'An unknown error occurred while fetching the damaged property asset details.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-red-500 mt-8" />
        <p className="mt-2 text-red-500">{errorMessage}</p>
        <Button onClick={() => refetch && refetch()} className="mt-4 mr-2">
          Try Again
        </Button>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  if (!damagedAsset && !isLoading) {
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-orange-500 mt-8" />
        <p className="mt-2 text-orange-500">Damaged property asset not found.</p>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Claim Details
      </Button>
      {damagedAsset && (
        <DamagedPropertyAssetForm
          onSubmit={handleSubmitEditAsset}
          claimId={claimNumber}
          assetId={assetId}
          initialData={damagedAsset}
          onCancel={handleBack}
          isSaving={isSaving}
        />
      )}
      {!isLoading && !error && !damagedAsset && (
        <div className="py-8 text-center text-muted-foreground">
          <p>Could not load damaged property asset data.</p>
        </div>
      )}
    </div>
  )
}
