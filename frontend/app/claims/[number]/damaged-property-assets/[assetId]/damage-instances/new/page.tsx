'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { DamageInstanceForm, DamageInstanceFormValues } from '@/components/damage-instance-form'
import { api } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'
import { DamageInstanceCreate, PropertyDamageType } from '@/lib/api/types'

export default function NewDamageInstancePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const assetId = params.assetId as string
  const [isSaving, setIsSaving] = useState(false)

  const handleBack = () => {
    router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}`)
  }

  const handleSubmitNewInstance = async (data: DamageInstanceFormValues) => {
    setIsSaving(true)
    try {
      // Map form values to the API expected format
      const instanceData: DamageInstanceCreate = {
        damage_type: data.damage_type ?? PropertyDamageType.OTHER, // Default to OTHER if not provided
        damage_description: data.damage_description ?? '', // Required field
        damage_severity: data.damage_severity ?? undefined,
        affected_area: undefined, // Not in form, but in API
        damage_cause: undefined, // Not in form, but in API
        date_of_damage: undefined, // Not in form, but in API
        repair_status: data.repair_status ?? undefined,
        repair_description: data.repair_description ?? undefined,
        repair_vendor: data.repair_vendor ?? undefined,
        estimated_repair_cost: data.estimated_repair_cost || undefined, // Convert empty string to undefined
        actual_repair_cost: undefined, // Not in form, but in API
        repair_start_date: data.repair_start_date ?? undefined,
        repair_completion_date: data.repair_completion_date ?? undefined,
        estimated_replacement_cost: undefined, // Not in form, but in API
        deductible_applied: undefined, // Not in form, but in API
        depreciation_amount: undefined, // Not in form, but in API
      }

      console.log('Creating damage instance with data:', instanceData)
      await api.claims.createDamageInstance(claimNumber, assetId, instanceData)
      toast({ title: 'Success', description: 'Damage instance added successfully.' })
      router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}`)
    } catch (error: any) {
      console.error('Error creating damage instance:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to add damage instance. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Asset Details
      </Button>
      <DamageInstanceForm
        onSubmit={handleSubmitNewInstance}
        claimId={claimNumber}
        assetId={assetId}
        onCancel={handleBack}
        isSaving={isSaving}
      />
    </div>
  )
}
