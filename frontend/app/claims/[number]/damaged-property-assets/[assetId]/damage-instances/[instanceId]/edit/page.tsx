'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { DamageInstance, DamageInstanceUpdate, PropertyDamageType } from '@/lib/api/types'
import { DamageInstanceForm, DamageInstanceFormValues } from '@/components/damage-instance-form'
import { useToast } from '@/components/ui/use-toast'

export default function EditDamageInstancePage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const assetId = params.assetId as string
  const instanceId = params.instanceId as string
  const [isSaving, setIsSaving] = useState(false)

  const {
    data: damageInstance,
    isLoading,
    error,
    refetch,
  } = useApi<DamageInstance | null>(
    () =>
      claimNumber && assetId && instanceId
        ? api.claims.getDamageInstance(claimNumber, assetId, instanceId)
        : Promise.resolve(null),
    [claimNumber, assetId, instanceId]
  )

  const handleBack = () => {
    router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}`)
  }

  const handleSubmitEditInstance = async (data: DamageInstanceFormValues) => {
    setIsSaving(true)
    try {
      if (!instanceId) throw new Error('Instance ID is missing.')

      // Map form values to the API expected format
      const instanceData: DamageInstanceUpdate = {
        damage_type: data.damage_type ?? undefined,
        damage_description: data.damage_description ?? undefined,
        damage_severity: data.damage_severity ?? undefined,
        repair_status: data.repair_status ?? undefined,
        repair_description: data.repair_description ?? undefined,
        repair_vendor: data.repair_vendor ?? undefined,
        estimated_repair_cost: data.estimated_repair_cost ?? undefined,
        repair_start_date: data.repair_start_date ?? undefined,
        repair_completion_date: data.repair_completion_date ?? undefined,
      }

      console.log('Updating damage instance with data:', instanceData)
      await api.claims.updateDamageInstance(claimNumber, assetId, instanceId, instanceData)
      toast({ title: 'Success', description: 'Damage instance updated successfully.' })
      router.push(`/claims/${claimNumber}/damaged-property-assets/${assetId}`)
    } catch (error: any) {
      console.error('Error updating damage instance:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to update damage instance. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading damage instance details...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage = 'An unknown error occurred while fetching the damage instance details.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-red-500 mt-8" />
        <p className="mt-2 text-red-500">{errorMessage}</p>
        <Button onClick={() => refetch && refetch()} className="mt-4 mr-2">
          Try Again
        </Button>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Asset Details
        </Button>
      </div>
    )
  }

  if (!damageInstance && !isLoading) {
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-orange-500 mt-8" />
        <p className="mt-2 text-orange-500">Damage instance not found.</p>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Asset Details
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Asset Details
      </Button>
      {damageInstance && (
        <DamageInstanceForm
          onSubmit={handleSubmitEditInstance}
          claimId={claimNumber}
          assetId={assetId}
          instanceId={instanceId}
          initialData={damageInstance}
          onCancel={handleBack}
          isSaving={isSaving}
        />
      )}
      {!isLoading && !error && !damageInstance && (
        <div className="py-8 text-center text-muted-foreground">
          <p>Could not load damage instance data.</p>
        </div>
      )}
    </div>
  )
}
