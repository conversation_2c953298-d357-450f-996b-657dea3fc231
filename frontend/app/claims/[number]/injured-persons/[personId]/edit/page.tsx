'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Loader2, AlertCircle } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { InjuredPerson, InjuredPersonUpdate } from '@/lib/api/types'
import { InjuredPersonForm, InjuredPersonFormValues } from '@/components/injured-person-form'
import { useToast } from '@/components/ui/use-toast'

export default function EditInjuredPersonPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const personId = params.personId as string
  const [isSaving, setIsSaving] = useState(false)

  const {
    data: injuredPerson,
    isLoading,
    error,
    refetch,
  } = useApi<InjuredPerson | null>(
    () =>
      claimNumber && personId
        ? api.claims.getInjuredPerson(claimNumber, personId)
        : Promise.resolve(null),
    [claimNumber, personId]
  )

  const handleBack = () => {
    router.push(`/claims/${claimNumber}?tab=bodily_injury`)
  }

  const handleSubmitEditInjuredPerson = async (data: InjuredPersonFormValues) => {
    setIsSaving(true)
    try {
      if (!personId) throw new Error('Person ID is missing.')

      await api.claims.updateInjuredPerson(claimNumber, personId, data as InjuredPersonUpdate)
      toast({ title: 'Success', description: 'Injured person updated successfully.' })
      router.push(`/claims/${claimNumber}?tab=bodily_injury`)
    } catch (error: any) {
      console.error('Error updating injured person:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to update injured person. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="ml-2">Loading injured person details...</p>
      </div>
    )
  }

  if (error) {
    let errorMessage = 'An unknown error occurred while fetching injured person details.'
    if (typeof error === 'string') {
      errorMessage = error
    } else if (error && typeof (error as any).message === 'string') {
      errorMessage = (error as any).message
    }
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-red-500 mt-8" />
        <p className="mt-2 text-red-500">{errorMessage}</p>
        <Button onClick={() => refetch && refetch()} className="mt-4 mr-2">
          Try Again
        </Button>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  if (!injuredPerson && !isLoading) {
    return (
      <div className="container mx-auto py-8 text-center">
        <Button variant="outline" onClick={handleBack} className="mb-4 float-left">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div className="clear-both"></div>
        <AlertCircle className="h-8 w-8 mx-auto text-orange-500 mt-8" />
        <p className="mt-2 text-orange-500">Injured person not found.</p>
        <Button variant="secondary" onClick={handleBack} className="mt-4">
          Back to Claim Details
        </Button>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Claim Details
      </Button>
      {injuredPerson && (
        <InjuredPersonForm
          onSubmit={handleSubmitEditInjuredPerson}
          claimId={claimNumber}
          personId={personId}
          initialData={injuredPerson}
          onCancel={handleBack}
          isSaving={isSaving}
        />
      )}
      {!isLoading && !error && !injuredPerson && (
        <div className="py-8 text-center text-muted-foreground">
          <p>Could not load injured person data.</p>
        </div>
      )}
    </div>
  )
}
