'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft } from 'lucide-react'
import { InjuredPersonForm, InjuredPersonFormValues } from '@/components/injured-person-form'
import { api } from '@/lib/api'
import { useToast } from '@/components/ui/use-toast'

export default function NewInjuredPersonPage() {
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const claimNumber = params.number as string
  const [isSaving, setIsSaving] = useState(false)

  const handleBack = () => {
    router.push(`/claims/${claimNumber}?tab=bodily_injury`)
  }

  const handleSubmitNewInjuredPerson = async (data: InjuredPersonFormValues) => {
    setIsSaving(true)
    try {
      await api.claims.createInjuredPerson(claimNumber, data as any)
      toast({ title: 'Success', description: 'Injured person added successfully.' })
      router.push(`/claims/${claimNumber}?tab=bodily_injury`)
    } catch (error: any) {
      console.error('Error creating injured person:', error)
      toast({
        title: 'Error',
        description: error.message || 'Failed to add injured person. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsSaving(false)
    }
  }

  return (
    <div className="container mx-auto py-8">
      <Button variant="outline" onClick={handleBack} className="mb-4" disabled={isSaving}>
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Claim Details
      </Button>
      <InjuredPersonForm
        onSubmit={handleSubmitNewInjuredPerson}
        claimId={claimNumber}
        onCancel={handleBack}
        isSaving={isSaving}
      />
    </div>
  )
}
