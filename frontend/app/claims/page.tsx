'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ClaimsTable } from '@/components/claims-table'
import { Search } from 'lucide-react'
import { useDebounce } from '@/hooks/useDebounce'
import { ClaimStatus, ClaimType } from '@/lib/api/types'

export default function ClaimsPage() {
  const [searchInput, setSearchInput] = useState('')
  const [status, setStatus] = useState<ClaimStatus | undefined>(undefined)
  const [type, setType] = useState<ClaimType | undefined>(undefined)

  // Debounce search input to avoid excessive API calls
  const debouncedSearch = useDebounce(searchInput, 300)

  const handleClearFilters = () => {
    setSearchInput('')
    setStatus(undefined)
    setType(undefined)
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Claims</h1>
          <p className="text-muted-foreground">Manage and track insurance claims</p>
        </div>
      </div>

      {/* Search and Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
          <CardDescription>Find claims by number, claimant name, or description</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 md:flex-row md:items-end">
            {/* Search Input */}
            <div className="flex-1">
              <label htmlFor="search" className="block text-sm font-medium mb-2">
                Search Claims
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by claim number, claimant name, or description..."
                  value={searchInput}
                  onChange={e => setSearchInput(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="min-w-[200px]">
              <label htmlFor="status-filter" className="block text-sm font-medium mb-2">
                Status
              </label>
              <Select
                value={status || 'all'}
                onValueChange={value =>
                  setStatus(value === 'all' ? undefined : (value as ClaimStatus))
                }
              >
                <SelectTrigger id="status-filter">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="DRAFT">Draft</SelectItem>
                  <SelectItem value="INVESTIGATION">Investigation</SelectItem>
                  <SelectItem value="SETTLEMENT">Settlement</SelectItem>
                  <SelectItem value="LITIGATION">Litigation</SelectItem>
                  <SelectItem value="RECOVERY">Recovery</SelectItem>
                  <SelectItem value="CLOSED_SETTLED">Closed - Settled</SelectItem>
                  <SelectItem value="CLOSED_DENIED">Closed - Denied</SelectItem>
                  <SelectItem value="CLOSED_WITHDRAWN">Closed - Withdrawn</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Type Filter */}
            <div className="min-w-[200px]">
              <label htmlFor="type-filter" className="block text-sm font-medium mb-2">
                Type
              </label>
              <Select
                value={type || 'all'}
                onValueChange={value => setType(value === 'all' ? undefined : (value as ClaimType))}
              >
                <SelectTrigger id="type-filter">
                  <SelectValue placeholder="All types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All types</SelectItem>
                  <SelectItem value="AUTO">Auto</SelectItem>
                  <SelectItem value="PROPERTY">Property</SelectItem>
                  <SelectItem value="GENERAL_LIABILITY">General Liability</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters Button */}
            <Button variant="outline" onClick={handleClearFilters} className="shrink-0">
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Claims Table */}
      <ClaimsTable search={debouncedSearch} status={status} type={type} />
    </div>
  )
}
