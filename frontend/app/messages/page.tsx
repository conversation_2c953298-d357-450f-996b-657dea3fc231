import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { PlusIcon, Search } from 'lucide-react'

export default function MessagesPage() {
  const conversations = [
    {
      id: '1',
      name: '<PERSON>',
      lastMessage: "I've sent the requested documents for my claim.",
      time: '10:30 AM',
      unread: true,
      avatar: '/placeholder-user.jpg',
      initials: '<PERSON><PERSON>',
    },
    {
      id: '2',
      name: '<PERSON>',
      lastMessage: 'When can I expect to hear back about my claim?',
      time: 'Yesterday',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: '<PERSON>',
    },
    {
      id: '3',
      name: '<PERSON>',
      lastMessage: 'Thank you for the update on my claim status.',
      time: 'Yesterday',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: '<PERSON><PERSON>',
    },
    {
      id: '4',
      name: '<PERSON>',
      lastMessage: 'I have a question about the settlement offer.',
      time: 'Monday',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: '<PERSON>',
    },
    {
      id: '5',
      name: '<PERSON>',
      lastMessage: 'Can you provide more details about the required documentation?',
      time: 'Monday',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: 'DW',
    },
    {
      id: '6',
      name: 'Jennifer Lee',
      lastMessage: "I've scheduled the property inspection for next Tuesday.",
      time: 'Last week',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: 'JL',
    },
    {
      id: '7',
      name: 'Michael Brown',
      lastMessage: 'The repair shop needs authorization for additional work.',
      time: 'Last week',
      unread: false,
      avatar: '/placeholder-user.jpg',
      initials: 'MB',
    },
  ]

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      <div className="flex items-center justify-between py-4">
        <h1 className="text-3xl font-bold tracking-tight">Messages</h1>
        <Button className="flex items-center gap-1">
          <PlusIcon className="h-4 w-4" />
          New Message
        </Button>
      </div>

      <div className="flex flex-1 overflow-hidden border rounded-md">
        <div className="w-1/3 border-r">
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search messages..." className="pl-8" />
            </div>
          </div>
          <div className="overflow-y-auto h-[calc(100vh-12rem)]">
            {conversations.map(conversation => (
              <div
                key={conversation.id}
                className={`p-4 border-b hover:bg-muted/50 cursor-pointer ${conversation.id === '1' ? 'bg-muted' : ''}`}
              >
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage
                        src={conversation.avatar || '/placeholder.svg'}
                        alt={conversation.name}
                      />
                      <AvatarFallback>{conversation.initials}</AvatarFallback>
                    </Avatar>
                    {conversation.unread && (
                      <span className="absolute top-0 right-0 h-3 w-3 rounded-full bg-primary border-2 border-background"></span>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="font-medium truncate">{conversation.name}</h3>
                      <span className="text-xs text-muted-foreground">{conversation.time}</span>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">
                      {conversation.lastMessage}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex-1 flex flex-col">
          <div className="p-4 border-b">
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src="/placeholder-user.jpg" alt="John Smith" />
                <AvatarFallback>JS</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="font-medium">John Smith</h3>
                <p className="text-xs text-muted-foreground">Claim #ACME-2023-0000127</p>
              </div>
            </div>
          </div>

          <div className="flex-1 p-4 overflow-y-auto">
            <div className="space-y-4">
              <div className="flex justify-center">
                <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-md">
                  Today, 10:30 AM
                </span>
              </div>

              <div className="flex items-start gap-3">
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarImage src="/placeholder-user.jpg" alt="John Smith" />
                  <AvatarFallback>JS</AvatarFallback>
                </Avatar>
                <Card className="max-w-[80%]">
                  <CardContent className="p-3">
                    <p className="text-sm">
                      Hello, I've sent the requested documents for my claim.
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="flex items-start gap-3 justify-end">
                <Card className="bg-primary text-primary-foreground max-w-[80%]">
                  <CardContent className="p-3">
                    <p className="text-sm">
                      Thank you, John. I've received the documents and will review them shortly. Is
                      there anything else you'd like to add?
                    </p>
                  </CardContent>
                </Card>
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarImage src="/placeholder-user.jpg" alt="You" />
                  <AvatarFallback>ME</AvatarFallback>
                </Avatar>
              </div>
            </div>
          </div>

          <div className="p-4 border-t">
            <div className="flex gap-2">
              <Input placeholder="Type a message..." className="flex-1" />
              <Button>Send</Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
