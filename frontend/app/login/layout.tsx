import type React from 'react'
import { ThemeProvider } from '@/components/theme-provider'
import { Toaster } from '@/components/ui/toaster'
import { Inter } from 'next/font/google'

const inter = Inter({ subsets: ['latin'] })

export default function LoginLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className={`${inter.className} antialiased min-h-screen bg-background`}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <div className="min-h-screen">{children}</div>
        <Toaster />
      </ThemeProvider>
    </div>
  )
}
