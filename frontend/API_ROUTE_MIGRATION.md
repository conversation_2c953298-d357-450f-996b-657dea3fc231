# API Route Migration Guide

This document provides a step-by-step guide for migrating existing API routes to the new standardized format.

## Migration Steps

### 1. Import the required utilities

Replace existing imports with the standardized utility imports:

```typescript
import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ContentType,
  ErrorCode,
  createErrorResponse,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'
```

### 2. Implement logging and request ID tracking

Add the following code at the beginning of each handler method:

```typescript
// Generate request ID and start timing
const requestId = logRequest(request, '/api/v1/your-route')
const getElapsedTime = createTimer()
const method = request.method
const path = request.nextUrl.pathname
```

### 3. Use the standardized request handlers

Replace custom request handling logic with the utility functions:

**For GET requests:**

```typescript
export async function GET(request: NextRequest) {
  // Request logging and timing setup (see step 2)

  try {
    return handleGetRequest(request, '/api/v1/your-endpoint', {
      errorMessage: 'Error fetching data',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
```

**For POST/PUT/PATCH/DELETE requests:**

```typescript
export async function POST(request: NextRequest) {
  // Request logging and timing setup (see step 2)

  try {
    return handleDataRequest(request, '/api/v1/your-endpoint', {
      method: 'POST', // Change to PUT, PATCH, or DELETE as needed
      errorMessage: 'Error processing request',
      allowedContentTypes: [ContentType.JSON], // Add other content types if needed
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
```

### 4. Add content type validation (for non-GET requests)

Content type validation is already included in the `handleDataRequest` function, but you can customize the allowed content types:

```typescript
allowedContentTypes: [ContentType.JSON, ContentType.FORM_DATA, ContentType.MULTIPART]
```

### 5. Use standard error codes

Replace custom error codes with the standardized error codes from the `ErrorCode` enum:

- `ErrorCode.AUTH_REQUIRED`: Missing authentication
- `ErrorCode.AUTH_INVALID`: Invalid authentication credentials
- `ErrorCode.FORBIDDEN`: Authenticated but not authorized
- `ErrorCode.NOT_FOUND`: Resource not found
- `ErrorCode.VALIDATION_ERROR`: Invalid request parameters
- `ErrorCode.BACKEND_ERROR`: Error from backend service
- `ErrorCode.API_ROUTE_ERROR`: Error in the API route handler
- `ErrorCode.JSON_PARSE_ERROR`: Error parsing JSON response
- `ErrorCode.CONTENT_TYPE_ERROR`: Unsupported content type

### 6. Testing the migrated route

After migration, test the route to ensure:

1. Authentication is handled correctly
2. Content type validation works as expected
3. Error responses have the correct format
4. Success responses maintain the expected format
5. Request and response logging is working properly

## Example Migrated Route

```typescript
import { NextRequest } from 'next/server'
import {
  handleGetRequest,
  handleDataRequest,
  ContentType,
  ErrorCode,
  createErrorResponse,
} from '@/lib/api-proxy-utils'
import { logRequest, logError, createTimer } from '@/lib/api-logging'

/**
 * Handles GET requests to /api/v1/users
 * Proxies the request to the backend API and returns the response
 */
export async function GET(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/users')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleGetRequest(request, '/api/v1/users', {
      errorMessage: 'Error fetching users',
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while fetching users',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}

/**
 * Handles POST requests to /api/v1/users
 * Proxies the request to the backend API and returns the response
 */
export async function POST(request: NextRequest) {
  // Generate request ID and start timing
  const requestId = logRequest(request, '/api/v1/users')
  const getElapsedTime = createTimer()
  const method = request.method
  const path = request.nextUrl.pathname

  try {
    return handleDataRequest(request, '/api/v1/users', {
      method: 'POST',
      errorMessage: 'Error creating user',
      allowedContentTypes: [ContentType.JSON],
    })
  } catch (error) {
    const elapsedTime = getElapsedTime()
    logError(requestId, method, path, 500, error, elapsedTime)

    return createErrorResponse(
      'An unexpected error occurred while creating user',
      500,
      ErrorCode.API_ROUTE_ERROR,
      undefined,
      requestId,
      method,
      path
    )
  }
}
```

## Reference

For more details, refer to:

- [API Route Standards Documentation](./docs/API_ROUTE_STANDARDS.md)
- [Template API Route](./app/api/template/route.ts)
- [API Logging Utilities](./lib/api-logging.ts)
- [API Proxy Utilities](./lib/api-proxy-utils.ts)
