# Claimentine Scripts

This directory contains utility scripts for the Claimentine project, primarily focused on data population and testing scenarios.

## Data Populator

The **Data Populator** is a comprehensive script system designed to populate the Claimentine database with realistic sample data for development, testing, and demonstration purposes.

### Quick Start

```bash
# From the project root directory
cd scripts/data_populator
python main.py
```

### What It Does

The data populator creates a complete, realistic claims management environment including:

- **System Configuration**: Essential permissions, authority levels, and reserve configurations
- **User Accounts**: Multiple realistic users with different roles and authority levels
- **Customer Companies**: Diverse business entities representing different industries
- **Claims Scenarios**: Realistic claim narratives across all major claim types
- **Supporting Data**: Notes, tasks, financial records, and related entities

### Documentation

- **[Data Populator Guide](data_populator/README.md)** - Comprehensive guide to the data populator
- **[User Accounts Reference](data_populator/USERS.md)** - Details of all created user accounts
- **[Narrative Scenarios](data_populator/NARRATIVES.md)** - Description of all claim scenarios
- **[Technical Architecture](data_populator/ARCHITECTURE.md)** - Technical implementation details

### Prerequisites

1. **Backend Running**: The Claimentine backend must be running
2. **Database Access**: PostgreSQL database must be accessible
3. **CLI Available**: The Claimentine CLI must be installed and functional
4. **Dependencies**: All Python dependencies must be installed

### Safety Features

- **Database Reset**: Safely resets the database before populating
- **Controlled Execution**: Runs in phases with clear logging
- **Error Handling**: Comprehensive error handling and rollback capabilities
- **Validation**: Validates data integrity throughout the process

### Output

After successful execution, you'll have:
- 8 user accounts (1 admin + 7 operational users)
- 8 customer companies
- 9 realistic claim scenarios with full supporting data
- Complete financial records, tasks, and notes
- Ready-to-use demonstration environment

For detailed information, see the [Data Populator Guide](data_populator/README.md). 