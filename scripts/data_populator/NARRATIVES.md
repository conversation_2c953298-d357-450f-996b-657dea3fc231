# Narrative Scenarios Reference

This document describes all the realistic claim scenarios created by the Claimentine Data Populator. Each narrative represents a complete, interconnected claim story with realistic data, timelines, and business workflows.

## Overview

The data populator creates **9 comprehensive claim scenarios** across all major claim types:

- **3 Auto Claims**: Collision, hit-and-run, theft
- **3 Property Claims**: Fire damage, water damage, vandalism  
- **3 General Liability Claims**: Slip-and-fall, workplace injury, products liability

Each scenario includes complete claim details, financial records, tasks, notes, and related entities to create realistic testing and demonstration environments.

## Auto Claims Scenarios

### 1. Auto Collision At-Fault
**File**: `auto_collision_at_fault.py`
**Customer**: Acme Manufacturing Corp
**Claim Type**: AUTO

#### Scenario Details
A company delivery truck rear-ends another vehicle at a traffic light, resulting in property damage and minor injuries.

#### Key Elements
- **Vehicle**: 2019 Ford Transit delivery truck
- **Driver**: Company employee on delivery route
- **Incident**: Rear-end collision at intersection
- **Damages**: Both vehicles damaged, minor injuries to other driver
- **Liability**: Clear at-fault situation

#### Data Created
- Complete claim with auto-specific details
- Property damage records for both vehicles
- Injured person record with medical details
- Financial reserves for liability and property damage
- Investigation tasks and notes
- Witness statements
- Attorney involvement (defense)

#### Business Value
- Tests at-fault liability scenarios
- Demonstrates multi-vehicle damage handling
- Shows injury claim management
- Validates financial reserve calculations

### 2. Auto Hit-and-Run
**File**: `auto_hit_and_run.py`
**Customer**: Sunshine Retail Group
**Claim Type**: AUTO

#### Scenario Details
A company vehicle is struck by an unidentified hit-and-run driver in a parking lot, resulting in significant damage.

#### Key Elements
- **Vehicle**: 2020 Honda Civic company car
- **Incident**: Hit-and-run in retail store parking lot
- **Investigation**: Ongoing search for responsible party
- **Coverage**: Uninsured motorist claim
- **Evidence**: Security camera footage, witness accounts

#### Data Created
- Comprehensive claim with investigation focus
- Property damage assessment
- Uninsured motorist coverage details
- Investigation tasks and timeline
- Evidence collection notes
- Police report references

#### Business Value
- Tests uninsured motorist scenarios
- Demonstrates investigation workflows
- Shows evidence management
- Validates coverage determination

### 3. Auto Theft Comprehensive
**File**: `auto_theft_comprehensive.py`
**Customer**: Metro Construction LLC
**Claim Type**: AUTO

#### Scenario Details
A company pickup truck is stolen from a job site, including valuable tools and equipment in the cargo area.

#### Key Elements
- **Vehicle**: 2018 Chevrolet Silverado work truck
- **Theft Location**: Construction job site
- **Cargo Loss**: Professional tools and equipment
- **Coverage**: Comprehensive coverage claim
- **Recovery**: Vehicle recovered but damaged

#### Data Created
- Theft claim with comprehensive coverage
- Cargo loss itemization
- Recovery and damage assessment
- Financial calculations for total loss
- Investigation and police coordination
- Equipment replacement estimates

#### Business Value
- Tests comprehensive coverage scenarios
- Demonstrates cargo loss handling
- Shows theft investigation processes
- Validates total loss calculations

## Property Claims Scenarios

### 4. Property Fire Damage
**File**: `property_fire_damage.py`
**Customer**: TechStart Solutions
**Claim Type**: PROPERTY

#### Scenario Details
An electrical fire damages the company's office building, affecting equipment, inventory, and business operations.

#### Key Elements
- **Property**: Commercial office building
- **Cause**: Electrical system malfunction
- **Damages**: Fire, smoke, and water damage
- **Business Impact**: Temporary closure and relocation
- **Coverage**: Property damage and business interruption

#### Data Created
- Property claim with fire damage details
- Business interruption calculations
- Equipment and inventory losses
- Temporary relocation expenses
- Fire investigation reports
- Restoration timeline and costs

#### Business Value
- Tests fire damage scenarios
- Demonstrates business interruption claims
- Shows complex damage assessment
- Validates restoration processes

### 5. Property Water Damage
**File**: `property_water_damage.py`
**Customer**: Green Valley Farms
**Claim Type**: PROPERTY

#### Scenario Details
A burst pipe floods the farm's equipment storage facility, damaging machinery and stored products.

#### Key Elements
- **Property**: Agricultural storage facility
- **Cause**: Frozen pipe burst during cold weather
- **Damages**: Water damage to equipment and inventory
- **Mitigation**: Emergency water extraction and drying
- **Coverage**: Property damage claim

#### Data Created
- Water damage claim details
- Equipment damage assessment
- Inventory loss calculations
- Mitigation and restoration costs
- Emergency response documentation
- Preventive measure recommendations

#### Business Value
- Tests water damage scenarios
- Demonstrates agricultural claims
- Shows mitigation importance
- Validates emergency response

### 6. Property Vandalism
**File**: `property_vandalism.py`
**Customer**: Downtown Restaurant Group
**Claim Type**: PROPERTY

#### Scenario Details
Vandals break windows and damage the exterior of a restaurant location, requiring repairs and security upgrades.

#### Key Elements
- **Property**: Restaurant building exterior
- **Incident**: Intentional vandalism overnight
- **Damages**: Broken windows, graffiti, damaged signage
- **Security**: Enhanced security measures needed
- **Coverage**: Vandalism coverage claim

#### Data Created
- Vandalism claim with police report
- Property damage assessment
- Repair and replacement costs
- Security upgrade recommendations
- Investigation and evidence collection
- Prevention strategy development

#### Business Value
- Tests vandalism coverage
- Demonstrates security considerations
- Shows repair vs. replacement decisions
- Validates prevention strategies

## General Liability Claims Scenarios

### 7. GL Slip-and-Fall
**File**: `gl_slip_and_fall.py`
**Customer**: Coastal Shipping Co
**Claim Type**: GENERAL_LIABILITY

#### Scenario Details
A visitor slips and falls on a wet floor at the company's warehouse facility, resulting in injuries and potential liability.

#### Key Elements
- **Incident Type**: Premises liability - slip and fall
- **Location**: Company warehouse
- **Injured Party**: Business visitor
- **Cause**: Wet floor without adequate warning
- **Injuries**: Back injury requiring medical treatment

#### Data Created
- General liability claim details
- Injured person medical records
- Premises liability investigation
- Safety protocol review
- Medical expense tracking
- Legal representation coordination

#### Business Value
- Tests premises liability scenarios
- Demonstrates safety protocol importance
- Shows injury claim management
- Validates liability determination

### 8. GL Premises Workplace Injury
**File**: `gl_premises_workplace.py`
**Customer**: Precision Auto Parts
**Claim Type**: GENERAL_LIABILITY

#### Scenario Details
A contractor working at the company facility is injured when equipment malfunctions, raising liability questions.

#### Key Elements
- **Incident Type**: Premises liability - workplace injury
- **Injured Party**: Independent contractor
- **Cause**: Equipment malfunction during work
- **Injuries**: Hand injury requiring surgery
- **Liability**: Shared responsibility investigation

#### Data Created
- Workplace injury claim
- Contractor relationship documentation
- Equipment inspection records
- Safety compliance review
- Medical treatment coordination
- Liability allocation analysis

#### Business Value
- Tests contractor liability scenarios
- Demonstrates equipment safety issues
- Shows complex liability determination
- Validates safety compliance

### 9. GL Products Liability
**File**: `gl_products_liability.py`
**Customer**: Precision Auto Parts
**Claim Type**: GENERAL_LIABILITY

#### Scenario Details
A defective auto part manufactured by the company causes a vehicle accident, resulting in product liability claims.

#### Key Elements
- **Incident Type**: Products liability
- **Product**: Defective brake component
- **Consequence**: Vehicle accident due to brake failure
- **Injuries**: Multiple parties injured
- **Liability**: Manufacturing defect investigation

#### Data Created
- Products liability claim
- Manufacturing defect analysis
- Recall consideration process
- Multiple injured parties
- Expert witness coordination
- Regulatory compliance review

#### Business Value
- Tests products liability scenarios
- Demonstrates manufacturing defect claims
- Shows recall decision processes
- Validates regulatory compliance

## Common Elements Across All Narratives

### Financial Components
- **Reserves**: Appropriate reserve amounts for each claim type
- **Payments**: Realistic payment schedules and amounts
- **Adjustments**: Reserve adjustments based on claim development
- **Coverage**: Proper coverage determination and limits

### Workflow Elements
- **Tasks**: Realistic task assignments and timelines
- **Notes**: Detailed documentation of claim activities
- **Status Updates**: Proper claim status progression
- **Assignments**: Appropriate adjuster assignments based on specialization

### Supporting Entities
- **Injured Persons**: When applicable, with medical details
- **Witnesses**: Contact information and statements
- **Attorneys**: Defense and plaintiff representation
- **Vendors**: Repair shops, medical providers, experts

### Investigation Components
- **Evidence Collection**: Photos, documents, reports
- **Expert Analysis**: Engineering, medical, accident reconstruction
- **Legal Review**: Coverage analysis, liability determination
- **Compliance**: Regulatory requirements and reporting

## Narrative Execution Flow

### 1. Scenario Setup
- Select appropriate customer and claim type
- Generate realistic incident details
- Assign appropriate adjuster based on specialization

### 2. Claim Creation
- Create initial claim with complete details
- Set up financial reserves
- Generate initial documentation

### 3. Investigation Development
- Add investigation tasks
- Create witness and evidence records
- Document findings and analysis

### 4. Financial Management
- Process payments as appropriate
- Adjust reserves based on developments
- Track expenses and recoveries

### 5. Resolution Activities
- Complete investigation tasks
- Finalize settlements or denials
- Document lessons learned

## Customization and Extension

### Adding New Narratives
1. **Create new file** in `narratives/` directory
2. **Follow naming convention**: `{type}_{description}.py`
3. **Implement `populate_scenario()`** function
4. **Use existing patterns** for consistency
5. **Add to main.py** scenario list

### Modifying Existing Narratives
1. **Edit scenario file** directly
2. **Maintain realistic data** patterns
3. **Update documentation** as needed
4. **Test thoroughly** with clean database

### Narrative Template Structure
```python
def populate_scenario():
    """Populate the [Scenario Name] narrative scenario."""
    logger.info("Executing [Scenario Name] scenario...")
    
    # 1. Get customer and users
    customer = created_data["customers"]["CUST"]
    adjuster = created_data["users"]["adjuster_name"]
    
    # 2. Create claim
    claim = create_claim(...)
    
    # 3. Add financial details
    create_claim_financials(...)
    
    # 4. Add supporting entities
    create_injured_person_for_claim(...)
    create_witness_for_claim(...)
    
    # 5. Add tasks and notes
    create_task_for_claim(...)
    create_note_for_claim(...)
    
    logger.info("✅ [Scenario Name] scenario completed")
```

## Business Value and Testing

### Comprehensive Coverage
- **All Claim Types**: Auto, Property, General Liability
- **Various Complexities**: Simple to complex scenarios
- **Different Outcomes**: Settlements, denials, ongoing claims
- **Multiple Stakeholders**: Adjusters, managers, customers, third parties

### Realistic Workflows
- **Investigation Processes**: Evidence collection, expert analysis
- **Financial Management**: Reserves, payments, adjustments
- **Task Management**: Assignments, deadlines, completions
- **Documentation**: Notes, reports, correspondence

### Testing Scenarios
- **Authority Limits**: Financial approval workflows
- **Role Permissions**: Access control and restrictions
- **Business Rules**: Coverage determination, liability assessment
- **Integration**: Cross-system data flow and consistency

### Demonstration Value
- **Complete Stories**: End-to-end claim narratives
- **Realistic Data**: Industry-standard practices and amounts
- **Professional Presentation**: Suitable for client demonstrations
- **Training Material**: Educational value for new users

The narrative scenarios provide a comprehensive foundation for testing, training, and demonstrating the full capabilities of the Claimentine claims management system. 