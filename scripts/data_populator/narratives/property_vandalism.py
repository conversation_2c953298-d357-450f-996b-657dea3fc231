"""Property Vandalism Narrative Scenario.

This scenario creates a realistic commercial property vandalism claim where
a retail store suffers extensive damage during civil unrest.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create a Property Vandalism scenario.
    
    Scenario: Retail electronics store suffers extensive vandalism damage
    during civil unrest, including broken windows, stolen inventory, and graffiti.
    """
    logger.info("🏪💥 Creating Property Vandalism Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["DTRG"]["id"]  # Downtown Restaurant Group (also owns retail)
    adjuster_id = created_data["users"]["lisa_anderson"]["id"]  # Property Claims Specialist
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(12)  # Within last 12 days
    incident_location = "TechWorld Electronics, 1455 Main Street, Downtown, CA 90210"
    
    # Create the main claim
    logger.info("Creating main property vandalism claim...")
    claim_data = create_claim(
        claim_type="PROPERTY",
        customer_id=customer_id,
        description="Electronics retail store suffered extensive vandalism during civil unrest. Multiple windows broken, front door damaged, significant inventory stolen including laptops, phones, and tablets. Graffiti throughout interior. Security system disabled. Store forced to close for repairs and inventory replacement.",
        claimant_name="Downtown Restaurant Group - TechWorld Division",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="PROP-TECH-2024-004",
        property_type="RETAIL_STORE",
        property_address="1455 Main Street, Downtown, CA 90210",
        damage_type="VANDALISM",
        estimated_value=125000.00,
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>"
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Store manager witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Jennifer Park",
        contact_info="<EMAIL>, (*************",
        statement="I arrived the morning after the unrest to find the store completely ransacked. All front windows were broken, the security gate was torn down, and most of our high-value inventory was gone. The damage was extensive - they even spray-painted the walls and destroyed our point-of-sale system."
    )
    
    # Police officer witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Sergeant Mike Rodriguez",
        contact_info="<EMAIL>, (*************",
        statement="Responded to multiple vandalism reports in the area during civil unrest. TechWorld Electronics was one of the hardest hit. Extensive property damage and theft. Security cameras were destroyed but neighboring businesses may have footage. Multiple suspects involved."
    )
    
    # Security company witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Tom Wilson",
        contact_info="<EMAIL>, (*************",
        statement="Our alarm system was triggered at 11:47 PM but response was delayed due to civil unrest. When we arrived, the damage was already done. Security cameras were destroyed early in the incident. The security gate was cut with power tools."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Extensive vandalism damage during civil unrest. Store completely ransacked with broken windows, stolen inventory, and graffiti damage. Police report filed. Store closed indefinitely pending repairs and inventory replacement."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Preliminary inventory assessment shows approximately $85,000 in stolen electronics including laptops, smartphones, tablets, and gaming equipment. Property damage estimated at $40,000 for windows, doors, fixtures, and cleanup."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Civil authority coverage may apply due to government-imposed curfew and area restrictions. Business interruption exposure significant as store cannot reopen until repairs completed and inventory restocked."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Coordinating with law enforcement on investigation. Multiple businesses in area affected. Recovery of stolen property unlikely. Insurance adjuster and forensic accountant assigned to verify inventory losses."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Inventory Verification",
        description="Work with forensic accountant to verify stolen inventory and establish values",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Property Damage Assessment",
        description="Assess structural damage, window replacement, and cleanup requirements",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Security System Evaluation",
        description="Evaluate security system failure and potential subrogation against security company",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Civil Authority Coverage",
        description="Evaluate civil authority coverage for government-imposed restrictions",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Business Interruption Analysis",
        description="Calculate business interruption losses during closure and restocking period",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "PROPERTY_DAMAGE", "amount": 90000.00},
        {"type": "BUSINESS_INTERRUPTION", "amount": 35000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=125000.00,
        reserves=reserves,
        deductible=10000.00,
        coverage_limit=1000000.00
    )
    
    logger.info(f"✅ Property Vandalism scenario completed for claim {claim_number}")
    logger.info(f"   - Property: TechWorld Electronics retail store")
    logger.info(f"   - Cause: Vandalism during civil unrest")
    logger.info(f"   - Damage: $85K stolen inventory + $40K property damage")
    logger.info(f"   - Total Reserves: $125,000 (PD: $90K, BI: $35K)")
    logger.info(f"   - Estimated Value: $125,000") 