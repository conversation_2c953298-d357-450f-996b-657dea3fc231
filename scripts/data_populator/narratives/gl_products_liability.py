"""General Liability Products Liability Narrative Scenario.

This scenario creates a realistic products liability claim where a defective
appliance causes both personal injury and property damage.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_injured_person_for_claim,
    create_injury_for_injured_person,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create a GL Products Liability scenario.
    
    Scenario: Defective coffee maker causes electrical fire and burns
    to user, resulting in both BI and property damage claims.
    """
    logger.info("⚡ Creating GL Products Liability Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["PAPT"]["id"]  # Precision Auto Parts (also makes appliances)
    adjuster_id = created_data["users"]["sarah_johnson"]["id"]  # Senior Claims Adjuster
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(20)  # Within last 20 days
    incident_location = "Consumer residence: 456 Maple Street, Riverside, CA"
    
    # Create the main claim
    logger.info("Creating main GL products liability claim...")
    claim_data = create_claim(
        claim_type="GENERAL_LIABILITY",
        customer_id=customer_id,
        description="Defective coffee maker manufactured by insured caused electrical fire in consumer's kitchen. User sustained burns to hand and arm while attempting to unplug malfunctioning unit. Fire damaged kitchen cabinets and countertop. Product was 8 months old and used normally.",
        claimant_name="Patricia Williams",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="GL-ACME-2024-003",
        gl_incident_type="PRODUCTS_LIABILITY",
        manufacturer_name="Precision Auto Parts - Home Division",
        product_type="Coffee Maker Model PAP-CM-2023",
        has_property_damage=True,
        property_damage_description="Kitchen fire damage: upper cabinets, countertop, and electrical outlet. Smoke damage to adjacent areas.",
        property_damage_owner="Patricia Williams",
        property_damage_value=8500.00,
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create injured person
    logger.info("Creating injured person...")
    injured_person_data = create_injured_person_for_claim(
        claim_id=claim_number,
        name="Patricia Williams",
        person_type="CUSTOMER",
        contact_info="<EMAIL>, " + generate_realistic_phone(),
        incident_report_status="FILED",
        incident_location=incident_location,
        incident_description="Consumer was using coffee maker normally when it began sparking and smoking. Sustained burns while attempting to unplug the unit as fire started.",
        age=42,
        report_filer_name="Fire Marshal Tom Anderson",
        report_filer_contact="<EMAIL>, (*************"
    )
    
    injured_person_id = injured_person_data["id"]
    
    # Create injuries
    logger.info("Creating injuries...")
    
    # Hand burns
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Second-degree burns to right hand and forearm from electrical contact and heat exposure",
        injury_type="Electrical/Thermal Burns",
        injury_severity="Moderate",
        medical_treatment_requirements="HOSPITALIZATION",
        treatment_nature="Emergency room treatment, burn care, antibiotics, pain medication",
        medical_provider_name="Riverside General Hospital",
        medical_provider_address="123 Hospital Drive, Riverside, CA 92501",
        estimated_cost=4200.00,
        insurance_billing_status="PENDING"
    )
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Fire department witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Fire Marshal Tom Anderson",
        contact_info="<EMAIL>, (*************",
        statement="Responded to kitchen fire caused by electrical appliance malfunction. Coffee maker showed signs of internal electrical failure. Burn pattern consistent with electrical short circuit. Product appears to be manufacturing defect rather than user error."
    )
    
    # Neighbor witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Robert Kim",
        contact_info="<EMAIL>, (555) 567-8901",
        statement="I heard Patricia screaming and saw smoke coming from her kitchen window. When I got there, she was holding her burned hand and the coffee maker was on fire. She said it just started sparking while she was making her morning coffee."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Fire department investigation confirms electrical malfunction in coffee maker caused fire. Product was 8 months old, used normally, and showed no signs of abuse. Consumer sustained burns attempting to unplug malfunctioning unit. Clear products liability exposure."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Retained product expert to examine coffee maker. Preliminary findings suggest manufacturing defect in electrical wiring. Similar model may have recall potential. Coordinating with insured's risk management team on potential recall implications."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Consumer very cooperative and reasonable. Medical treatment ongoing for burns. Property damage estimate received - $8,500 for kitchen repairs. No indication of product misuse or modification. Strong liability case."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Insured confirms this is first reported incident with this model. Product was manufactured 10 months ago. Checking production records for similar defects. May need to consider broader recall if defect is systemic."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Product Examination",
        description="Retain electrical engineer to examine defective coffee maker and determine cause of failure",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Medical Records Review",
        description="Obtain complete medical records and treatment plan for burn injuries",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Property Damage Assessment",
        description="Inspect kitchen damage and coordinate repair estimates",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Recall Investigation",
        description="Work with insured to investigate potential recall implications for similar products",
        priority="HIGH",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Settlement Negotiation",
        description="Evaluate total exposure and negotiate settlement for BI and property damage",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "BODILY_INJURY", "amount": 12000.00},
        {"type": "PROPERTY_DAMAGE", "amount": 10000.00},
        {"type": "DEFENSE_COST", "amount": 8000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=30000.00,
        reserves=reserves,
        deductible=2500.00,
        coverage_limit=2000000.00
    )
    
    logger.info(f"✅ GL Products Liability scenario completed for claim {claim_number}")
    logger.info(f"   - Product: Coffee Maker Model PAP-CM-2023")
    logger.info(f"   - Injured Person: Patricia Williams (42 years old)")
    logger.info(f"   - Injury: Second-degree electrical/thermal burns")
    logger.info(f"   - Property Damage: $8,500 kitchen fire damage")
    logger.info(f"   - Total Reserves: $30,000 (BI: $12K, PD: $10K, Defense: $8K)")
    logger.info(f"   - Estimated Value: $30,000") 