"""Auto Collision At-Fault Narrative Scenario.

This scenario creates a realistic auto collision claim where the insured driver
is at fault, causing bodily injury and property damage to third parties.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_injured_person_for_claim,
    create_injury_for_injured_person,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
    generate_realistic_address,
)


def populate_scenario() -> None:
    """Create an Auto Collision at-fault scenario.
    
    Scenario: Insured driver rear-ends another vehicle at intersection,
    causing injuries to other driver and damage to both vehicles.
    """
    logger.info("🚗 Creating Auto Collision At-Fault Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["ACME"]["id"]  # Acme Manufacturing Corp
    adjuster_id = created_data["users"]["david_thompson"]["id"]  # Auto Claims Specialist
    supervisor_id = created_data["users"]["jennifer_davis"]["id"]  # Regional Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(15)  # Within last 15 days
    incident_location = "Intersection of Main St and Oak Ave, Industrial District, CA"
    
    # Create the main claim
    logger.info("Creating main auto collision claim...")
    claim_data = create_claim(
        claim_type="AUTO",
        customer_id=customer_id,
        description="Insured driver rear-ended third party vehicle while approaching intersection. Insured was distracted by phone call and failed to notice stopped traffic. Third party driver sustained neck injury. Both vehicles sustained moderate damage.",
        claimant_name="Jennifer Martinez",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="AUTO-ACME-2024-003",
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
        vehicle_vin="1HGBH41JXMN109186",
        vehicle_make="Honda",
        vehicle_model="Accord",
        vehicle_year="2022",
        driver_name="Robert Chen",
        driver_license="CA-********",
        point_of_impact="FRONT",
        incident_type="COLLISION",
        collision_type="REAR_END",
        passenger_count=0,
        has_property_damage=True,
        property_damage_description="Third party 2021 Toyota Camry - rear bumper, trunk, and rear lights damaged. Estimated repair cost $4,500.",
        property_damage_owner="Jennifer Martinez",
        property_damage_value=4500.00
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create injured person (third party driver)
    logger.info("Creating injured person...")
    injured_person_data = create_injured_person_for_claim(
        claim_id=claim_number,
        name="Jennifer Martinez",
        person_type="THIRD_PARTY_DRIVER",
        contact_info="<EMAIL>, " + generate_realistic_phone(),
        incident_report_status="FILED",
        incident_location=incident_location,
        incident_description="Driver of vehicle that was rear-ended at intersection. Was stopped at red light when struck from behind.",
        age=34,
        report_filer_name="Officer Sarah Williams - LAPD",
        report_filer_contact="<EMAIL>, (*************"
    )
    
    injured_person_id = injured_person_data["id"]
    
    # Create injury (whiplash/neck strain)
    logger.info("Creating injuries...")
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Neck strain and whiplash from rear-end collision impact. Experiencing pain, stiffness, and headaches.",
        injury_type="Whiplash/Neck Strain",
        injury_severity="Moderate",
        medical_treatment_requirements="OUTPATIENT",
        treatment_nature="X-ray, MRI, physical therapy, pain medication",
        medical_provider_name="Metro Orthopedic Center",
        medical_provider_address="789 Medical Plaza, Downtown, CA 90210",
        estimated_cost=3200.00,
        insurance_billing_status="PENDING"
    )
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Police officer witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Officer Sarah Williams",
        contact_info="<EMAIL>, (*************",
        statement="Responded to rear-end collision at Main/Oak intersection. Insured driver admitted to being distracted by phone call. Clear fault determination - insured driver failed to maintain safe following distance. No citations issued to third party driver."
    )
    
    # Bystander witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Michael Rodriguez",
        contact_info="<EMAIL>, (*************",
        statement="I was waiting at the bus stop and saw the whole thing. The Honda driver was clearly not paying attention and slammed into the Toyota that was stopped at the red light. The impact was pretty hard."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Police report received. Clear liability - insured driver rear-ended third party at intersection. Insured admits distraction by phone call. Third party driver transported to hospital with neck injury complaints. Both vehicles towed from scene."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Spoke with insured driver Robert Chen. He confirms he was taking a business call and didn't notice traffic had stopped. Accepts full responsibility. No disputes on liability. Vehicle is company fleet vehicle."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Third party Jennifer Martinez contacted. She's experiencing ongoing neck pain and has started physical therapy. Provided medical authorization. Repair estimate for her vehicle received - $4,500 for rear-end damage."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Obtain Police Report",
        description="Request complete police report from LAPD for intersection collision",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Vehicle Damage Assessment",
        description="Inspect both vehicles and obtain repair estimates. Coordinate with preferred shops.",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Medical Records Review",
        description="Obtain and review medical records from Metro Orthopedic Center for third party injury",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Settlement Negotiation",
        description="Evaluate total damages and negotiate settlement with third party",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "BODILY_INJURY", "amount": 8000.00},
        {"type": "PROPERTY_DAMAGE", "amount": 6000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=14000.00,
        reserves=reserves,
        deductible=500.00,
        coverage_limit=100000.00
    )
    
    logger.info(f"✅ Auto Collision At-Fault scenario completed for claim {claim_number}")
    logger.info(f"   - Injured Person: Jennifer Martinez (34 years old, third party)")
    logger.info(f"   - Injury: Whiplash/neck strain (moderate)")
    logger.info(f"   - Property Damage: $4,500 (third party vehicle)")
    logger.info(f"   - Total Reserves: $14,000 (BI: $8,000, PD: $6,000)")
    logger.info(f"   - Estimated Value: $14,000") 