"""Auto Hit and Run Narrative Scenario.

This scenario creates a realistic auto hit and run claim where the insured
vehicle is damaged by an unidentified driver who flees the scene.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_injured_person_for_claim,
    create_injury_for_injured_person,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create an Auto Hit and Run scenario.
    
    Scenario: Insured vehicle struck by unknown driver who fled the scene.
    Insured driver sustained minor injuries, vehicle significantly damaged.
    """
    logger.info("🚗💨 Creating Auto Hit and Run Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["COST"]["id"]  # Coastal Shipping Co
    adjuster_id = created_data["users"]["david_thompson"]["id"]  # Auto Claims Specialist
    supervisor_id = created_data["users"]["jennifer_davis"]["id"]  # Regional Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(8)  # Within last 8 days
    incident_location = "Highway 101 Southbound, Mile Marker 45, near Ventura, CA"
    
    # Create the main claim
    logger.info("Creating main auto hit and run claim...")
    claim_data = create_claim(
        claim_type="AUTO",
        customer_id=customer_id,
        description="Insured vehicle was struck by unknown vehicle that fled the scene. Impact occurred during lane change on Highway 101. Unknown vehicle was dark colored sedan, possibly stolen. Insured driver sustained minor injuries. Significant damage to passenger side of insured vehicle.",
        claimant_name="Coastal Shipping Co",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="AUTO-PAPT-2024-001",
        vehicle_vin="1HGCM82633A123456",
        vehicle_make="Honda",
        vehicle_model="Civic",
        vehicle_year="2021",
        driver_name="Lisa Chen",
        driver_license="CA-********",
        point_of_impact="PASSENGER_SIDE",
        incident_type="COLLISION",
        collision_type="SIDESWIPE",
        passenger_count=0,
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create injured person (insured driver)
    logger.info("Creating injured person...")
    injured_person_data = create_injured_person_for_claim(
        claim_id=claim_number,
        name="Lisa Chen",
        person_type="INSURED_DRIVER",
        contact_info="<EMAIL>, " + generate_realistic_phone(),
        incident_report_status="FILED",
        incident_location=incident_location,
        incident_description="Driver of insured vehicle that was struck by hit-and-run driver during lane change on highway. Sustained minor injuries from impact and airbag deployment.",
        age=29,
        report_filer_name="Officer Mark Johnson - CHP",
        report_filer_contact="<EMAIL>, (*************"
    )
    
    injured_person_id = injured_person_data["id"]
    
    # Create injury (minor injuries from impact)
    logger.info("Creating injuries...")
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Minor neck strain and bruising from seatbelt and airbag deployment during sideswipe collision",
        injury_type="Neck Strain/Bruising",
        injury_severity="Minor",
        medical_treatment_requirements="OUTPATIENT",
        treatment_nature="Emergency room evaluation, X-rays, pain medication, follow-up care",
        medical_provider_name="Ventura County Medical Center",
        medical_provider_address="3291 Loma Vista Road, Ventura, CA 93003",
        estimated_cost=1200.00,
        insurance_billing_status="PENDING"
    )
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # CHP officer witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Officer Mark Johnson",
        contact_info="<EMAIL>, (*************",
        statement="Responded to hit-and-run collision on Highway 101. Insured vehicle showed impact damage consistent with sideswipe. No debris from other vehicle found. Driver was coherent and provided detailed description of fleeing vehicle. BOLO issued for dark sedan with front-end damage."
    )
    
    # Witness driver
    create_witness_for_claim(
        claim_id=claim_number,
        name="Robert Martinez",
        contact_info="<EMAIL>, (555) 456-7890",
        statement="I was driving behind both vehicles when it happened. The dark car came up fast in the left lane and clipped the Honda when it tried to change lanes. The dark car never stopped - just kept going. I got a partial plate number: 8G something, couldn't see the rest."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Hit-and-run collision on Highway 101. CHP report filed, BOLO issued for dark sedan. Insured driver transported to hospital with minor injuries. Vehicle towed to preferred shop for damage assessment. Uninsured motorist coverage applicable."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Spoke with insured driver Lisa Chen. She was changing lanes when struck by speeding vehicle. Other driver never stopped or slowed down. No contact information available. Witness provided partial plate number. Police investigation ongoing."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Vehicle damage assessment completed. Significant passenger side damage including door, quarter panel, and mirror. Airbag deployed. Repair estimate $8,500. Vehicle driveable but unsafe. Rental car authorized."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="CHP investigation update: No matching vehicles found in database. Likely stolen vehicle or false plates. Case remains open but recovery unlikely. Processing under uninsured motorist coverage."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Police Investigation Follow-up",
        description="Follow up with CHP on hit-and-run investigation and any leads on fleeing vehicle",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Vehicle Damage Assessment",
        description="Complete damage assessment and obtain repair estimates for passenger side damage",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Medical Records Review",
        description="Obtain medical records from Ventura County Medical Center for driver injuries",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Uninsured Motorist Processing",
        description="Process claim under uninsured motorist coverage due to hit-and-run",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Rental Car Coordination",
        description="Coordinate rental car for insured while vehicle is being repaired",
        priority="LOW",
        assignee_id=adjuster_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "BODILY_INJURY", "amount": 3000.00},
        {"type": "PROPERTY_DAMAGE", "amount": 9000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=12000.00,
        reserves=reserves,
        deductible=500.00,
        coverage_limit=100000.00
    )
    
    logger.info(f"✅ Auto Hit and Run scenario completed for claim {claim_number}")
    logger.info(f"   - Injured Person: Lisa Chen (29 years old, insured driver)")
    logger.info(f"   - Injury: Minor neck strain and bruising")
    logger.info(f"   - Vehicle Damage: $8,500 passenger side damage")
    logger.info(f"   - Total Reserves: $12,000 (BI: $3,000, PD: $9,000)")
    logger.info(f"   - Estimated Value: $12,000") 