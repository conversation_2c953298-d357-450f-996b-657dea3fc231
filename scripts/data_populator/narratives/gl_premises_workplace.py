"""General Liability Premises Workplace Injury Narrative Scenario.

This scenario creates a realistic premises liability claim where a contractor
is injured at a construction site due to unsafe conditions.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_injured_person_for_claim,
    create_injury_for_injured_person,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create a GL Premises Workplace Injury scenario.
    
    Scenario: Contractor falls through unsafe flooring at construction site,
    resulting in serious injuries and potential OSHA violations.
    """
    logger.info("🏗️ Creating GL Premises Workplace Injury Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["ACME"]["id"]  # Acme Manufacturing Corp (also does construction)
    adjuster_id = created_data["users"]["sarah_johnson"]["id"]  # Senior Claims Adjuster
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(10)  # Within last 10 days
    incident_location = "Acme Construction Site - Building 3, 2200 Industrial Way, Commerce, CA"
    
    # Create the main claim
    logger.info("Creating main GL premises workplace injury claim...")
    claim_data = create_claim(
        claim_type="GENERAL_LIABILITY",
        customer_id=customer_id,
        description="Subcontractor fell through temporary flooring on second floor of construction site. Flooring was not properly secured and lacked adequate support. Worker sustained multiple fractures and head injury. OSHA investigation pending. Site safety protocols under review.",
        claimant_name="Miguel Rodriguez",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="GL-METR-2024-002",
        gl_incident_type="PREMISES_LIABILITY",
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>"
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create injured person
    logger.info("Creating injured person...")
    injured_person_data = create_injured_person_for_claim(
        claim_id=claim_number,
        name="Miguel Rodriguez",
        person_type="CONTRACTOR",
        contact_info="<EMAIL>, " + generate_realistic_phone(),
        incident_report_status="FILED",
        incident_location=incident_location,
        incident_description="Subcontractor was walking across temporary flooring on second floor when boards gave way. Fell approximately 12 feet to concrete below.",
        age=38,
        report_filer_name="Site Supervisor - James Wilson",
        report_filer_contact="<EMAIL>, (*************"
    )
    
    injured_person_id = injured_person_data["id"]
    
    # Create injuries
    logger.info("Creating injuries...")
    
    # Multiple fractures
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Multiple fractures: broken left leg (tibia and fibula), fractured ribs, and wrist fracture from fall through flooring",
        injury_type="Multiple Fractures",
        injury_severity="Severe",
        medical_treatment_requirements="HOSPITALIZATION",
        treatment_nature="Emergency surgery, orthopedic repair, pain management, physical therapy",
        medical_provider_name="Commerce General Hospital",
        medical_provider_address="1200 Medical Center Drive, Commerce, CA 90040",
        estimated_cost=45000.00,
        insurance_billing_status="PENDING"
    )
    
    # Head injury
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Concussion and head trauma from impact with concrete floor",
        injury_type="Head Trauma/Concussion",
        injury_severity="Moderate",
        medical_treatment_requirements="HOSPITALIZATION",
        treatment_nature="CT scan, neurological monitoring, concussion protocol",
        medical_provider_name="Commerce General Hospital",
        medical_provider_address="1200 Medical Center Drive, Commerce, CA 90040",
        estimated_cost=8500.00,
        insurance_billing_status="PENDING"
    )
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Site supervisor witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="James Wilson",
        contact_info="<EMAIL>, (*************",
        statement="I was on the first floor when I heard the crash. The temporary flooring on the second floor wasn't properly secured - we had been waiting for additional support beams. Miguel was supposed to stay off that area but may not have gotten the message. This is a serious safety violation."
    )
    
    # Co-worker witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Carlos Mendez",
        contact_info="<EMAIL>, (555) 789-0123",
        statement="I work with Miguel. We were told the second floor was safe to walk on. The boards looked secure from below. There were no warning signs or barriers. Miguel is very safety-conscious - he wouldn't have gone up there if he thought it was dangerous."
    )
    
    # OSHA inspector witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Inspector Sarah Chen",
        contact_info="<EMAIL>, (555) 987-6543",
        statement="Preliminary investigation shows inadequate temporary flooring support and lack of proper safety barriers. Site safety protocols appear insufficient. Full OSHA investigation ongoing with potential citations pending."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Serious workplace injury at construction site. Contractor fell through unsecured temporary flooring. Multiple fractures and head trauma. OSHA investigation initiated immediately. Site shut down pending safety review."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Injured worker transported to Commerce General Hospital. Surgery required for leg fractures. Neurological evaluation for head trauma. Worker's compensation claim also filed. Coordination with WC carrier required."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Site safety review reveals multiple violations. Temporary flooring not properly secured, inadequate support beams, missing safety barriers. Safety manager terminated. New protocols being implemented."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="OSHA investigation ongoing. Potential citations for willful safety violations. Criminal referral possible. Media attention likely. Coordinating with legal counsel and risk management team."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="OSHA Coordination",
        description="Coordinate with OSHA investigation and respond to information requests",
        priority="HIGH",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Medical Records Review",
        description="Obtain complete medical records and treatment plan from Commerce General Hospital",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Site Safety Investigation",
        description="Retain safety expert to investigate site conditions and safety protocol failures",
        priority="HIGH",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Legal Counsel Coordination",
        description="Coordinate with legal counsel on potential criminal and regulatory exposure",
        priority="HIGH",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Workers Comp Coordination",
        description="Coordinate with workers compensation carrier to avoid duplicate coverage",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "BODILY_INJURY", "amount": 75000.00},
        {"type": "DEFENSE_COST", "amount": 25000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=100000.00,
        reserves=reserves,
        deductible=5000.00,
        coverage_limit=2000000.00
    )
    
    logger.info(f"✅ GL Premises Workplace Injury scenario completed for claim {claim_number}")
    logger.info(f"   - Injured Person: Miguel Rodriguez (38 years old, contractor)")
    logger.info(f"   - Injuries: Multiple fractures, head trauma")
    logger.info(f"   - Incident: Fall through unsafe temporary flooring")
    logger.info(f"   - Total Reserves: $100,000 (BI: $75K, Defense: $25K)")
    logger.info(f"   - Estimated Value: $100,000") 