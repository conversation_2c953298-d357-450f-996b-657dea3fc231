"""Property Fire Damage Narrative Scenario.

This scenario creates a realistic commercial property fire claim with
significant structural and inventory damage.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create a Property Fire Damage scenario.
    
    Scenario: Electrical fire in warehouse causes significant structural
    damage and inventory loss at manufacturing facility.
    """
    logger.info("🔥 Creating Property Fire Damage Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["ACME"]["id"]  # Acme Manufacturing Corp
    adjuster_id = created_data["users"]["lisa_anderson"]["id"]  # Property Claims Specialist
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(7)  # Within last 7 days
    incident_location = "Acme Manufacturing Warehouse B, 1500 Industrial Blvd, Commerce, CA"
    
    # Create the main claim
    logger.info("Creating main property fire claim...")
    claim_data = create_claim(
        claim_type="PROPERTY",
        customer_id=customer_id,
        description="Electrical fire originated in main electrical panel of Warehouse B during overnight hours. Fire spread to storage areas causing significant structural damage and inventory loss. Sprinkler system activated but fire department response required. No injuries reported.",
        claimant_name="Acme Manufacturing Corp",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="PROP-DTRG-2024-002",
        property_type="WAREHOUSE",
        property_address="1500 Industrial Blvd, Commerce, CA 90040",
        damage_type="FIRE",
        estimated_value=185000.00,
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>"
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Security guard witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Carlos Martinez",
        contact_info="<EMAIL>, (*************",
        statement="I was doing my 2 AM rounds when I noticed smoke coming from Warehouse B. I immediately called 911 and activated the fire alarm. The fire appeared to be coming from the electrical room area. I evacuated the building and waited for fire department."
    )
    
    # Fire Chief witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Chief Robert Thompson",
        contact_info="<EMAIL>, (*************",
        statement="Responded to structure fire at 2:15 AM. Fire originated in electrical panel room and spread to adjacent storage areas. Sprinkler system helped contain spread but significant damage to structure and contents. Preliminary investigation suggests electrical malfunction."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Fire department report received. Fire originated in main electrical panel due to apparent electrical malfunction. Significant damage to 40% of warehouse structure and approximately $120,000 in inventory loss. Building currently uninhabitable pending repairs."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Met with insured facility manager. Confirmed no recent electrical work or known issues. Last electrical inspection was 6 months ago with no violations. Security footage shows fire started around 1:45 AM with no human activity in the area."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Preliminary damage assessment completed. Structural damage includes roof section, electrical systems, HVAC, and flooring. Inventory loss includes raw materials and finished goods. Business interruption expected for 3-4 months during reconstruction."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Electrical contractor inspection scheduled. Need to determine if electrical panel defect was manufacturing issue or installation problem. May have subrogation potential against electrical contractor or panel manufacturer."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Fire Department Investigation",
        description="Obtain complete fire department investigation report and cause determination",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Structural Engineering Assessment",
        description="Engage structural engineer to assess building integrity and repair requirements",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Inventory Valuation",
        description="Work with insured to document and value damaged inventory and raw materials",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Electrical Investigation",
        description="Retain electrical expert to investigate cause and determine subrogation potential",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Business Interruption Analysis",
        description="Evaluate business interruption exposure and coordinate with BI specialist",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "PROPERTY_DAMAGE", "amount": 150000.00},
        {"type": "BUSINESS_INTERRUPTION", "amount": 35000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=185000.00,
        reserves=reserves,
        deductible=10000.00,
        coverage_limit=2000000.00
    )
    
    logger.info(f"✅ Property Fire Damage scenario completed for claim {claim_number}")
    logger.info(f"   - Property: Acme Manufacturing Warehouse B")
    logger.info(f"   - Cause: Electrical panel malfunction")
    logger.info(f"   - Damage: Structural + $120K inventory loss")
    logger.info(f"   - Total Reserves: $185,000 (PD: $150K, BI: $35K)")
    logger.info(f"   - Estimated Value: $185,000") 