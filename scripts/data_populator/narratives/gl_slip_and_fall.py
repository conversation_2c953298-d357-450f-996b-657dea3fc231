"""General Liability Slip and Fall Narrative Scenario.

This scenario creates a realistic slip and fall claim at a retail store,
including injured persons, injuries, witnesses, notes, tasks, and financials.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_injured_person_for_claim,
    create_injury_for_injured_person,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_description,
    generate_realistic_phone,
    generate_realistic_address,
)


def populate_scenario() -> None:
    """Create a General Liability slip and fall scenario.
    
    Scenario: Customer slips on wet floor at Sunshine Retail Group store,
    sustains minor injuries, store has liability concerns.
    """
    logger.info("🎬 Creating GL Slip and Fall Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["SUNR"]["id"]  # Sunshine Retail Group
    adjuster_id = created_data["users"]["sarah_johnson"]["id"]  # Senior Claims Adjuster
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(30)  # Within last 30 days
    incident_location = "Sunshine Retail Group - Downtown Store, Aisle 7 near produce section"
    
    # Create the main claim
    logger.info("Creating main GL claim...")
    claim_data = create_claim(
        claim_type="GENERAL_LIABILITY",
        customer_id=customer_id,
        description="Customer slip and fall on wet floor in produce section. Customer sustained minor injuries to knee and back. Store employee was mopping nearby but no wet floor signs were visible.",
        claimant_name="Margaret Thompson",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="GL-SUNR-2024-001",
        gl_incident_type="PREMISES_LIABILITY",
        owner_name="Sunshine Retail Group",
        owner_address="123 Main Street, Downtown, CA 90210",
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create injured person
    logger.info("Creating injured person...")
    injured_person_data = create_injured_person_for_claim(
        claim_id=claim_number,
        name="Margaret Thompson",
        person_type="PATRON",
        contact_info="<EMAIL>, " + generate_realistic_phone(),
        incident_report_status="FILED",
        incident_location=incident_location,
        incident_description="Customer was walking through produce section when she slipped on wet floor. Employee was mopping nearby but no warning signs were posted.",
        age=58,
        report_filer_name="Store Manager - David Chen",
        report_filer_contact="<EMAIL>, (*************"
    )
    
    injured_person_id = injured_person_data["id"]
    
    # Create injuries
    logger.info("Creating injuries...")
    
    # Knee injury
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Twisted right knee during fall, experiencing pain and swelling",
        injury_type="Knee Sprain",
        injury_severity="Moderate",
        medical_treatment_requirements="OUTPATIENT",
        treatment_nature="X-ray, physical examination, anti-inflammatory medication",
        medical_provider_name="Downtown Urgent Care",
        medical_provider_address="456 Health Ave, Downtown, CA 90210",
        estimated_cost=850.00,
        insurance_billing_status="PENDING"
    )
    
    # Back injury
    create_injury_for_injured_person(
        claim_id=claim_number,
        person_id=injured_person_id,
        injury_description="Lower back strain from impact with floor",
        injury_type="Back Strain",
        injury_severity="Minor",
        medical_treatment_requirements="PHYSICAL_THERAPY",
        treatment_nature="Physical therapy sessions, muscle relaxants",
        medical_provider_name="Downtown Physical Therapy",
        medical_provider_address="789 Wellness Blvd, Downtown, CA 90210",
        estimated_cost=1200.00,
        insurance_billing_status="NOT_SUBMITTED"
    )
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Store employee witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="James Rodriguez",
        contact_info="<EMAIL>, (*************",
        statement="I was mopping the floor in the produce section when I heard the customer fall. I immediately went to help her. I admit I had not yet put up the wet floor signs as I was still in the process of cleaning.",
        statement_date=(incident_date + timedelta(hours=2)).strftime("%Y-%m-%d"),
        witness_type="EMPLOYEE"
    )
    
    # Customer witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Robert Kim",
        contact_info="<EMAIL>, (*************",
        statement="I was shopping nearby and saw the lady slip and fall. The floor was definitely wet and I didn't see any warning signs. The employee with the mop was about 10 feet away.",
        statement_date=(incident_date + timedelta(days=1)).strftime("%Y-%m-%d"),
        witness_type="CUSTOMER"
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Initial report received. Customer slipped on wet floor in produce section. Store employee was mopping but no wet floor signs were posted. Customer sustained knee and back injuries. Liability appears clear - failure to post warning signs."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Spoke with store manager David Chen. He confirmed employee was mopping and admitted wet floor signs were not yet posted. Store has security camera footage of incident. Requesting copy for review."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Reviewed security footage. Clearly shows customer slip and fall. Employee visible mopping nearby with no warning signs. Customer appears to be walking normally before fall. Footage supports liability."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Obtain Security Camera Footage",
        description="Request and review security camera footage from store showing the slip and fall incident",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Review Medical Records",
        description="Obtain and review medical records from Downtown Urgent Care and Physical Therapy",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Settlement Evaluation",
        description="Evaluate settlement options based on medical costs and liability assessment",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "BODILY_INJURY", "amount": 15000.00},
        {"type": "DEFENSE_COST", "amount": 5000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=20000.00,
        reserves=reserves,
        deductible=1000.00,
        coverage_limit=1000000.00
    )
    
    logger.info(f"✅ GL Slip and Fall scenario completed for claim {claim_number}")
    logger.info(f"   - Injured Person: Margaret Thompson (58 years old)")
    logger.info(f"   - Injuries: Knee sprain (moderate), Back strain (minor)")
    logger.info(f"   - Witnesses: Store employee, Customer witness")
    logger.info(f"   - Total Reserves: $20,000 (BI: $15,000, Defense: $5,000)")
    logger.info(f"   - Estimated Value: $20,000")


if __name__ == "__main__":
    populate_scenario() 