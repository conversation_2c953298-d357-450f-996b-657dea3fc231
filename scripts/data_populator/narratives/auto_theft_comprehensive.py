"""Auto Theft Comprehensive Coverage Narrative Scenario.

This scenario creates a realistic auto theft claim where a company delivery
vehicle and its cargo are stolen, covered under comprehensive coverage.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create an Auto Theft Comprehensive scenario.
    
    Scenario: Company delivery van with valuable cargo stolen from
    parking lot during delivery stop.
    """
    logger.info("🚚 Creating Auto Theft Comprehensive Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["COST"]["id"]  # Coastal Shipping Co
    adjuster_id = created_data["users"]["david_thompson"]["id"]  # Auto Claims Specialist
    supervisor_id = created_data["users"]["jennifer_davis"]["id"]  # Regional Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(5)  # Within last 5 days
    incident_location = "Metro Shopping Center, 2500 Commerce Way, Los Angeles, CA"
    
    # Create the main claim
    logger.info("Creating main auto theft claim...")
    claim_data = create_claim(
        claim_type="AUTO",
        customer_id=customer_id,
        description="Company delivery van stolen from shopping center parking lot during routine delivery. Vehicle contained approximately $15,000 worth of electronics cargo. Driver was inside making delivery for 20 minutes. Vehicle was locked but running with keys inside per company protocol.",
        claimant_name="Coastal Shipping Co",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="AUTO-COST-2024-007",
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
        vehicle_vin="1FTFW1ET5DFC10312",
        vehicle_make="Ford",
        vehicle_model="Transit",
        vehicle_year="2023",
        driver_name="Miguel Santos",
        driver_license="CA-********",
        incident_type="THEFT",
        cargo_theft=True,
        cargo_description="Electronics shipment: 25 tablets, 15 laptops, 30 smartphones - total value approximately $15,000"
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Security guard witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Officer Maria Rodriguez",
        contact_info="<EMAIL>, (*************",
        statement="Responded to vehicle theft report. Driver states he left vehicle running while making delivery per company policy. Security cameras show two suspects approaching vehicle at 2:47 PM and driving away. Vehicle was reported stolen within 30 minutes of theft."
    )
    
    # Store manager witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Janet Wilson",
        contact_info="<EMAIL>, (*************",
        statement="The delivery driver was in our store for about 20 minutes completing the delivery. He mentioned he left the van running because it was a quick stop. I saw him run out when he realized the van was gone. Our security cameras captured the theft."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Police report filed. Vehicle theft occurred during routine delivery stop. Driver followed company protocol by leaving vehicle running for quick delivery. Security footage confirms theft by two unknown suspects. Vehicle and cargo total loss expected."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Spoke with driver Miguel Santos. He confirms following company delivery protocol. This was his regular route and he had made similar deliveries without incident. No personal items of value were in vehicle. Company GPS tracking shows vehicle movement stopped 15 miles from theft location."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Contacted LAPD auto theft division. Vehicle has not been recovered. Given cargo value and time elapsed, likely total loss. Cargo manifest confirms $15,000 in electronics. Reviewing company delivery protocols for potential policy violations."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Security footage obtained from shopping center. Clear images of suspects and theft in progress. Footage provided to police for investigation. No apparent negligence by driver - followed established company procedures."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Police Investigation Follow-up",
        description="Coordinate with LAPD auto theft division on recovery efforts and investigation status",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Vehicle Valuation",
        description="Obtain current market value for 2023 Ford Transit delivery van",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Cargo Documentation",
        description="Verify cargo manifest and obtain invoices for stolen electronics shipment",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Policy Review",
        description="Review company delivery protocols and policy compliance for coverage determination",
        priority="MEDIUM",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Settlement Processing",
        description="Process total loss settlement for vehicle and cargo once investigation complete",
        priority="LOW",
        assignee_id=adjuster_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves (vehicle + cargo)
    reserves = [
        {"type": "PROPERTY_DAMAGE", "amount": 45000.00}  # Vehicle $30K + Cargo $15K
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=45000.00,
        reserves=reserves,
        deductible=1000.00,
        coverage_limit=100000.00
    )
    
    logger.info(f"✅ Auto Theft Comprehensive scenario completed for claim {claim_number}")
    logger.info(f"   - Vehicle: 2023 Ford Transit delivery van")
    logger.info(f"   - Cargo: $15,000 electronics shipment")
    logger.info(f"   - Total Loss: Vehicle + cargo theft")
    logger.info(f"   - Total Reserves: $45,000 (Vehicle: $30K, Cargo: $15K)")
    logger.info(f"   - Estimated Value: $45,000") 