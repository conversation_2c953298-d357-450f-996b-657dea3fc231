"""Property Water Damage Narrative Scenario.

This scenario creates a realistic commercial property water damage claim
with business interruption exposure at a restaurant.
"""

import sys
from datetime import datetime, timedelta
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from core_utils import (
    logger,
    created_data,
    create_claim,
    create_claim_financials,
    create_witness_for_claim,
    create_note_for_claim,
    create_task_for_claim,
    generate_realistic_date_range,
    generate_realistic_phone,
)


def populate_scenario() -> None:
    """Create a Property Water Damage scenario.
    
    Scenario: Burst pipe in restaurant causes significant water damage
    and business interruption during peak season.
    """
    logger.info("💧 Creating Property Water Damage Narrative Scenario")
    
    # Get customer and user data
    customer_id = created_data["customers"]["DTRG"]["id"]  # Downtown Restaurant Group
    adjuster_id = created_data["users"]["lisa_anderson"]["id"]  # Property Claims Specialist
    supervisor_id = created_data["users"]["robert_wilson"]["id"]  # Claims Manager
    
    # Generate incident details
    incident_date = generate_realistic_date_range(3)  # Within last 3 days
    incident_location = "Downtown Bistro, 789 Main Street, Downtown, CA 90210"
    
    # Create the main claim
    logger.info("Creating main property water damage claim...")
    claim_data = create_claim(
        claim_type="PROPERTY",
        customer_id=customer_id,
        description="Main water supply line burst in restaurant kitchen during dinner service. Water flooded kitchen, dining room, and basement storage areas. Restaurant forced to close immediately. Significant damage to equipment, flooring, and inventory. Peak holiday season timing increases business interruption exposure.",
        claimant_name="Downtown Restaurant Group",
        claimant_email="<EMAIL>",
        claimant_phone=generate_realistic_phone(),
        incident_date=incident_date.strftime("%Y-%m-%d"),
        incident_location=incident_location,
        jurisdiction="CA",
        assigned_to=adjuster_id,
        supervisor=supervisor_id,
        policy_number="PROP-SUNR-2024-003",
        property_type="RESTAURANT",
        property_address="789 Main Street, Downtown, CA 90210",
        damage_type="WATER",
        estimated_value=95000.00,
        reporter_phone=generate_realistic_phone(),
        reporter_email="<EMAIL>",
    )
    
    claim_id = claim_data["id"]
    claim_number = claim_data["claim_number"]
    
    # Create witnesses
    logger.info("Creating witnesses...")
    
    # Restaurant manager witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Maria Gonzalez",
        contact_info="<EMAIL>, (*************",
        statement="I was in the kitchen when we heard a loud bang and water started gushing from the wall behind the prep station. Within minutes, the entire kitchen was flooded. We had to evacuate all customers immediately. The water was coming out so fast we couldn't stop it."
    )
    
    # Plumber witness
    create_witness_for_claim(
        claim_id=claim_number,
        name="Tom Rodriguez",
        contact_info="<EMAIL>, (*************",
        statement="Responded to emergency call. Main 4-inch supply line had burst due to age and corrosion. Line was original to building construction 25 years ago. Failure was sudden and catastrophic. No indication of negligent maintenance - just normal wear and aging."
    )
    
    # Create claim notes
    logger.info("Creating claim notes...")
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Emergency response completed. Main water line burst during dinner service causing immediate flooding. Restaurant evacuated and closed. Plumber confirms pipe failure due to age/corrosion, not negligence. Water extraction and drying equipment deployed immediately."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Damage assessment in progress. Kitchen equipment, flooring, and basement storage significantly damaged. Food inventory total loss due to contamination. Dining room furniture and fixtures affected. Estimated 2-3 weeks closure for repairs during peak holiday season."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Business interruption exposure significant due to timing. December is restaurant's highest revenue month. Daily revenue averages $8,000 during holiday season. Coordinating with BI specialist for detailed analysis of lost income and extra expenses."
    )
    
    create_note_for_claim(
        claim_id=claim_number,
        content="Insured very cooperative. Provided financial records for BI analysis. No coverage disputes anticipated. Working with preferred contractors for emergency repairs to minimize business interruption period. Temporary kitchen setup being explored."
    )
    
    # Create tasks
    logger.info("Creating tasks...")
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Water Damage Mitigation",
        description="Coordinate emergency water extraction, drying, and mold prevention measures",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Equipment Assessment",
        description="Evaluate kitchen equipment damage and determine repair vs replacement costs",
        priority="HIGH",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Business Interruption Analysis",
        description="Analyze financial records and calculate lost income during closure period",
        priority="HIGH",
        assignee_id=supervisor_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Contractor Coordination",
        description="Coordinate with preferred contractors for expedited repairs to minimize BI exposure",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    create_task_for_claim(
        claim_id=claim_number,
        title="Inventory Documentation",
        description="Document and value damaged food inventory and supplies",
        priority="MEDIUM",
        assignee_id=adjuster_id
    )
    
    # Create financial details
    logger.info("Creating financial details...")
    
    # Set up initial reserves
    reserves = [
        {"type": "PROPERTY_DAMAGE", "amount": 65000.00},
        {"type": "BUSINESS_INTERRUPTION", "amount": 30000.00}
    ]
    
    create_claim_financials(
        claim_id=claim_number,
        estimated_value=95000.00,
        reserves=reserves,
        deductible=5000.00,
        coverage_limit=1000000.00
    )
    
    logger.info(f"✅ Property Water Damage scenario completed for claim {claim_number}")
    logger.info(f"   - Property: Downtown Bistro restaurant")
    logger.info(f"   - Cause: Burst main water supply line")
    logger.info(f"   - Damage: Kitchen, dining room, basement flooding")
    logger.info(f"   - Business Impact: 2-3 week closure during peak season")
    logger.info(f"   - Total Reserves: $95,000 (PD: $65K, BI: $30K)")
    logger.info(f"   - Estimated Value: $95,000") 