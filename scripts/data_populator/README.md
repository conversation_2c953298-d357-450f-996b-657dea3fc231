# Claimentine Data Populator

A comprehensive data population system that creates realistic sample data for the Claimentine claims management system.

## Overview

The Data Populator is designed to create a complete, realistic claims management environment for development, testing, and demonstration purposes. It follows a selective code duplication approach rather than running full backend setup and cleanup, ensuring clean and controlled data creation.

## Architecture

The system is organized into four main phases:

### Phase 1: Database Reset and Essential Setup
- Resets the database to a clean state
- Creates essential system configuration (permissions, authority levels, reserve configs)
- Sets up a controlled admin user for CLI authentication

### Phase 2: Common Users and Customers
- Creates realistic user accounts with different roles and authority levels
- Creates diverse customer companies representing various industries
- Authenticates CLI for subsequent operations

### Phase 3: Narrative Scenarios
- Executes realistic claim scenarios across all major claim types
- Creates interconnected data (claims, financials, tasks, notes, etc.)
- Follows realistic business workflows and timelines

### Phase 4: Validation and Reporting
- Validates data integrity and relationships
- Generates summary reports of created entities
- Provides verification of successful population

## File Structure

```
data_populator/
├── main.py              # Main orchestrator script
├── core_utils.py        # Core utilities and entity creation functions
├── narratives/          # Individual claim scenario implementations
│   ├── auto_collision_at_fault.py
│   ├── auto_hit_and_run.py
│   ├── auto_theft_comprehensive.py
│   ├── gl_premises_workplace.py
│   ├── gl_products_liability.py
│   ├── gl_slip_and_fall.py
│   ├── property_fire_damage.py
│   ├── property_vandalism.py
│   └── property_water_damage.py
├── README.md            # This file
├── USERS.md             # User accounts reference
├── NARRATIVES.md        # Narrative scenarios description
└── ARCHITECTURE.md      # Technical implementation details
```

## Usage

### Prerequisites

1. **Backend Running**: Ensure the Claimentine backend is running
   ```bash
   # From project root
   make backend-start
   ```

2. **Database Access**: PostgreSQL database must be accessible and configured

3. **CLI Functional**: Claimentine CLI must be installed and working
   ```bash
   # Test CLI functionality
   cd cli
   poetry run clm --help
   ```

4. **Dependencies**: Ensure all dependencies are installed
   ```bash
   # Backend dependencies (includes Faker)
   cd backend
   poetry install
   
   # CLI dependencies
   cd cli
   poetry install
   ```

### Running the Data Populator

```bash
# From the project root directory
cd scripts/data_populator
python main.py
```

### Expected Output

The script will log its progress through each phase:

```
🎯 Starting Claimentine Data Populator
============================================================
📋 Phase 1: Database Reset and Essential Setup
✅ Database reset completed
✅ Essential configuration inserted
✅ Controlled superuser created

📋 Phase 2: Creating Common Users and Customers
✅ CLI authentication successful
✅ Created user: Sarah Johnson (<EMAIL>) - ADJUSTER
✅ Created user: Michael Chen (<EMAIL>) - ADJUSTER
...
✅ Created customer: Acme Manufacturing Corp (ACME)
...

📋 Phase 3: Executing Narrative Scenarios
✅ Executing GL Slip and Fall scenario
✅ Executing Auto Collision At-Fault scenario
...

📋 Phase 4: Final Validation and Reporting
📊 Summary of created entities:
  - users: 8 created
  - customers: 8 created
  - claims: 9 created
  - notes: 27 created
  - tasks: 18 created

🎉 Data population completed successfully!
```

## What Gets Created

### System Configuration
- **Permissions**: All essential system permissions
- **Authority Levels**: BASIC, INTERMEDIATE, SENIOR, SUPERVISOR, MANAGER, ADMIN
- **Reserve Configurations**: Standard reserve types for all claim types
- **Admin User**: `<EMAIL>` / `admin` for system administration

### User Accounts
See [USERS.md](USERS.md) for complete details.

**Summary:**
- 1 Admin user (for system access)
- 5 Adjusters (various specializations and authority levels)
- 2 Managers (different authority levels)

### Customer Companies
- **Acme Manufacturing Corp** (ACME) - Industrial equipment manufacturing
- **Sunshine Retail Group** (SUNR) - Regional retail chain
- **Metro Construction LLC** (METR) - Commercial construction
- **TechStart Solutions** (TECH) - Technology startup
- **Green Valley Farms** (GVFM) - Agricultural business
- **Coastal Shipping Co** (COST) - Maritime shipping
- **Downtown Restaurant Group** (DTRG) - Restaurant chain
- **Precision Auto Parts** (PAPT) - Automotive parts

### Claim Scenarios
See [NARRATIVES.md](NARRATIVES.md) for complete details.

**Summary:**
- 3 Auto claims (collision, hit-and-run, theft)
- 3 Property claims (fire, water damage, vandalism)
- 3 General Liability claims (slip-and-fall, workplace injury, products liability)

Each scenario includes:
- Complete claim details with realistic data
- Financial records (reserves, payments)
- Tasks and workflow items
- Notes and documentation
- Related entities (injured persons, witnesses, attorneys where applicable)

## Safety and Error Handling

### Database Safety
- **Complete Reset**: Database is fully reset before population
- **Transaction Safety**: Operations are designed to be atomic where possible
- **Validation**: Data integrity is validated throughout the process

### Error Handling
- **Phase Isolation**: Errors in one phase don't affect others
- **Detailed Logging**: Comprehensive logging for troubleshooting
- **Graceful Failures**: Clear error messages and cleanup on failure

### Recovery
If the script fails:
1. Check the logs for specific error messages
2. Ensure all prerequisites are met
3. Verify backend and database connectivity
4. Re-run the script (it will reset the database automatically)

## Customization

### Adding New Users
Edit the `create_common_users()` function in `core_utils.py`:

```python
# Add to the adjusters or managers list
adjusters = [
    # ... existing users ...
    ("New", "User", "ADJUSTER", "INTERMEDIATE", "Claims", "Claims Adjuster"),
]
```

### Adding New Customers
Edit the `create_common_customers()` function in `core_utils.py`:

```python
customers = [
    # ... existing customers ...
    ("New Company Name", "NEWC", "Company description"),
]
```

### Adding New Narratives
1. Create a new file in `narratives/` following the existing pattern
2. Implement a `populate_scenario()` function
3. Add the scenario to the list in `main.py`

## Technical Details

For technical implementation details, see [ARCHITECTURE.md](ARCHITECTURE.md).

## Troubleshooting

### Common Issues

**"CLI authentication failed"**
- Ensure the backend is running
- Check database connectivity
- Verify admin user was created successfully

**"Command not found: poetry"**
- Ensure Poetry is installed and in PATH
- Try running from the correct directory

**"Database connection failed"**
- Check PostgreSQL is running
- Verify database configuration in backend
- Ensure database exists and is accessible

**"Permission denied"**
- Check file permissions on script files
- Ensure you're running from the correct directory
- Verify Python environment has necessary packages

### Getting Help

1. Check the logs for detailed error messages
2. Verify all prerequisites are met
3. Ensure the backend and CLI are working independently
4. Review the [Technical Architecture](ARCHITECTURE.md) for implementation details

## Contributing

When adding new scenarios or modifying the data populator:

1. Follow the existing patterns and naming conventions
2. Add appropriate logging and error handling
3. Update documentation to reflect changes
4. Test thoroughly with a clean database
5. Ensure backward compatibility where possible 