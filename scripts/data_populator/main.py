#!/usr/bin/env python3
"""Main orchestrator for the Claimentine data populator.

This script coordinates the complete data population process:
1. Database reset and essential setup
2. Creation of common users and customers
3. Discovery and execution of narrative scenarios
4. Final validation and reporting

⚠️  WARNING: This script performs a COMPLETE DATABASE RESET! ⚠️
All existing data will be permanently deleted and replaced with sample data.
Only run this script in development/testing environments.

Usage:
    python scripts/data_populator/main.py
"""

# SAFETY VARIABLE - Must be set to True to allow execution
# This prevents accidental runs that would destroy all database data
ALLOW_EXECUTION = True  # Set to False to disable script execution

import logging
import os
import sys
from pathlib import Path

# Add the script directory to Python path for imports
script_dir = Path(__file__).parent
sys.path.insert(0, str(script_dir))

from core_utils import (
    db_reset_and_essential_setup, 
    created_data, 
    logger,
    authenticate_cli,
    create_common_users,
    create_common_customers
)

def check_execution_safety():
    """Check if script execution is allowed and warn about destructive nature."""
    if not ALLOW_EXECUTION:
        logger.error("🚫 SCRIPT EXECUTION DISABLED")
        logger.error("=" * 60)
        logger.error("This script is currently disabled for safety.")
        logger.error("To enable execution, set ALLOW_EXECUTION = True in main.py")
        logger.error("")
        logger.error("⚠️  WARNING: This script will DESTROY ALL DATABASE DATA!")
        logger.error("Only enable if you want to reset the database with sample data.")
        logger.error("=" * 60)
        sys.exit(1)
    
    # Show warning even when enabled
    logger.warning("⚠️  DATABASE RESET WARNING ⚠️")
    logger.warning("=" * 60)
    logger.warning("This script will COMPLETELY RESET the database!")
    logger.warning("ALL existing data will be permanently deleted.")
    logger.warning("Only proceed if this is a development/testing environment.")
    logger.warning("=" * 60)
    
    # Give user a chance to abort
    try:
        response = input("\nDo you want to continue? Type 'YES' to proceed: ")
        if response != "YES":
            logger.info("Script execution cancelled by user.")
            sys.exit(0)
    except KeyboardInterrupt:
        logger.info("\nScript execution cancelled by user.")
        sys.exit(0)

def execute_narrative_scenarios():
    """Execute all narrative scenarios."""
    logger.info("Executing narrative scenarios...")
    
    # Define all scenarios to execute
    scenarios = [
        ("narratives.gl_slip_and_fall", "GL Slip and Fall"),
        ("narratives.auto_collision_at_fault", "Auto Collision At-Fault"),
        ("narratives.property_fire_damage", "Property Fire Damage"),
        ("narratives.auto_theft_comprehensive", "Auto Theft Comprehensive"),
        ("narratives.gl_products_liability", "GL Products Liability"),
        ("narratives.property_water_damage", "Property Water Damage"),
        ("narratives.gl_premises_workplace", "GL Premises Workplace Injury"),
        ("narratives.auto_hit_and_run", "Auto Hit and Run"),
        ("narratives.property_vandalism", "Property Vandalism"),
    ]
    
    # Execute each scenario
    for module_name, scenario_name in scenarios:
        try:
            module = __import__(module_name, fromlist=['populate_scenario'])
            populate_scenario = getattr(module, 'populate_scenario')
            populate_scenario()
        except Exception as e:
            logger.error(f"❌ Failed to execute {scenario_name} scenario: {e}")
            raise e
    
    logger.info("✅ All narrative scenarios completed successfully!")

def main():
    """Main entry point for the data populator."""
    # Safety check - must pass before any operations
    check_execution_safety()
    
    logger.info("🎯 Starting Claimentine Data Populator")
    logger.info("=" * 60)
    
    try:
        # Phase 1: Database reset and essential setup
        logger.info("📋 Phase 1: Database Reset and Essential Setup")
        db_reset_and_essential_setup()
        
        # Phase 2: Create common users and customers
        logger.info("\n📋 Phase 2: Creating Common Users and Customers")
        authenticate_cli()
        create_common_users()
        create_common_customers()
        
        # Phase 3: Execute narrative scenarios
        logger.info("\n📋 Phase 3: Executing Narrative Scenarios")
        execute_narrative_scenarios()
        
        # Phase 4: Final validation and reporting (to be implemented)
        logger.info("\n📋 Phase 4: Final Validation and Reporting")
        logger.info("⚠️ Phase 4 implementation pending...")
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Data population completed successfully!")
        logger.info(f"📊 Summary of created entities:")
        for entity_type, entities in created_data.items():
            if entities:
                logger.info(f"  - {entity_type}: {len(entities)} created")
        
    except KeyboardInterrupt:
        logger.warning("\n⚠️ Data population interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n❌ Data population failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 