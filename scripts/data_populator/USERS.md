# User Accounts Reference

This document provides detailed information about all user accounts created by the Claimentine Data Populator.

## Overview

The data populator creates **8 user accounts** representing a realistic claims management organization structure:

- **1 Administrative User** - For system administration and CLI authentication
- **5 Claims Adjusters** - Various specializations and authority levels
- **2 Managers** - Different management levels and authority

All users (except admin) use the default password: `password123`

## Administrative User

### Admin User
- **Email**: `<EMAIL>`
- **Password**: `admin`
- **Role**: ADMIN
- **Authority Level**: ADMIN
- **Purpose**: System administration and CLI authentication
- **Department**: System
- **Created In**: Phase 1 (Essential Setup)

## Claims Adjusters

### <PERSON> - Senior Claims Adjuster
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADJUSTER
- **Authority Level**: INTERMEDIATE
- **Department**: Claims
- **Job Title**: Senior Claims Adjuster
- **Specialization**: General claims handling with intermediate authority
- **Can Handle**: Most claim types, moderate financial authority

### <PERSON> - Lead Claims Adjuster
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADJUSTER
- **Authority Level**: SENIOR
- **Department**: Claims
- **Job Title**: Lead Claims Adjuster
- **Specialization**: Complex claims, team leadership
- **Can Handle**: High-value claims, complex investigations, mentoring

### Emily Rodriguez - Claims Adjuster
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADJUSTER
- **Authority Level**: BASIC
- **Department**: Claims
- **Job Title**: Claims Adjuster
- **Specialization**: Entry-level adjuster, routine claims
- **Can Handle**: Standard claims, requires supervision for complex cases

### David Thompson - Auto Claims Specialist
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADJUSTER
- **Authority Level**: INTERMEDIATE
- **Department**: Claims
- **Job Title**: Auto Claims Specialist
- **Specialization**: Automotive claims, collision investigation
- **Can Handle**: Auto liability, collision, comprehensive claims

### Lisa Anderson - Property Claims Specialist
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: ADJUSTER
- **Authority Level**: SENIOR
- **Department**: Claims
- **Job Title**: Property Claims Specialist
- **Specialization**: Property damage, commercial property
- **Can Handle**: Fire, water damage, commercial property claims

## Managers

### Robert Wilson - Claims Manager
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: MANAGER
- **Authority Level**: MANAGER
- **Department**: Claims
- **Job Title**: Claims Manager
- **Responsibilities**: Team management, high-value claim approval
- **Reports**: Manages adjuster team, escalation point

### Jennifer Davis - Regional Claims Manager
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Role**: MANAGER
- **Authority Level**: SUPERVISOR
- **Department**: Claims
- **Job Title**: Regional Claims Manager
- **Responsibilities**: Regional oversight, policy implementation
- **Reports**: Oversees multiple locations, strategic decisions

## Authority Levels Explained

### BASIC
- **Financial Limit**: Low (typically under $10,000)
- **Claim Types**: Routine, straightforward claims
- **Approval Required**: For most significant decisions
- **Typical Role**: Entry-level adjusters

### INTERMEDIATE
- **Financial Limit**: Moderate (typically $10,000 - $50,000)
- **Claim Types**: Standard claims, some complexity
- **Approval Required**: For high-value or complex decisions
- **Typical Role**: Experienced adjusters

### SENIOR
- **Financial Limit**: High (typically $50,000 - $250,000)
- **Claim Types**: Complex claims, investigations
- **Approval Required**: For exceptional cases only
- **Typical Role**: Senior adjusters, specialists

### SUPERVISOR
- **Financial Limit**: Very High (typically $250,000+)
- **Claim Types**: All claim types, oversight role
- **Approval Required**: Rare, for policy exceptions
- **Typical Role**: Supervisors, team leads

### MANAGER
- **Financial Limit**: Highest (typically unlimited within policy)
- **Claim Types**: All claim types, final authority
- **Approval Required**: Only for policy changes
- **Typical Role**: Department managers

### ADMIN
- **Financial Limit**: System-level access
- **Claim Types**: System administration
- **Approval Required**: None (system level)
- **Typical Role**: System administrators

## Role Permissions

### ADJUSTER Role Permissions
- View and edit assigned claims
- Create and update claim notes
- Manage claim tasks
- Process payments within authority limit
- Generate standard reports
- Update claim status (within authority)

### MANAGER Role Permissions
- All ADJUSTER permissions
- View all claims in department
- Assign claims to adjusters
- Approve high-value decisions
- Access management reports
- Manage team members
- Override adjuster decisions (within authority)

### ADMIN Role Permissions
- All system permissions
- User management
- System configuration
- Database access
- All reports and analytics
- Policy and configuration changes

## Usage in Narratives

The narrative scenarios utilize these users realistically:

### Claim Assignment Patterns
- **Basic claims** → Emily Rodriguez (BASIC)
- **Auto claims** → David Thompson (Auto Specialist)
- **Property claims** → Lisa Anderson (Property Specialist)
- **Complex claims** → Michael Chen (SENIOR) or Sarah Johnson (INTERMEDIATE)
- **Management oversight** → Robert Wilson or Jennifer Davis

### Workflow Examples
1. **New Claim**: Assigned to appropriate specialist based on type
2. **Escalation**: BASIC → INTERMEDIATE → SENIOR → MANAGER
3. **Approval**: Higher authority users approve lower authority decisions
4. **Supervision**: Managers review and approve significant actions

## Testing Scenarios

These users enable testing of:

### Authority-Based Workflows
- Financial approval limits
- Claim assignment rules
- Escalation procedures
- Override capabilities

### Role-Based Access Control
- Permission enforcement
- Data visibility rules
- Action restrictions
- Report access levels

### Realistic Business Processes
- Team collaboration
- Supervision and mentoring
- Specialization benefits
- Management oversight

## Login Information Summary

| User | Email | Password | Role | Authority | Specialization |
|------|-------|----------|------|-----------|----------------|
| Admin | <EMAIL> | admin | ADMIN | ADMIN | System Admin |
| Sarah Johnson | <EMAIL> | password123 | ADJUSTER | INTERMEDIATE | General Claims |
| Michael Chen | <EMAIL> | password123 | ADJUSTER | SENIOR | Lead Adjuster |
| Emily Rodriguez | <EMAIL> | password123 | ADJUSTER | BASIC | Entry Level |
| David Thompson | <EMAIL> | password123 | ADJUSTER | INTERMEDIATE | Auto Specialist |
| Lisa Anderson | <EMAIL> | password123 | ADJUSTER | SENIOR | Property Specialist |
| Robert Wilson | <EMAIL> | password123 | MANAGER | MANAGER | Claims Manager |
| Jennifer Davis | <EMAIL> | password123 | MANAGER | SUPERVISOR | Regional Manager |

## Customization

To modify the user creation:

1. **Edit `core_utils.py`** in the `create_common_users()` function
2. **Follow the pattern**:
   ```python
   ("First", "Last", "ROLE", "AUTHORITY", "Department", "Job Title")
   ```
3. **Update this documentation** to reflect changes
4. **Test thoroughly** to ensure proper permissions and workflows

## Security Notes

- **Default Passwords**: All users (except admin) use `password123`
- **Production Use**: Change all passwords before production deployment
- **Authority Limits**: Ensure authority levels match business requirements
- **Regular Review**: Periodically review user access and permissions 