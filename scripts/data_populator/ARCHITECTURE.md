# Data Populator Technical Architecture

This document provides detailed technical information about the implementation, design decisions, and architecture of the Claimentine Data Populator system.

## Design Philosophy

### Selective Code Duplication Approach
The data populator uses a **selective code duplication** strategy rather than running full backend setup and cleanup:

**Benefits:**
- **Clean Execution**: No unwanted data or side effects
- **Controlled Environment**: Precise control over what gets created
- **Performance**: Faster execution without unnecessary operations
- **Reliability**: Reduced complexity and failure points
- **Maintainability**: Clear separation of concerns

**Implementation:**
- Essential data is duplicated from backend models
- CLI commands are used for entity creation
- Database operations are direct and targeted
- No dependency on full backend initialization

### Phase-Based Architecture
The system is organized into distinct phases for clarity and error isolation:

1. **Phase 1**: Database reset and essential setup (no CLI dependency)
2. **Phase 2**: Common entities via CLI (requires authentication)
3. **Phase 3**: Narrative scenarios (complex interconnected data)
4. **Phase 4**: Validation and reporting (verification and summary)

## Technical Components

### Core Architecture

```
data_populator/
├── main.py              # Orchestrator - coordinates all phases
├── core_utils.py        # Core utilities and entity creation
└── narratives/          # Individual scenario implementations
    ├── auto_*.py        # Auto claim scenarios
    ├── property_*.py    # Property claim scenarios
    └── gl_*.py          # General liability scenarios
```

### Key Design Patterns

#### 1. Command Pattern
CLI operations are wrapped in a consistent command pattern:

```python
def run_cli_command(command: List[str], cwd: str = None) -> Dict[str, Any]:
    """Execute CLI command and return parsed JSON result."""
    result = subprocess.run(command, cwd=cwd, capture_output=True, text=True, check=True)
    return json.loads(result.stdout)
```

#### 2. Factory Pattern
Entity creation functions follow a factory pattern:

```python
def create_user(first_name, last_name, email, role, authority_role, ...):
    """Create user via CLI and store in created_data."""
    # Build command
    # Execute via CLI
    # Parse result
    # Store in tracking dictionary
    # Return result
```

#### 3. Registry Pattern
Created entities are tracked in a central registry:

```python
created_data = {
    "users": {},
    "customers": {},
    "claims": {},
    "notes": {},
    "tasks": {},
    # ... other entity types
}
```

## Implementation Details

### Database Operations

#### Direct Database Access (Phase 1)
Phase 1 uses direct database operations for essential setup:

```python
def db_reset_and_essential_setup():
    """Reset database and insert essential configuration."""
    # 1. Drop and recreate all tables
    # 2. Insert permissions directly
    # 3. Insert authority levels directly
    # 4. Insert reserve configurations directly
    # 5. Create admin user directly
```

**Rationale:**
- No CLI dependency during database reset
- Ensures clean state before CLI operations
- Direct control over essential configuration
- Faster execution for system-level setup

#### CLI-Based Operations (Phases 2-3)
Phases 2-3 use CLI commands for entity creation:

```python
def create_claim(claim_type, customer_id, description, claimant_name, **kwargs):
    """Create claim via CLI command."""
    command = ["poetry", "run", "clm", "claims", "create", ...]
    result = run_cli_command(command, cwd="../../cli")
    # Store and return result
```

**Rationale:**
- Leverages existing CLI validation and business logic
- Ensures consistency with normal application usage
- Tests CLI functionality as part of data population
- Maintains proper audit trails and logging

### Error Handling Strategy

#### Hierarchical Error Handling
```python
try:
    # Phase execution
    phase_function()
except SpecificError as e:
    # Handle specific error types
    logger.error(f"Specific error: {e}")
    # Attempt recovery or cleanup
except Exception as e:
    # Handle unexpected errors
    logger.error(f"Unexpected error: {e}")
    # Log full traceback
    # Exit gracefully
```

#### Error Isolation
- Each phase is isolated from others
- Narrative scenarios are independent
- Database reset ensures clean recovery
- Detailed logging for troubleshooting

### Data Generation Strategy

#### Realistic Data with Faker
```python
from faker import Faker
fake = Faker()

def generate_realistic_name() -> tuple[str, str]:
    """Generate realistic first and last names."""
    return fake.first_name(), fake.last_name()

def generate_realistic_email(first_name: str, last_name: str, domain: str = None) -> str:
    """Generate realistic email address."""
    domain = domain or "claimentine.com"
    return f"{first_name.lower()}.{last_name.lower()}@{domain}"
```

#### Deterministic Patterns
While using Faker for realism, certain patterns are deterministic:
- User roles and authority levels follow business logic
- Customer types represent diverse industries
- Claim scenarios follow realistic workflows
- Financial amounts are within reasonable ranges

### CLI Integration

#### Authentication Management
```python
def authenticate_cli() -> None:
    """Authenticate CLI with admin user."""
    admin_data = created_data["users"]["admin"]
    command = [
        "poetry", "run", "clm", "auth", "login",
        "--email", admin_data["email"],
        "--password", admin_data["password"]
    ]
    subprocess.run(command, cwd="../../cli", check=True)
```

#### Command Execution
```python
def run_cli_command(command: List[str], cwd: str = None) -> Dict[str, Any]:
    """Execute CLI command and return parsed JSON result."""
    try:
        result = subprocess.run(
            command,
            cwd=cwd,
            capture_output=True,
            text=True,
            check=True
        )
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        logger.error(f"CLI command failed: {' '.join(command)}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        raise e
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response: {e}")
        logger.error(f"Raw output: {result.stdout}")
        raise e
```

## Data Model Integration

### Entity Relationships
The data populator creates realistic entity relationships:

```
Customer
├── Claims (multiple)
│   ├── Financials (1:1)
│   ├── Notes (1:many)
│   ├── Tasks (1:many)
│   ├── Injured Persons (1:many)
│   │   └── Injuries (1:many)
│   ├── Witnesses (1:many)
│   └── Attorneys (1:many)
└── Users (assigned adjusters)
```

### Data Consistency
- Foreign key relationships are maintained
- Business rules are enforced through CLI validation
- Data integrity is verified through the existing system
- Audit trails are automatically created

## Performance Considerations

### Execution Time
- **Phase 1**: ~10-15 seconds (database operations)
- **Phase 2**: ~30-45 seconds (user/customer creation)
- **Phase 3**: ~60-90 seconds (narrative scenarios)
- **Total**: ~2-3 minutes for complete population

### Memory Usage
- Minimal memory footprint
- CLI processes are short-lived
- Data tracking dictionary is lightweight
- No large data structures held in memory

### Database Impact
- Single transaction per CLI command
- Proper indexing utilized
- No bulk operations that could lock tables
- Graceful handling of concurrent access

## Security Considerations

### Authentication
- Admin user created with known credentials
- CLI authentication uses standard login process
- No hardcoded secrets in production paths
- Proper credential management for different environments

### Data Privacy
- All generated data is synthetic
- No real personal information used
- Faker library ensures realistic but fake data
- Safe for development and demonstration use

### Access Control
- Respects existing permission system
- Users created with appropriate authority levels
- Role-based access control maintained
- No privilege escalation or bypassing

## Extensibility

### Adding New Entity Types
1. **Create factory function** in `core_utils.py`
2. **Add CLI command wrapper** following existing patterns
3. **Update tracking dictionary** structure
4. **Add to narrative scenarios** as needed

### Adding New Narratives
1. **Create scenario file** in `narratives/` directory
2. **Implement `populate_scenario()`** function
3. **Follow existing patterns** for consistency
4. **Add to main.py** execution list

### Modifying Existing Scenarios
1. **Edit scenario files** directly
2. **Maintain realistic data** patterns
3. **Update documentation** as needed
4. **Test with clean database**

## Testing Strategy

### Unit Testing
- Individual utility functions can be unit tested
- Mock CLI commands for isolated testing
- Validate data generation functions
- Test error handling scenarios

### Integration Testing
- Full execution with clean database
- Verify entity relationships
- Validate business rule enforcement
- Test CLI integration points

### End-to-End Testing
- Complete data population cycle
- Verify system functionality with populated data
- Test user workflows with created entities
- Validate reporting and analytics

## Monitoring and Logging

### Logging Strategy
```python
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Usage patterns
logger.info("✅ Success message")
logger.warning("⚠️ Warning message")
logger.error("❌ Error message")
```

### Progress Tracking
- Phase-based progress reporting
- Entity creation counters
- Execution time tracking
- Success/failure indicators

### Error Reporting
- Detailed error messages
- Full stack traces for debugging
- CLI command output capture
- Recovery suggestions

## Deployment Considerations

### Environment Requirements
- Python 3.8+ with required packages
- PostgreSQL database access
- Claimentine backend running
- Claimentine CLI installed and functional

### Configuration Management
- Environment-specific settings
- Database connection parameters
- CLI path configuration
- Logging level configuration

### Automation Integration
- Can be integrated into CI/CD pipelines
- Suitable for automated testing environments
- Docker container compatibility
- Kubernetes job execution

## Future Enhancements

### Potential Improvements
1. **Parallel Execution**: Run narrative scenarios in parallel
2. **Configuration Files**: External configuration for scenarios
3. **Data Validation**: Enhanced validation of created data
4. **Rollback Capability**: Selective rollback of specific entities
5. **Performance Metrics**: Detailed performance monitoring
6. **Custom Scenarios**: User-defined scenario templates

### Scalability Considerations
- Support for larger datasets
- Batch processing capabilities
- Distributed execution options
- Cloud deployment strategies

## Troubleshooting Guide

### Common Issues and Solutions

#### "CLI Authentication Failed"
- **Cause**: Backend not running or admin user creation failed
- **Solution**: Verify backend status, check database connectivity
- **Debug**: Check Phase 1 logs for admin user creation

#### "Command Not Found: poetry"
- **Cause**: Poetry not installed or not in PATH
- **Solution**: Install Poetry or update PATH
- **Debug**: Test `poetry --version` from CLI directory

#### "Database Connection Failed"
- **Cause**: PostgreSQL not running or configuration issues
- **Solution**: Start PostgreSQL, verify connection settings
- **Debug**: Test database connection independently

#### "JSON Parse Error"
- **Cause**: CLI command returned non-JSON output
- **Solution**: Check CLI command syntax and output format
- **Debug**: Run CLI command manually to see raw output

### Debugging Techniques
1. **Enable verbose logging** for detailed output
2. **Run phases individually** to isolate issues
3. **Check CLI commands manually** to verify functionality
4. **Examine database state** after each phase
5. **Review error logs** for specific failure points

This technical architecture provides the foundation for understanding, maintaining, and extending the Claimentine Data Populator system. 